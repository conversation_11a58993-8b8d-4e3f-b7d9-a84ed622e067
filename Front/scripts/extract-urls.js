const fs = require('fs');
const path = require('path');

// 递归获取所有js和ts文件
function getAllFiles(dirPath, arrayOfFiles) {
  const files = fs.readdirSync(dirPath);

  arrayOfFiles = arrayOfFiles || [];

  files.forEach((file) => {
    const filePath = path.join(dirPath, file);
    if (fs.statSync(filePath).isDirectory()) {
      arrayOfFiles = getAllFiles(filePath, arrayOfFiles);
    } else {
      // 只处理js和ts文件
      if (filePath.match(/\.(js|ts)$/)) {
        arrayOfFiles.push(filePath);
      }
    }
  });

  return arrayOfFiles;
}

try {
  const urls = new Set(); // 使用Set去重
  const serviceDir = 'src/service';
  const files = getAllFiles(serviceDir);

  // 处理每个文件
  files.forEach((file) => {
    const content = fs.readFileSync(file, 'utf-8');
    const urlPattern = /url:\s*'([^']+)'/g;
    let match;

    while ((match = urlPattern.exec(content)) !== null) {
      urls.add(match[1]); // 保存完整URL路径
    }
  });

  // 格式化输出内容，每个URL独占一行
  const output = Array.from(urls).join('\n');

  // 写入到url.txt文件
  fs.writeFileSync('url.txt', output, 'utf-8');
  console.log('URL列表已成功保存到 url.txt');
  console.log(`共处理 ${files.length} 个文件，提取 ${urls.size} 个URL`);
} catch (error) {
  console.error('发生错误：', error.message);
}
