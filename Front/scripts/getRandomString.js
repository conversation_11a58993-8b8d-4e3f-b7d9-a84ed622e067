const readline = require('readline');
const fs = require('fs');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function generateRandomString(length) {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

async function main() {
  const prefix = await new Promise((resolve) => {
    rl.question('请输入JSON key的前缀: ', resolve);
  });

  const count = await new Promise((resolve) => {
    rl.question('请输入要生成的数量: ', resolve);
  });

  const numCount = parseInt(count, 10);
  if (isNaN(numCount) || numCount <= 0) {
    console.log('请输入有效的正整数！');
    rl.close();
    return;
  }

  const result = {};
  for (let i = 0; i < numCount; i++) {
    const randomStr = generateRandomString(12);
    const key = `${prefix}-${randomStr}`;
    result[key] = '';
  }

  fs.writeFileSync('random.json', JSON.stringify(result, null, 2));
  console.log(`已生成random.json文件，包含 ${numCount} 个随机key`);

  rl.close();
}

main().catch(console.error);
