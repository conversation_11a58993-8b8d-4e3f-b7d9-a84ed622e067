import { useReducer } from 'react';

type SetStateAction<T> = Partial<T> | ((prevState: T) => Partial<T>);

function useObjectState<T extends object>(initialState: T) {
  return useReducer((state: T, setStateAction: SetStateAction<T>) => {
    if (typeof setStateAction === 'function') {
      return { ...state, ...setStateAction(state) };
    }
    return { ...state, ...setStateAction };
  }, initialState);
}

export default useObjectState;
