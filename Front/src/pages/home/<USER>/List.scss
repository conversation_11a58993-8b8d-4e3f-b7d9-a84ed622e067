.VarStyle {
  // overflow-y: auto;
  height: 100%;

  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 -24px 24px;
    padding: 24px 24px 0;

    h1 {
      font-size: 20px !important;
      margin: 0;
    }

    .btnGroup {
      button {
        &:first-child {
          margin-right: 16px;
        }
      }
    }

    .rightSide {
      display: flex;

      .ant-select-selector {
        border-top-left-radius: 6px !important;
        border-bottom-left-radius: 6px !important;
        border: 1px solid #fff;
      }

      .ant-select-selector:hover {
        border-color: $border_hover !important;
      }

      .ant-select-selector:focus {
        border-color: $border_hover !important;
      }

      .ant-input-affix-wrapper {
        border-top-right-radius: 6px !important;
        border-bottom-right-radius: 6px !important;
        border: 1px solid #fff;
        border-left-color: #d9d9d9 !important;

        .ant-input-suffix {
          color: rgba(0, 0, 0, 0.45);
        }
      }

      .ant-input-affix-wrapper:hover {
        border-color: $border_hover !important;
      }

      .ant-input-affix-wrapper:focus {
        border-color: $border_hover !important;
      }

      .DTButton {
        margin-left: 8px;
      }
    }
  }

  .filter {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .smallTitle {
      font-size: 18px;
      font-weight: bold;
    }
  }

  .tableList {
    background-color: white;
    padding: 24px;

    .toolbar {
      display: flex;
      justify-content: space-between;
      margin-bottom: 24px;

      .title {
        font-family: PingFangSC-Medium;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 24px;
        font-weight: 500;
      }
    }
  }

}