import React, { Component } from 'react';
import { BarsOutlined, SmileOutlined, SolutionOutlined, UserOutlined } from '@ant-design/icons';
import { Steps } from 'antd';
import './createTitle.scss';

const { Step } = Steps;

class CreateTitle extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // nowStep: this.props.nowStep
    };
  }

  // onChange(current) {
  //   console.log(current);
  //   this.setState({ nowStep: current });
  // }

  render() {
    return (
      <div className="stepTitleBox">
        <Steps
          type="navigation"
          size="small"
          current={this.props.nowStep}
          // onChange={this.onChange.bind(this)}
        >
          <Step title="填写基本信息" icon={<SolutionOutlined />} />
          <Step title="选择目标客户" icon={<UserOutlined />} />
          <Step title="设置活动规则" icon={<BarsOutlined />} />
          <Step title="保存活动配置" icon={<SmileOutlined />} />
        </Steps>
      </div>
    );
  }
}

export default CreateTitle;
