import React, { Component } from 'react';
import { CheckCircleFilled, LeftOutlined } from '@ant-design/icons';
import './createTitle.scss';

class CreateTitle extends Component {
  constructor(props) {
    super(props);
    this.props = props;
    // console.log(this.props);
  }

  render() {
    return (
      <div className="titleBox">
        <div className="campaignCreateTitle">
          <LeftOutlined className="leftIcon" style={{ fontWeight: '800' }} />
          <span className="marketCreateTitleName">{this.props.createTitle.TitleName}</span>
        </div>
        {/* 进度步骤 */}
        <div className="progressStepBox">
          <div className="progressStep">
            {/* 第一步 */}
            <div className="firstStep step">
              <div className="stepContent1 stepContent">
                <CheckCircleFilled style={{ fontSize: '20px', color: '#00B659' }} />
                <span>{this.props.createTitle.zeroStepName}</span>
              </div>
            </div>
            {/* 第二步 */}
            <div className={`${this.props.createTitle.nowStep === 2 && 'active'} step second`}>
              <div className="border-right-empty secondStepLeft leftBorder">
                <span />
              </div>
              <div className="stepContent2 stepContent">
                {this.props.createTitle.nowStep === 2 ? (
                  <div className="step2Icon stepIcon">2</div>
                ) : (
                  <CheckCircleFilled style={{ fontSize: '20px', color: '#00B659' }} />
                )}
                <span>填写基本信息</span>
              </div>
              <div className="border-right-empty secondStepRight rightBorder">
                <span />
              </div>
            </div>
            {/* 第三步 */}
            {/* <div className={this.props.createTitle.nowStep === 3 ? 'active thirdStep step' : 'thirdStep step'}> */}
            <div className={`${this.props.createTitle.nowStep === 3 && 'active'} thirdStep step`}>
              <div className="stepContent3 stepContent">
                {/* {
                      this.props.createTitle.nowStep >= 3
                        ? <Icon type="check-circle" />
                        : <div className="step2Icon stepIcon"><span>3</span></div>
                    } */}
                <div className="step3Icon stepIcon">3</div>
                <span>设置活动规则</span>
              </div>
              <div className="border-right-empty thirdStepRight rightBorder">
                <span />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default CreateTitle;
