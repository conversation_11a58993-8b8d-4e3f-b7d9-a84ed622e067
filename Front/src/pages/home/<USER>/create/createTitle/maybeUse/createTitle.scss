// @import 'assets/css/variable.scss';

$noActiveBorder: #ddd;
$activeBorder: #ff7b49;
$activeBackground: #ffece4;

$arrowDivHieght: 20px;
$arrowDivWidth: 30px;

$arrowSpanHeight: 19px;
$arrowSpanWidth: 29px;

.titleBox {
  .campaignCreateTitle {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #2095e9;
    font-size: 18px;
    margin-bottom: 15px;

    .leftIcon {
      font-weight: bolder;
    }

    .marketCreateTitleName {
      margin-left: 10px;
      display: inline-block;
      font-size: 16px;
      color: #333;
    }
  }

  .progressStepBox {
    background: $white;
    display: flex;
    align-items: center;
    justify-content: center;

    .progressStep {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 15px;

      .step {
        position: relative;

        .stepContent1 {
          width: 100px !important;
          border-left: 1px solid $noActiveBorder;
        }

        .stepContent {
          border-top: 1px solid $noActiveBorder;
          border-bottom: 1px solid $noActiveBorder;
          height: 40px;
          width: 150px;
          display: flex;
          align-items: center;
          justify-content: flex-end;

          span {
            margin-left: 10px;
          }

          .stepIcon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: $noActiveBorder;
            color: $white;
            display: flex;
            align-items: center;
            justify-content: center;

            span {
              display: block;
            }
          }
        }

        .border-right-empty {
          width: 0;
          height: 0;
          border-left: $arrowDivWidth solid $noActiveBorder;
          border-bottom: $arrowDivHieght solid transparent;
          border-top: $arrowDivHieght solid transparent;
          margin-right: 0px;
          position: absolute;

          span {
            display: block;
            width: 0;
            height: 0;
            border-left: $arrowSpanWidth solid $white;
            border-bottom: $arrowSpanHeight solid transparent;
            border-top: $arrowSpanHeight solid transparent;
            position: absolute;
            left: -30px;
            top: -$arrowSpanHeight;
          }
        }

        .secondStepLeft {
          span {
            border-left: $arrowSpanWidth solid $white  !important;
          }
        }

        .secondStepRight {
          z-index: 1;
          border-left: $arrowDivWidth solid $activeBorder;
        }

        .rightBorder {
          right: -30px;
          top: 0px;
        }
      }

      .active {
        .stepContent {
          border-top: 1px solid $activeBorder;
          border-bottom: 1px solid $activeBorder;
          background: $activeBackground;

          .stepIcon {
            background: $activeBorder;
          }
        }

        .border-right-empty {
          border-left: $arrowDivWidth solid $activeBorder;

          span {
            border-left: $arrowSpanWidth solid $activeBackground;
          }
        }
      }
    }
  }
}

.ant-form-item-children {
  width: 100%;
  display: inline-block;

  .ant-calendar-picker {
    width: 100% !important;
    min-width: 0 !important;
  }
}