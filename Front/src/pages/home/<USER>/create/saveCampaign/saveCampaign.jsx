import React, { Component } from 'react';
import {
  Button
  // Icon
} from 'antd';
import { Link } from 'react-router-dom';
import './saveCampaign.scss';

class saveCampaign extends Component {
  // constructor(props) {
  //   super(props);
  //   this.props = props;
  // }

  saveEffect() {
    // console.log('aaa');
  }

  render() {
    return (
      <div className="saveCampaignBox">
        <div className="createSecondTitle">
          <span className="headName">设置活动规则</span>
          <div className="nextOrNo">
            <Button
              onClick={this.props.callBack.bind(null, {
                type: 'saveCampaignBack'
              })}
            >
              上一步
            </Button>
          </div>
        </div>
        <div className="saveContent">
          {/* <div className="saveTest saveItem">
            <div className="heartBox">
              <Icon type="heart" theme="filled" style={{ fontSize: '50px', color: 'pink' }} />
            </div>
            <p>执行一次测试活动</p>
            <Button type="primary" className="colorGreen">发送测试邮件</Button>
          </div> */}
          <div className="saveEffect saveItem">
            <div className="heartBox">
              <svg className="icon" viewBox="0 0 1024 1024" width="50" height="50" fill="pink">
                <path d="M63.791885 555.886533l896.41623-448.208115L790.262538 783.725658 459.709053 679.144788l295.070342-360.434026-380.976898 334.288552L63.791885 555.886533 63.791885 555.886533z" />
                <path d="M448.50385 731.435735l108.316961 31.747051-84.039022 153.137773L448.50385 731.435735 448.50385 731.435735z" />
              </svg>
            </div>
            <p>保存后，自动执行</p>
            <Link to="/aimarketer/home/<USER>">
              <Button type="primary" onClick={this.saveEffect.bind(this)}>
                保存
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }
}

export default saveCampaign;
