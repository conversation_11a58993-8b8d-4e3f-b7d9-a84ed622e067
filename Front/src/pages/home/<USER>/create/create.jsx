import React, { Component } from 'react';
import { Breadcrumb, Spin, message } from 'antd';
import { Link } from 'react-router-dom';
import _ from 'lodash';
import CampaignManageService from '../univelsal/campaignManageService';
import CreateTitle from './createTitle/createTitle';
import CreateBase from './createBase/createBase';
import SelectUser from './selectUser/selectUser';
import SetRule from './setRule/setRule';
import SaveCampaign from './saveCampaign/saveCampaign';
import CreateRule from './createRule/createRule';
import './create.scss';

class create extends Component {
  constructor(props) {
    super(props);
    this.campaignManageService = new CampaignManageService();
    this.state = {
      nowStep: 0, // 步骤进度，从0开始表示，第一步：填写基本信息、第二步：选择目标用户、第三步：设置活动规则、第四部：保存活动规则调准到活动列表页。
      campaignId: '', // 活动id，基本信息填写完成后生成
      createFormData: {}, // 填写基本信息数据
      selectUserData: [], // 选择用户数据
      counts: null, // 保存目标用户计算人数
      setRuleData: [], // 设置活动规则数据
      nowComponent: '', // 当前显示组件，与进度同步
      init: false, // 初始化
      isLoading: true // 默认有loading图标
    };
    this.callBack = this.callBack.bind(this);
  }

  async componentDidMount() {
    this.setState({ init: true, isLoading: false });
    const { pathname } = this.props.history.location;
    const EventId = pathname.slice(pathname.lastIndexOf('/') + 1);
    // 当从url截取到数字时，代表是编辑活动，像后端请求数据
    if (!isNaN(EventId)) {
      this.EventId = EventId;
      this.setState({ isLoading: true });
      try {
        const alldata = await this.campaignManageService.getCampaign(EventId);
        this.setState(
          {
            createFormData: alldata.campaign,
            selectUserData: alldata.campaignSegments,
            setRuleData: alldata.campaignRules,
            isLoading: false
          },
          () => {
            this.temporary();
          }
        );
      } catch (error) {
        message.error('数据加载失败', 1.5);
      }
    } else {
      this.temporary();
    }
  }

  temporary() {
    const { nowStep, selectUserData, createFormData, setRuleData, campaignId, segmentId, counts } = this.state;
    switch (nowStep) {
      case 0:
        this.setState({
          nowComponent: <CreateBase callBack={this.callBack} createFormData={createFormData} />
        });
        break;
      case 1:
        this.setState({
          nowComponent: <SelectUser callBack={this.callBack} selectUserData={selectUserData} counts={counts} />
        });
        break;
      case 2:
        this.setState({
          nowComponent: (
            <SetRule
              callBack={this.callBack}
              setRuleData={setRuleData}
              segmentId={segmentId}
              campaignId={campaignId}
              mode="edit"
            />
          )
        });
        break;
      case 3:
        this.setState({
          nowComponent: <SaveCampaign callBack={this.callBack} />
        });
        break;
      case 4:
        this.setState({
          nowComponent: <CreateRule callBack={this.callBack} />
        });
        break;
      default:
        break;
    }
  }

  callBack(data) {
    const { selectUserData, createFormData, setRuleData, campaignId, counts } = this.state;
    const segmentId = selectUserData.length && selectUserData[0].segmentId;
    switch (data.type) {
      // 填写基本表单下一步
      case 'createBaseNext':
        this.setState({
          nowStep: 1,
          createFormData: data.value,
          campaignId: data.value.id,
          nowComponent: (
            <SelectUser
              callBack={this.callBack}
              selectUserData={selectUserData}
              createFormData={data.value}
              counts={counts}
            />
          )
        });
        break;
      // 取消填写基本表单
      case 'createBaseCancel':
        this.props.history.go(-1);
        break;
      // 选择用户下一步
      case 'selectUserNext':
        _.merge(setRuleData[0], { segmentId: data.value[0].segmentId });
        this.setState({
          nowStep: 2,
          selectUserData: data.value,
          counts: data.counts,
          segmentId: data.value[0].segmentId,
          nowComponent: (
            <SetRule
              callBack={this.callBack}
              setRuleData={setRuleData}
              segmentId={data.value[0].segmentId}
              campaignId={campaignId}
            />
          )
        });
        break;
      // 选择用户上一步
      case 'selectUserBack':
        this.setState({
          nowStep: 0,
          // selectUserData: data.value,
          counts: data.counts || null,
          nowComponent: <CreateBase callBack={this.callBack} createFormData={createFormData} />
        });
        break;
      // 设置活动规则下一步
      case 'setRuleNext':
        this.setState({
          nowStep: 3,
          setRuleData: data.value,
          nowComponent: <SaveCampaign callBack={this.callBack} />
        });
        break;
      // 设置活动规则上一步
      case 'setRuleBack':
        this.setState({
          nowStep: 1,
          nowComponent: (
            <SelectUser
              callBack={this.callBack}
              selectUserData={selectUserData}
              createFormData={createFormData}
              counts={counts}
            />
          )
        });
        break;
      // 保存活动上一步
      case 'saveCampaignBack':
        this.setState({
          nowStep: 2,
          nowComponent: (
            <SetRule setRuleData={setRuleData} callBack={this.callBack} segmentId={segmentId} campaignId={campaignId} />
          )
        });
        break;

      case 'createRuleSave':
        this.setState({
          nowComponent: <SelectUser callBack={this.callBack} selectUserData={selectUserData} />
        });
        break;
      case 'createRuleBack':
        this.setState({
          nowStep: 2,
          nowComponent: <CreateBase callBack={this.callBack} createFormData={createFormData} />
        });
        break;
      default:
        break;
    }
  }

  // 配置数据转换为option，在setRule组件中和createBase组件中都有用到
  // changeStep(value) {
  //   console.log(value);
  // }

  render() {
    return (
      this.state.init && (
        <div className="campaignCreateBox">
          <Breadcrumb>
            <Breadcrumb.Item>
              <Link to="/aimarketer/home/<USER>">活动列表</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>{this.EventId ? '编辑营销活动' : '新建营销活动'}</Breadcrumb.Item>
          </Breadcrumb>
          <CreateTitle
            nowStep={this.state.nowStep}
            // changeStep={this.changeStep.bind(this)}
          />
          {this.state.isLoading ? <Spin tip="Loading..." /> : this.state.nowComponent}
        </div>
      )
    );
  }
}

export default create;
