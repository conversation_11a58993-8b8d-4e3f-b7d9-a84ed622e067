export default {
  cn: {
    'operationCenter-OtMN1NpsMd1Y': '保存成功',
    'operationCenter-lo8HNDxg3v39': '请添加流程配置',
    'operationCenter-GDja0OrzcwOZ': '未完成填写[{{name}}]',
    'operationCenter-uuzBFmx4r2CH': '[{{name}}]检验未通过({{errMsg}})',
    'operationCenter-z7YiK2csdTwQ': '审批中',
    'operationCenter-w9i9ksFFuDy8': '审批通过',
    'operationCenter-8OIvF3o1AKJx': '驳回',
    'operationCenter-ux7ICkmsl4EG': '已撤销',
    'operationCenter-bPiln6LdkkTl': '已取消',
    'operationCenter-QNFwSg8RAvP0': '待提交审批',
    'operationCenter-YsWc6lKyCvra': '分群不可用：节点ID[{{nodeId}}] [{{id}}]{{name}} 处于草稿/禁用状态',
    'operationCenter-0neg2YxlKzSn': '分群不可用：节点ID[{{nodeId}}] [{{id}}]{{name}} 为{{status}}状态',
    'operationCenter-D94Rzs9mG5kf': '是否保存当前内容为草稿',
    'operationCenter-cDDn0wBie8v7': '保存',
    'operationCenter-vOxoHKaLw7dc': '不保存',
    'operationCenter-qoNKDnG5bq03': '退出创建',
    'operationCenter-Q2yqQFHME9of': '上一步',
    'operationCenter-WYfqlXa7zoMJ': '保存画布草稿 版本: V{{version}}',
    'operationCenter-4T0Yl5d9OycK': '测试画布 版本: V{{version}}',
    'operationCenter-eerBQOfNx7k5': '保存为草稿',
    'operationCenter-Jou1581EaErs': '测试版本: V{{version}}',
    'operationCenter-R5jEByK7FGMU': '测试流程',
    'operationCenter-zhJgFUtXfsLI': '活动组件',
    'operationCenter-GX0K6iDudVM1': '组件箱数据加载中',
    'operationCenter-0WhuqVYG4Ejn': '暂未有该节点的处理',
    'operationCenter-BWq6rUJ7squt': '人',
    'operationCenter-cxA1AXGQr5Vw': '无流程数据'
  },
  en: {
    'operationCenter-OtMN1NpsMd1Y': 'Save successful',
    'operationCenter-lo8HNDxg3v39': 'Please add process configuration',
    'operationCenter-GDja0OrzcwOZ': 'Incomplete configuration for [{{name}}]',
    'operationCenter-uuzBFmx4r2CH': '[{{name}}] validation failed ({{errMsg}})',
    'operationCenter-z7YiK2csdTwQ': 'Under Review',
    'operationCenter-w9i9ksFFuDy8': 'Approved',
    'operationCenter-8OIvF3o1AKJx': 'Rejected',
    'operationCenter-ux7ICkmsl4EG': 'Withdrawn',
    'operationCenter-bPiln6LdkkTl': 'Cancelled',
    'operationCenter-QNFwSg8RAvP0': 'Pending Approval',
    'operationCenter-YsWc6lKyCvra': 'Segment unavailable: Node ID[{{nodeId}}] [{{id}}]{{name}} is in draft/disabled status',
    'operationCenter-0neg2YxlKzSn': 'Segment unavailable: Node ID[{{nodeId}}] [{{id}}]{{name}} is in {{status}} status',
    'operationCenter-D94Rzs9mG5kf': 'Save current content as draft?',
    'operationCenter-cDDn0wBie8v7': 'Save',
    'operationCenter-vOxoHKaLw7dc': "Don't Save",
    'operationCenter-qoNKDnG5bq03': 'Exit Creation',
    'operationCenter-Q2yqQFHME9of': 'Previous Step',
    'operationCenter-WYfqlXa7zoMJ': 'Save Canvas Draft Version: V{{version}}',
    'operationCenter-4T0Yl5d9OycK': 'Test Canvas Version: V{{version}}',
    'operationCenter-eerBQOfNx7k5': 'Save as Draft',
    'operationCenter-Jou1581EaErs': 'Test Version: V{{version}}',
    'operationCenter-R5jEByK7FGMU': 'Test Process',
    'operationCenter-zhJgFUtXfsLI': 'Activity Components',
    'operationCenter-GX0K6iDudVM1': 'Loading component box data',
    'operationCenter-0WhuqVYG4Ejn': 'No handler for this node type yet',
    'operationCenter-BWq6rUJ7squt': ' people',
    'operationCenter-cxA1AXGQr5Vw': 'No process data'
  }
};
