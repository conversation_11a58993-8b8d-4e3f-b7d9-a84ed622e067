// @import 'assets/css/variable.scss';

.campaignCreateBox {
  .ant-breadcrumb {
    padding: 15px 0;
  }

  // background:;
  // 二级导航栏样式
  .createSecondTitle {
    margin-top: 20px;
    padding: 10px 30px;
    background: $white;
    border-bottom: 1px solid #ccc;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .headName {
      font-weight: bold;
    }

    .nextOrNo {
      button {
        margin-left: 20px;
      }
    }
  }

  // 增加选项的小按钮框
  .addItem {
    border-radius: 15px;
    margin: 0;
    height: 25px;
    padding: 0 10px;
  }

  .ant-spin {
    position: absolute;
    left: 50%;
    top: 300px;
  }
}