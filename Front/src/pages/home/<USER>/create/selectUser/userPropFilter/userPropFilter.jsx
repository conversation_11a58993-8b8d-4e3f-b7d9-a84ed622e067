import React, { Component } from 'react';
import FilterListGroup from 'components/filter/filterListGroup';
import FilterListGroupModel from 'components/filter/filterListGroupModel';
import DataEngineService from 'pages/home/<USER>/univelsal/dataEngineService';
// import DictionaryService from 'service/dictionaryService';
import { connect } from 'react-redux';
import _ from 'lodash';
import Log from 'utils/log';

const dataEngineService = new DataEngineService();
let filterListGroupModel = new FilterListGroupModel();
// const dictionaryService = new DictionaryService();

// const typeId = 'reference';
// const TABLE_ID_KEY = 'EVENT_TABLE_ID';
const log = Log.getLogger('UserPropFilter');
const mapStateToProps = (state) => {
  return {
    referenceData: state.referencedata
  };
};

class UserPropFilter extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: null,
      detailClosable: false, // 过滤标签是否可以删除
      mode: 'edit', // edit all detail,
      isInited: false, // 是否已经初始化完成
      businessType: 'SEGMENT_BUSINESS',
      level1List: [],
      level2List: [],
      propertyList: [],
      ...props
    };
  }

  static getDerivedStateFromProps(props, state) {
    log.debug('getDerivedStateFromProps', props, state);
    return {
      ...state,
      ...props
    };
  }

  componentDidMount() {
    log.debug('componentDidMount');
    this.initData();
  }

  async initData() {
    log.debug('initData');
    const { businessType } = this.state;
    try {
      // /** 获取tableId */
      // const reList = await dictionaryService.getOneLevelData(typeId);
      // const tableId = _.find(reList, ['code', TABLE_ID_KEY]).value;
      // if (!tableId) {
      //   return log.error(`${reList} 中不包含 ${TABLE_ID_KEY}, 无法得到tableId`);
      // }

      /** 获取level1/level2数据 */
      let [level1List, level2List] = await Promise.all([
        dataEngineService.level1List({ businessType }),
        dataEngineService.level2List({ businessType })
      ]);
      /** 清洗level1/level2数据，如果name和value都是空字符串或者null，去掉这行 */
      level1List = level1List.filter((l) => l.name && l.value);
      level2List = level2List.filter((l) => l.name && l.value);

      /** 获取属性list并转化数据 */
      const propertyList = await dataEngineService.propertyList({
        businessType
      });
      await this.conversionPropList(propertyList);

      this.setState({ isInited: true, level1List, level2List, propertyList });
    } catch (error) {
      log.error(error);
    }
  }

  /** 转化property数据 */
  async conversionPropList(list) {
    const { businessType } = this.state;
    if (_.isArray(list)) {
      await Promise.all(
        list.map(async (data) => {
          data.tableSchemaId = data?.tableSchema?.id;
          data.tableId = data.table?.id;
          data.type = data.tableSchema?.dataType;
          data.name = data.tableSchema?.displayName;
          data.value = data.tableSchema?.name;
          if (data.isEnum) {
            if (data.enumValue) {
              data.items = data.enumValue ? JSON.parse(data.enumValue) : [];
            } else {
              const items = await dataEngineService.findFilterEnum({
                businessType,
                tableId: data.table?.id,
                schemaId: data.tableSchema?.id
              });
              data.items = items.map((d) => d.value).filter((item) => item);
            }
          }
        })
      );
    }
  }

  onChange = (value) => {
    let ret = false;
    log.debug('onChange', value);
    if (value.isValid()) {
      ret = value.toJson();
    }
    log.debug('onChange value.toJson', ret);
    this.props.onChange && this.props.onChange(ret);
  };

  render() {
    log.debug('render', this.state);
    const { isInited, value, mode, detailClosable, level1List, level2List, propertyList } = this.state;
    if (isInited) {
      filterListGroupModel = FilterListGroupModel.fromJson(value, propertyList);
      // console.log('---------------', filterListGroupModel);
      // 默认添加一条
      // if (this.filterListGroupModel.filterListGroup.length === 0) {
      // 	this.filterListGroupModel.addFilterListWithFilter(null, null);
      // }
      return (
        <>
          <p>设置条件:</p>
          <FilterListGroup
            mode={mode}
            detailClosable={detailClosable}
            level1List={level1List}
            level2List={level2List}
            propertyList={propertyList}
            filterListGroupModel={filterListGroupModel}
            onChange={this.onChange}
          />
        </>
      );
    }
    return null;
  }
}

export default connect(mapStateToProps)(UserPropFilter);
