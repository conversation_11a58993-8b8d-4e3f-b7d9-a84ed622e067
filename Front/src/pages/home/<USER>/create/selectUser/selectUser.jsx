import React, { Component } from 'react';
import { <PERSON><PERSON>, <PERSON>, message } from 'antd';
import { withRouter } from 'react-router-dom';
import './selectUser.scss';
// import UserPropFilter from 'pages/home/<USER>/create/selectUser/userPropFilter/userPropFilter';
import TagPropFilter from 'pages/home/<USER>/create/selectUser/tagPropFilter/tagPropFilter';
import CampaignManageService from 'pages/home/<USER>/univelsal/campaignManageService';

const campaignManageService = new CampaignManageService();
const CONNECTORUPMAP = {
  true: 'AND',
  false: 'OR'
};
const CONNECTORDOWNMAP = {
  AND: true,
  OR: false
};

class selectUser extends Component {
  constructor(props) {
    super(props);
    // const filterInfoModel = props.selectUserData.length && JSON.parse(props.selectUserData[0].filters);
    const counts =
      props.selectUserData.length && props.selectUserData[0].segment
        ? props.selectUserData[0].segment.customerCount
        : props.counts !== null
          ? props.counts
          : 0;
    const tags =
      props.selectUserData.length && props.selectUserData[0].tags ? JSON.parse(props.selectUserData[0].tags) : [];
    const connector =
      props.selectUserData.length && props.selectUserData[0].connector
        ? CONNECTORDOWNMAP[props.selectUserData[0].connector]
        : false;

    this.state = {
      filterInfo: {}, // 选择的数据
      // filterInfoModel, // 回显数据
      counts,
      loading: false,
      computing: false,
      tagPropObj: {
        tags,
        connector
      }
    };
    this.prevStep = this.prevStep.bind(this);
    this.onCount = this.onCount.bind(this);
  }

  /** 组件数据更新 */
  onChange = (value) => {
    this.setState({ filterInfo: value });
  };

  onTagChange = (tagPropObj) => {
    this.setState({ tagPropObj });
  };

  /** 计算人数 */
  async onCount() {
    const { filterInfo, tagPropObj } = this.state;

    this.setState({ counts: 0, computing: true });
    const data = {
      filters: filterInfo === false ? {} : filterInfo,
      tags: JSON.stringify(tagPropObj.tags),
      connector: CONNECTORUPMAP[tagPropObj.connector]
    };
    try {
      const counts = await campaignManageService.countSegment(data);
      this.setState({ counts, computing: false });
    } catch {
      this.setState({ computing: false });
    }
  }

  /** 上一步 */
  prevStep() {
    const { filterInfo, counts, tagPropObj } = this.state;
    const selectUserData = [];
    selectUserData.push({
      filters: JSON.stringify(filterInfo),
      tags: JSON.stringify(tagPropObj.tags),
      connector: CONNECTORUPMAP[tagPropObj.connector]
    });
    this.props.callBack({
      type: 'selectUserBack',
      value: selectUserData,
      customerCount: counts
    });
  }

  /** 下一步 */
  async nextStep(type) {
    const { filterInfo, counts, tagPropObj } = this.state;
    // if (!filterInfo) {
    //   return message.error('填写不完整，请填写完成后再下一步', 1.5);
    // } else if (!filterInfo.length && !tagPropObj.tags.length) {
    //   return message.error('标签规则至少填写一条', 1.5);
    // }

    if (!tagPropObj.tags.length) {
      return message.error('至少选择一项用户分群', 1.5);
    }
    this.setState({ loading: true });
    const { createFormData } = this.props;
    const campaignId = createFormData.id;
    const data = {
      name: createFormData.name,
      filters: filterInfo === false ? {} : filterInfo,
      tags: JSON.stringify(tagPropObj.tags),
      connector: CONNECTORUPMAP[tagPropObj.connector],
      campaignId
    };
    try {
      const { segmentId } = await campaignManageService.saveSegment(data);
      const selectUserData = [];
      selectUserData.push({
        segmentId,
        filters: JSON.stringify(filterInfo),
        tags: JSON.stringify(tagPropObj.tags),
        connector: CONNECTORUPMAP[tagPropObj.connector],
        segment: {
          customerCount: counts
        }
      });
      this.setState({ loading: false });
      if (type === 'save') {
        this.props.history.push('/aimarketer/home/<USER>');
      } else {
        this.props.callBack({
          type: 'selectUserNext',
          value: selectUserData,
          counts
        });
      }
    } catch {
      this.setState({ loading: false });
    }
  }

  render() {
    // const { counts, loading, computing, filterInfo, tagPropObj } = this.state;
    const { counts, loading, computing, tagPropObj } = this.state;

    return (
      <div className="selectUserBox">
        <Spin spinning={loading}>
          <div className="createSecondTitle">
            <span className="headName">选择目标用户</span>
            <div className="nextOrNo">
              <Button onClick={this.prevStep}>上一步</Button>
              <Button type="primary" className="testScope" onClick={this.nextStep.bind(this)}>
                下一步
              </Button>
              <Button type="primary" onClick={this.nextStep.bind(this, 'save')}>
                保存并返回
              </Button>
            </div>
          </div>
          <div className="createSecondContent">
            <p className="nums">
              <span>去重总人数</span>
              <span>{counts.toLocaleString()}</span>
            </p>
            {/* <p><Button type="primary" loading={computing} disabled={(!filterInfo || !filterInfo.length) && !tagPropObj.tags.length} onClick={this.onCount}>计算人数</Button></p> */}
            <p>
              <Button type="primary" loading={computing} disabled={!tagPropObj.tags.length} onClick={this.onCount}>
                计算人数
              </Button>
            </p>
            {/* <UserPropFilter onChange={this.onChange} value={filterInfoModel} /> */}
            <TagPropFilter onChange={this.onTagChange} tagPropObj={tagPropObj} />
          </div>
        </Spin>
      </div>
    );
  }
}

export default withRouter(selectUser);
