import React, { Component } from 'react';
// import { Switch, Select, Row, Col, Tag, Tooltip } from 'antd';
import { Select, Row, Col, Tag, Tooltip } from 'antd';
import _ from 'lodash';
import './tagPropFilter.scss';
import MultiSelectAsync from 'pages/home/<USER>/univelsal/multiSelectAsync/multiSelectAsync';

const { Option } = Select;
const CONNECTOR_MAP = {
  AND: '且',
  OR: '或'
};

export default class TagPropFilter extends Component {
  constructor(props) {
    super(props);
    this.state = {
      tagPropObj: {
        connector: false,
        tags: []
      },
      ...props
    };
  }

  handleChange = (tags) => {
    const { tagPropObj } = this.state;
    this.setState({
      tagPropObj: {
        ...tagPropObj,
        tags
      }
    });
    this.props.onChange({ ...tagPropObj, tags });
  };

  handleSwitch = (connector) => {
    const { tagPropObj } = this.state;
    this.setState({
      tagPropObj: {
        ...tagPropObj,
        connector
      }
    });
    this.props.onChange({ ...tagPropObj, connector });
  };

  render() {
    const { tagPropObj, mode } = this.state;
    const { connector, tags } = tagPropObj;
    const tagOption = `[${_.map(tags, (data) =>
      data.name.length > 8 ? `${data.name.slice(0, 8)}...` : data.name
    ).join(',')}]`;
    const tagTitle = `[${_.map(tags, (data) => data.name).join(',')}]`;
    const isShowTitle = _.some(tags, (data) => data.name.length > 8);
    return mode === 'detail' ? (
      tags.length > 0 ? (
        <div className="tagPropFilterDetail">
          {this.props.value.length > 0 ? <Tag>{CONNECTOR_MAP[connector]}</Tag> : null}
          <div className="tagGroup">
            <Tooltip title={isShowTitle ? tagTitle : null}>
              <Tag color="magenta">
                用户分群包含
                {tagOption}
              </Tag>
            </Tooltip>
          </div>
        </div>
      ) : null
    ) : (
      <div className="tagPropFilterEdit">
        {/* <div className="switchTag">
          <Switch checkedChildren="且" unCheckedChildren="或" onChange={this.handleSwitch} checked={connector} />
        </div> */}
        <Row gutter={24}>
          <Col span={6}>
            <Select style={{ width: '100%' }} defaultValue="eventTag">
              <Option value="eventTag">用户分群</Option>
            </Select>
          </Col>
          <Col span={3}>
            <Select style={{ width: '100%' }} defaultValue="in">
              <Option value="in">包含</Option>
            </Select>
          </Col>
          <Col span={6}>
            <MultiSelectAsync callBack={this.handleChange} value={tags} />
          </Col>
        </Row>
      </div>
    );
  }
}
