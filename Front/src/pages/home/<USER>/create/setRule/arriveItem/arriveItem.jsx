import React, { Component } from 'react';
import { Input, Select, Button } from 'antd';
import { addKey, removeKey, haveNoneValue } from 'pages/home/<USER>/univelsal/utils';
import ArriveRule from '../arriveRule/arriveRule';
import ArriveWay from '../arriveWay/arriveWay';
import './arriveItem.scss';

// const optionConfig = {
//   arrayWay: { MINUTE: '分钟', HOUR: '小时', DAY: '天' }
// };

class ArriveItem extends Component {
  constructor(props) {
    super(props);
    let stepsData;
    if (!props.firstReach && props.value.length === 0) {
      stepsData = [
        {
          notFinished: {},
          finished: {},
          event: {},
          eventProperties: [],
          key: Math.random()
        }
      ];
    } else {
      stepsData = [];
    }
    this.state = {
      stepsData,
      updata: true, // 是否更新组件，当子组件数据更迭是，不更新组件，性能优化
      init: false,
      notFinishAdd: false,
      arrayWay: { MINUTE: '分钟', HOUR: '小时', DAY: '天' }
    };
    this.firstReach = props.firstReach;
  }

  componentDidMount() {
    this.setState({ init: true });
    if (this.props.value.length === 0) return;
    if (Object.getOwnPropertyNames(this.props.value).length !== 0) {
      this.setState({ stepsData: addKey(this.props.value) });
    }
  }

  // 用来处理当首次接受数据时：stepsData的值为[]情况，当再次接受数据且有值时，重新给stepsData赋值。
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.state.stepsData.length !== 0) return;
    if (nextProps.value.length === 0 || nextProps.value === '[]' || this.state.stepsData.length !== 0) return;
    if (Object.getOwnPropertyNames(nextProps.value).length !== 0) {
      this.setState({ stepsData: addKey(nextProps.value) });
    }
  }

  shouldComponentUpdate(newProps, newState) {
    if (newProps.firstReach !== this.firstReach) {
      this.firstReach = newProps.firstReach;
      const { stepsData } = this.state;
      if (stepsData[0]) stepsData[0].notFinished = {};
      this.setState({ updata: false, stepsData });
      return true;
    }
    if (!newState.updata) return false;
    return true;
  }

  // 当前组件数据发生变化如增加或者移除，输入框与选择框改变。
  dataChange(type, index, value) {
    const { stepsData } = this.state;
    switch (type) {
      case 'addArriveItem':
        stepsData.push({
          key: Math.random(),
          notFinished: {},
          finished: {},
          event: {},
          eventProperties: []
        });
        break;
      // 移除一条触发条件，
      case 'removeArriveItem':
        if (
          index === 0 &&
          stepsData.length > 1 &&
          Object.getOwnPropertyNames(stepsData[1].notFinished).length > 0 &&
          !this.props.firstReach
        ) {
          stepsData[1].notFinished = {};
        }
        stepsData.splice(index, 1);
        break;
      case 'input':
        const inputValue = value.target.value.trim();
        if (!inputValue) {
          stepsData[index].notFinished = {};
        } else {
          stepsData[index].notFinished.intervalTime = inputValue;
          if (!stepsData[index].notFinished.intervalTimeUnit) {
            stepsData[index].notFinished.intervalTimeUnit = 'MINUTE';
          }
        }
        break;
      case 'select':
        stepsData[index].notFinished.intervalTimeUnit = value;
        break;
      default:
        break;
    }
    this.setState({ stepsData, updata: true });
    const data = JSON.parse(JSON.stringify(stepsData));
    return this.props.callBack({
      type: 'arriveItem',
      value: haveNoneValue(removeKey(data))
    });
  }

  // 当子组件数据发生改变时的回调函数
  sonsDataChange(data) {
    // console.log(data);
    const { stepsData } = this.state;
    switch (data.type) {
      case 'arriveWay':
        const stepsIndex = data.message[0];
        // 当未完成添加触达时，给未完成增加时间间隔和单位，使其必填
        if (
          data.message[1] === 'notFinished' &&
          Object.getOwnPropertyNames(stepsData[stepsIndex].notFinished).length === 0
        ) {
          stepsData[stepsIndex].notFinished = {
            intervalTime: '',
            intervalTimeUnit: ''
          };
        }
        // 当删除所有未完成触达时，且行为间隔为空时，允许用户下一步，将notFinished改为空对象
        if (data.value && data.value.length === 0 && !stepsData[stepsIndex].notFinished.intervalTime) {
          stepsData[stepsIndex].notFinished = {};
        }
        if (!data.value) {
          const typeOfFinish = data.message[1];
          stepsData[stepsIndex][typeOfFinish].reachChannelList = data.value;
          return this.props.callBack({ type: 'arriveItem', value: false });
        }
        // 添加触达时，当事件未选择时，不通过
        if (Object.getOwnPropertyNames(stepsData[stepsIndex].event).length === 0) {
          const typeOfFinish = data.message[1];
          stepsData[stepsIndex][typeOfFinish].reachChannelList = data.value;
          return this.props.callBack({ type: 'arriveItem', value: false });
        }
        // 给触达赋值
        const typeOfFinish = data.message[1];
        stepsData[stepsIndex][typeOfFinish].reachChannelList = data.value;
        this.setState({ stepsData, updata: false });
        break;
      case 'arriveRuleEvent':
        if (!data.value) return this.props.callBack({ type: 'arriveItem', value: false });
        stepsData[data.index].event = data.value;
        this.setState({ stepsData, updata: false });
        break;
      case 'arriveRuleEventProperties':
        if (!data.value) return this.props.callBack({ type: 'arriveItem', value: false });
        stepsData[data.index].eventProperties = data.value;
        this.setState({ stepsData, updata: false });
        break;
      default:
        break;
    }
    const data2 = JSON.parse(JSON.stringify(this.state.stepsData));
    // console.log(data2);
    return this.props.callBack({
      type: 'arriveItem',
      value: haveNoneValue(removeKey(data2))
    });
  }

  render() {
    const { mode, firstReach, configToOption, smsInitData } = this.props;
    const { stepsData, notFinishAdd, init } = this.state;
    return (
      init && (
        <div className="allArriveItemBox">
          {stepsData.map((item, index) => {
            return (
              <div className="arriveItemBox" key={item.key}>
                <ArriveRule
                  index={index}
                  callBack={this.sonsDataChange.bind(this)}
                  value={item.eventProperties}
                  event={item.event}
                  mode={mode}
                />
                <div className="overAndNo">
                  {
                    // 编辑条件下，首次触达开启，第一条数据有未完成，详情情况下，首次触达开启且有未完成数据时才显示,详情时情况是只有当有数据时才显示
                    ((firstReach && mode === 'edit') ||
                      (firstReach && Object.getOwnPropertyNames(item.notFinished).length !== 0) ||
                      (mode === 'edit' && !firstReach && index !== 0) ||
                      (mode === 'detail' && Object.getOwnPropertyNames(item.notFinished).length !== 0) ||
                      (mode === 'detail' && Object.getOwnPropertyNames(item.notFinished).length !== 0)) && (
                      <div className="over">
                        <div className={notFinishAdd ? 'overContent notFinishAdd' : 'overContent '}>
                          <div className="overIcon overAndNoIcon">
                            <div className="circle" />
                            未完成
                          </div>
                          <div className="intervalName">行为间隔</div>
                          <Input
                            placeholder="请输入时间"
                            value={item.notFinished && item.notFinished.intervalTime}
                            onChange={this.dataChange.bind(this, 'input', index)}
                            ref={(ref) => (this.input = ref)}
                            disabled={mode === 'detail'}
                          />
                          <Select
                            className="selectTimeUnit"
                            placeholder="请选择时间单位"
                            onChange={this.dataChange.bind(this, 'select', index)}
                            value={item.notFinished.intervalTimeUnit ? item.notFinished.intervalTimeUnit : 'MINUTE'}
                            getPopupContainer={(triggerNode) => triggerNode.parentNode}
                            disabled={mode === 'detail'}
                          >
                            {configToOption(this.state.arrayWay)}
                          </Select>
                        </div>
                        <ArriveWay
                          value={item.notFinished && item.notFinished.reachChannelList}
                          fatherMessage={[index, 'notFinished']}
                          buttonName={!notFinishAdd ? 'notFinishAddButton' : 'noOverArriveWay'}
                          callBack={this.sonsDataChange.bind(this)}
                          configToOption={configToOption}
                          mode={mode}
                          smsInitData={smsInitData}
                        />
                      </div>
                    )
                  }
                  {(mode !== 'detail' ||
                    (item.finished.reachChannelList && item.finished.reachChannelList.length > 0)) && (
                    <div
                      className={`${
                        mode === 'detail' &&
                        item.finished.reachChannelList &&
                        item.finished.reachChannelList.length > 0 &&
                        'detailNoOver'
                      } noOver`}
                    >
                      <div className="overIcon overAndNoIcon">
                        <div className="circle" />
                        完成
                      </div>
                      <ArriveWay
                        value={item.finished ? item.finished.reachChannelList : null}
                        fatherMessage={[index, 'finished']}
                        buttonName="overArriveWay"
                        callBack={this.sonsDataChange.bind(this)}
                        configToOption={configToOption}
                        mode={mode}
                        smsInitData={smsInitData}
                      />
                    </div>
                  )}
                </div>
                {mode !== 'detail' && (
                  <Button
                    className="removeAriveItem"
                    type="primary"
                    onClick={this.dataChange.bind(this, 'removeArriveItem', index)}
                  >
                    移除
                  </Button>
                )}
              </div>
            );
          })}
          {mode !== 'detail' && (
            <Button
              className="addItem arriveAddItem"
              type="dashed"
              onClick={this.dataChange.bind(this, 'addArriveItem')}
              disabled={stepsData.length >= 10}
            >
              + 添加触发条件
            </Button>
          )}
        </div>
      )
    );
  }
}

export default ArriveItem;
