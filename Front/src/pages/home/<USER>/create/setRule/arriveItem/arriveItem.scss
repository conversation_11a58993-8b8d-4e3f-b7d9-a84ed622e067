// @import 'assets/css/variable.scss';

.allArriveItemBox {
  .arriveItemBox {
    padding-left: 25px;
    padding-top: 0;
    margin-top: 15px;
    position: relative;
    border-bottom: 1px solid #ccc;

    .overAndNo {
      margin-top: 15px;

      .over {
        .overContent {
          display: flex;
          align-items: center;
          justify-content: flex-start;

          .intervalName {
            margin: 0 10px 0 15px;
          }

          .ant-input {
            width: 100px;
          }

          .ant-select-selection {
            width: 100px;
          }

          .selectTimeUnit {
            margin: 0 10px;
            width: 140px;

            .ant-select-selection {
              width: 140px;

              .ant-select-selection__rendered {
                margin-right: 5px;
              }
            }
          }
        }

        .notFinishAdd {}

        .noOverArriveWay {
          position: absolute;
          left: 425px;
          top: -30px;
        }

        .notFinishAddButton {
          position: absolute;
          left: 425px;
          top: -30px;
        }

        .arriveWayItem {
          margin-left: 95px;
        }
      }

      .noOver {
        .addItem {
          position: absolute;
          left: 100px;
          top: -25px;
        }

        .arriveWayItem {
          margin-left: 95px;
        }

        .overAndNoIcon {
          color: #52C41A;
          border-color: #52C41A;

          .circle {
            border-color: #52C41A;
          }
        }
      }

      .detailNoOver {
        margin-bottom: -42px;

        .arrivewayBox {
          position: relative;
          top: -42px;
        }
      }

      .overAndNoIcon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 25px;
        border-radius: 15px;
        border: 1px solid $primary_color;

        .circle {
          width: 15px;
          height: 15px;
          border-radius: 10px;
          border: 5px solid $primary_color;
          margin-right: 3px;
        }
      }
    }

    .removeAriveItem {
      position: absolute;
      right: 0;
      top: 10px;
    }
  }

  .arriveAddItem {
    margin-top: 10px !important;
  }
}