import React, { Component } from 'react';
import FilterListGroup from 'components/filter/filterListGroup';
import FilterListGroupModel from 'components/filter/filterListGroupModel';
import CampaignManageService from 'pages/home/<USER>/univelsal/campaignManageService';
import DataEngineService from 'pages/home/<USER>/univelsal/dataEngineService';
import { Select } from 'antd';
import './arriveRule.scss';

const { Option } = Select;
class ArriveRule extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: props.value ? props.value : null,
      detailClosable: false, // 过滤标签是否可以删除
      isInited: false, // 是否已经初始化完成,
      select: {}, // 选择项数据
      businessType: 'SEGMENT_BUSINESS',
      selectList: [], // 选择项列表
      selectListPropety: [], // 选择项对应的属性列表
      selectOver: false // 是否选择完毕
    };
    this.dataEngineService = new DataEngineService();
    this.campaignManageService = new CampaignManageService();
    this.filterListGroupModel = new FilterListGroupModel();
  }

  async componentDidMount() {
    const { event } = this.props;
    const list = await this.campaignManageService.eventList();
    this.setState({ selectList: list, isInited: true });
    if (Object.getOwnPropertyNames(event).length !== 0) {
      await this.getEventProperty(event.id);
      this.filterListGroupModel = FilterListGroupModel.fromJson(this.state.value, this.state.selectListPropety);
      this.setState({
        select: event,
        selectOver: true
      });
    }
  }

  configToOption(arr) {
    const obj = [];
    if (Array.isArray(arr)) {
      for (let i = 0; i < arr.length; i++) {
        obj.push(
          <Option value={arr[i].name} key={arr[i].id}>
            {arr[i].name}
          </Option>
        );
      }
    } else {
      for (const key in arr) {
        obj.push(
          <Option key={key} value={key}>
            {arr[key]}
          </Option>
        );
      }
    }
    return obj.map((item) => {
      return item;
    });
  }

  // 过滤组件内容发生变化是的回调函数
  onChange(value) {
    let ret = false;
    const obj = { type: 'arriveRuleEventProperties', value: [] };
    if (value.isValid()) {
      ret = value.toJson();
    }
    if (ret) {
      obj.value = ret;
      obj.index = this.props.index;
    } else {
      obj.value = ret;
    }
    this.props.callBack && this.props.callBack(obj);
  }

  // 获取触发条件的属性列表函数，并对属性进行过滤。
  async getEventProperty(id) {
    // 获取事件属性列表
    const data = await this.campaignManageService.eventPropertiesList(id);
    if (!data) {
      return;
    }
    await Promise.all(
      data.map(async (p, i) => {
        data[i] = {
          level1: p.tableFilter.level1,
          level2: p.tableFilter.level2,
          tableId: p.tableFilter.table.id,
          tableSchemaId: p.tableFilter.tableSchema.id,
          type: p.tableFilter.tableSchema.dataType,
          name: p.tableFilter.tableSchema.displayName,
          value: p.tableFilter.tableSchema.name,
          isEnum: p.tableFilter.isEnum
        };
        if (p.tableFilter.isEnum) {
          if (p.tableFilter.enumValue) {
            data[i].items = p.enumValue ? JSON.parse(p.enumValue) : [];
          } else {
            // 获取枚举值
            const { businessType } = this.state;
            const items = await this.dataEngineService.findFilterEnum({
              businessType,
              tableId: p.tableFilter.table.id,
              schemaId: p.tableFilter.tableSchema.id
            });
            // const items = await this.dataEngineService.findFilterEnum(p.tableFilter.table.id, p.tableFilter.tableSchema.id);
            data[i].items = items.map((d) => d.value).filter((item) => item);
          }
        }
      })
    );
    this.setState({
      selectListPropety: data,
      selectOver: true
    });
  }

  // 选择触发条件下拉框更改时回调函数
  selectChange(value) {
    let id;
    const { selectList } = this.state;
    for (let i = 0; i < selectList.length; i++) {
      if (value === selectList[i].name) {
        id = selectList[i].id;
        this.setState({ select: selectList[i] });
        this.props.callBack({
          type: 'arriveRuleEvent',
          value: selectList[i],
          index: this.props.index
        });
        this.props.callBack({
          type: 'arriveRuleEventProperties',
          index: this.props.index,
          value: []
        });

        break;
      }
    }
    this.filterListGroupModel = FilterListGroupModel.fromJson(null);
    this.getEventProperty(id);
  }

  render() {
    return (
      this.state.isInited && (
        <div className="touchCondition">
          <div className="number">{this.props.index + 1}</div>
          <Select
            className="selectNodeModule"
            placeholder="请选择触发条件"
            onChange={this.selectChange.bind(this)}
            getPopupContainer={(triggerNode) => triggerNode.parentNode}
            value={this.state.select.name}
            disabled={this.props.mode === 'detail'}
          >
            {this.configToOption(this.state.selectList)}
          </Select>
          {this.state.selectOver && (
            <FilterListGroup
              mode={this.props.mode}
              detailClosable={this.state.detailClosable}
              filterListGroupModel={this.filterListGroupModel}
              propertyList={this.state.selectListPropety}
              onChange={this.onChange.bind(this)}
            />
          )}
        </div>
      )
    );
  }
}

export default ArriveRule;
