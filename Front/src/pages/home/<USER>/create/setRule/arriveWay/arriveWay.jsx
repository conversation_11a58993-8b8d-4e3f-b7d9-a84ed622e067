import React, { Component } from 'react';
import { CloseCircleFilled } from '@ant-design/icons';
import { Button, Select, DatePicker, message } from 'antd';
import dayjs from 'dayjs';
import CampaignManageService from 'pages/home/<USER>/univelsal/campaignManageService';
import { dealOneDictionary, addKey, removeKey, haveNoneValue } from 'pages/home/<USER>/univelsal/utils';
import './arriveWay.scss';

const campaignManageService = new CampaignManageService();
const form = {
  page: 1,
  size: 10,
  search: [
    {
      operator: 'LIKE',
      propertyName: 'name',
      value: ''
    },
    {
      operator: 'EQ',
      propertyName: 'status',
      value: 'NORMAL'
    },
    {
      operator: 'EQ',
      propertyName: 'bizType'
    },
    {
      operator: 'EQ',
      propertyName: 'userId'
    },
    {
      operator: 'DATE_BETWEEN',
      propertyName: 'createTime',
      value: ''
    }
  ],
  sorts: [
    {
      direction: 'desc',
      propertyName: 'updateTime'
    }
  ]
};

class ArriveWay extends Component {
  constructor(props) {
    super(props);
    this.handleSearch = this.handleSearch.bind(this);
    this.state = {
      reachChannelList: this.props.haveDefaultOne ? [{ type: '', option: { selectTime: '' }, key: Math.random() }] : [],
      smsOptionData: dealOneDictionary(this.props.smsInitData),
      smsSearchData: false,
      // arrayWay: { TEL: '电销', SMS: '短信' }
      arrayWay: { SMS: '短信' }
    };
  }

  componentDidMount() {
    if (!this.props.value || this.props.value.length === 0) return;
    this.setState(
      {
        reachChannelList: addKey(this.props.value)
      } /* () => { console.log(this.state.reachChannelList, this.state.smsOptionData); } */
    );
  }

  /**
   * 数据发生变化的回调函数,根据回调的参数，相对应的修改state，并调用父级的回调将数据传到上一层
   * @param  {string,number,every}
   */
  firstArriveHandle(type, index, value) {
    // console.log(type, index, value);
    const { reachChannelList } = this.state;
    switch (type) {
      // 添加一条触达，当有时间与没时间添加触达不同，差别在于option
      case 'addarriveWay':
        if (this.props.haveTime) {
          reachChannelList.push({
            type: '',
            option: { selectTime: '' },
            key: Math.random()
          });
        } else {
          reachChannelList.push({ type: '', key: Math.random() });
        }
        break;
      // 选择触达渠道，根据选择的值对option做处理，并对type赋值
      case 'selectArriveWay':
        // 在非首次触达模式下，用户选择一次电销之后不允许再次选择电销，无意义。
        if (value === 'TEL' && !this.props.haveTime) {
          for (let i = 0; i < reachChannelList.length - 1; i++) {
            if (reachChannelList[i].type === 'TEL') return message.error('电销已有，不可重复');
          }
        }
        if (value === 'TEL' && reachChannelList[index].option && reachChannelList[index].option.selectNodeModule)
          delete reachChannelList[index].option.selectNodeModule;
        // 用户选择了电销之后可能再选择短信，所以不能直接用option.selecNodeModule="",而是用对象重新赋值引用
        if (value === 'TEL' && !this.props.haveTime && reachChannelList[index].option) {
          delete reachChannelList[index].option;
        } else if (value === 'SMS' && this.props.haveTime) {
          reachChannelList[index].option = {
            selectNodeModule: { id: '', name: '' },
            selectTime: ''
          };
        } else if (value === 'SMS' && !this.props.haveTime) {
          reachChannelList[index].option = {
            selectNodeModule: { id: '', name: '' }
          };
        }
        reachChannelList[index].type = value;
        break;
      // 选择短信模板
      case 'selectNodeModule':
        const { smsSearchData, smsOptionData } = this.state;
        if (this.dealSomeSmsModle(value, reachChannelList)) return message.error('短信已有，不可重复');
        if (value) {
          reachChannelList[index].option.selectNodeModule = {
            id: value,
            name: this.findName(value, smsSearchData || smsOptionData)
          };
        } else {
          reachChannelList[index].option.selectNodeModule = {
            id: '',
            name: ''
          };
        }
        this.setState({ smsSearchData: false });
        break;
      case 'selectTime':
        reachChannelList[index].option.selectTime = dayjs(value).valueOf();
        break;
      case 'minus':
        if (this.props.haveDefaultOne && this.state.reachChannelList.length === 1) {
          return message.error('开启首次触达后至少要一条触达渠道', 1);
        }
        reachChannelList.splice(index, 1);
        break;
      default:
        break;
    }
    this.setState({ reachChannelList });
    const data = JSON.parse(JSON.stringify(this.state.reachChannelList));
    // 根据是否有父组件信息来回调不同的数据
    if (this.props.fatherMessage) {
      this.props.callBack({
        value: haveNoneValue(removeKey(data)),
        type: 'arriveWay',
        message: this.props.fatherMessage
      });
    } else {
      this.props.callBack({
        value: haveNoneValue(removeKey(data)),
        type: 'arriveWay'
      });
    }
  }

  // 处理是否已有相同短信
  dealSomeSmsModle(value, arr) {
    if (this.props.haveTime) return false;
    const myArr = arr.slice(0, arr.length - 1);
    if (myArr.length === 0) return false;
    for (let i = 0; i < myArr.length; i++) {
      if (myArr[i].option && myArr[i].option.selectNodeModule && myArr[i].option.selectNodeModule.id === value)
        return true;
    }
    return false;
  }

  // 通过短信id找到短信的name
  findName(id, arr) {
    return arr.filter((item) => {
      return item.value === id;
    })[0].name;
  }

  // 短信搜索框
  handleSearch(value) {
    if (this.time) {
      clearTimeout(this.time);
      this.time = null;
    }
    if (value) {
      this.time = setTimeout(async () => {
        form.search[0].value = value;
        const data = await campaignManageService.pageQuery(form);
        this.setState({ smsSearchData: dealOneDictionary(data.content) });
      }, 500);
    } else {
      this.setState({ smsSearchData: false });
    }
  }

  // // 数据回显，短信暂停在option中找不到对应数据
  // dealNotFindOption(obj, arr) {

  // }

  render() {
    const { reachChannelList, smsOptionData, arrayWay, smsSearchData } = this.state;
    return (
      <div className="arrivewayBox">
        {this.props.mode !== 'detail' && (
          <Button
            className={`addItem ${this.props.buttonName}`}
            type="dashed"
            onClick={this.firstArriveHandle.bind(this, 'addarriveWay')}
            disabled={reachChannelList.length >= 5}
          >
            + 添加触达
          </Button>
        )}

        <div className="arriveWayItemBox">
          {reachChannelList.map((item, index1) => {
            return (
              <div className="arriveWayItem" key={item.key}>
                <div className="arriveWayText">触达渠道</div>
                <Select
                  className="selectArriveWay"
                  placeholder="请选择触达渠道"
                  value={arrayWay[reachChannelList[index1].type]}
                  onChange={this.firstArriveHandle.bind(this, 'selectArriveWay', index1)}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  disabled={this.props.mode === 'detail'}
                >
                  {this.props.configToOption(arrayWay)}
                </Select>
                {item.type === 'SMS' && (
                  <>
                    <div className="NodeModuleText">短信模板</div>
                    <Select
                      allowClear
                      showSearch
                      className="selectNodeModule"
                      placeholder="请选择短信模板"
                      value={reachChannelList[index1].option.selectNodeModule.name}
                      onChange={this.firstArriveHandle.bind(this, 'selectNodeModule', index1)}
                      getPopupContainer={(triggerNode) => triggerNode.parentNode}
                      disabled={this.props.mode === 'detail'}
                      defaultActiveFirstOption={false}
                      filterOption={false}
                      onSearch={this.handleSearch}
                      // notFoundContent={fetching ? <Spin size="small" /> : null}
                    >
                      {this.props.configToOption(smsSearchData || smsOptionData)}
                    </Select>
                  </>
                )}
                {this.props.haveTime && (
                  <>
                    <div className="nodeDataText">触达时间</div>
                    <DatePicker
                      className="selectTime"
                      showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
                      format="YYYY-MM-DD HH:mm:ss"
                      value={
                        reachChannelList[index1].option.selectTime
                          ? dayjs(parseInt(reachChannelList[index1].option.selectTime))
                          : null
                      }
                      onChange={this.firstArriveHandle.bind(this, 'selectTime', index1)}
                      getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                      disabled={this.props.mode === 'detail'}
                    />
                  </>
                )}
                <div className="minusButtonBox">
                  {this.props.mode !== 'detail' && (
                    <Button className="minusButton" onClick={this.firstArriveHandle.bind(this, 'minus', index1)}>
                      <CloseCircleFilled />
                    </Button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }
}

export default ArriveWay;
