// @import 'assets/css/variable.scss';

.setRuleBox {
  background: $white;

  .setRuleContent {
    padding: 0 20px 15px;

    .firstArrive {
      border-bottom: 1px solid #ccc;

      .arriveSwitch {
        display: flex;
        align-items: center;
        margin: 10px 0;

        .switchButton {
          margin-left: 5px;
        }
      }
    }

    .conditionBox {
      .conditionTitle {
        margin-top: 15px;
      }
    }
  }

}