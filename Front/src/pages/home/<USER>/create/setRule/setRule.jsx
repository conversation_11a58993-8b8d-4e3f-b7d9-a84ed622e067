import React, { Component } from 'react';
import { Button, Switch, message, Empty, Select, Spin } from 'antd';
import CampaignManageService from 'pages/home/<USER>/univelsal/campaignManageService';
import SmsTemplateService from 'service/smsManageService';
import { withRouter } from 'react-router-dom';
import ArriveWay from './arriveWay/arriveWay';
import ArriveItem from './arriveItem/arriveItem';
import './setRule.scss';

const { Option } = Select;
class setRule extends Component {
  constructor(props) {
    super(props);
    this.campaignManageService = new CampaignManageService();
    this.smsTemplateService = new SmsTemplateService();
    this.state = {
      setRuleData: {
        firstReach: false, // 首次触达是否开启
        firstReachChannel: [], // 首次触达开启后触达数据
        steps: [],
        campaignId: props.campaignId, // 活动ID，保存活动基本信息后可取到
        segmentId: props.segmentId // 分群id，筛选客户后可取到
        // campaignId: 214, // 活动ID，保存活动基本信息后可取到
        // segmentId: 2123 // 分群id，筛选客户后可取到
      },
      mode: props.mode ? props.mode : 'edit', // 模式默认为编辑状态edit,共三种显示结果  edit(默认)/all(全显示也可编辑)/detail(只显示详情，不可编辑)
      updata: true, // 是否更新组件，当子组件数据更迭时，不更新组件，性能优化
      init: false,
      nextLoading: false, // 下一步时的loading
      smsQueryData: {
        page: 1,
        size: 10,
        search: [
          {
            operator: 'LIKE',
            propertyName: 'name',
            value: ''
          },
          {
            operator: 'EQ',
            propertyName: 'status',
            value: 'NORMAL'
          },
          {
            operator: 'EQ',
            propertyName: 'bizType'
          },
          {
            operator: 'EQ',
            propertyName: 'userId'
          },
          {
            operator: 'DATE_BETWEEN',
            propertyName: 'createTime',
            value: ''
          }
        ],
        sorts: [
          {
            direction: 'desc',
            propertyName: 'updateTime'
          }
        ]
      },
      smsData: []
    };
    this.firstReach = false; // 首次触达是否开启，用来判断更新前后数据是否一致
  }

  async componentDidMount() {
    // 初始化没有数据时，及新建活动规则时，打开初始化开关
    const smsData = (await this.smsTemplateService.pageQuery(this.state.smsQueryData)).content;
    this.setState({ smsData });
    if (!this.props.setRuleData || this.props.setRuleData.length === 0) return this.setState({ init: true });
    // 初始化有数据时，进行数据的赋值
    if (this.props.setRuleData.length !== 0) {
      const setRuleData = JSON.parse(JSON.stringify(this.props.setRuleData));
      if (setRuleData[0]) {
        setRuleData[0].firstReachChannel = JSON.parse(setRuleData[0].firstReachChannel);
        setRuleData[0].steps = JSON.parse(setRuleData[0].steps);
        this.setState({ setRuleData: setRuleData[0], init: true });
      } else {
        this.setState({ init: true });
      }
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { setRuleData } = nextProps;
    if (setRuleData.length !== 0 && typeof setRuleData[0].firstReachChannel === 'string') {
      setRuleData[0].firstReachChannel = JSON.parse(setRuleData[0].firstReachChannel);
      setRuleData[0].steps = JSON.parse(setRuleData[0].steps);
      this.setState({ setRuleData: setRuleData[0] });
    }
  }

  shouldComponentUpdate(newProps, newState) {
    if (newState.setRuleData.firstReach !== this.firstReach) {
      this.firstReach = !this.firstReach;
      return true;
    }
    if (!newState.updata) return false;
    return true;
  }

  configToOption(arr) {
    if (!arr) return;
    const obj = [];
    if (Array.isArray(arr)) {
      for (let i = 0; i < arr.length; i++) {
        if (typeof arr[i] === 'object') {
          obj.push(
            <Option value={arr[i].value} key={arr[i]}>
              {arr[i].name}
            </Option>
          );
        } else {
          obj.push(
            <Option value={arr[i]} key={arr[i]}>
              {arr[i]}
            </Option>
          );
        }
      }
    } else {
      for (const key in arr) {
        obj.push(
          <Option key={key} value={key}>
            {arr[key]}
          </Option>
        );
      }
    }
    return obj.map((item) => {
      return item;
    });
  }

  // 点击下一步向后端发送设置规则数据，发送成功后调用父级回调，进入保存活动配置页面
  async handleSubmit(value) {
    const { setRuleData } = this.state;
    const { firstReach, firstReachChannel, steps } = setRuleData;
    if (firstReach && firstReachChannel.length === 0) return message.error('首次触达已开启，请填写首次触达', 1.5);
    if (!firstReach && steps.length === 0) return message.error('首次触达未开启时，至少一条触发条件！', 1.5);
    if (!firstReachChannel || !steps) return message.error('填写不完整，请填写完成后再下一步', 1.5);
    this.setState({ nextLoading: true, updata: true });
    try {
      setRuleData.firstReachChannel = JSON.stringify(setRuleData.firstReachChannel);
      setRuleData.steps = JSON.stringify(setRuleData.steps);
      const result = await this.campaignManageService.saveCampaignRule(setRuleData);
      this.setState({ nextLoading: false, updata: true });
      if (value === 'saveAndBack') return this.props.history.push('/aimarketer/home/<USER>');
      this.props.callBack({ type: 'setRuleNext', value: [result] });
    } catch (error) {
      setRuleData.firstReachChannel = JSON.parse(setRuleData.firstReachChannel);
      setRuleData.steps = JSON.parse(setRuleData.steps);
      this.setState({ nextLoading: false, updata: true });
    }
  }

  // 点击上一步向父组件传递处理过的数据
  backStep() {
    this.props.callBack({
      type: 'setRuleBack'
    });
  }

  // 是否首次触达，如果首次触达开了又关，则将首次触达中的触达数据清空
  switchOnchange(value) {
    const { setRuleData } = this.state;
    setRuleData.firstReach = value;
    setRuleData.firstReachChannel = [];
    this.setState({ setRuleData, updata: true });
  }

  // 子组件数据发生变化时的回调函数
  sonsChange(data) {
    // console.log(data);
    const { setRuleData } = this.state;
    switch (data.type) {
      // 首次触达中添加触达数据改变赋值
      case 'arriveWay':
        setRuleData.firstReachChannel = data.value;
        break;
      // 触发条件中数据改变赋值
      case 'arriveItem':
        setRuleData.steps = data.value;
        break;
      default:
        break;
    }
    this.setState({ setRuleData, updata: false });
  }

  render() {
    const { setRuleData, nextLoading } = this.state;
    return (
      this.state.init &&
      (this.props.setRuleData.length === 0 && this.props.mode === 'detail' ? (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无活动规则" />
      ) : (
        <Spin spinning={nextLoading}>
          <div className="setRuleBox">
            {this.props.mode === 'detail' ? null : (
              <div className="createSecondTitle">
                <span className="headName">设置活动规则</span>
                <div className="nextOrNo">
                  <Button onClick={this.backStep.bind(this, 'setRuleBack')}>上一步</Button>
                  <Button type="primary" className="testScope" onClick={this.handleSubmit.bind(this, 'setRuleNext')}>
                    下一步
                  </Button>
                  <Button type="primary" className="testScope" onClick={this.handleSubmit.bind(this, 'saveAndBack')}>
                    保存并返回
                  </Button>
                </div>
              </div>
            )}
            <div className="setRuleContent">
              <div className="firstArrive">
                <div className="arriveSwitch">
                  首次触达
                  <Switch
                    className="switchButton"
                    checkedChildren="开"
                    unCheckedChildren="关"
                    onChange={this.switchOnchange.bind(this)}
                    checked={setRuleData.firstReach}
                    disabled={this.props.mode === 'detail'}
                  />
                </div>
                {setRuleData.firstReach && (
                  <ArriveWay
                    haveTime
                    haveDefaultOne
                    value={setRuleData.firstReachChannel}
                    buttonName="firstArriveButton"
                    callBack={this.sonsChange.bind(this)}
                    configToOption={this.configToOption}
                    mode={this.state.mode}
                    smsInitData={this.state.smsData}
                  />
                )}
              </div>
              <div className="conditionBox">
                <div className="conditionTitle">按完成情况配置触发条件:</div>
                <div className="conditionItemBox">
                  <ArriveItem
                    firstReach={setRuleData.firstReach}
                    callBack={this.sonsChange.bind(this)}
                    configToOption={this.configToOption}
                    value={setRuleData.steps}
                    mode={this.state.mode}
                    smsInitData={this.state.smsData}
                  />
                </div>
              </div>
            </div>
          </div>
        </Spin>
      ))
    );
  }
}

export default withRouter(setRule);
