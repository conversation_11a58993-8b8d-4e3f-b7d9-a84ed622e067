// @import 'assets/css/variable.scss';

.ruleBox {
  .ruleMain {
    display: flex;
    background: $white;

    .ruleMenu {
      width: 150px;
    }

    .ruleContent {
      flex-grow: 1;
      padding: 10px 20px;
    }

    .ant-menu {
      background: $white;
      height: 100%;

      .ant-menu-item {
        margin: 0;
      }
    }
  }

  .ruleButtonBox {
    text-align: right;
  }

  .ant-drawer-right {
    .ant-drawer-title {
      margin-left: 20px;
    }

    .ant-drawer-close {
      left: 0;
    }
  }

  .formMainContent {
    display: flex;
    flex-wrap: wrap;

    .ant-form-item {
      display: flex;

      .ant-form-item-control-wrapper {
        flex: 1;
      }

      .ant-form-item-children {
        width: 100%;
        display: inline-block;

        .ant-calendar-picker {
          width: 100%;
          min-width: 0 !important;
        }
      }
    }
  }
}