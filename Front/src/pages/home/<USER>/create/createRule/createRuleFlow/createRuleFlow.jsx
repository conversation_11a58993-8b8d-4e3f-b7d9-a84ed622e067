import React, { Component } from 'react';
import './createRuleFlow.scss';

class createRuleFlow extends Component {
  // 只有当拖拽的源到达目标后才会触发
  drop(e) {
    e.persist();
    e.preventDefault();
    const cloneTarget = e.dataTransfer.getData('Text');
    const cloneLabel = document.getElementsByClassName(cloneTarget)[0];
    cloneLabel.removeAttribute('draggable');
    const myData = cloneLabel.cloneNode(true);
    e.target.appendChild(myData);
  }

  dragOver(e) {
    e.persist();
    if (this.dragName.match(/\d/g)[0] !== e.target.className.match(/\d/g)[0]) return;
    e.preventDefault();
  }

  dragStart(e) {
    e.persist();
    this.dragName = e.target.className;
    e.dataTransfer.setData('Text', e.target.className);
  }

  render() {
    return (
      <div>
        <div className="mainRuleBox">
          <div className="leftMeunBox">
            我是左边
            <div id="dragDom1" className="dragDom1 dragDom" draggable="true" onDragStart={this.dragStart.bind(this)}>
              1
            </div>
            <div id="dragDom2" className="dragDom2 dragDom" draggable="true" onDragStart={this.dragStart.bind(this)}>
              2
            </div>
          </div>
          <div className="mainRuleBox">
            <div
              id="target1"
              className="target1 target"
              onDrop={this.drop.bind(this)}
              onDragOver={this.dragOver.bind(this)}
            >
              1
            </div>
            <div
              id="target2"
              className="target2 target"
              onDrop={this.drop.bind(this)}
              onDragOver={this.dragOver.bind(this)}
            >
              2
            </div>
            我是右边
          </div>
        </div>
      </div>
    );
  }
}

export default createRuleFlow;
