import React, { Component } from 'react';
import {
  But<PERSON>,
  Drawer
  // , <PERSON>u, Icon
} from 'antd';
import {
  // Route, Switch, Redirect,
  withRouter
} from 'react-router-dom';

import ChooseCustomer from './chooseCustomer/chooseCustomer';
import FilterParticipant from './filterParticipant/filterParticipant';
import FirstArrive from './firstArrive/firstArrive';
import NoteSale from './noteSale/noteSale';
import PhoneSale from './phoneSale/phoneSale';
import FinishBehavior from './finishBehavior/finishBehavior';
import NoFinishBehavior from './noFinishBehavior/noFinishBehavior';
import Integral from './integral/integral';
import './createRule.scss';

class createRule extends Component {
  constructor(props) {
    super(props);
    this.state = {
      chooseCustomer: false, // 选择客户抽屉变量
      filterParticipant: false, // 过滤已参与抽屉变量
      firstArrive: false, // 首次触达抽屉变量
      phoneSale: false, // 电话销售抽屉变量
      noteSale: false, // 短信销售抽屉变量
      finishBehavior: false, // 完成行为抽屉变量
      noFinishBehavior: false, // 未完成行为抽屉变量
      integral: false, // 积分抽屉变量
      ruleData: {} // 规则数据
    };
    this.DrawerWidth = 720;
  }

  propsCallback(value) {
    switch (value) {
      case 'back':
        this.props.callBack({
          type: 'createRuleBack',
          value: 'createRuleBack'
        });
        break;
      case 'save':
        this.props.callBack({
          type: 'createRuleSave',
          value: 'createRuleSave'
        });
        break;
      default:
        break;
    }
  }

  showDrawer(value) {
    this.setState({
      [value]: true
    });
  }

  onClose(value) {
    this.setState({
      [value]: false
    });
  }

  sonsSubmit(data) {
    // console.log(data);
    const { ruleData } = this.state;
    ruleData[data.type] = data.value;
    this.setState({ ruleData });
  }

  handleClick = (e) => {
    this.props.history.push(e.key);
  };

  render() {
    return (
      <div className="ruleBox">
        <div className="createSecondTitle">
          <span className="headName">设置活动规则</span>
          <div className="nextOrNo">
            <Button onClick={this.propsCallback.bind(this, 'back')}>上一步</Button>
            <Button type="primary" className="testScope" onClick={this.propsCallback.bind(this, 'save')}>
              下一步
            </Button>
          </div>
        </div>
        <div className="ruleMain">
          <div className="ruleContent">
            <ChooseCustomer />
          </div>
        </div>
        <div className="testButton">
          <div className="chooseCustomerBox">
            <Button type="primary" onClick={this.showDrawer.bind(this, 'chooseCustomer')}>
              客户筛选
            </Button>
            <Drawer
              title="客户筛选"
              width={this.DrawerWidth}
              closable
              getContainer={false}
              onClose={this.onClose.bind(this, 'chooseCustomer')}
              open={this.state.chooseCustomer}
            >
              <ChooseCustomer close={this.onClose.bind(this)} submit={this.sonsSubmit.bind(this)} />
            </Drawer>
          </div>
          <div className="filterParticipantBox">
            <Button type="primary" onClick={this.showDrawer.bind(this, 'filterParticipant')}>
              过滤已参与
            </Button>
            <Drawer
              title="过滤已参与"
              width={this.DrawerWidth}
              getContainer={false}
              onClose={this.onClose.bind(this, 'filterParticipant')}
              open={this.state.filterParticipant}
            >
              <FilterParticipant close={this.onClose.bind(this)} submit={this.sonsSubmit.bind(this)} />
            </Drawer>
          </div>
          <div className="firstArriveBox">
            <Button type="primary" onClick={this.showDrawer.bind(this, 'firstArrive')}>
              首次触达
            </Button>
            <Drawer
              title="设置触发条件/首次触达"
              width={this.DrawerWidth}
              getContainer={false}
              onClose={this.onClose.bind(this, 'firstArrive')}
              open={this.state.firstArrive}
            >
              <FirstArrive close={this.onClose.bind(this)} submit={this.sonsSubmit.bind(this)} />
            </Drawer>
          </div>
          <div className="phoneSaleBox">
            <Button type="primary" onClick={this.showDrawer.bind(this, 'phoneSale')}>
              电话销售
            </Button>
            <Drawer
              title="推送电销名单"
              width={this.DrawerWidth}
              getContainer={false}
              onClose={this.onClose.bind(this, 'phoneSale')}
              open={this.state.phoneSale}
            >
              <PhoneSale close={this.onClose.bind(this)} submit={this.sonsSubmit.bind(this)} />
            </Drawer>
          </div>
          <div className="noteSaleBox">
            <Button type="primary" onClick={this.showDrawer.bind(this, 'noteSale')}>
              短信销售
            </Button>
            <Drawer
              title="短信销售"
              width={this.DrawerWidth}
              getContainer={false}
              onClose={this.onClose.bind(this, 'noteSale')}
              open={this.state.noteSale}
            >
              <NoteSale close={this.onClose.bind(this)} submit={this.sonsSubmit.bind(this)} />
            </Drawer>
          </div>
          <div className="finishBehaviorBox">
            <Button type="primary" onClick={this.showDrawer.bind(this, 'finishBehavior')}>
              完成行为
            </Button>
            <Drawer
              title="完成行为"
              width={this.DrawerWidth}
              getContainer={false}
              onClose={this.onClose.bind(this, 'finishBehavior')}
              open={this.state.finishBehavior}
            >
              <FinishBehavior close={this.onClose.bind(this)} submit={this.sonsSubmit.bind(this)} />
            </Drawer>
          </div>
          <div className="noFinishBehaviorBox">
            <Button type="primary" onClick={this.showDrawer.bind(this, 'noFinishBehavior')}>
              未完成行为
            </Button>
            <Drawer
              title="未完成行为"
              width={this.DrawerWidth}
              getContainer={false}
              onClose={this.onClose.bind(this, 'noFinishBehavior')}
              open={this.state.noFinishBehavior}
            >
              <NoFinishBehavior close={this.onClose.bind(this)} submit={this.sonsSubmit.bind(this)} />
            </Drawer>
          </div>
          <div className="integralBox">
            <Button type="primary" onClick={this.showDrawer.bind(this, 'integral')}>
              积分
            </Button>
            <Drawer
              title="设置积分奖励"
              width={this.DrawerWidth}
              getContainer={false}
              onClose={this.onClose.bind(this, 'integral')}
              open={this.state.integral}
            >
              <Integral close={this.onClose.bind(this)} submit={this.sonsSubmit.bind(this)} />
            </Drawer>
          </div>
        </div>
      </div>
    );
  }
}

export default withRouter(createRule);
