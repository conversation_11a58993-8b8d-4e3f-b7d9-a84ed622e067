import React, { Component } from 'react';
import { CloseOutlined, FilterOutlined, MinusOutlined } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Select, Switch, DatePicker } from 'antd';
import { Link } from 'react-router-dom';
import dayjs from 'dayjs';
import FilterParticipant from '../filterParticipant/filterParticipant';
import './chooseCustomer.scss';

const children = [];
for (let i = 10; i < 36; i++) {
  children.push(i);
}

const { Option } = Select;
const { RangePicker } = DatePicker;

class ChooseCustomer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      conditionData: [{ selectType: '', selectTab: '' }]
    };
  }

  handleSubmit() {
    // this.props.submit({ type: 'chooseCustomer', value: values });
  }

  handleChange(type, index, value, value2) {
    const conditionData = JSON.parse(JSON.stringify(this.state.conditionData));
    switch (type) {
      case 'selectType':
        conditionData[index].selectType = value;
        if (value === '已有标签分类') {
          conditionData[index].selectTab = [];
        } else {
          conditionData[index].selfDefind = {
            filterData: [{ key: Math.random() }]
          };
        }
        break;
      case 'tagItem':
        conditionData[index].selectTab = value;
        break;
      case 'behavious':
      case 'selectTime':
        conditionData[index].selfDefind[type] = value;
        break;

      case 'selectDefindWay':
      case 'selectOparator':
      case 'selectItem':
        conditionData[index].selfDefind.filterData[value][type] = value2;
        break;
      case 'filterIcon':
        conditionData[index].selfDefind.filterData.push({ key: Math.random() });
        break;
      case 'filterMinus':
        conditionData[index].selfDefind.filterData.splice(value, 1);
        break;

      case 'minus':
        conditionData.splice(index, 1);
        break;
      default:
        break;
    }
    this.setState({ conditionData });
  }

  addCondition() {
    const { conditionData } = this.state;
    conditionData.push({ selectType: '', selectTab: '', key: Math.random() });
    this.setState({
      conditionData
    });
  }

  listButtonChange() {}

  render() {
    return (
      <div className="chooseCustomerBox">
        <div className="noPartNumber">
          去重总人数：
          <span>50349</span>
        </div>
        <div className="count">
          <Button type="primary">计算人数</Button>
          <div className="countRight">
            白名单
            <Switch
              checkedChildren="开"
              unCheckedChildren="关"
              defaultChecked={false}
              className="whiteListButton listButton"
              onChange={this.listButtonChange.bind(this, 'white')}
            />
            黑名单
            <Switch
              checkedChildren="开"
              unCheckedChildren="关"
              defaultChecked={false}
              className="listButton"
              onChange={this.listButtonChange.bind(this, 'black')}
            />
          </div>
        </div>
        <div className="setCondition">
          <div className="conditionTitle">设置条件:</div>
          <div className="conditionContent">
            {this.state.conditionData.map((item, index) => {
              // console.log(`${item.selectType}${index}` || index);
              return (
                <div className="conditionBox" key={`${item.key}` || index}>
                  <Select
                    className="selectType"
                    placeholder="请选择筛选分类"
                    onChange={this.handleChange.bind(this, 'selectType', index)}
                    // defaultValue={item.selectType}
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  >
                    <Option value="自定义筛选">自定义筛选</Option>
                    <Option value="已有标签分类">已有标签分群</Option>
                  </Select>
                  {item.selectType === '' ? null : item.selectType === '已有标签分类' ? (
                    <>
                      <div className="alreadType">
                        <Select
                          mode="multiple"
                          placeholder="请选择标签分群"
                          onChange={this.handleChange.bind(this, 'tagItem', index)}
                          getPopupContainer={(triggerNode) => triggerNode.parentNode}
                        >
                          {children.map((item2) => {
                            return <Option key={item2.toString(36) + item2}>{item2.toString(36) + item2}</Option>;
                          })}
                        </Select>
                      </div>
                      <div>
                        <Link to="/aimarketer/home/<USER>/create">
                          <Button className="newTagButton">新建标签分群</Button>
                        </Link>
                      </div>
                      <div className="minusButtonBox">
                        <Button className="minusButton" onClick={this.handleChange.bind(this, 'minus', index)}>
                          <MinusOutlined />
                        </Button>
                      </div>
                    </>
                  ) : (
                    <div className="useDefindBox">
                      <div className="defindSelectType">
                        <Select
                          className="behavious"
                          placeholder="请选择行为"
                          onChange={this.handleChange.bind(this, 'behavious', index)}
                          getPopupContainer={(triggerNode) => triggerNode.parentNode}
                        >
                          <Option value="注册">注册</Option>
                          <Option value="预留信息">预留信息</Option>
                          <Option value="申请">申请</Option>
                          <Option value="还款">还款</Option>
                        </Select>
                      </div>
                      <div className="defindDataText">在时间段：</div>
                      <div className="defindData">
                        <RangePicker
                          className="selectTime"
                          showTime={{
                            defaultValue: dayjs('00:00:00', 'HH:mm:ss')
                          }}
                          format="YYYY-MM-DD HH:mm:ss"
                          onChange={this.handleChange.bind(this, 'selectTime', index)}
                          getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                        />
                      </div>
                      <Button className="filterIcon" onClick={this.handleChange.bind(this, 'filterIcon', index)}>
                        <FilterOutlined />
                      </Button>
                      <div className="minusButtonBox">
                        <Button className="minusButton" onClick={this.handleChange.bind(this, 'minus', index)}>
                          <MinusOutlined />
                        </Button>
                      </div>
                      {item.selfDefind.filterData.map((item2, index2) => {
                        return (
                          <div key={item2.key} className="filterItem">
                            <div className="defindDataText">属性过滤:</div>
                            <Select
                              className="selectDefindWay"
                              placeholder="请选择渠道"
                              onChange={this.handleChange.bind(this, 'selectDefindWay', index, index2)}
                              getPopupContainer={(triggerNode) => triggerNode.parentNode}
                            >
                              <Option value="注册">注册</Option>
                              <Option value="预留信息">预留信息</Option>
                              <Option value="申请">申请</Option>
                              <Option value="还款">还款</Option>
                            </Select>
                            <Select
                              className="selectOparator"
                              placeholder="操作符"
                              onChange={this.handleChange.bind(this, 'selectOparator', index, index2)}
                              getPopupContainer={(triggerNode) => triggerNode.parentNode}
                            >
                              <Option value="等于">等于</Option>
                              <Option value="不等于">不等于</Option>
                              <Option value="包含">包含</Option>
                              <Option value="不包含">不包含</Option>
                              <Option value="有值">有值</Option>
                              <Option value="空值">空值</Option>
                            </Select>
                            <Select
                              className="selectItem"
                              mode="multiple"
                              placeholder="请选择标签分群"
                              onChange={this.handleChange.bind(this, 'selectItem', index, index2)}
                              getPopupContainer={(triggerNode) => triggerNode.parentNode}
                            >
                              {children.map((item3) => {
                                return <Option key={item3.toString(36) + item3}>{item3.toString(36) + item3}</Option>;
                              })}
                            </Select>
                            <Button
                              className="minusButton"
                              onClick={this.handleChange.bind(this, 'filterMinus', index)}
                            >
                              <CloseOutlined />
                            </Button>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        <div className="addCondition">
          <Button type="dashed" onClick={this.addCondition.bind(this)}>
            + 添加筛选条件
          </Button>
        </div>
        <div className="filterParticipant">
          <div className="filterTitle">过滤已参与的活动用户:</div>
          <FilterParticipant />
        </div>
        <div className="chooseCustomerButtonBox ruleButtonBox">
          {/* <Button onClick={() => this.props.close('chooseCustomer')}>
                        取消
                    </Button> */}
          <Button onClick={this.handleSubmit.bind(this)} type="primary">
            保存
          </Button>
        </div>
      </div>
    );
  }
}

export default Form.create({ name: 'ChooseCustomer' })(ChooseCustomer);
