// @import 'assets/css/variable.scss';

.chooseCustomerBox {
    .noPartNumber {
        span {
            color: $primary_color;
            font-size: 20px;
        }
    }

    .count {
        margin: 5px 0 10px;
        ;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .countRight {
            display: flex;
            align-items: center;

            .whiteListButton {
                margin-right: 20px;
            }

            .listButton {
                margin-left: 5px;
            }
        }
    }

    .setCondition {
        .conditionTitle {
            margin-top: 20px;
        }

        .conditionContent {
            .conditionBox {
                margin-bottom: 20px;
                display: flex;
                justify-content: flex-start;
                align-items: flex-start;
                width: min-content;
                min-width: 900px;
                padding: 10px;
                border: 1px dashed #ccc;
                border-radius: 10px;

                .selectType {
                    width: 150px;
                }

                .alreadType {
                    flex-grow: 1;
                    padding: 0 20px;
                    min-width: 300px;

                    .ant-select {
                        width: 100%;
                    }
                }

                .useDefindBox {
                    flex-grow: 1;
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    flex-wrap: wrap;
                    padding: 0 20px;
                    max-width: 800px;

                    .defindSelectType {
                        min-width: 100px;
                        margin-right: 20px;

                        .behavious {
                            width: 120px;
                        }
                    }

                    .ant-select {
                        width: 100%;
                    }

                    .filterIcon {
                        font-size: 20px;
                        border: none;
                        margin-right: 0;
                    }

                    .filterItem {
                        width: 800px;
                        min-height: 40px;
                        display: flex;
                        justify-content: flex-start;
                        align-items: center;

                        .defindDataText {
                            min-width: 80px;
                        }

                        .selectDefindWay {
                            min-width: 100px;
                            max-width: 120px;
                        }

                        .selectOparator {
                            min-width: 100px;
                            max-width: 120px;
                        }

                        .selectItem {
                            flex-grow: 1
                        }

                        .ant-select {
                            margin-right: 15px;
                        }
                    }

                }

                .alreadType {
                    .newTagButton {
                        margin: 0;
                    }
                }

                .minusButtonBox {
                    .minusButton {
                        margin-right: 5px;
                        border-radius: 50%;
                        border-width: 2px;
                        width: 32px;
                        height: 32px;
                        padding: 0;
                        text-align: center;
                        line-height: 25px;
                        font-size: 20px;

                        .anticon-minus {
                            width: 20px;
                            height: 20px;
                            font-size: 20px;
                        }
                    }
                }

            }
        }
    }

    .addCondition {
        padding-bottom: 20px;
        border-bottom: 1px solid #ccc;

        .ant-btn-dashed {
            border-radius: 15px;
        }
    }

    .filterTitle {
        margin: 20px 0;
    }
}