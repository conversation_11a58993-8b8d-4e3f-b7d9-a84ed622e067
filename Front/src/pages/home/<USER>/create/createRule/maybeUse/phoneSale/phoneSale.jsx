import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Row, Col, Input } from 'antd';
import './phoneSale.scss';

class PhoneSale extends Component {
  handleSubmit() {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        this.props.submit({ type: 'phoneSale', value: values });
        this.props.close('phoneSale');
      }
    });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    return (
      <div className="phoneSaleBox">
        <div className="phoneSaleButtonBox ruleButtonBox">
          <Button onClick={() => this.props.close('phoneSale')}>取消</Button>
          <Button onClick={this.handleSubmit.bind(this)} type="primary">
            确定
          </Button>
        </div>
        <Form className="form-phoneSale">
          <Row className="formMainContent" gutter={24}>
            <Col span={16}>
              <Form.Item label="短信签名:">
                {getFieldDecorator('nodeSignature', {
                  rules: [
                    { required: true, message: '请输入活动名称!' },
                    { max: 60, message: '活动名请不要超出60个字符' },
                    {
                      pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
                      message: '请输入字母、数字、字符(._-)或者汉字'
                    },
                    { whitespace: true, message: '活动名称不能为空' }
                    // { validator: _.debounce(this.handleValidator.bind(this), 200), message: '活动名已存在' }
                  ]
                })(<Input placeholder="请输入活动名称" />)}
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    );
  }
}

export default Form.create({ name: 'phoneSale' })(PhoneSale);
