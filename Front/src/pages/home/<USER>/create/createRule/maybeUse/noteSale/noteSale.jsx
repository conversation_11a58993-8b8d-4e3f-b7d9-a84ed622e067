import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Row, Col, Select, Input } from 'antd';
import './noteSale.scss';

const { Option } = Select;
const { TextArea } = Input;
class NoteSale extends Component {
  handleSubmit() {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        this.props.submit({ type: 'noteSale', value: values });
        this.props.close('noteSale');
      }
    });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    return (
      <div className="noteSaleBox">
        <div className="noteSaleButtonBox ruleButtonBox">
          <Button onClick={() => this.props.close('noteSale')}>取消</Button>
          <Button onClick={this.handleSubmit.bind(this)} type="primary">
            确定
          </Button>
        </div>
        <Form className="form-noteSale">
          <Row className="formMainContent" gutter={24}>
            <Col span={16}>
              <Form.Item label="短信签名:">
                {getFieldDecorator('nodeSignature', {
                  rules: [{ required: true, message: '请选择短信签名!' }]
                })(
                  <Select placeholder="请选择短信签名" getPopupContainer={(triggerNode) => triggerNode.parentNode}>
                    <Option value="CUSTOM_BASIC_INFO">短信一</Option>
                    <Option value="CUSTOM_WARRANTY_INFO">短信二</Option>
                    <Option value="POSSESSION_INFO">短信三</Option>
                    <Option value="FINANCE_ACTION_INFO">短信四</Option>
                  </Select>
                )}
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="短信签名:">
                {getFieldDecorator('noteDescribe', {
                  rules: [{ required: true, message: '短信内容' }]
                })(
                  <TextArea
                    className="noteTaxtArea"
                    placeholder="请输入短信内容，不超过350个汉字"
                    autoSize={{ minRows: 4, maxRows: 9 }}
                  />
                )}
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    );
  }
}

export default Form.create({ name: 'noteSale' })(NoteSale);
