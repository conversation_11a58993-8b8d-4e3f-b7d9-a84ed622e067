import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Input, Row, Col, DatePicker, Select } from 'antd';
import dayjs from 'dayjs';
import './filterParticipant.scss';

const { Option } = Select;
const { RangePicker } = DatePicker;

class FilterParticipant extends Component {
  constructor(props) {
    super(props);
    this.state = {
      participant: true
    };
  }

  componentDidMount() {
    this.props.form.setFieldsValue({
      participantOption: this.props.form.getFieldValue('participantOption')
    });
  }

  handleChange() {
    this.setState({ participant: !this.state.participant });
  }

  handleSubmit() {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        this.props.submit({ type: 'filterParticipant', value: values });
        this.props.close('filterParticipant');
      }
    });
  }

  calculateHandle() {
    this.props.form.validateFields((err) => {
      if (!err) {
        // console.log(values);
      }
    });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    return (
      <div className="filterParticipantBox">
        <div className="filterParticipantButtonBox ruleButtonBox">
          <Button onClick={() => this.props.close('filterParticipant')}>取消</Button>
          <Button onClick={this.handleSubmit.bind(this)} type="primary">
            确定
          </Button>
        </div>

        <Form className="form-FilterParticipant">
          <Row className="formMainContent" gutter={24}>
            <Col span={16}>
              <Form.Item className="overCowsName" label="人群名称:">
                {getFieldDecorator('overCowsName', {
                  rules: [
                    { required: true, message: '请输入活动名称!' },
                    { max: 30, message: '活动名请不要超出30个字符' },
                    {
                      pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
                      message: '请输入字母、数字、字符(._-)或者汉字'
                    },
                    { whitespace: true, message: '人群名称不能为空' }
                    // { validator: _.debounce(this.handleValidator.bind(this), 200), message: '活动名已存在' }
                  ]
                })(<Input placeholder="请输入已参与人群名称" />)}
              </Form.Item>
            </Col>
            <Col span={16}>
              <Form.Item className="filterTime" label="过滤在:">
                {getFieldDecorator('filterTime', {
                  rules: [{ required: true, message: '必填项' }]
                })(
                  <RangePicker
                    // renderExtraFooter={() => 'extra footer'}
                    showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
                    format="YYYY-MM-DD HH:mm:ss"
                    getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                  />
                )}
                <div className="timeInner">时间内</div>
              </Form.Item>
            </Col>
          </Row>
          <Row className="formMainContent" gutter={24}>
            <Col span={7}>
              <Form.Item className="participantOption" label="已参与:">
                {getFieldDecorator('participantOption', {
                  initialValue: ['POSSESSION_INFO'],
                  rules: [{ required: true, message: '必填项' }]
                })(
                  <Select
                    placeholder="请选择产品类型"
                    onChange={this.handleChange.bind(this)}
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  >
                    <Option value="CUSTOM_WARRANTY_INFO">全部活动</Option>
                    <Option value="POSSESSION_INFO">指定活动</Option>
                  </Select>
                )}
              </Form.Item>
            </Col>
            {this.state.participant ? (
              <Col span={16}>
                <Form.Item className="participant">
                  {getFieldDecorator('participant', {
                    // rules: [{ required: true, message: '必填项' }]
                  })(
                    <Select
                      mode="multiple"
                      style={{ width: '100%' }}
                      placeholder="请输入活动名称"
                      // initialValue={['china']}
                      optionLabelProp="label"
                    >
                      <Option value="china" label="China">
                        China (中国)
                      </Option>
                      <Option value="usa" label="USA">
                        {' '}
                        (美国)
                      </Option>
                      <Option value="japan" label="Japan">
                        Japan (日本)
                      </Option>
                      <Option value="koean" label="Koean">
                        Koean (韩国)
                      </Option>
                    </Select>
                  )}
                </Form.Item>
              </Col>
            ) : null}
          </Row>
        </Form>
        <div className="calculate">
          <Button type="primary" onClick={this.calculateHandle.bind(this)}>
            计算人数
          </Button>
          {/* <span></span> */}
        </div>
      </div>
    );
  }
}

export default Form.create({ name: 'filterParticipant' })(FilterParticipant);
