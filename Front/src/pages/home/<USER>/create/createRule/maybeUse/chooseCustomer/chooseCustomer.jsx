import React, { Component } from 'react';
import { MinusOutlined } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Input, Row, Col, Select } from 'antd';
import { Link } from 'react-router-dom';
import './chooseCustomer.scss';

const children = [];
for (let i = 10; i < 36; i++) {
  children.push(i);
}

const { Option } = Select;
class ChooseCustomer extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  handleSubmit() {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        this.props.submit({ type: 'chooseCustomer', value: values });
        this.props.close('chooseCustomer');
      }
    });
  }

  handleChange(type) {
    switch (type) {
      case 'selectType':
        break;

      default:
        break;
    }
  }

  addCondition() {}

  render() {
    const { getFieldDecorator } = this.props.form;
    return (
      <div className="chooseCustomerBox">
        <div className="chooseCustomerButtonBox ruleButtonBox">
          <Button onClick={() => this.props.close('chooseCustomer')}>取消</Button>
          <Button onClick={this.handleSubmit.bind(this)} type="primary">
            确定
          </Button>
        </div>

        <Form className="form-chooseCustomer">
          <Row className="formMainContent" gutter={24}>
            <Col span={16}>
              <Form.Item className="cowsName" label="人群名称:">
                {getFieldDecorator('cowsName', {
                  rules: [
                    { required: true, message: '请输入活动名称!' },
                    { max: 30, message: '活动名请不要超出30个字符' },
                    {
                      pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
                      message: '请输入字母、数字、字符(._-)或者汉字'
                    },
                    { whitespace: true, message: '人群名称不能为空' }
                    // { validator: _.debounce(this.handleValidator.bind(this), 200), message: '活动名已存在' }
                  ]
                })(<Input placeholder="请输入人群名称，最多30个字符" />)}
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <Row className="conditionBox" gutter={24}>
          <Col span={5}>
            <Select
              placeholder="请选择产品类型"
              onChange={this.handleChange.bind(this, 'selectType')}
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
              defaultValue="已有标签分类"
            >
              <Option value="自定义筛选">自定义筛选</Option>
              <Option value="已有标签分类">已有标签分群</Option>
            </Select>
          </Col>
          <Col span={12}>
            <Select
              mode="multiple"
              placeholder="Please select"
              onChange={this.handleChange.bind(this)}
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
            >
              {children.map((item) => {
                return <Option key={item.toString(36) + item}>{item.toString(36) + item}</Option>;
              })}
            </Select>
          </Col>
          <Col span={7}>
            <Button className="minusButton">
              <MinusOutlined />
            </Button>
            <Link to="/aimarketer/home/<USER>/create">
              <Button className="newTagButton">新建标签分群</Button>
            </Link>
          </Col>
        </Row>
        <div className="addCondition">
          <Button type="dashed" onClick={this.addCondition.bind(this)}>
            + 添加筛选条件
          </Button>
        </div>
        <div className="count">
          <Button type="primary">计算人数</Button>
        </div>
      </div>
    );
  }
}

export default Form.create({ name: 'ChooseCustomer' })(ChooseCustomer);
