import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Row, Col, DatePicker } from 'antd';
import dayjs from 'dayjs';
import './firstArrive.scss';

class FirstArrive extends Component {
  handleSubmit() {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        this.props.submit({ type: 'firstArrive', value: values });
        this.props.close('firstArrive');
      }
    });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    return (
      <div className="firstArriveBox">
        <div className="firstArriveButtonBox ruleButtonBox">
          <Button onClick={() => this.props.close('firstArrive')}>取消</Button>
          <Button onClick={this.handleSubmit.bind(this)} type="primary">
            确定
          </Button>
        </div>
        <p>首次触达是指活动开始前的预热通知，所选时间点需要在活动开始时间当天或之前</p>
        <Form className="form-firstArrive">
          <Row className="formMainContent" gutter={24}>
            <Col span={16}>
              <Form.Item className="overCowsName" label="触达时间:">
                {getFieldDecorator('overCowsName', {
                  rules: [{ required: true, message: '请选择触达时间!' }]
                })(
                  <DatePicker
                    showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
                    format="YYYY-MM-DD HH:mm:ss"
                    getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                  />
                )}
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    );
  }
}

export default Form.create({ name: 'firstArrive' })(FirstArrive);
