import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Row, Col, Select, Input, DatePicker } from 'antd';
import dayjs from 'dayjs';
import './integral.scss';

const { Option } = Select;
class Integral extends Component {
  constructor(props) {
    super(props);
    this.state = {
      afterPay: true // 默认付费方式为后付费
    };
  }

  handleSubmit() {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        this.props.submit({ type: 'integral', value: values });
        this.props.close('integral');
      }
    });
  }

  payWayChange() {
    this.setState({
      afterPay: !this.state.afterPay
    });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    return (
      <div className="integralBox">
        <div className="integralButtonBox ruleButtonBox">
          <Button onClick={() => this.props.close('integral')}>取消</Button>
          <Button onClick={this.handleSubmit.bind(this)} type="primary">
            确定
          </Button>
        </div>
        <Form className="form-integral">
          <Row className="formMainContent" gutter={24}>
            <Col span={16}>
              <Form.Item label="任务编码:" className="integralCode marginLeft">
                {getFieldDecorator('code', {
                  rules: [{ required: true, message: '请输入任务编码!' }]
                })(<Input placeholder="请输入任务编码" />)}
              </Form.Item>
            </Col>
          </Row>
          <Row className="formMainContent" gutter={24}>
            <Col span={16}>
              <Form.Item label="付费方式:" className="integralWay marginLeft">
                {getFieldDecorator('payWay', {
                  initialValue: ['afterPay'],
                  rules: [{ required: true, message: '请选择付费方式!' }]
                })(
                  <Select
                    placeholder="请选择付费方式"
                    onChange={this.payWayChange.bind(this)}
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  >
                    <Option value="beforePay">预付费</Option>
                    <Option value="afterPay">后付费</Option>
                  </Select>
                )}
              </Form.Item>
            </Col>
          </Row>
          {this.state.afterPay ? (
            <Row className="formMainContent" gutter={24}>
              <Col span={16}>
                <Form.Item label="积分过期时间:" className="integralTime">
                  {getFieldDecorator('dataPick', {
                    rules: [{ required: true, message: '请选择付费方式!' }]
                  })(
                    <DatePicker
                      showTime={{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }}
                      format="YYYY-MM-DD HH:mm:ss"
                      getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                    />
                  )}
                </Form.Item>
              </Col>
            </Row>
          ) : null}
          <Row className="formMainContent" gutter={24}>
            <Col span={16}>
              <Form.Item label="积分数量:" className="integralNumber marginLeft">
                {getFieldDecorator('integralNumber', {
                  rules: [{ required: true, message: '请输入积分数量!' }]
                })(<Input placeholder="请输入积分数量" />)}
                <p>达到条件后，每人奖励的积分数量</p>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    );
  }
}

export default Form.create({ name: 'integral' })(Integral);
