import heatStatistics from '@/service/heatStatistics';
import { useStore } from '@/store/canpaignV2';
import { useStore as _globalStore } from '@/store/globalStore';
import { Button, Drawer, Popover, Table, message } from 'antd';
import HeaderBreadcrumb from 'components/headerBreadcrumb';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { Component } from 'react';
import CampaignV2Service from 'service/CampaignV2Service';
import UserGroupService from 'service/UserGroupService';
import UserService from 'service/UserService';
import { clearCampaignV2Flows } from 'utils/clearFlows';
import { t } from 'utils/translation';
import CreateActivityForm from './createactivityform/index';
import DataDependency from './dataDependency';
import FlowChart from './flowChart/FlowChart';
import './index.scss';
// import FlowCanvas from './flowChart/flowCanvas/FlowCanvas';
import { breadcrumbConfig, formConfig } from '../config';

const columnsField = [
  {
    dataIndex: 'tableName',
    key: 'tableName',
    title: t('operationCenter-jA5V2MPLr4wV'),
    width: 150
  },
  {
    dataIndex: 'tableField',
    key: 'tableField',
    title: t('operationCenter-ZeXD9AStm1in'),
    width: 200
  },
  {
    dataIndex: 'fieldValue',
    key: 'fieldValue',
    title: t('operationCenter-msqdgVndHJ3b')
  }
];

const columnsSensitive = [
  {
    dataIndex: 'tableName',
    key: 'tableName',
    title: t('operationCenter-jA5V2MPLr4wV')
  },
  {
    dataIndex: 'tableField',
    key: 'tableField',
    title: t('operationCenter-ZeXD9AStm1in')
  }
];

const querystring = require('querystring');

const campaignV2Service = new CampaignV2Service();
const userService = new UserService();

const withStore = (BaseComponent) => (props) => {
  const store = useStore();
  const globalStore = _globalStore();
  return <BaseComponent {...props} store={{ ...store, ...globalStore }} />;
};

export default withStore(
  class Index extends Component {
    constructor(props) {
      super(props);
      const params = querystring.parse(window.location.search.substr(1));
      this.userGroupService = new UserGroupService();
      this.state = {
        id: params?.id, // 只获取地址栏的参数id，来显示创建还是编辑
        campaignId: params?.id || this.props.location.state?.id, // 活动id，如果地址栏id不存在取路由传过来的id
        // campaignType: (params?.type) || (this.props.location.state?.campaignType), // 活动类型， 如果地址栏type不存在取路由传过来的type
        flowsId: params?.flows || this.props.location.state?.flows, // 活动类型， 如果地址栏type不存在取路由传过来的type
        step: 0, // 步骤
        detailObj: {}, // 营销活动详情
        flowsData: '',
        // eslint-disable-next-line react/no-unused-state
        isPrompt: true,
        templateValue: this.props.location.state?.templateValue || null,
        userId: null,
        authAndRole: {},
        fieldTableData: [],
        sensitiveTableData: [],
        showdataDependency: false,
        needCheck: true,
        waitTimes: 1,
        clearFlows: this.props.location.state?.clearFlows && this.props.location.state?.clearFlows // 是否是分享数据 true
      };
      // console.log(params, 'params');
      this.onSubmit = this.onSubmit.bind(this);
      this.updateState = this.updateState.bind(this);
      this.dispatch = this.props.store.dispatch;
    }

    async componentDidMount() {
      const { campaignId, id, flowsId, templateValue, clearFlows } = this.state;
      const params = querystring.parse(window.location.search.substr(1));
      const userInfo = await userService.getCurrentUser();
      this.setState({ userId: userInfo.id });

      if (flowsId) {
        if (_.isArray(flowsId)) {
          this.setState({ flowsData: flowsId });
        } else if (templateValue) {
          this.setState({ flowsData: templateValue.campaignFlows });
        } else {
          const flowsData = await campaignV2Service.getTemplate({ id: flowsId });
          flowsData.flows.forEach((node) => {
            node.detail = {
              ...node.detail,
              busiType: node.busiType
            };
          });
          this.setState({ flowsData: flowsData.flows });
        }
      }
      const isDetail = this.props.location.state?.name;
      if (campaignId) {
        // 使用name字段判断是否是详情页过来的数据，如果是直接取传过来的数据
        // let detailObj = isDetail ? this.props.location.state : await campaignV2Service.get(campaignId);
        let detailObj = isDetail
          ? this.props.location.state
          : await campaignV2Service.getV2({
              id: Number(campaignId),
              deptId: window.getDeptId(),
              companyId: localStorage.getItem('organizationId')
            });
        const authRole = await this.userGroupService.getAuthRoleAndAuthConfByUserId({
          createUserId: detailObj.createUserId
        });
        const fieldList = authRole.busiAuthConfVoList
          .map((item) => {
            if (_.isArray(item.values) && !item.values.length) {
              return undefined;
            }
            return {
              tableName: item.busiAuthConf.table.displayName,
              tableField: item.busiAuthConf.busiName,
              fieldValue: _.isNil(item.values) ? t('operationCenter-HekpOyIJdB7F') : _.join(item.values, ',')
            };
          })
          .filter((filterItem) => filterItem);

        const sensitiveList = authRole.dataSecConfVoList
          .filter((item) => item.checked)
          .map((item2) => {
            return {
              tableName: item2.dataSecConf.table.displayName,
              tableField: item2.dataSecConf.tableSchema.displayName
            };
          });
        this.setState({
          authAndRole: authRole,
          fieldTableData: fieldList,
          sensitiveTableData: sensitiveList
        });

        // localStorage.setItem('campaign_type', detailObj.campaignType);
        const { campaignTarget, beginTime, endTime, remark, flows } = detailObj;
        const name = !id ? `${detailObj.name}-副本` : detailObj.name; // 区分复制流程后新建页面name
        // value用来回显form表单，form表单字段不能多
        detailObj = {
          ...detailObj,
          name,
          campaignTarget,
          remark,
          time: clearFlows ? undefined : [dayjs(beginTime), dayjs(endTime)],
          flows: clearFlows ? clearCampaignV2Flows(flows) : flows,
          enforcementRules: {
            calcRule: detailObj.calcRule,
            scheduleConf: detailObj.scheduleConf,
            scheduleRate: detailObj.scheduleRate
          }
        };

        // todo v1.2.39版本这里步骤默认值为0 初始化接口之后才是1 || 2 是为了以后做版本控制
        this.setState({
          detailObj,
          flowsData: clearFlows ? clearCampaignV2Flows(flows) : flows,
          campaignType: detailObj.campaignType,
          needCheck: detailObj?.needCheck,
          waitTimes: detailObj?.waitTimes || 1,
          step: params?.version ? 2 : 1
        });
      } else {
        this.setState({ campaignType: params.type, step: params?.version ? 2 : 1 });
      }

      window.addEventListener('beforeunload', this.beforeunload);
    }

    // 监听this.state.detailobjd的变化
    componentDidUpdate(prevProps, prevState) {
      if (this.state.detailObj !== prevState.detailObj) {
        this.dispatch({
          campaignInfo: this.state.detailObj
        });
      }
    }

    componentWillUnmount() {
      window.removeEventListener('beforeunload', this.beforeunload);
    }

    beforeunload(e) {
      const confirmationMessage = t('operationCenter-crUeA5tB1lDq');
      (e || window.event).returnValue = confirmationMessage;
      return confirmationMessage;
    }

    // 基本信息保存
    async onSubmit(values, isStep) {
      const { id, flowsData, campaignType, detailObj, userId } = this.state;
      const [beginTime, endTime] = values.time;
      values.beginTime = dayjs(beginTime).unix() * 1000;
      values.endTime = dayjs(endTime).unix() * 1000;
      values.calcRule = values.enforcementRules.calcRule;
      values.scheduleConf = values.enforcementRules.scheduleConf;
      values.scheduleRate = values.enforcementRules.scheduleRate;
      values.campaignType = campaignType;
      values.phase = 'DRAFT';
      values.updateUserId = userId;
      values.createUserId = detailObj?.createUserId;
      if (id) {
        values.id = id; // 如果是编辑的话增加id字段
        values.approvalNo = detailObj.approvalNo;
      }
      values.needCheck = detailObj?.needCheck;
      values.waitTimes = detailObj?.waitTimes || 1;
      delete values.time; // 删除无用的time字段

      if (flowsData) {
        values.flows = flowsData;
      }
      if (detailObj.flows) {
        values.flows = detailObj.flows;
      }
      if (campaignType === 'TEMPLATE' && _.isEmpty(values.flows) && this.props.location.state?.templateValue) {
        values.flows = this.props.location.state?.templateValue.campaignFlows;
      }
      // todo 这里逻辑是 如果是复制流程跳过来的/ 或者是流程画布模板调过来的就取缓存，否则取本身自带的id或者创建默认的deptId
      values.deptId = this.props.location.state ? window.getDeptId() : detailObj?.deptId || window.getDeptId();
      const firstStep = await campaignV2Service.save(values);
      // await campaignV2Service.saveUserOperationRecord({
      //   targetId: firstStep.id,
      //   targetType: 'CAMPAIGN',
      //   type: 'RECENT',
      //   createUserId: userId,
      //   updateUserId: userId,
      //   createTime: dayjs().valueOf(),
      //   updateTime: dayjs().valueOf()
      // });

      const data = {
        detailObj: {
          ...firstStep,
          enforcementRules: {
            calcRule: firstStep.calcRule,
            scheduleRate: firstStep.scheduleRate,
            scheduleConf: firstStep.scheduleConf
          },
          time: [dayjs(firstStep.beginTime), dayjs(firstStep.endTime)]
        },
        isPrompt: false,
        id: firstStep.id
      };
      if (campaignType === 'TEMPLATE' && _.isEmpty(data.detailObj.flows) && this.props.location.state?.templateValue) {
        data.detailObj.flows = this.props.location.state?.templateValue.campaignFlows;
      }
      this.setState(data, () => {
        message.success(t('operationCenter-CvsS6aGOlfBt'), 1);
      });

      const authRole = await this.userGroupService.getAuthRoleAndAuthConfByUserId({
        createUserId: firstStep.createUserId
      });
      const fieldList = authRole.busiAuthConfVoList
        .map((item) => {
          if (_.isArray(item.values) && !item.values.length) {
            return undefined;
          }
          return {
            tableName: item.busiAuthConf.table.displayName,
            tableField: item.busiAuthConf.busiName,
            fieldValue: _.isNil(item.values) ? t('operationCenter-HekpOyIJdB7F') : _.join(item.values, ',')
          };
        })
        .filter((filterItem) => filterItem);

      const sensitiveList = authRole.dataSecConfVoList
        .filter((item) => item.checked)
        .map((item2) => {
          return {
            tableName: item2.dataSecConf.table.displayName,
            tableField: item2.dataSecConf.tableSchema.displayName
          };
        });
      if (this.props?.location?.state?.id) {
        this.updateHeat(this.props?.location?.state?.id, 'CAMPAIGN', 'COPY');
      } else if (this.props?.location?.state?.templateValue?.id) {
        this.updateHeat(this.props?.location?.state?.templateValue?.id, 'CAMPAIGN_TEMPLATE', 'USE');
      }
      this.setState({
        authAndRole: authRole,
        fieldTableData: fieldList,
        sensitiveTableData: sensitiveList
      });

      if (isStep) {
        let search = '';
        if (this.props.history.location.search) {
          if (this.props.history.location.search.indexOf('fullScreen') !== -1) {
            if (this.props.history.location.search.indexOf('id=') !== -1) {
              search = this.props.history.location.search;
            } else {
              search = `${this.props.history.location.search}&id=${firstStep.id}`;
            }
          } else {
            if (this.props.history.location.search.indexOf('id=') !== -1) {
              search = `${this.props.history.location.search}&fullScreen=${true}`;
            } else {
              search = `${this.props.history.location.search}&fullScreen=${true}&id=${firstStep.id}`;
            }
          }
        } else {
          search = `?fullScreen=${true}`;
        }

        if (this.props.store.open_campaign_version && data.detailObj?.calcRule === 'SCHEDULE') {
          search = `${search}&version=${this.state.version || 1}&createVersion=true`;
        }
        const url = `${this.props.history.location.pathname}${search}`;
        this.props.history.push(url, {
          templateValue: this.props.location.state?.templateValue
        });
        // eslint-disable-next-line react/no-unused-state
        this.setState({ step: 2, isPrompt: true, campaignId: firstStep.id });
      } else {
        // 在营销活动点击保存时，如果是模板 就跳转到模板页面
        if (this.props?.location?.state?.templateValue?.id) {
          this.props.history.push('/aimarketer/home/<USER>/template');
        } else {
          this.props.history.push('/aimarketer/home/<USER>');
        }
      }

      return firstStep;
    }

    /**
     *
     * @param {*} id id
     * @param {*} dataType 类型
     * @param {*} operateType 收藏/引用
     */
    updateHeat = async (id, dataType, operateType) => {
      await heatStatistics.saveOrUpdateHeatStatistics({
        projectId: localStorage.getItem('projectId'),
        dataId: id,
        dataType,
        operateType,
        operateUserId: localStorage.getItem('userId')
      });
    };

    /**
     * 保存流程详情
     */
    saveFlows = async (flows, phase, byType) => {
      const _flows = _.cloneDeep(flows);
      _flows.map((item) => {
        if (item.detail) {
          if (item.detail?.whiteSegmentList)
            item.detail.whiteSegmentList = _.filter(item.detail.whiteSegmentList, (item) => {
              return !_.isEmpty(item);
            });
          if (item.detail?.blackSegmentList)
            item.detail.blackSegmentList = _.filter(item.detail.blackSegmentList, (item) => {
              return !_.isEmpty(item);
            });
        }
        return item;
      });
      const { detailObj } = this.state;
      const isReally = await campaignV2Service.listRelySource({
        scenarioCode: detailObj?.scenario?.code,
        deptId: detailObj.deptId || window.getDeptId(),
        flows: _flows,
        id: detailObj.id
      });
      // 这里增加一步校验 如果都为否，则直接改为false
      const _needCheck = isReally.some((item) => item.needCheck);
      const params = {
        ...detailObj,
        approvalStatus: byType && byType === 'ENABLE' ? 'PENDING' : undefined,
        phase,
        flows: _flows,
        campaignType: this.state.campaignType,
        deptId: detailObj.deptId || window.getDeptId(),
        needCheck: !_needCheck ? false : this.state.needCheck,
        waitTimes: this.state.waitTimes
      };

      const resultData = await campaignV2Service.save(params);
      this.setState({
        detailObj: {
          ...resultData,
          enforcementRules: {
            calcRule: resultData.calcRule,
            scheduleRate: resultData.scheduleRate,
            scheduleConf: resultData.scheduleConf
          },
          time: [dayjs(resultData.beginTime), dayjs(resultData.endTime)],
          flows: _flows
        },
        needCheck: !_needCheck ? false : this.state.needCheck
      });
      return resultData;
    };

    /**
     * 作假运行
     */
    fakeExecute = async (fakeBusi) => {
      const { detailObj } = this.state;
      message.success(t('operationCenter-hA35HiFvZK6W'), 1);
      const calcLog = await campaignV2Service.fakeExecute({
        campaignId: detailObj.id,
        ...fakeBusi
      });
      this.props.history.push(`/aimarketer/home/<USER>/detail?id=${calcLog.campaignId}&calcLogId=${calcLog.id}`);
    };

    goBack = (flows) => {
      this.setState({
        step: 1
        // detailObj: { ...this.state.detailObj, count: ++this.state.count }
      });
      this.props.history.push(`/aimarketer/home/<USER>/create?id=${this.state.id}`, {
        templateValue: { campaignFlows: flows }
      });
    };

    cancelPrompt = () => {
      // eslint-disable-next-line react/no-unused-state
      this.setState({ isPrompt: false });
    };

    updateState(_state) {
      this.setState(_state);
    }

    isScenarioDisabled = !!this.state?.campaignId || !!this.props.location.state?.name;

    render() {
      const { campaignType, id, step, detailObj, authAndRole, fieldTableData, sensitiveTableData } = this.state;
      const { campaign_version_flows } = this.props.store;
      const params = querystring.parse(window.location.search.substr(1));
      const { flows } = detailObj;
      const content = (
        <div className="authRole">
          <div className="busiAuth">
            <div className="title">{t('operationCenter-uf9udRuQQ9oW')}</div>
            <div className="content">
              <Table
                dataSource={fieldTableData}
                columns={columnsField}
                style={{ width: '100%' }}
                pagination={false}
                size="middle"
              />
            </div>
          </div>
          <div className="dataAuth">
            <div className="title" style={{ paddingTop: 16 }}>
              {t('operationCenter-bpBAl1ABrPij')}
            </div>
            {/* {
          _.find(authAndRole.dataSecConfVoList, item => item.checked) ? <div className="content">
            {_.reduce(authAndRole.dataSecConfVoList, (a, b) => {
              b.checked && a.push(<div key={`${b.dataSecConf.id}`} className="authItem">
                <span>{`${b.dataSecConf.table.name}.${b.dataSecConf.tableSchema.name}`} </span>
              </div>);
              return a;
            }, [])}
            <span>已脱敏显示</span>
          </div> : '无限制'
        } */}
            {_.find(authAndRole.dataSecConfVoList, (item) => item.checked) ? (
              <Table
                dataSource={sensitiveTableData}
                columns={columnsSensitive}
                pagination={false}
                style={{ width: '100%' }}
                size="middle"
              />
            ) : (
              t('operationCenter-HekpOyIJdB7F')
            )}
          </div>
        </div>
      );

      return (
        <div className="campaignV2Create">
          {/* <Prompt
          when={isPrompt}
          message="还没有保存，确定要退出吗？"
        /> */}
          <div className="campaignV2Create-header">
            <HeaderBreadcrumb config={breadcrumbConfig} type={campaignType} id={id} />
            {step === 2 ? (
              <div className="flex gap-8 items-center">
                {this.props.store.open_campaign_version && this.props.store?.campaignInfo?.calcRule === 'SCHEDULE' && (
                  <div>{t('operationCenter-BhTCRWTv3NT6', { version: params?.version || 1 })}</div>
                )}
                <Button type="primary" onClick={() => this.setState({ showdataDependency: true })}>
                  {t('operationCenter-tZgvde0i2lmn')}
                </Button>
                <Popover trigger="click" overlayClassName="dataAuthPopup" content={content} placement="bottomRight">
                  <Button type="primary">{t('operationCenter-j0m8zBo83cCS')}</Button>
                </Popover>
              </div>
            ) : null}
          </div>

          <section className={`${step === 1 ? 'campaignV2Create-section' : 'campaignV2Create-section-step2'}`}>
            {step === 1 ? (
              <CreateActivityForm
                step={step}
                isScenarioDisabled={this.isScenarioDisabled}
                cancelPrompt={this.cancelPrompt}
                config={formConfig}
                serviceName={campaignV2Service}
                value={_.cloneDeep(detailObj)}
                id={id}
                onSubmit={this.onSubmit}
                state={this.state}
              />
            ) : step === 2 ? (
              <FlowChart
                cancelPrompt={this.cancelPrompt}
                flows={
                  params?.createVersion
                    ? _.cloneDeep(flows)
                    : params?.version
                      ? campaign_version_flows[params?.version]
                      : _.cloneDeep(flows)
                }
                saveFlows={this.saveFlows}
                fakeExecute={this.fakeExecute}
                goBack={this.goBack}
                id={this.state.campaignId}
                campaignId={this.state.campaignId}
                // batchId={}
                campaignInfo={this.state.detailObj}
                scenario={this.state.detailObj?.scenario}
              />
            ) : null}
          </section>
          <Drawer
            title={t('operationCenter-tZgvde0i2lmn')}
            placement="right"
            width={600}
            onClose={() => {
              if (!this.state.waitTimes || this.state.waitTimes < 1) {
                this.setState({ waitTimes: 1 });
              }
              this.setState({ showdataDependency: false });
            }}
            open={this.state.showdataDependency}
            destroyOnClose
            className="templateDrawer"
            bodyStyle={{ marginBottom: 48 }}
          >
            <DataDependency
              updateState={this.updateState}
              needCheck={this.state.needCheck}
              waitTimes={this.state.waitTimes}
              value={this.state.detailObj}
            />
          </Drawer>
        </div>
      );
    }
  }
);
