/* eslint-disable react-hooks/exhaustive-deps */
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Drawer, Input, message, Select } from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import BusinessEntity from 'service/businessEntity';
import BusinessVariable from 'service/businessVariable';

const entityArrList = [
  'entity_user',
  'entity_employee',
  'entity_item',
  'entity_equity',
  'entity_content',
  'entity_device',
  'entity_department'
];

const CreateBusinessVariable = (props) => {
  const {
    visible,
    action,
    value,
    form: { getFieldDecorator, validateFields }
  } = props;
  const [loading, setLoading] = useState(false);
  const [entityList, setEntityList] = useState([]);

  useEffect(() => {
    (async () => {
      const _entityList = await BusinessEntity.listBy([]);
      setEntityList(_entityList);
    })();
  }, []);

  const okHandle = () => {
    validateFields(async (err, fields) => {
      if (err) return;
      try {
        setLoading(true);
        const data = { ...value, ...fields };
        if (!entityArrList.includes(data.entityCode)) {
          data.entityCode = data.entityCode.match(/\[(.+?)\]/)[1];
        }
        await BusinessVariable.save(data);
        message.success('保存成功');
        setLoading(false);
        action({ visible: false, value: {} }, true);
      } catch (err) {
        console.error(err.message);
        setLoading(false);
      }
    });
  };

  const handleValidator = async (rule, v, callback) => {
    if (!v) return callback();
    let bool;
    try {
      if (value?.id) {
        bool = await BusinessVariable.ensureUnique({
          [rule.field]: v.trim(),
          id: value.id
        });
      } else {
        bool = await BusinessVariable.ensureUnique({
          [rule.field]: v.trim()
        });
      }
      if (bool) return callback();
      callback(v);
    } catch (error) {
      callback();
    }
  };

  return (
    <Drawer
      title={value.id ? '编辑业务变量' : '新建业务变量'}
      open={visible}
      // onOk={okHandle}
      // maskClosable={false}
      className="scenarioDrawer"
      onClose={() => action({ visible: false })}
      confirmLoading={loading}
      width="600"
    >
      <Form layout="vertical">
        <Form.Item label="所属业务实体">
          {getFieldDecorator('entityCode', {
            // getValueFromEvent: (event) => event.target.value.trim(),
            rules: [{ required: true, message: '请选择' }],
            initialValue: !_.isEmpty(value) ? `${value.entityName}[${value.entityCode}]` : undefined
          })(
            <Select placeholder="请选择" optionFilterProp="children" allowClear showSearch disabled={value?.id}>
              {entityList.map((n) => (
                <Select.Option key={n.entityCode} value={n.entityCode}>
                  {n.entityName}[{n.entityCode}]
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
        <Form.Item label="业务变量名称">
          {getFieldDecorator('variableName', {
            getValueFromEvent: (event) => event.target.value.trim(),
            rules: [
              { required: true, message: '请输入' },
              { pattern: /^[^\s]*$/, message: '不能输入空格' },
              { max: 64, message: '最多不能超过64个字符' },
              {
                validator: handleValidator,
                message: '业务变量名称已存在'
              }
            ],
            initialValue: value.variableName
          })(<Input placeholder="请输入" autocomplete="off" />)}
        </Form.Item>
        <Form.Item label="业务变量说明">
          {getFieldDecorator('variableDescribe', {
            rules: [
              { required: true, message: '请输入' },
              { pattern: /^[^\s]*$/, message: '不能输入空格' },
              { max: 64, message: '最多不能超过64个字符' },
              {
                validator: handleValidator,
                message: '业务变量说明已存在'
              }
            ],
            getValueFromEvent: (event) => event.target.value.trim(),
            initialValue: value.variableDescribe
          })(<Input placeholder="请输入" autocomplete="off" />)}
        </Form.Item>
      </Form>

      <div style={{ position: 'fixed', bottom: 12, right: 20 }}>
        <Button onClick={() => action({ visible: false })} style={{ marginRight: 8 }}>
          取消
        </Button>
        <Button type="primary" onClick={okHandle} loading={loading}>
          确定
        </Button>
      </div>
    </Drawer>
  );
};

export default Form.create()(CreateBusinessVariable);
