import { UploadOutlined } from '@ant-design/icons';
import { Button, Form, Input, message, Upload } from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import CalendarService from 'service/calendarService';
import { t } from 'utils/translation';
import './index.scss';

const { TextArea } = Input;
export default ({ onClose, onOk, record }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const normFile = (e) => {
    if (Array.isArray(e)) {
      return e;
    }
    const fileValue = e.fileList[0]?.response?.body;
    // let url = fileValue?.result || null;
    if (!_.isEmpty(e.fileList)) {
      e.fileList[0] = {
        ...e.fileList[0],
        // url: transformUrl(url), // 暂时取消下载地址
        status: fileValue?.code === 1 ? 'done' : 'error'
      };
    }
    return e?.fileList;
  };

  const save = async () => {
    try {
      setLoading(true);
      const formValue = await form.validateFields();
      onOk && onOk(formValue, record?.id);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const uploadProps = {
    name: 'file',
    listType: 'picture',
    action: '/analyzer/analyzer/file/upload.do',
    headers: {
      authorization: localStorage.getItem('aim_authorization'),
      'PROJECT-ID': localStorage.getItem('projectId')
    },
    data: {
      type: 'RESOURCE_UPLOAD'
    }
  };

  const beforeUpload = (file) => {
    const isIcs = file.type === 'text/calendar';
    if (!isIcs) {
      message.error(t('dataCenter-1M0YnbVgBJpM'));
    }
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error(t('dataCenter-Spk8Pp0gw0cC'));
    }
    return isIcs && isLt5M;
  };

  useEffect(() => {
    let _record = _.cloneDeep(record);
    if (record) {
      _record = {
        ..._record,
        fileValue: [
          {
            name: _record?.icsFileName || '',
            type: 'text/calendar',
            status: 'done',
            response: {
              body: {
                code: 1,
                result: record?.icsFilePath,
                sourceName: _record?.icsFileName || ''
              }
            }
          }
        ]
      };
    }
    form.setFieldsValue(_record || null);
  }, [record]);

  return (
    <div className="calendarCreate">
      <Form
        form={form}
        layout="vertical"
        // initialValues={() => {
        //   return record || null;
        // }}
      >
        <Form.Item
          label={t('dataCenter-vMVZL2hfjlqD')}
          name="name"
          rules={[
            { required: true, message: t('dataCenter-XIIVt0SkTJcD') },
            { pattern: /^\S+$/, message: t('dataCenter-UKBuUKQQpZBn') },
            () => ({
              validator: async (_, value) => {
                try {
                  let tableNameUnique;
                  const params = {
                    name: value,
                    id: record?.id && record?.id,
                    projectId: localStorage.getItem('projectId')
                  };
                  if (record?.id) {
                    tableNameUnique = await CalendarService.ensureUnique(params);
                  } else {
                    tableNameUnique = await CalendarService.ensureUnique(params);
                  }
                  if (!tableNameUnique) {
                    return Promise.reject(new Error(t('dataCenter-7Vr3iuk1ABJT')));
                  } else {
                    return Promise.resolve();
                  }
                } catch (error) {
                  return Promise.reject(new Error(t('dataCenter-9dD6som642Qx')));
                }
              }
            })
          ]}
        >
          <Input showCount maxLength={64} placeholder={t('dataCenter-rFBoHIkArPom')} />
        </Form.Item>
        <Form.Item label={t('dataCenter-lhhRefmmJAZY')} name="memo">
          <TextArea rows={4} showCount maxLength={200} placeholder={t('dataCenter-rFBoHIkArPom')} />
        </Form.Item>
        <Form.Item
          label={t('dataCenter-cO88anapksb0')}
          name="fileValue"
          valuePropName="fileList"
          getValueFromEvent={normFile}
          rules={[{ required: true, message: t('dataCenter-K7yeMesKy8NT') }]}
        >
          <Upload {...uploadProps} maxCount={1} beforeUpload={beforeUpload}>
            <Button icon={<UploadOutlined />}>{t('dataCenter-NB8PVouRXgNh')}</Button>
          </Upload>
        </Form.Item>
      </Form>
      <span style={{ color: 'rgba(0,0,0,.45' }}>{t('dataCenter-DfIyHool3v6Y')}</span>
      <div style={{ position: 'fixed', bottom: 12, right: 20 }}>
        <Button onClick={onClose} style={{ marginRight: 8 }}>
          {t('dataCenter-jRYwYMTCiOLJ')}
        </Button>
        <Button type="primary" onClick={save} loading={loading}>
          {t('dataCenter-aHK4ExF4wknu')}
        </Button>
      </div>
    </div>
  );
};
