// @import 'assets/css/variable.scss';

.nextOrNo {
  .ant-btn {
    margin-left: 20px;
  }
}

.createBox {
  min-width: 1200px;

  .formBox {
    background: $white;
    padding: 20px 30px;

    .form-base-mesg {
      .formMainContent>div:nth-child(2n + 1) {
        margin-right: 50px;
      }

      .formMainContent {
        display: flex;
        flex-wrap: wrap;

        .ant-form-item {
          display: flex;

          .ant-form-item-control-wrapper {
            flex: 1;
          }

          .ant-form-item-children {
            width: 100%;
            display: inline-block;

            .ant-calendar-picker {
              width: 100% !important;
              min-width: 0 !important;
            }
          }
        }

        .notRequired .ant-form-item-label {
          margin-left: 11px;
        }

        .shortTimeExcept .ant-form-item-label {
          margin-left: -17px;
        }

        .exceptIntegral .ant-form-item-label {
          margin-left: -30px;
        }

        .shortTimeDescription .ant-form-item-label {
          margin-left: 10px;
        }

        .divider {
          margin-top: 0;
        }

        .timePicker {
          display: flex;

          .ant-form-item {
            flex-grow: 1;
          }

          .endTime {
            .ant-form-item-required::before {
              content: "" !important;
            }
          }
        }
      }

      // switch前面加上必填标志，与原型图一致
      .sendCredit {
        margin-bottom: 10px;

        label::before {
          display: inline-block;
          margin-right: 4px;
          color: #f5222d;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: "*";
        }
      }
    }
  }
}