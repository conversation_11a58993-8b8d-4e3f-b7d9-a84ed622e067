import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Row,
  Col,
  Input,
  Select,
  DatePicker,
  Button,
  Spin,
  Divider, // Cascader
  message
} from 'antd';
import dayjs from 'dayjs';
import { withRouter } from 'react-router-dom';
import CampaignManageService from 'pages/home/<USER>/univelsal/campaignManageService';
import {
  // CAMPAIGN_TARGET_MAP,
  CAMPAIGN_TYPE_MAP
} from '../../config';
import './createBase.scss';

const { TextArea } = Input;
const { Option } = Select;

class CreateBase extends Component {
  constructor(props) {
    super(props);
    this.campaignManageService = new CampaignManageService();
    this.handleValidator = this.handleValidator.bind(this);
    this.state = {
      campaignType: 'SHORTERM', // 活动是短期活动还是长期活动,默认为短期活动
      campaignTarget: 'CONVERT', // 活动目标(临时版)
      switchOnOff: false, // 积分发放开关，默认为false
      id: this.props.createFormData ? this.props.createFormData.id : false, // 活动ID
      loading: false, // 默认有loading图标
      status: 'DRAFT' // 活动状态默认为草稿，只有草稿状态才能进行下一步的编辑
      // selectProduct: [] // 选择产品数据
    };
  }

  async componentDidMount() {
    /*  let productData = this.dealProductData2(await this.campaignManageService.getProductList());
    if (Object.getOwnPropertyNames(this.props.createFormData).length !== 0) {
      this.setState({
        loading: false,
        status: this.props.createFormData.status,
        selectProduct: productData
      },
      () => {
        this.setFieldsValueAsy(this.props.createFormData);
      });
    } else {
      this.setState({
        loading: false,
        selectProduct: productData
      });
    } */
    if (Object.getOwnPropertyNames(this.props.createFormData).length !== 0) {
      this.setState(
        {
          loading: false,
          status: this.props.createFormData.status
        },
        () => {
          this.setFieldsValueAsy(this.props.createFormData);
        }
      );
    }
  }

  // 处理产品信息数据，用于二级Cascader，也可在二级数据字典中使用
  dealProductData2(arr) {
    const productData = [];
    for (let i = 0; i < arr.length; i++) {
      const obj = {};
      obj.value = arr[i].id.level2;
      obj.label = arr[i].label;
      obj.children = [];
      for (let j = 0; j < arr[i].itemList.length; j++) {
        obj.children.push({
          value: arr[i].itemList[j].code,
          label: arr[i].itemList[j].label
        });
      }
      productData.push(obj);
    }
    return productData;
  }

  setFieldsValueAsy(data) {
    if (data.campaignType === 'LONGTERM' || data.sendCredit) {
      this.setState(
        {
          campaignType: data.campaignType,
          switchOnOff: data.sendCredit
        },
        () => {
          this.setFieldsValue(data);
        }
      );
    } else {
      this.setFieldsValue(data);
    }
  }

  // 给表单数据赋初值
  setFieldsValue(data) {
    // console.log(data);
    if (this.state.campaignType === 'SHORTERM') {
      this.props.form.setFieldsValue({
        name: data.name, // 活动名称
        beginTime: dayjs(data.beginTime),
        endTime: dayjs(data.endTime),
        // selectProduct: data.productType && data.productName && [data.productType, data.productName],
        campaignType: data.campaignType, // 活动类型，长期还是短期
        // campaignTarget: data.campaignTarget, // 活动目标
        estCustomerCount: data.estCustomerCount, // 预计获客人数
        estBuyerCount: data.estBuyerCount, // 预计成交人数
        estLoanAmount: data.estLoanAmount, // 预计放款金额
        estCost: data.estCost, // 预计活动成本
        comment: data.comment // 活动描述
      });
    } else {
      this.props.form.setFieldsValue({
        name: data.name, // 活动名称
        beginTime: dayjs(data.beginTime),
        endTime: dayjs(data.endTime),
        // selectProduct: data.productType && data.productName && [data.productType, data.productName],
        campaignType: data.campaignType, // 活动类型，长期还是短期
        // campaignTarget: data.campaignTarget, // 活动目标
        comment: data.comment // 活动描述
      });
    }
  }

  // 短期活动与长期活动点击切换
  campaignTypeOnChange(value) {
    this.setState({ campaignType: value });
  }

  // 积分发放按钮
  switchOnchange(value) {
    this.setState({ switchOnOff: value });
  }

  // 点击下一步或者保存
  handleSubmit(value) {
    this.props.form.validateFields(async (err, values) => {
      values.sendCredit = this.state.switchOnOff;
      values.campaignType = this.state.campaignType;
      values.campaignTarget = this.state.campaignTarget;
      values.status = this.state.status;
      if (!err) {
        this.setState({ loading: true });
        values.beginTime = dayjs(values.beginTime).valueOf();
        values.endTime = dayjs(values.endTime).valueOf();
        if (values.selectProduct) {
          [values.productType, values.productName] = [values.selectProduct[0], values.selectProduct[1]];
        }
        delete values.selectProduct;
        let result = {};
        if (this.state.id) {
          values.id = this.state.id;
          try {
            result = await this.campaignManageService.saveCampaign(values);
            this.setState({ loading: false });
            if (value === 'saveAndBack') return this.props.history.push('/aimarketer/home/<USER>');
            if (this.state.status === 'DRAFT') {
              // this.props.history.push('/aimarketer/home/<USER>');
              this.props.callBack({ type: 'createBaseNext', value: result });
            } else {
              message.success('保存成功,非草稿状态下只能编辑基本信息', 1.5);
              setTimeout(() => {
                this.props.history.push('/aimarketer/home/<USER>');
              }, 1500);
            }
          } catch (error) {
            this.setState({ loading: false });
          }
        } else {
          try {
            result = await this.campaignManageService.saveCampaign(values);
            this.setState({ loading: false });
            // this.props.history.push('/aimarketer/home/<USER>');
            if (value === 'saveAndBack') return this.props.history.push('/aimarketer/home/<USER>');
            values.id = result.id;
            this.props.callBack({ type: 'createBaseNext', value: result });
          } catch (error) {
            this.setState({ loading: false });
          }
        }
      }
    });
  }

  // 点击取消按钮
  cancel() {
    this.props.callBack({
      type: 'createBaseCancel',
      value: 'createBaseCancel'
    });
  }

  // 判断名称是否唯一
  async handleValidator(rule, value, callback) {
    if (!value) return callback();
    // if (value.trim() === this.state.tagName) return callback();
    const { id } = this.state;
    let bool;
    try {
      if (id) {
        bool = await this.campaignManageService.ensureNameUnique({
          name: value.trim(),
          id
        });
      } else {
        bool = await this.campaignManageService.ensureNameUnique({
          name: value.trim()
        });
      }
      if (bool) return callback();
      callback(value);
    } catch (error) {
      callback();
    }
  }

  disabledEndDate = (endValue) => {
    const startValue = this.props.form.getFieldValue('beginTime');
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.valueOf() <= startValue.valueOf();
  };

  configToOption(arr) {
    const obj = [];
    if (Array.isArray(arr)) {
      for (let i = 0; i < arr.length; i++) {
        if (typeof arr[i] === 'object') {
          obj.push(
            <Option value={arr[i].value} key={arr[i]}>
              {arr[i].name}
            </Option>
          );
        } else {
          obj.push(
            <Option value={arr[i]} key={arr[i]}>
              {arr[i]}
            </Option>
          );
        }
      }
    } else {
      for (const key in arr) {
        obj.push(
          <Option key={key} value={key}>
            {arr[key]}
          </Option>
        );
      }
    }
    return obj.map((item) => {
      return item;
    });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    // let { selectProduct } = this.state;
    return (
      <div className="createBox">
        <Spin spinning={this.state.loading}>
          <div className="createSecondTitle">
            <span className="headName">填写活动基本信息</span>
            <div className="nextOrNo">
              <Button onClick={this.cancel.bind(this)}>取消</Button>
              {this.state.status === 'DRAFT' && (
                <Button type="primary" className="testScope" onClick={this.handleSubmit.bind(this)}>
                  下一步
                </Button>
              )}

              <Button type="primary" onClick={this.handleSubmit.bind(this, 'saveAndBack')}>
                保存并返回
              </Button>
            </div>
          </div>
          <div className="formBox">
            <Form className="form-base-mesg">
              <Row className="formMainContent" gutter={24}>
                <Col span={10}>
                  <Form.Item className="formBasename" label="活动名称:">
                    {getFieldDecorator('name', {
                      rules: [
                        { required: true, message: '请输入活动名称!' },
                        { max: 60, message: '活动名请不要超出60个字符' },
                        {
                          pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
                          message: '请输入字母、数字、字符(._-)或者汉字'
                        },
                        { whitespace: true, message: '活动名称不能为空' },
                        {
                          validator: this.handleValidator,
                          message: '活动名称已存在'
                        }
                      ]
                    })(<Input placeholder="请输入活动名称" />)}
                  </Form.Item>
                </Col>
                {/* <Col span={10}>
                  <Form.Item label="活动目标:">
                    {getFieldDecorator('campaignTarget', {
                      rules: [{ required: true, message: '请选择活动目标!' }]
                    })(<Select
                      className="ant-select-open"
                      placeholder="请选择活动目标"
                      getPopupContainer={(triggerNode) => triggerNode.parentNode}
                    >
                      {this.configToOption(CAMPAIGN_TARGET_MAP)}
                    </Select>)}
                  </Form.Item>
                </Col> */}
                <Col span={10} className="timePicker">
                  <Form.Item label="活动时间:">
                    {getFieldDecorator('beginTime', {
                      rules: [{ required: true, message: '请选择生效时间!' }]
                    })(
                      <DatePicker
                        placeholder="请选择生效时间"
                        showTime={{
                          defaultValue: dayjs('00:00:00', 'HH:mm:ss')
                        }}
                        format="YYYY-MM-DD HH:mm:ss"
                        getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                        disabled={this.state.status !== 'DRAFT'}
                      />
                    )}
                  </Form.Item>
                  <Form.Item label="至:" className="endTime">
                    {getFieldDecorator('endTime', {
                      rules: [{ required: true, message: '请选择失效时间!' }]
                    })(
                      <DatePicker
                        placeholder="请选择失效时间"
                        disabledDate={this.disabledEndDate}
                        showTime={{
                          defaultValue: dayjs('00:00:01', 'HH:mm:ss')
                        }}
                        format="YYYY-MM-DD HH:mm:ss"
                        getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                      />
                    )}
                  </Form.Item>
                </Col>
                {/* <Col span={10}>
                  <Form.Item label="选择产品:" className="notRequired">
                    {getFieldDecorator('selectProduct', {
                      // rules: [{ required: true, message: '请选择产品名称!' }]
                    })(<Cascader
                      options={selectProduct}
                      placeholder="请选择产品类型和名称"
                      getPopupContainer={(triggerNode) => triggerNode.parentNode}
                    />)}
                  </Form.Item>
                </Col> */}
                <Col span={10}>
                  <Form.Item label="活动类型:">
                    {getFieldDecorator('campaignType', {
                      initialValue: ['SHORTERM'],
                      rules: [{ required: true, message: '请选择活动类型!' }]
                    })(
                      <Select
                        className="ant-select-open"
                        placeholder="请选择活动类型"
                        onChange={this.campaignTypeOnChange.bind(this)}
                        getPopupContainer={(triggerNode) => triggerNode.parentNode}
                        disabled={this.state.status !== 'DRAFT'}
                      >
                        {this.configToOption(CAMPAIGN_TYPE_MAP)}
                      </Select>
                    )}
                  </Form.Item>
                </Col>
                {/* <Col span={10}>
                  <Form.Item label="产品类型:" className="notRequired">
                    {getFieldDecorator('productType', {
                      // rules: [{ required: true, message: '请选择标签类型!' }]
                    })(<Select
                      className="ant-select-open"
                      placeholder="请选择产品类型"
                      getPopupContainer={(triggerNode) => triggerNode.parentNode}
                      onChange={this.selectProduct.bind(this, 'type')}
                    >
                      {this.configToOption(productType)}
                    </Select>)}
                  </Form.Item>
                </Col>
                <Col span={10}>
                  <Form.Item label="产品名称:" className="notRequired">
                    {getFieldDecorator('productName', {
                      // rules: [{ required: true, message: '请选择产品名称!' }]
                    })(<Select
                      className="ant-select-open"
                      placeholder="请选择产品名称"
                      getPopupContainer={(triggerNode) => triggerNode.parentNode}
                      onChange={this.selectProduct.bind(this, 'name')}
                    >
                      {this.configToOption(productName)}
                    </Select>)}
                  </Form.Item>
                </Col> */}
              </Row>
              {this.state.campaignType === 'SHORTERM' && (
                <>
                  {/* <Row className="formMainContent" gutter={24}>
                      <Col span={16}>
                        <Form.Item className="sendCredit" label="积分发放:">
                          {getFieldDecorator('sendCredit', {
                            // rules: [{ required: true, message: ' ' }]
                          })(<Switch
                            checkedChildren="开"
                            unCheckedChildren="关"
                            className="switchButton"
                            onChange={this.switchOnchange.bind(this)}
                            checked={this.state.switchOnOff}
                          />)}
                        </Form.Item>
                      </Col>
                    </Row> */}
                  {/* this.state.switchOnOff
                      && <Row className="formMainContent" gutter={24}>
                        <Col span={10}>
                          <Form.Item className="estCreditAmount" label="预计积分总量:">
                            {getFieldDecorator('estCreditAmount', {
                              rules: [
                                { pattern: /^\d+$/g, message: '请输入数字' },
                                { required: true, message: '请选择活动类型!' }
                              ]
                            })(<Input
                              placeholder="请输入预计积分总量"
                            />)}
                          </Form.Item>
                        </Col>
                      </Row> */}
                  <Row className="formMainContent" gutter={24}>
                    <Col className="noMaregin" span={19}>
                      <Divider className="divider" />
                    </Col>
                  </Row>
                  <Row className="formMainContent" gutter={24}>
                    <Col span={10}>
                      <Form.Item className="estCustomerCount shortTimeExcept" label="预计获客人数:">
                        {getFieldDecorator('estCustomerCount', {
                          rules: [
                            // { pattern: /^\d*$/g, message: '请输入数字' }
                            {
                              pattern: /^\d{0,11}$/,
                              message: '请输入数字，且人数在一百亿以内'
                            }
                          ]
                        })(<Input placeholder="请输入预计获客人数" />)}
                      </Form.Item>
                    </Col>
                    <Col span={10}>
                      <Form.Item className="estBuyerCount shortTimeExcept" label="预计成交人数:">
                        {getFieldDecorator('estBuyerCount', {
                          rules: [
                            // { pattern: /^\d+$/g, message: '请输入数字' }
                            {
                              pattern: /^\d{0,11}$/,
                              message: '请输入数字，且人数在一百亿以内'
                            }
                          ]
                        })(<Input placeholder="请输入预计成交人数" />)}
                      </Form.Item>
                    </Col>
                    <Col span={10}>
                      <Form.Item className="estLoanAmount shortTimeExcept" label="预计放款金额:">
                        {getFieldDecorator('estLoanAmount', {
                          rules: [
                            // { pattern: /^\d+$/g, message: '请输入数字' }
                            {
                              pattern: /^\d{0,11}$/,
                              message: '请输入整数，且金额在一百亿以内'
                            }
                          ]
                        })(<Input placeholder="请输入预计放款金额" />)}
                      </Form.Item>
                    </Col>
                    <Col span={10}>
                      <Form.Item className="estCost shortTimeExcept" label="预计活动成本:">
                        {getFieldDecorator('estCost', {
                          rules: [
                            {
                              pattern: /^\d{0,11}$/,
                              message: '请输入整数，且金额在一百亿以内'
                            }
                          ]
                        })(<Input placeholder="请输入预计活动成本" />)}
                      </Form.Item>
                    </Col>
                  </Row>
                </>
              )}
              <Row className="formMainContent" gutter={24}>
                <Col span={21} className="noMaregin">
                  <Form.Item
                    className={this.state.campaignType === 'SHORTERM' ? 'shortTimeDescription' : 'comment'}
                    label="活动描述:"
                  >
                    {getFieldDecorator('comment', {
                      rules: [
                        {
                          required: !(this.state.campaignType === 'SHORTERM'),
                          message: '请输入活动描述!'
                        },
                        { max: 1000, message: '活动描述请不要超出1000个字符' }
                      ]
                    })(
                      <TextArea
                        className="eventTaxtArea"
                        placeholder="请输入活动描述，1000字以内"
                        autoSize={{ minRows: 3, maxRows: 9 }}
                      />
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </div>
        </Spin>
      </div>
    );
  }
}

export default withRouter(Form.create({ name: 'base_message' })(CreateBase));
