export default {
  cn: {
    'operationCenter-sO0MvBUjsiXa': '分群',
    'operationCenter-FBUJ2ziCSasp': '标签',
    'operationCenter-CsAYzVEM7eoz': '人',
    'operationCenter-RU5tPeprCs56': '是否校验',
    'operationCenter-FLUfLOZWUgkK': '是',
    'operationCenter-r0k7iL7ezZXO': '否',
    'operationCenter-VYbFygjzdL2O': '最近计算时间',
    'operationCenter-PUnFrFka10VU': '开启后，当依赖数据未就绪时策略会等待，在接受到数据就绪通知后再开启；',
    'operationCenter-rfMWQyPi0DNT': '如果在指定时间段内数据未就绪，则批次会执行失败',
    'operationCenter-vPl4ORN8PJv9': '等待',
    'operationCenter-xAhE6S4KENMU': '小时',
    'operationCenter-8YOc3xU4yF3U': '客群数据',
    'operationCenter-vk5VMumrsIVB': '节点ID：',
    'operationCenter-wmVQ881QDXmv': '标签数据',
    'operationCenter-WM91rNhXUy': '失败原因',
    'operationCenter-KbX2zp5Iqk': '是否就绪'
  },
  en: {
    'operationCenter-sO0MvBUjsiXa': 'Segment',
    'operationCenter-FBUJ2ziCSasp': 'Tag',
    'operationCenter-CsAYzVEM7eoz': ' people',
    'operationCenter-RU5tPeprCs56': 'Validation Required',
    'operationCenter-FLUfLOZWUgkK': 'Yes',
    'operationCenter-r0k7iL7ezZXO': 'No',
    'operationCenter-VYbFygjzdL2O': 'Last Calculation Time',
    'operationCenter-PUnFrFka10VU': 'When enabled, the strategy will wait when dependent data is not ready, and start after receiving data ready notification;',
    'operationCenter-rfMWQyPi0DNT': 'If data is not ready within the specified time period, the batch will fail to execute',
    'operationCenter-vPl4ORN8PJv9': 'Wait',
    'operationCenter-xAhE6S4KENMU': 'hours',
    'operationCenter-8YOc3xU4yF3U': 'Segment Data',
    'operationCenter-vk5VMumrsIVB': 'Node ID: ',
    'operationCenter-wmVQ881QDXmv': 'Tag Data',
    'operationCenter-WM91rNhXUy': 'Failure Reason',
    'operationCenter-KbX2zp5Iqk': 'Ready'
  }
};
