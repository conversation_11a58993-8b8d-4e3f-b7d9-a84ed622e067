import React, { useState } from 'react';
import { Table, Button, Drawer, Space, Form, Input } from 'antd';
import { connect } from 'react-redux';

import getMenuTitle from 'utils/menuTitle';

import './index.scss';

const stateToProps = (state) => {
  return {
    meunData: state.meunData.menus.main
  };
};

const pagination = {
  showTotal: () => `共 ${7} 条`,
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['5', '10', '20', '50', '100'],
  defaultPageSize: 5
};

const mockData = [
  {
    id: 4785,
    name: '镇江分行_观影福利月月领活动',
    time: '2022-11-17 ~ 2022-12-31',
    status: '运行中',
    amount: '80,000',
    amount2: '53,872',
    percent: '67.34%',
    user: '谭海威'
  },
  {
    id: 1029,
    name: '无锡分行_待发薪四季度促活',
    time: '2022-10-01 ~ 2022-12-31',
    status: '运行中',
    amount: '120,000',
    amount2: '89,265',
    percent: '74.39%',
    user: '谭海威'
  },
  {
    id: 5069,
    name: '南京分行_代发薪客群高山游活动',
    time: '2022-11-10 ~ 2022-12-31',
    status: '运行中',
    amount: '60,000',
    amount2: '54,126',
    percent: '90.21%',
    user: '谭海威'
  },
  {
    id: 4327,
    name: '江苏省行_开门红营销活动',
    time: '2022-11-30 ~ 2023-03-31',
    status: '运行中',
    amount: '1,200,000',
    amount2: '228,399',
    percent: '19.03%',
    user: '谭海威'
  },
  {
    id: 5225,
    name: '江苏总行_全省信用卡拉新营销',
    time: '2022-11-30 ~ 2023-03-31',
    status: '运行中',
    amount: '560,000',
    amount2: '32,866',
    percent: '5.87%',
    user: '谭海威'
  },
  {
    id: 5613,
    name: '中国银行促活_镇江分行',
    time: '2022-11-20 ~ 2023-12-31',
    status: '运行中',
    amount: '150,000',
    amount2: '73,287',
    percent: '48.86%',
    user: '谭海威'
  },
  {
    id: 2514,
    name: '盐城分行_沉睡用户激活',
    time: '2022-09-22 ~ 2023-12-31',
    status: '运行中',
    amount: '100,000',
    amount2: '86,253',
    percent: '86.25%',
    user: '谭海威'
  }
];

function BudgetManage(props) {
  const [open, setOpen] = useState(false);

  const columns = [
    {
      title: '活动ID',
      key: 'id',
      dataIndex: 'id'
    },
    {
      title: '活动名称',
      key: 'name',
      dataIndex: 'name'
    },
    {
      title: '活动时间',
      key: 'time',
      dataIndex: 'time'
    },
    {
      title: '活动状态',
      key: 'status',
      dataIndex: 'status'
    },
    {
      title: '预算额度',
      dataIndex: 'amount',
      key: 'amount'
    },
    {
      title: '消耗额度',
      dataIndex: 'amount2',
      key: 'amount2'
    },
    {
      title: '消耗百分比',
      dataIndex: 'percent',
      key: 'percent'
    },
    {
      title: '预算审核人',
      dataIndex: 'user',
      key: 'user'
    }
  ];

  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  return (
    <div className="budgetManageWrap">
      <header>
        <h1>{getMenuTitle(props.meunData, props.history.location.pathname)}</h1>
        <div className="rightSide">
          <Button className="DTButton bor_ra-6" type="primary" onClick={showDrawer}>
            新建预算
          </Button>
        </div>
      </header>
      <div className="cardWrap">
        <div className="item">
          <div className="title">年度预算总额</div>
          <div className="count">6,000,000</div>
        </div>
        <div className="item">
          <div className="title">年度消耗预算总额</div>
          <div className="count">2,637,825</div>
        </div>
        <div className="item">
          <div className="title">消耗百分比</div>
          <div className="count">43.96%</div>
        </div>
        <div className="item">
          <div className="title">年度预算剩余</div>
          <div className="count">3,362,175</div>
        </div>
        <div className="item">
          <div className="title">剩余百分比</div>
          <div className="count">56.04%</div>
        </div>
      </div>
      <div className="tableWrap">
        <Table
          dataSource={mockData}
          columns={columns}
          className="table1 bor_ra-6"
          rowKey="id"
          pagination={pagination}
          scroll={{ x: 1300 }}
        />
      </div>

      <Drawer
        title={
          <div className="titleWrap">
            <div>新建预算</div>
            <Space>
              <Button onClick={onClose} className="bor_ra-6">
                取消
              </Button>
              <Button type="primary" onClick={onClose} className="bor_ra-6">
                保存
              </Button>
            </Space>
          </div>
        }
        placement="right"
        className="drawerWrap"
        width={800}
        closable={false}
        onClose={onClose}
        open={open}
        // footer={
        //     <Space>
        //     <Button onClick={onClose}>Cancel</Button>
        //     <Button type="primary" onClick={onClose}>
        //       OK
        //     </Button>
        //   </Space>
        // }
      >
        <Form layout="vertical">
          <Form.Item label="关联活动ID">
            <Input style={{ width: '80%' }} className="bor_ra-6" placeholder="请输入关联活动ID" />
          </Form.Item>
          <Form.Item
            label={
              <div className="flexWrap">
                <div>活动预算额度</div>
                <div className="label">货币：人民币 单位：元</div>
              </div>
            }
          >
            <Input style={{ width: '80%' }} className="bor_ra-6" placeholder="请输入活动预算额度" />
          </Form.Item>
          <Form.Item
            label={
              <div className="flexWrap">
                <div>积分折抵</div>
                <div className="label2">货币：人民币 单位：元</div>
              </div>
            }
          >
            <Input style={{ width: '80%' }} className="bor_ra-6" placeholder="请输入积分折抵" />
          </Form.Item>
          <Form.Item label="预算审核人">
            <Input style={{ width: '80%' }} className="bor_ra-6" placeholder="请输入预算审核人" />
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  );
}

export default connect(stateToProps)(BudgetManage);
