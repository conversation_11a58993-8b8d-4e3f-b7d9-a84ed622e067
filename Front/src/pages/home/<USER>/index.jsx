import { ExclamationCircleOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Input, Modal, Select, Table, message } from 'antd';
import ColumnActionCom from 'components/featurecoms/tableactioncom/index';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';
import UserService from 'service/UserService';
import BusinessEntity from 'service/businessEntity';
import BusinessVariable from 'service/businessVariable';
import CheckAuth from 'utils/checkAuth';
import getMenuTitle from 'utils/menuTitle';
import { setNativeValue } from 'utils/universal';
import './List.scss';
import { entityMap, initParam } from './config';
import EditScenario from './create';
import FilterComponents from './filterComponents';

const userService = new UserService();
const { confirm } = Modal;
const pagination = {
  showTotal: (totals) => `共 ${totals} 条`,
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
};

const stateToProps = (state) => {
  return {
    meunData: state.meunData.menus.main
  };
};

const TagList = (props) => {
  const [param, setParam] = useState(_.cloneDeep(initParam));
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editModalInfo, setEditModalInfo] = useState({
    visible: false,
    value: {}
  });

  const [search, setSearch] = useState([]);
  const [searchSelectVal, setSearchSelectVal] = useState('variableName');
  const inputRef = useRef(null);

  const [myFilter, setMyFilter] = useState([]);

  useEffect(() => {
    const getList = async () => {
      try {
        setLoading(true);
        const finalParam = _.cloneDeep(param);
        // finalParam.search.push({ operator: 'EQ', propertyName: 'product', value: 'BMS' });
        const result = await BusinessVariable.query(finalParam);
        pagination.total = result.totalElements;
        pagination.current = param.page;
        pagination.pageSize = param.size;
        setList(result.content);
        setLoading(false);
      } catch (error) {
        console.error(error.message);
        setLoading(false);
      }
    };
    getList();
  }, [param]);

  const handleTableChange = (lastpagination, filtersArg, sorter) => {
    param.page = lastpagination.current;
    param.size = lastpagination.pageSize;
    if (sorter.field) {
      param.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setParam({ ...param });
  };

  const editAction = (data, flag) => {
    setEditModalInfo({ ...editModalInfo, ...data });
    if (flag) {
      setParam({ ...param });
    }
  };

  const columns = [
    {
      title: '所属业务实体',
      dataIndex: 'entityCode',
      width: 160,
      render: (text) => entityMap[text]
    },
    {
      title: '业务变量名称',
      dataIndex: 'variableName',
      width: 150
    },
    {
      title: '业务变量说明',
      dataIndex: 'variableDescribe',
      width: 200
    },
    {
      title: '更新者',
      dataIndex: 'updateUserName',
      width: 120,
      ellipsis: true
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 200,
      sorter: true
    },
    {
      title: '操作',
      className: 'td-set',
      width: 120,
      fixed: 'right',
      render: (text, record) => {
        const featureData = {
          // 特征
          edit: {
            text: '编辑',
            code: 'aim_business_variable_edit'
          },
          dropdownData: {
            delete: {
              text: '删除',
              code: 'aim_business_variable_edit'
            }
          }
        };
        return (
          <ColumnActionCom closeDropdown featureData={featureData} record={record} onClick={onColumnActionClick} />
        );
      }
    }
  ];

  const onColumnActionClick = (type, _, record) => {
    if (type === 'edit') {
      setEditModalInfo({ visible: true, value: record });
    } else if (type === 'delete') {
      confirm({
        title: <span style={{ fontWeight: '600' }}>是否删除变量?</span>,
        icon: <ExclamationCircleOutlined />,
        okText: '确认删除',
        content: (
          <div>
            <div>
              您将删除业务变量：{record.variableDescribe}[{record.variableName}]
            </div>
            <div>该操作无法撤销，删除后所有策略包括在运行的策略中变量将失效。</div>
          </div>
        ),
        async onOk() {
          try {
            await BusinessVariable.delById(record.id);
            message.success('删除成功');
            setParam({ ...param });
          } catch (err) {
            console.error(err.message);
          }
        },
        onCancel() {}
      });
    }
  };

  const onTextChange = _.debounce((val) => {
    let _search = _.cloneDeep(search);
    if (searchSelectVal === 'variableName') {
      const index = _.findIndex(_search, (v) => v.propertyName === 'variableName');
      if (index !== -1) {
        _search.splice(index, 1);
      }
      _search = [..._search, { operator: 'LIKE', propertyName: 'variableName', value: val }];
    } else {
      const index = _.findIndex(_search, (v) => v.propertyName === 'variableDescribe');
      if (index !== -1) {
        _search.splice(index, 1);
      }
      _search = [..._search, { operator: 'LIKE', propertyName: 'variableDescribe', value: val }];
    }
    setSearch(_search);
    setParam({ ...param, search: [...myFilter, ..._search], page: 1 });
  }, 500);

  const onSelect = (value) => {
    // inputRef.current.state.value = '';
    setNativeValue([inputRef.current.input], '');
    let _search = _.cloneDeep(search);
    const index = _.findIndex(_search, (v) => v.propertyName === 'variableName');
    if (index !== -1) {
      _search.splice(index, 1);
    }
    _search = [
      ..._search,
      {
        operator: 'EQ',
        propertyName: value,
        value: inputRef.current.input.value
      }
    ];
    setSearch(_search);
    setSearchSelectVal(value);
  };

  const [items, setItems] = useState([
    {
      connector: '所属业务实体',
      operator: 'EQ',
      type: 'select',
      label: '所属业务实体',
      name: 'entityCode',
      options: []
    },
    {
      connector: '更多过滤',
      type: 'more',
      components: [
        {
          label: '更新者',
          type: 'select',
          operator: 'EQ',
          name: 'updateUserId',
          connector: 'AND',
          options: []
        },
        {
          label: '更新时间',
          type: 'date',
          name: 'updateTime',
          connector: 'AND',
          operator: 'DATE_BETWEEN'
        }
      ]
    }
  ]);

  useEffect(() => {
    (async () => {
      const reData = await Promise.all([BusinessEntity.listBy([]), userService.listBy()]);
      const _items = _.cloneDeep(items);
      _items[0].options = _.map(reData[0], (item) => ({
        label: item.entityName,
        value: item.entityCode
      }));
      _items[1].components[0].options = _.map(reData[1], (item) => ({
        label: item.name,
        value: item.id
      }));
      setItems(_items);
    })();
  }, []);

  const changeFilter = (value) => {
    setMyFilter(value);
    setParam({ ...param, search: [...value, ...search], page: 1 });
  };

  return (
    <div className="VarStyle">
      <header>
        <h1>{getMenuTitle(props.meunData, props.history.location.pathname)}</h1>
        <div className="rightSide">
          <Input.Group compact>
            <Select value={searchSelectVal} onSelect={onSelect}>
              <Select.Option value="variableName">变量名称</Select.Option>
              <Select.Option value="variableDescribe">变量说明</Select.Option>
            </Select>
            <Input
              onChange={(e) => onTextChange(e.target.value)}
              style={{ width: '208px' }}
              suffix={<SearchOutlined />}
              ref={inputRef}
            />
          </Input.Group>
          <CheckAuth code="aim_business_variable_edit">
            <Button
              className="DTButton bor_ra-6"
              type="primary"
              onClick={() => setEditModalInfo({ visible: true, value: {} })}
            >
              新建
            </Button>
          </CheckAuth>
        </div>
      </header>
      <div className="filter">
        <div className="smallTitle">业务变量列表</div>
        <div className="filterComponents">
          <FilterComponents items={items} onChange={changeFilter} />
        </div>
      </div>
      <div className="tableList">
        <Table
          columns={columns}
          dataSource={list}
          bordered={false}
          loading={loading}
          onChange={handleTableChange}
          pagination={pagination}
          rowKey="id"
          scroll={{ x: 1300 }}
        />
      </div>

      {editModalInfo.visible && <EditScenario {...editModalInfo} action={editAction} />}
    </div>
  );
};

export default connect(stateToProps)(TagList);
