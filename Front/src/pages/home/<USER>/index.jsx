import { Breadcrumb, Button, Descriptions, Form, Input, Modal, Select, Tabs, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { t } from 'utils/translation';

import { transformUrl } from 'utils/universal';

import _ from 'lodash';
import { statusList } from './config';

import MyToDoListService from 'service/myToDoListService';

import ApprovalHistroy from './approvalHistory';
import ApprovalProcess from './approvalProcess';
import './index.scss';

const { TextArea } = Input;

const querystring = require('querystring');

const typeEunm = {
  SEGMENT: 'approve-approveDetail-enum-userSegment',
  CAMPAIGN: 'approve-approveDetail-enum-processCanvas',
  MARKET_WORKS: 'approve-approveDetail-enum-marketingWorks',
  LABEL: 'approve-approveDetail-enum-labelSystem',
  TOUCH_CHANNEL: 'approve-approveDetail-enum-messageTemplate'
};

const detailUrlList = {
  CAMPAIGN: 'approve-approveDetail-enum-processDetail',
  SEGMENT: 'approve-approveDetail-enum-segmentDetail'
};

const detailUrlList2 = {
  CAMPAIGN: 'home/campaignV2',
  SEGMENT: 'home/portraitCenter/userGroup'
};

const ApproveDetail = (props) => {
  const [approveDetailData, setApproveDetailData] = useState([]);
  const [processData, setProcessData] = useState([]);
  const [open, setOpen] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState([]);
  const [loading, setLoading] = useState(false);
  const [processType, setProcessType] = useState('');
  const [approvalStatus, setApproveStatus] = useState('CANCEL');
  const [historyParam, setHistoryParam] = useState([]);
  const [tabKey, setTabKey] = useState('history');

  const [form] = Form.useForm();

  const { definition } = querystring.parse(window.location.search.substr(1));

  const currentUserId = Number(localStorage.getItem('userId'));

  const {
    approvalNo,
    contentName,
    contentType,
    createUserId,
    contentUrl,
    mark,
    contentId,
    taskId,
    approvalType,
    createTime,
    currentApproval
  } = props.location.state;

  const items = [
    {
      key: 'history',
      label: t('approve-approveDetail-tab-approvalHistory')
    },
    approvalType === 'ACTIVITI' && {
      key: 'process',
      label: t('approve-approveDetail-tab-approvalProcess')
    }
  ];

  useEffect(() => {
    const getApproveDetailData = async () => {
      try {
        setConfirmLoading(true);
        setLoading(true);
        let finalParam = _.cloneDeep(historyParam);
        finalParam = [{ operator: 'EQ', propertyName: 'approvalNo', value: approvalNo }];
        const result =
          approvalType === 'ACTIVITI'
            ? await MyToDoListService.processHistoryListByV2(finalParam)
            : await MyToDoListService.processHistoryListBy(finalParam);

        if (approvalType === 'ACTIVITI') {
          result.unshift({ status: 'START' });
        }
        const approvalRes = await MyToDoListService.query2({
          page: 1,
          search: [
            {
              operator: 'EQ',
              propertyName: 'approvalNo',
              value: approvalNo
            }
          ],
          size: 10,
          sorts: [
            {
              propertyName: 'createTime',
              direction: 'desc'
            }
          ]
        });

        setProcessData(approvalRes.content[0]);
        setApproveStatus(approvalRes.content[0].status);
        setApproveDetailData(result);
        setConfirmLoading(false);
        setLoading(false);
      } catch (err) {
        console.error(err);
      } finally {
        setConfirmLoading(false);
        setLoading(false);
      }
    };

    getApproveDetailData();
  }, [historyParam]);

  const handleCancel = () => {
    setOpen(false);
  };

  const showModal = (type) => {
    setOpen(true);
    setProcessType(type);
  };

  const onView = async () => {
    if (contentType === 'MARKET_WORKS') {
      const result = await MyToDoListService.getAuthorization({
        loginId: currentUserId,
        projectId: localStorage.getItem('projectId')
      });

      const newUrl =
        contentUrl.indexOf('?') >= 0
          ? `${contentUrl}&Authorization=${result}`
          : `${contentUrl}?Authorization=${result}`;
      window.open(newUrl, '_blank');
    } else if (approvalType === 'BMS' && mark === 'INSIDE') {
      const newUrl = transformUrl(
        `${detailUrlList2[contentType]}/detail?id=${contentId}&definition=${true}&status=${true}&promoterId=${createUserId}&approvalType=BMS`
      );

      window.open(newUrl, '_blank');
    } else if (approvalType === 'ACTIVITI' && processData.mark === 'INSIDE') {
      const newUrl = transformUrl(
        `${detailUrlList2[processData.contentType]}/detail?id=${processData.contentId}&definition=${true}&status=${true}&promoterId=${processData.createUserId}&nodeCreateTime=${createTime}&taskId=${taskId}&currentApproval=${currentApproval}&approvalType=ACTIVITI`
      );

      window.open(newUrl, '_blank');
    } else {
      const newContentUrl = contentUrl.includes('?') ? `${contentUrl}&isTag=true` : `${contentUrl}?isTag=true`;
      const pathName = transformUrl(`home/iframe?src=${newContentUrl}`);
      window.open(pathName, '_blank');
    }
  };

  const onSubmit = async () => {
    try {
      const { opinion } = await form.validateFields();

      let finalParam = _.cloneDeep(historyParam);
      finalParam = [{ operator: 'EQ', propertyName: 'approvalNo', value: approvalNo }];

      const result = await MyToDoListService.processHistoryListBy(finalParam);
      if (approvalStatus !== 'RUNNING') {
        message.error(
          `${result[result.length - 1].contentName} ${t('status')}${statusList[result[result.length - 1].status]}`
        );
        setOpen(false);
        setHistoryParam({ ...historyParam });
        return;
      }
      const params = {
        approvalNo,
        taskId,
        nodeCreateTime: approvalType === 'ACTIVITI' && createTime ? createTime : undefined,
        approverId: currentUserId,
        contentType,
        status: processType,
        opinion,
        projectId: localStorage.getItem('projectId')
      };

      await MyToDoListService.saveProcessLabelInstance(params);
      message.success(t('approve-approveDetail-message-approvalSuccess'));
      setHistoryParam({ ...historyParam });
      setConfirmLoading(false);
      handleCancel();
      approvalType === 'ACTIVITI' && props.history.push(`/aimarketer/home/<USER>'2' });
    } catch (error) {
      setConfirmLoading(false);
    }
  };

  return (
    <div className="approvalDetail">
      <div className="approveDetailWrap bg-[#fff] flex flex-col mt-0 mb-0 ml-[-24px] mr-[-24px] overflow-auto pt-[14px] pl-[24px] pr-[24px] pb-0">
        <div>
          <Breadcrumb>
            <Breadcrumb.Item>
              {definition ? (
                <a onClick={() => props.history.goBack()}>{t('approve-approveDetail-approvalItems')}</a>
              ) : (
                <a onClick={() => props.history.goBack()}>{t(detailUrlList[contentType])}</a>
              )}
            </Breadcrumb.Item>
            <Breadcrumb.Item>{t('approve-approveDetail-breadcrumb-approvalDetail')}</Breadcrumb.Item>
          </Breadcrumb>
        </div>
        <div className=" flex justify-between items-center">
          <h2 className=" font-[600] mt-[8px]">{t('approve-approveDetail-title')}</h2>
          <div>
            <Button
              className="mr-[8px]"
              disabled={
                !!(
                  approvalStatus === 'PASS' ||
                  approvalStatus === 'REJECT' ||
                  createUserId === currentUserId ||
                  approvalStatus === 'BACKOUT' ||
                  approvalStatus === 'CANCEL' ||
                  currentApproval === 'DONE'
                )
              }
              onClick={() => showModal('REJECT')}
            >
              {t('approve-approveDetail-button-reject')}
            </Button>
            <Button
              type="primary"
              disabled={
                !!(
                  approvalStatus === 'PASS' ||
                  approvalStatus === 'REJECT' ||
                  createUserId === currentUserId ||
                  approvalStatus === 'BACKOUT' ||
                  approvalStatus === 'CANCEL' ||
                  currentApproval === 'DONE'
                )
              }
              onClick={() => showModal('PASS')}
            >
              {t('approve-approveDetail-button-approve')}
            </Button>
          </div>
        </div>
        <div>
          <Descriptions>
            <Descriptions.Item label={t('approve-approveDetail-label-approvalNumber')}>{approvalNo}</Descriptions.Item>
            <Descriptions.Item label={t('approve-approveDetail-label-approvalItemName')}>
              <a onClick={() => onView()}>{contentName}</a>
            </Descriptions.Item>
            <Descriptions.Item label={t('approve-approveDetail-label-type')}>
              {t(typeEunm[contentType])}
            </Descriptions.Item>
          </Descriptions>
        </div>
        <Tabs items={items} activeKey={tabKey} onChange={(e) => setTabKey(e)} />
      </div>
      <div
        className="mt-[24px] mb-[24px] bg-[#fff] overflow-auto "
        style={{ borderRadius: 6, height: 'calc(100vh - 284px)' }}
      >
        {tabKey === 'history' ? (
          <ApprovalHistroy approveDetailData={approveDetailData} loading={loading} approvalType={approvalType} />
        ) : (
          <ApprovalProcess deployKey={processData.deployKey} />
        )}
      </div>
      <Modal
        title={t('approve-approveDetail-modal-title')}
        className="approveModalWrap"
        open={open}
        destroyOnClose
        okText={
          processType === 'PASS'
            ? t('approve-approveDetail-modal-button-confirmApprove')
            : t('approve-approveDetail-modal-button-confirmReject')
        }
        onOk={() => onSubmit()}
        confirmLoading={confirmLoading}
        onCancel={handleCancel}
      >
        <div className="processForm">
          <Form
            name="basic"
            form={form}
            layout="vertical"
            colon={false}
            initialValues={{
              processName: contentName,
              id: null,
              opinion: null,
              status: null
            }}
          >
            <Form.Item label={t('approve-approveDetail-modal-label-approvalItemName')} name="processName">
              <Select disabled />
            </Form.Item>

            <Form.Item
              label={t('approve-approveDetail-modal-label-approvalOpinion')}
              name="opinion"
              rules={[{ max: 100, message: t('approve-approveDetail-modal-validation-approvalOpinion') }]}
            >
              <TextArea
                placeholder={t('approve-approveDetail-modal-placeholder-approvalOpinion')}
                autoSize={{
                  minRows: 2,
                  maxRows: 6
                }}
              />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </div>
  );
};

export default ApproveDetail;
