import { Alert, Radio } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { t } from 'utils/translation';
import { funnelAnalysisContext } from '../funnelAnalysisContext';
import './updateChart.scss';

export default function UpdateChart(props) {
  const { boardType, detailObj } = props;
  const history = useHistory();
  const { state, dispatch } = useContext(funnelAnalysisContext);
  const { displayType } = state;
  const [boardList, setBoardList] = useState([]);
  const [campaignList, setCampaignList] = useState([]);
  const { id } = useParams();

  useEffect(() => {
    const init = async () => {
      const getChartsFromBoard = await FunnelAnalysis.getChartsFromBoard({ id });
      const getChartsFromCampaign = await FunnelAnalysis.getChartsFromCampaign({
        id
      });
      setBoardList(getChartsFromBoard);
      setCampaignList(getChartsFromCampaign);
    };
    init();
  }, []);

  const clickBoards = (clickId, type, campaignType) => {
    if (type === 'board') {
      window.open(`/aimarketer/home/<USER>/dataDashboard/${clickId}`);
    } else {
      if (campaignType === 'CAMPAIGNS') {
        window.open(`/aimarketer/home/<USER>/detail/${clickId}`);
      } else {
        window.open(`/aimarketer/home/<USER>/detail?id=${clickId}`);
      }
    }
  };

  return (
    <div className="funnelUpdateChart">
      <div className="content">
        <Alert
          message={
            localStorage.getItem('isActivityAnalysis')
              ? t('analysisCenter-rujf8rp3eWmM')
              : t('analysisCenter-FY9LuPketRid')
          }
          type="info"
          showIcon
        />
        <div className="displayType">
          <div>{t('analysisCenter-cq7e9FxcueJP')}</div>
          <div>
            <Radio.Group onChange={(e) => dispatch({ displayType: e.target.value })} value={displayType}>
              <Radio value="COLUMN_CHART">{t('analysisCenter-cr6e5w4yuAjN')}</Radio>
              <Radio value="PERCENT_COLUMN_CHART">{t('analysisCenter-OJs6hHI4EzO8')}</Radio>
              <Radio value="LINE_CHART">{t('analysisCenter-yyKc2HlW0r4w')}</Radio>
            </Radio.Group>
          </div>
        </div>

        {!_.isEmpty(boardList) && (
          <div className="showBoard">
            <div className="title">{t('analysisCenter-7hbGbdnDWgXf')}</div>
            <div className="boards">
              {boardList.map((item, index) => {
                return (
                  <a className="boardsItem" key={index} onClick={() => clickBoards(item?.id, 'board')}>
                    {item?.boardName}
                  </a>
                );
              })}
            </div>
          </div>
        )}

        {localStorage.getItem('isActivityAnalysis') ? (
          <div className="mt-[16px]">
            <div className="mb-[8px]">{t('analysisCenter-yM3PIFBwWatT')}</div>
            <div className="cursor-pointer">
              {boardType === 'campaigns' ? (
                <a
                  onClick={() => history.push(`/aimarketer/home/<USER>/detail/${detailObj.id}`)}
                >{`[${detailObj.id}]${detailObj.name}`}</a>
              ) : (
                <a
                  onClick={() => history.push(`/aimarketer/home/<USER>/detail?id=${detailObj.id}`)}
                >{`[${detailObj.id}]${detailObj.name}`}</a>
              )}
            </div>
          </div>
        ) : (
          !_.isEmpty(campaignList) && (
            <div className="showBoard">
              <div className="title">{t('analysisCenter-6tebXHi8JVUj')}</div>
              <div className="boards">
                {campaignList.map((item, index) => {
                  return (
                    <a
                      className="boardsItem"
                      key={index}
                      onClick={() => clickBoards(item?.campaignId, 'campaign', item?.type)}
                    >
                      {item?.type === 'CAMPAIGNS' ? item?.campaigns?.name : item?.campaign?.name}
                    </a>
                  );
                })}
              </div>
            </div>
          )
        )}
      </div>
    </div>
  );
}
