import { CloseOutlined, DownOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import {
  Button,
  Card,
  Divider,
  Drawer,
  Dropdown,
  Empty,
  Input,
  InputNumber,
  Modal,
  Popover,
  Select,
  Space,
  Spin,
  Tooltip,
  Tree,
  message
} from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { getDeptPath } from 'pages/home/<USER>/dataPermissions/config';
import React, { useEffect, useReducer, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import CampaignV2Service from 'service/CampaignV2Service';
import CampaignsService from 'service/CampaignsService';
import ScenarioService from 'service/ScenarioService';
import UserService from 'service/UserService';
import AnalysisCenterService from 'service/analysisCenterService';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { useDebounce, useQuery } from 'utils/customhooks';
import { t } from 'utils/translation';
import { SelectTime } from 'wolf-static-cpnt';
import { MyIconV2 } from '../../../../utils/myIcon';
import RenderChart from './charts/renderCharts';
import RenderTable from './charts/renderTable';
import Dimension from './dimension';
import GlobalFilter from './filter';
import { funnelAnalysisContext } from './funnelAnalysisContext';
import './index.scss';
import SaveChart from './saveChart/saveChart';
import SaveGroup from './saveGroup/index';
import Steps from './steps';
import UpdateChart from './updateChart/updateChart';

const querystring = require('querystring');

const { Option } = Select;

const reducer = (state, action) => ({ ...state, ...action });
const adc = {
  MINUTE: t('analysisCenter-DYUgyLUF5Cxb'),
  HOUR: t('analysisCenter-nvOH8cY57GAe'),
  DAY: t('analysisCenter-82qJCQ5QjMyQ')
};

const campaignV2Service = new CampaignV2Service();
const campaignsService = new CampaignsService();
const userService = new UserService();

// const shortcutOptions = {
//   今天: [dayjs().startOf('day'), dayjs().endOf('day')],
//   昨日: [dayjs().subtract(1, 'days').startOf('day'), dayjs().subtract(1, 'days').endOf('day')],
//   本周: [dayjs().startOf('week'), dayjs().endOf('week')],
//   本月: [dayjs().startOf('month'), dayjs().endOf('month')],
//   过去7天: [dayjs().subtract(7, 'days'), dayjs()],
//   过去30天: [dayjs().subtract(30, 'days'), dayjs()],
//   过去60天: [dayjs().subtract(60, 'days'), dayjs()]
// };

export default function FunnelAnalysisDom(props) {
  const parmas = querystring.parse(window.location.search.substr(1));
  const { id } = useParams();

  const queryParams = useQuery();
  const campaignId = queryParams.get('campaignId');
  const boardType = queryParams.get('boardType');
  const scenario = queryParams.get('scenarioId');
  const version = queryParams.get('version');
  const saveId = queryParams.get('saveId');
  const deptId = queryParams.get('deptId');

  const [state, dispatch] = useReducer(reducer, {
    name: t('analysisCenter-EqCBceR36Alg'),
    loading: false,
    scenarioId: null,
    dateRange2: [
      {
        type: 'RELATIVE',
        timeTerm: 'DAY',
        isPast: true,
        times: 7,
        truncateAsDay: true
      },
      { type: 'NOW', timeTerm: 'DAY', isPast: true, truncateAsDay: true }
    ],
    visibleConversion: false,
    chartConfig: {
      funnelDataQuery: {
        conversion: {
          stepTerm: 'DAY',
          amount: 7
        }
      }
    },
    timeTerm: 'DAY',
    tableColumn: null,
    tableList: null,
    filterTableList: [],
    chartList: null,
    lineChartList: null,
    stepList: [],
    dimensionGroup: [],
    globalFilters: [],
    scenarioList: [],
    chartDisplayType: 'COLUMN_CHART',
    chartLoading: false,
    tableLoading: false,
    isMultiStep: false, // 是否禁用维度分组
    selectedRowKeys: [],
    offSave: true,
    open: true,
    defaultFunnel: null,
    showSaveGroup: false,
    storageType: null, // CONVERT,LOSS   漏斗保存分群类型 转化/流失
    saveGroupValue: null, // 保存分群的值
    visibleDrawer: false,
    updateChart: false,
    displayType: 'COLUMN_CHART' // 最终图表展示的类型 COLUMN_CHART,PERCENT_COLUMN_CHART,LINE_CHART,
  });
  const [scenarioList, setScenarioList] = useState([]);
  const [filterConfig, setFilterConfig] = useState({});
  const [chartConfigData, setChartConfigData] = useState([]);
  const [userId, setUserId] = useState(null);
  const [boardDetail, setBoardDetail] = useState({});
  const [campaignTreeData, setCampaignTreeData] = useState([]);
  const [deptPath, setDeptPath] = useState(getDeptPath());
  const [campaignList, setCampaignList] = useState([]);
  const timer = useRef(null);
  const onRef = useRef(null);

  useEffect(() => {
    dispatch({ offSave: true });
  }, [
    state.stepList,
    state.globalFilters,
    state.dimensionGroup,
    state.chartConfig.funnelDataQuery.conversion,
    state.timeTerm,
    state.dateRange2
  ]);

  // 过滤
  useEffect(() => {
    if (!_.isEmpty(state.tableList)) {
      const _filterTableList = [];
      state.tableList.forEach((item, index) => {
        // push进去前6个
        if (index < 6) {
          _filterTableList.push(item.key);
        }
      });
      dispatch({ filterTableList: _filterTableList });
    }
  }, [state.tableList]);

  // 遍历维度分组是否禁用
  useEffect(() => {
    const isFlag = state.stepList.some((item) => item.multiStep);
    if (isFlag) {
      dispatch({ isMultiStep: true });
    } else {
      dispatch({ isMultiStep: false });
    }
  }, [state.stepList]);

  const GetQueryString = (name) => {
    const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
    const r = window.location.search.substr(1).match(reg); // search,查询？后面的参数，并匹配正则
    if (r != null) return unescape(r[2]);
    return null;
  };

  const saveRecordInfo = async (id, userId) => {
    await campaignsService.saveUserOperationRecord({
      targetId: id,
      id: state.defaultFunnel?.recentUserOperationRecord?.id,
      targetType: 'CHART',
      type: 'RECENT',
      createUserId: userId,
      updateUserId: userId,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });
  };

  useEffect(() => {
    init();
    return () => {
      timer.current && clearTimeout(timer.current);
      localStorage.removeItem('isActivityAnalysis');
      // localStorage.removeItem('timefilter');
    };
  }, []);

  /**
   * @param {object} result 刷新传入
   */
  const init = async (result) => {
    if (campaignId) {
      localStorage.setItem('activityCache', true);
    }
    let _scenarioList = await ScenarioService.scenarioList([]);
    _scenarioList = _scenarioList.sort((a, b) => a.orderNum - b.orderNum);
    const userInfo = await userService.getCurrentUser();
    setUserId(userInfo.id);
    setScenarioList(_scenarioList);
    dispatch({
      scenarioList: _scenarioList
    });
    if (!_.isNil(id)) {
      dispatch({
        chartLoading: true,
        tableLoading: true
      });
      // let info = await AnalysisCenterService.getChartConfig(id);
      let info = await AnalysisCenterService.getChartConfigV2({
        id: Number(id),
        deptId: deptId || window.getDeptId()
      });
      if (info) {
        setDeptPath(getDeptPath(info?.deptId));
        setChartConfigData(info);
        const _dimensionGroup = info?.chartConfig?.funnelDataQuery?.dimensionGroup;
        _dimensionGroup.map((item) => {
          const arr = [];
          item.filters &&
            item.filters.forEach((i) => {
              arr.push(i?.groupName);
            });
          item.filterValue = arr;
          return item;
        });

        const timeFilter = GetQueryString('timeFilter');
        // 设置状态来判定是否第一次进入页面
        if (timeFilter && !localStorage.getItem('timeFilter')) {
          const timeFilterArr = timeFilter.split(',');
          localStorage.setItem('timeFilter', JSON.stringify(timeFilter));
          info = {
            ...info,
            dateRange2: [
              {
                type: 'ABSOLUTE',
                timestamp: parseInt(timeFilterArr[0]),
                times: 30,
                timeTerm: 'DAY',
                isPast: true
              },
              {
                type: 'ABSOLUTE',
                timestamp: parseInt(timeFilterArr[1]),
                times: 0,
                timeTerm: 'DAY',
                isPast: true
              }
            ]
          };
          setFilterConfig({
            startTime: timeFilterArr[0],
            endTime: timeFilterArr[1]
          });
        }

        const calcTime = parmas.calcState ? JSON.parse(localStorage.getItem('dateRange')) : info.dateRange2;
        await dispatch({
          name: result?.name || info.name,
          chartConfig: info.chartConfig,
          // dateRange2: info.dateRange2,
          dateRange2: calcTime,
          stepList: info.chartConfig.funnelDataQuery.stepList,
          scenarioId: info.scenario.id,
          globalFilters: info.chartConfig.funnelDataQuery.globalFilters,
          dimensionGroup: _dimensionGroup,
          displayType: info.displayType,
          timeTerm: info.chartConfig.funnelDataQuery.timeConfig.timeTerm,
          chartDisplayType: info.displayType,
          selectedRowKeys: info.chartConfig.funnelDataQuery.displayGroupNames,
          defaultFunnel: info
        });
        const tableColumn = await FunnelAnalysis.queryFunnelTableColumn(info.chartConfig.funnelDataQuery);
        await doRequest(info, 'TABLE', calcTime);
        dispatch({ tableColumn: tableColumn.columns, offSave: false });
      }
    } else {
      if (state.stepList.length > 1) return;
      const _stepList = _.cloneDeep(state.stepList);
      _stepList.push(
        {
          step: '1',
          visibleSetp: false,
          name: t('analysisCenter-Rc0jBDYd8o4D'),
          type: 'EVENT',
          event: {}
        },
        {
          step: '2',
          visibleSetp: false,
          name: t('analysisCenter-Rc0jBDYd8o4D'),
          type: 'EVENT',
          event: {}
        }
      );
      dispatch({
        stepList: _stepList,
        scenarioId:
          campaignId && boardType === 'flowCanvas'
            ? Number(scenario)
            : _scenarioList.find((item) => item.isDefault)?.id || _scenarioList[0].id
      });
    }
  };

  useEffect(() => {
    const getBoardInfo = async () => {
      if (campaignId) {
        if (boardType === 'campaigns') {
          const result = await campaignsService.getCampaignsV2({
            id: Number(campaignId),
            deptId: window.getDeptId()
          });

          if (!_.isNil(id)) {
            const treeResult = [
              {
                title: `${t('analysisCenter-pd54G1XffSIU')}[${result.id}] ${result.name}`,
                key: result.id,
                children: state.chartConfig.campaignFilters
                  ? state.chartConfig.campaignFilters.map((item) => {
                      return {
                        title: `${t('analysisCenter-Bb7EvUgKa0TZ')}[${item.id}] ${item.name}`,
                        key: item.id
                      };
                    })
                  : []
              }
            ];
            setCampaignTreeData(treeResult);
          } else {
            const res = await campaignsService.getCampaignFilter({
              type: 'CAMPAIGNS',
              id: Number(campaignId)
            });

            setCampaignList(res);

            const treeResult = [
              {
                title: `${t('analysisCenter-pd54G1XffSIU')}[${result.id}] ${result.name}`,
                key: result.id,
                children:
                  res && res.length
                    ? res.map((item) => {
                        return {
                          title: `${t('analysisCenter-Bb7EvUgKa0TZ')}[${item.id}] ${item.name}`,
                          key: item.id
                        };
                      })
                    : []
              }
            ];

            setCampaignTreeData(treeResult);
          }

          setBoardDetail(result);
        } else {
          if (!_.isNil(id)) {
            const treeResult = state.chartConfig.campaignFilters
              ? state.chartConfig.campaignFilters.map((item) => {
                  return {
                    title: `${t('analysisCenter-Bb7EvUgKa0TZ')}[${item.id}] ${item.name}`,
                    key: item.id,
                    isLeaf: true
                  };
                })
              : [];
            setBoardDetail(state.chartConfig.campaignFilters && state.chartConfig.campaignFilters[0]);
            setCampaignTreeData(treeResult);
          } else {
            const result = await campaignsService.getCampaignFilter({
              type: 'FLOW_CANVAS',
              id: Number(campaignId)
            });

            setCampaignList(result);
            const treeResult = [
              {
                title: `${t('analysisCenter-Bb7EvUgKa0TZ')}[${result[0].id}] ${result[0].name}`,
                key: result.id
              }
            ];
            setBoardDetail(result[0]);
            setCampaignTreeData(treeResult);
          }
        }
      }
    };

    getBoardInfo();
  }, [state.chartConfig]);

  const exitQuit = () => {
    props.history.go(-1);
  };

  const changeName = (e) => {
    let name;
    if (!_.isEmpty(e)) {
      if (e.target.value.length > 32) {
        tip();
      }
      name = e.target.value.substring(0, 32);
      dispatch({ name });
    }
  };

  const tip = useDebounce(() => {
    message.error(t('analysisCenter-UBrafWb7omAS'));
  }, 500);

  const changeConversion = (conversion) => {
    dispatch({
      chartConfig: {
        funnelDataQuery: {
          ...state.chartConfig.funnelDataQuery,
          conversion: {
            ...state.chartConfig.funnelDataQuery.conversion,
            stepTerm: conversion
          }
        }
      }
    });
  };

  const handleVisibleChange = () => {
    dispatch({ visibleConversion: !state.visibleConversion });
  };

  const changeInputNumber = (e) => {
    dispatch({
      chartConfig: {
        funnelDataQuery: {
          ...state.chartConfig.funnelDataQuery,
          conversion: {
            ...state.chartConfig.funnelDataQuery.conversion,
            amount: !e ? 1 : e
          }
        }
      }
    });
  };

  const change = (value) => {
    dispatch({
      dateRange2: value
    });
  };

  const renderConversion = () => {
    const DAY = 360;
    const HOUR = 360 * 24;
    const MINUTE = 360 * 24 * 60;
    const { conversion } = state.chartConfig.funnelDataQuery;
    return (
      <div>
        <div className="conversion-title">{t('analysisCenter-zJQH63hLYr9q')}</div>
        <div className="conversion-content">
          <InputNumber
            min={1}
            onChange={changeInputNumber}
            value={conversion.amount}
            max={
              conversion.stepTerm === 'DAY' && conversion.amount > DAY
                ? DAY
                : conversion.stepTerm === 'HOUR' && conversion.amount > HOUR
                  ? HOUR
                  : conversion.stepTerm === 'MINUTE' && conversion.amount > MINUTE
                    ? MINUTE
                    : MINUTE
            }
          />
          <Select onChange={changeConversion} value={conversion.stepTerm}>
            <Option value="MINUTE">{t('analysisCenter-DYUgyLUF5Cxb')}</Option>
            <Option value="HOUR">{t('analysisCenter-nvOH8cY57GAe')}</Option>
            <Option value="DAY">{t('analysisCenter-82qJCQ5QjMyQ')}</Option>
          </Select>
        </div>
        <div className="tip">{t('analysisCenter-2juiHLHoPLow')}</div>
      </div>
    );
  };

  const changeScenario = (value) => {
    const oldValue = _.cloneDeep(state.scenarioId);
    const _stepList = _.cloneDeep(state.stepList);
    const flag = _.every(_stepList, (item) => item.name === t('analysisCenter-Rc0jBDYd8o4D'));

    if (!flag) {
      Modal.confirm({
        icon: <ExclamationCircleOutlined />,
        content: t('analysisCenter-ktk0RwaIiCZ9'),
        okText: t('analysisCenter-1LeHuH2BW9mm'),
        cancelText: t('analysisCenter-OiCiDIVFUxpj'),
        onOk() {
          dispatch({
            scenarioId: value,
            stepList: [],
            dimensionGroup: [],
            globalFilters: []
          });
        },
        onCancel() {
          dispatch({ scenarioId: oldValue });
        }
      });
    } else {
      dispatch({
        scenarioId: value,
        stepList: [],
        dimensionGroup: [],
        globalFilters: []
      });
    }
  };

  const saveFunnel = async (obj) => {
    const { type, forceCalc } = obj;
    if (type === 'newChart') {
      return dispatch({ updateChart: false, visibleDrawer: true });
    }
    const childValue = await onRef?.current?.saveCharts();
    dispatch({
      chartLoading: true,
      tableLoading: true,
      selectedRowKeys: []
    });
    try {
      const { name, chartConfig, dateRange2, stepList, scenarioId } = state;
      if (stepList.length < 2) {
        throw new Error(t('analysisCenter-nFKpNV1N3KWX'));
      }
      stepList.forEach((item) => {
        if (item.name === t('analysisCenter-Rc0jBDYd8o4D')) {
          throw new Error(t('analysisCenter-LIIuU1Vp0Q7S'));
        }
      });

      const _stepList = _.cloneDeep(stepList);

      _stepList.forEach((item) => {
        if (item.multiStep) {
          item.multiStepList.forEach((multiStepItem) => {
            multiStepItem.filters.forEach((item2) => {
              const keyList = Object.keys(item2);
              const key = keyList.find((keyItem) => keyItem === 'userLabel');

              if (key === 'userLabel') {
                const _itemValue = _.cloneDeep(item2.userLabel.filters[0].filters[0].value);
                if (_.isArray(_itemValue) && typeof _itemValue[0] === 'string') {
                  const filterArrayRes = _itemValue.map((item3) => {
                    const itemRes = item3.match(/\[(.+?)\]/g);
                    item3 = itemRes ? RegExp.$1 : item3;
                    return item3;
                  });
                  item2.userLabel.filters[0].filters[0].showValue =
                    item2.userLabel.filters[0].filters[0].showValue || item2.userLabel.filters[0].filters[0].value;
                  item2.userLabel.filters[0].filters[0].value = filterArrayRes;
                } else {
                  const result = _itemValue && !_.isArray(_itemValue) && _itemValue.toString().match(/\[(.+?)\]/g);

                  item2.userLabel.filters[0].filters[0].showValue =
                    item2.userLabel.filters[0].filters[0].showValue || item2.userLabel.filters[0].filters[0].value;
                  item2.userLabel.filters[0].filters[0].value = result
                    ? RegExp.$1
                    : item2.userLabel.filters[0].filters[0].value;
                }
              }
            });
          });
        } else {
          item.filters.forEach((item2) => {
            const keyList = Object.keys(item2);
            const key = keyList.find((keyItem) => keyItem === 'userLabel');
            if (key === 'userLabel') {
              const _itemValue = _.cloneDeep(item2.userLabel.filters[0].filters[0].value);
              if (_.isArray(_itemValue) && typeof _itemValue[0] === 'string') {
                const filterArrayRes = _itemValue.map((item3) => {
                  const itemRes = item3.match(/\[(.+?)\]/g);
                  item3 = itemRes ? RegExp.$1 : item3;
                  return item3;
                });
                item2.userLabel.filters[0].filters[0].showValue =
                  item2.userLabel.filters[0].filters[0].showValue || item2.userLabel.filters[0].filters[0].value;
                item2.userLabel.filters[0].filters[0].value = filterArrayRes;
              } else {
                const result = _itemValue && !_.isArray(_itemValue) && _itemValue.toString().match(/\[(.+?)\]/g);

                item2.userLabel.filters[0].filters[0].showValue =
                  item2.userLabel.filters[0].filters[0].showValue || item2.userLabel.filters[0].filters[0].value;
                item2.userLabel.filters[0].filters[0].value = result
                  ? RegExp.$1
                  : item2.userLabel.filters[0].filters[0].value;
              }
            }
          });
        }
      });

      const findScenarioId = scenarioList.find((item) => item.id === scenarioId);
      const _dimensionGroup = _.cloneDeep(state.dimensionGroup);

      const format = {
        USER_PROPERTIES: 'eventFilterProperty', // 事件
        USER_LABEL: 'userLabel', // 用户标签
        SEGMENT: 'segment', // 用户分群
        TABLE_FIELD: 'userProperty', // 表字段
        CAMPAIGN: 'campaign' // 活动
      };
      _dimensionGroup.map((item) => {
        const { type, filterValue } = item;
        if (type === 'USER_PROPERTIES') {
          const arr = [];
          filterValue.forEach((i) => {
            const _filters = _.cloneDeep(item.filters[0]);
            _filters.groupName = i;
            _filters[format[type]].filters[0].value = i;
            arr.push(_filters);
          });
          item.filters = arr;
        } else if (type === 'USER_LABEL' || type === 'TABLE_FIELD') {
          const arr = [];
          filterValue.forEach((i) => {
            const _filters = _.cloneDeep(item.filters[0]);
            _filters.groupName = i;
            _filters[format[type]].filters[0].filters[0].value = i;
            _filters[format[type]].filters[0].filters[0].showValue =
              item.showValue || _filters[format[type]].filters[0].filters[0].showValue;
            arr.push(_filters);
          });
          item.filters = arr;
        }
        return item;
      });

      const _globalFilters = _.cloneDeep(state.globalFilters);

      _globalFilters.forEach((item) => {
        const keyList = Object.keys(item);
        const key = keyList.find((keyItem) => keyItem === 'userLabel');
        if (key === 'userLabel') {
          const _itemValue = _.cloneDeep(item.userLabel.filters[0].filters[0].value);

          if (_.isArray(_itemValue) && typeof _itemValue[0] === 'string') {
            const filterArrayRes = _itemValue.map((item2) => {
              const itemRes = item2.match(/\[(.+?)\]/g);
              item2 = itemRes ? RegExp.$1 : item2;
              return item2;
            });
            item.userLabel.filters[0].filters[0].showValue =
              item.userLabel.filters[0].filters[0].showValue || item.userLabel.filters[0].filters[0].value;
            item.userLabel.filters[0].filters[0].value = filterArrayRes;
          } else {
            const result = _itemValue && !_.isArray(_itemValue) && _itemValue.toString().match(/\[(.+?)\]/g);

            item.userLabel.filters[0].filters[0].showValue =
              item.userLabel.filters[0].filters[0].showValue || item.userLabel.filters[0].filters[0].value;
            item.userLabel.filters[0].filters[0].value = result
              ? RegExp.$1
              : item.userLabel.filters[0].filters[0].value;
          }
        }
      });

      let data = {
        name,
        extendChartType: boardType || localStorage.getItem('isActivityAnalysis') ? 'CREATE_CAMPAIGN_CHART' : undefined,
        chartConfig: {
          ...chartConfig,
          funnelDataQuery: {
            ...chartConfig.funnelDataQuery,
            stepList: _stepList,
            timeConfig: {
              timeTerm: state.timeTerm
            },
            globalFilters: _globalFilters,
            dimensionGroup: _dimensionGroup,
            chartDisplayType: null,
            displayGroupNames: state.selectedRowKeys,
            uniqueId: state.uuid
          }
        },
        scenario: findScenarioId,
        dateRange2: dateRange2.map((item) => {
          return {
            ...item,
            truncateAsDay: true
          };
        }),
        chartType: 'NEW_FUNNEL',
        projectId: localStorage.getItem('projectId'),
        id: !_.isNil(id) ? id : null,
        displayType: state.displayType,
        deptId: state.defaultFunnel?.deptId || window.getDeptId(),
        ...(forceCalc ? { argChange: true } : {}),
        isBusinessTable: 0
      };

      if (!data.chartConfig.campaignFilters && campaignId) {
        data.chartConfig = {
          ...data.chartConfig,
          campaignFilters: campaignList
        };
      }

      if (forceCalc) {
        // 如果手动计算就把table表格选项清空
        data = {
          ...data,
          ...obj
        };
        data.chartConfig.funnelDataQuery.displayGroupNames = [];
        await doRequest(_.cloneDeep(data), 'TABLE');
        const tableColumn = await FunnelAnalysis.queryFunnelTableColumn(data.chartConfig.funnelDataQuery);
        dispatch({ tableColumn: tableColumn.columns });
      } else {
        data = {
          ...data,
          ...childValue,
          // todo 是否业务表 多维分析用
          isBusinessTable: 0,
          id: type === 'updateChart' ? id : null,
          deptId: type === 'updateChart' ? data?.deptId : window.getDeptId()
        };

        if (boardType === 'flowCanvas') {
          if (type === 'updateChart') {
            if (scenarioId !== chartConfigData.scenario.id) {
              throw new Error(t('analysisCenter-VTCAsROjiXlQ'));
            }
          } else {
            if (scenarioId !== Number(scenario)) {
              throw new Error(t('analysisCenter-VTCAsROjiXlQ'));
            }
          }
        }

        const result = await AnalysisCenterService.saveChartConfig(data);
        if (campaignId) {
          if (_.isNil(id)) {
            const selectValue = await campaignV2Service.campaignV2ChartBoardListBy([
              {
                operator: 'EQ',
                propertyName: 'campaignId',
                value: campaignId
              }
            ]);
            const widgets = selectValue.length ? selectValue[0].widgets : [];
            // const version = props.location.state.version;

            const _widgets = _.cloneDeep(widgets);

            _widgets.push({
              x: (_widgets.length * 4) % 12,
              y: widgets[widgets?.length - 1]?.y + 100 || 0,
              w: 4,
              h: 3,
              isResizable: true,
              i: `${result.id}`
            });

            await campaignV2Service.saveCampaignV2ChartBoard({
              widgets: _widgets,
              campaignId,
              version: Number(version),
              type: boardType === 'campaigns' ? 'CAMPAIGNS' : 'FLOW_CANVAS',
              id: Number(saveId)
            });
          }

          messageDom(type, data.chartBoards, result, campaignId, boardDetail);
        } else {
          messageDom(type, data.chartBoards, result);
        }
        saveRecordInfo(result.id, userId, result);
        // props.history.push('/aimarketer/home/<USER>/database');
      }
    } catch (err) {
      err.message && message.error(err.message);
      dispatch({
        chartLoading: false,
        tableLoading: false
      });
    }
  };

  const { refresh } = useRequest(() => init(), {
    manual: true
  });

  const messageDom = async (type, chartBoards, result, state, detailObj) => {
    if (parseInt(result?.id) === parseInt(id)) {
      dispatch({ name: result.name });
    }
    dispatch({
      chartLoading: false,
      tableLoading: false,
      visibleDrawer: false
    });

    if (type === 'updateChart') {
      dispatch({ updateChart: false });
      init();
      if (state) {
        if (boardType === 'campaigns') {
          message.success(
            <div style={{ display: 'inline-block' }}>
              <span>【{result.name}】</span>
              {t('analysisCenter-uVGLqdB6FXuO')}
              <span
                style={{
                  color: 'var(--ant-primary-color)',
                  cursor: 'pointer'
                }}
                onClick={() => props.history.push(`/aimarketer/home/<USER>/detail/${campaignId}`)}
              >
                【{detailObj.name}】
              </span>
              {t('analysisCenter-vwcoQEj93bfI')}
            </div>
          );
        } else {
          message.success(
            <div style={{ display: 'inline-block' }}>
              <span>【{result.name}】</span>
              {t('analysisCenter-uVGLqdB6FXuO')}
              <span
                style={{
                  color: 'var(--ant-primary-color)',
                  cursor: 'pointer'
                }}
                onClick={() => props.history.push(`/aimarketer/home/<USER>/detail?id=${campaignId}`)}
              >
                【{detailObj.name}】
              </span>
              {t('analysisCenter-tfKyeg9IiqkL')}
            </div>
          );
        }
      } else {
        return message.success(`【${result.name}】${t('analysisCenter-Mnx8YtS6khBU')}`);
      }
    } else {
      if (!_.isEmpty(chartBoards)) {
        message.success(
          <div style={{ display: 'inline-block' }}>
            【{result.name}】{t('analysisCenter-gMjC9Z3nSQNa')}
            {result.chartBoards.map((item) => {
              return (
                <a onClick={() => window.open(`/aimarketer/home/<USER>/dataDashboard/${item.id}`)}>
                  【{item.boardName}】
                </a>
              );
            })}
            {t('analysisCenter-PNk75CxEhPZJ')}
          </div>
        );
        if (!id) props.history.push(`/aimarketer/home/<USER>/analysisCenter/database/funnelAnalysis/${result.id}`);
        dispatch({ updateChart: false });
        refresh();
      } else {
        if (state) {
          if (boardType === 'campaigns') {
            message.success(
              <div style={{ display: 'inline-block' }}>
                <span>【{result.name}】</span>
                {t('analysisCenter-MP81zbyEytyV')}
                <span
                  style={{
                    color: 'var(--ant-primary-color)',
                    cursor: 'pointer'
                  }}
                  onClick={() => props.history.push(`/aimarketer/home/<USER>/detail/${campaignId}`)}
                >
                  【{detailObj.name}】
                </span>
                {t('analysisCenter-vwcoQEj93bfI')}
              </div>
            );
          } else {
            message.success(
              <div style={{ display: 'inline-block' }}>
                <span>【{result.name}】</span>
                {t('analysisCenter-MP81zbyEytyV')}
                <span
                  style={{
                    color: 'var(--ant-primary-color)',
                    cursor: 'pointer'
                  }}
                  onClick={() => props.history.push(`/aimarketer/home/<USER>/detail?id=${campaignId}`)}
                >
                  【{detailObj.name}】
                </span>
                {t('analysisCenter-tfKyeg9IiqkL')}
              </div>
            );
          }
        } else {
          message.success(`【${result.name}】${t('analysisCenter-7iaxqLsTXX2u')}`);
        }
        if (id) {
          init(parseInt(result?.id) === parseInt(id) ? result : null);
          dispatch({ updateChart: false });
          return false;
        } else {
          if (state) {
            localStorage.setItem('isActivityAnalysis', true);
            props.history.push(
              `/aimarketer/home/<USER>/analysisCenter/database/funnelAnalysis/${result.id}?campaignId=${campaignId}&boardType=${boardType}&scenarioId=${scenario}&version=${version}&saveId=${saveId}`
            );
          } else {
            props.history.push(`/aimarketer/home/<USER>/analysisCenter/database/funnelAnalysis/${result.id}`);
          }

          init();
          // dispatch({ name: result.name });
          refresh();
        }
      }
    }

    dispatch({ updateChart: false });
  };

  /**
   * @description: 异步计算接口
   * @param {*} data 需要深克隆data
   * @param {*} isTable 是否是表格
   * @return {*}
   */
  const doRequest = async (data, type, calcTime) => {
    try {
      if (type) data.chartConfig.funnelDataQuery.queryType = type;
      if (calcTime) data.dateRange2 = calcTime;
      const res = await FunnelAnalysis.calcFunnelChartResult(data);

      if (!res) {
        dispatch({
          chartLoading: false,
          tableLoading: false
        });
      } else if (res.header.code === 0) {
        // debugger;
        if (!_.isEmpty(res.body.chartResult)) {
          dispatch({ chartResult: res.body.chartResult });
        }

        res.body.chartResult.tableList.map((item, index) => {
          return (item.key = index);
        });

        if (type === 'TABLE') {
          const _selectedRowKeys = data.chartConfig.funnelDataQuery?.displayGroupNames || [];
          if (_.isEmpty(_selectedRowKeys)) {
            res.body.chartResult.tableList.forEach((item, index) => {
              if (index < 6) {
                _selectedRowKeys.push(item.name);
              }
            });
          }
          dispatch({
            chartList: res.body.chartResult.chartList,
            tableList: res.body.chartResult.tableList,
            lineChartList: res.body.chartResult.lineChartList,
            tableLoading: false,
            chartLoading: false,
            selectedRowKeys: _selectedRowKeys,
            offSave: !!state.chartLoading,
            defaultFunnel: data
          });
        }
        // if (res.body.chartResult) {
        //   dispatch({ loading: false });
        // }
      } else if (res.header.code === 210) {
        // dispatch({loading: false});
        timer.current = setTimeout(async () => {
          data.forceCalc = false;
          await doRequest(data, type);
        }, 3000);
      } else if (res.header.code === 1) {
        if (res.header.message) {
          message.info(res.header.message);
        }

        dispatch({
          chartLoading: false,
          tableLoading: false
        });
      }
    } catch (error) {
      // responseData[selectedChart] = {rowList: [], columnList: []};
      dispatch({
        chartLoading: false,
        tableLoading: false
      });
    }
  };

  const inputRef = useRef();

  const onBlurName = () => {
    if (inputRef.current.input.value === '') {
      const name = t('analysisCenter-EqCBceR36Alg');
      dispatch({ name });
    }
  };

  const setChartType = (type) => {
    dispatch({ chartDisplayType: type });
  };

  const clickIcon = () => {
    setFilterConfig({});
    init();
  };

  const clickOpen = (flag) => {
    dispatch({ open: flag });
  };

  return (
    <div className="funnelAnalysis">
      <Spin spinning={false}>
        <header>
          <div className="left">
            <span className="titleBack" onClick={exitQuit}>
              <MyIconV2 type="icon-arrowleft" style={{ fontSize: 20 }} />
            </span>
            <div className="leftHandleWrapper">
              <Input bordered={false} value={state.name} onChange={changeName} ref={inputRef} onBlur={onBlurName} />
            </div>
          </div>
          <div className="right">
            <Space className="btnGroup">
              <Button
                type="primary"
                loading={state.chartLoading || state.tableLoading}
                onClick={() => saveFunnel({ forceCalc: true })}
              >
                {t('analysisCenter-9xAxuXFg0LOD')}
              </Button>
              {boardType || localStorage.getItem('isActivityAnalysis') ? (
                <Button
                  onClick={() => (!_.isNil(id) ? dispatch({ updateChart: true }) : dispatch({ visibleDrawer: true }))}
                  disabled={
                    state.offSave || state.chartLoading || (state.tableLoading && _.isEmpty(state.selectedRowKeys))
                  }
                  // loading={state.initLoading}
                  buttonsRender={([leftButton, rightButton]) => [
                    React.cloneElement(leftButton, {
                      loading: state.initLoading,
                      key: '11'
                    }),
                    React.cloneElement(rightButton, {
                      loading: state.initLoading,
                      key: '22'
                    })
                  ]}
                  type="primary"
                >
                  {!_.isNil(id) ? t('analysisCenter-vuwOuTgQKs76') : t('analysisCenter-Vbwz0JapQNuP')}
                </Button>
              ) : (
                <Dropdown.Button
                  icon={<DownOutlined />}
                  onClick={() => (!_.isNil(id) ? dispatch({ updateChart: true }) : dispatch({ visibleDrawer: true }))}
                  disabled={
                    state.offSave || state.chartLoading || (state.tableLoading && _.isEmpty(state.selectedRowKeys))
                  }
                  // loading={state.initLoading}
                  buttonsRender={([leftButton, rightButton]) => [
                    React.cloneElement(leftButton, {
                      loading: state.initLoading,
                      key: '11'
                    }),
                    React.cloneElement(rightButton, {
                      loading: state.initLoading,
                      key: '22'
                    })
                  ]}
                  type="primary"
                  menu={{
                    items: [
                      {
                        key: '1',
                        label: t('analysisCenter-x0eV46ljTcsc'),
                        onClick: () => dispatch({ visibleDrawer: true })
                      }
                    ]
                  }}
                >
                  {!_.isNil(id) ? t('analysisCenter-vuwOuTgQKs76') : t('analysisCenter-Vbwz0JapQNuP')}
                </Dropdown.Button>
              )}
            </Space>
          </div>
        </header>
        <funnelAnalysisContext.Provider value={{ state, dispatch, scenarioList }}>
          <div className="content">
            <div className="left" style={{ display: !state.open && 'none' }}>
              <div className="resize-bar" />
              <div className="resize-line" />
              <div className="resize-save">
                <div className="screen">
                  <div>
                    {t('analysisCenter-Oe60fRFjzAcK')}
                    <Select
                      style={{ minWidth: '100px' }}
                      value={state.scenarioId}
                      bordered={false}
                      placeholder={t('analysisCenter-9FDvdRqacWIC')}
                      onChange={changeScenario}
                    >
                      {state.scenarioList.map((n) => (
                        <Option key={n.id} value={n.id}>
                          {n.name}
                        </Option>
                      ))}
                    </Select>
                  </div>
                  <div>
                    {t('analysisCenter-ei2GatJVWK21')}
                    <Popover
                      getPopupContainer={() => document.getElementsByClassName('screen')[0]}
                      content={renderConversion()}
                      title={t('analysisCenter-EcWcsflYm3w1')}
                      trigger="click"
                      overlayStyle={{ width: '320px' }}
                      placement="bottomRight"
                      className="conversion"
                      open={state.visibleConversion}
                      onOpenChange={handleVisibleChange}
                    >
                      <span>
                        {state.chartConfig.funnelDataQuery.conversion.amount}{' '}
                        {adc[state.chartConfig.funnelDataQuery.conversion.stepTerm]}
                      </span>
                      <span className="svg">
                        <svg
                          viewBox="64 64 896 896"
                          focusable="false"
                          data-icon="down"
                          width="1em"
                          height="1em"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z" />
                        </svg>
                      </span>
                    </Popover>
                  </div>
                  <div className="overflow-hidden whitespace-nowrap text-ellipsis">
                    {t('analysisCenter-Qzntk93gmvhm')}
                    <Tooltip title={deptPath} placement="topLeft">
                      <span>{deptPath}</span>
                    </Tooltip>
                  </div>
                </div>
                <div className="process">
                  <div className="setUpSteps">
                    <div className="title">{t('analysisCenter-6X0px854f9HH')}</div>
                    <Steps />
                  </div>
                  <div className="setGlobalFilter">
                    <div className="title">{t('analysisCenter-CqzeY8dA0buK')}</div>
                    <GlobalFilter />
                  </div>
                  <div className="setDimension">
                    <div className="title">{t('analysisCenter-OJbv21CO30VJ')}</div>
                    <Dimension />
                  </div>
                  {campaignId && (
                    <div>
                      <div className="title">{t('analysisCenter-kdm5L30vZo0Y')}</div>
                      <Tree
                        treeData={campaignTreeData}
                        selectable={false}
                        className={boardType === 'campaigns' ? '' : 'treeNoopNone'}
                      />
                    </div>
                  )}
                </div>
              </div>
              <div className="open" onClick={() => clickOpen(false)}>
                <MyIconV2 type="icon-icon-page" />
              </div>
            </div>
            <div className="right">
              {!state.open && (
                <div className="open" onClick={() => clickOpen(true)}>
                  <MyIconV2 type="icon-icon-page" />
                </div>
              )}
              <div className="rightScreen" style={{ paddingLeft: !state.open && '80px' }}>
                <SelectTime
                  data={state.dateRange2}
                  showTime
                  isAnalysis
                  style={{
                    maxWidth: 'auto'
                  }}
                  onChange={change}
                />
                <Select value={state.timeTerm} onChange={(e) => dispatch({ timeTerm: e })}>
                  <Option value="DAY">{t('analysisCenter-GtMd2t6tpe7Y')}</Option>
                  <Option value="HOUR">{t('analysisCenter-ESuuBkjMkwoM')}</Option>
                  <Option value="MONTH">{t('analysisCenter-EPTBLKpoylNS')}</Option>
                </Select>
                {/* <Select defaultValue="contrast">
                    <Option value="contrast">对比</Option>
                  </Select> */}
                <div className="chartType">
                  <Button
                    type={state.chartDisplayType === 'COLUMN_CHART' && 'primary'}
                    onClick={() => setChartType('COLUMN_CHART')}
                  >
                    {t('analysisCenter-gkpxwMqCnF56')}
                  </Button>
                  <Button
                    type={state.chartDisplayType === 'PERCENT_COLUMN_CHART' && 'primary'}
                    onClick={() => setChartType('PERCENT_COLUMN_CHART')}
                  >
                    {t('analysisCenter-dnNrk6XYg4Df')}
                  </Button>
                  <Button
                    type={state.chartDisplayType === 'LINE_CHART' && 'primary'}
                    onClick={() => setChartType('LINE_CHART')}
                  >
                    {t('analysisCenter-F1KE6VSvSzYh')}
                  </Button>
                </div>
              </div>
              {!_.isEmpty(filterConfig) && (
                <div className="term">
                  <span className="title">{t('analysisCenter-EXFFQj1pY6V1')}</span>
                  <Card style={{ width: '348px', height: '32px' }}>
                    <p>
                      {dayjs(parseInt(filterConfig.startTime)).format('YYYY-MM-DD HH:mm:ss')} ~{' '}
                      {dayjs(parseInt(filterConfig.endTime)).format('YYYY-MM-DD HH:mm:ss')}{' '}
                      <CloseOutlined onClick={clickIcon} />
                    </p>
                  </Card>
                </div>
              )}

              {!_.isEmpty(state.tableList) ? (
                <>
                  <div className="chart">
                    <RenderChart chartList={state.chartList} loading={state.chartLoading} />
                    {/* <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无数据，请选择至少2个漏斗步骤"
                /> */}
                  </div>
                  <div style={{ padding: '0 80px' }}>
                    <Divider />
                  </div>
                  <div className="table1">
                    <RenderTable loading={state.tableLoading} />
                  </div>
                </>
              ) : (
                <div className="empty">
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                </div>
              )}
            </div>
          </div>
          {state.showSaveGroup && <SaveGroup />}
          <Drawer
            title={t('analysisCenter-Vbwz0JapQNuP')}
            placement="right"
            width={600}
            onClose={() => dispatch({ visibleDrawer: false })}
            destroyOnClose
            footer={
              <div className="buttomFooter">
                <Button onClick={() => dispatch({ visibleDrawer: false })}>{t('analysisCenter-8luMKTPWOOa3')}</Button>
                <Button type="primary" onClick={saveFunnel}>
                  {t('analysisCenter-4gjEhJ57WZQH')}
                </Button>
              </div>
            }
            open={state.visibleDrawer}
          >
            <SaveChart
              onRef={onRef}
              chartName={state.name}
              boardType={boardType}
              campaignId={campaignId}
              detailObj={boardDetail}
            />
          </Drawer>

          <Drawer
            open={state.updateChart}
            title={t('analysisCenter-vuwOuTgQKs76')}
            width={560}
            destroyOnClose
            onClose={() => dispatch({ updateChart: false })}
            onCancel={() => dispatch({ updateChart: false })}
            footer={
              <div className="buttomFooter">
                <Button onClick={() => dispatch({ updateChart: false })} key="1">
                  {t('analysisCenter-8luMKTPWOOa3')}
                </Button>
                <Button
                  type="primary"
                  key="3"
                  onClick={() => saveFunnel({ type: 'updateChart' })}
                  loading={state.chartLoading || state.tableLoading}
                >
                  {t('analysisCenter-vuwOuTgQKs76')}
                </Button>
              </div>
            }
          >
            <UpdateChart boardType={boardType} detailObj={boardDetail} />
          </Drawer>
        </funnelAnalysisContext.Provider>
      </Spin>
    </div>
  );
}
