import { Alert, Form, Input, Radio, TreeSelect, message } from 'antd';
import _ from 'lodash';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import { useHistory } from 'react-router-dom';
import DashboardService from 'service/dashboardService';
import { t } from 'utils/translation';

const layouts = [
  [{ x: 0, y: 1000, w: 4, h: 3, isResizable: true }],
  [
    { x: 0, y: 1000, w: 4, h: 3, isResizable: true },
    { x: 4, y: 1000, w: 4, h: 3, isResizable: true }
  ],
  [
    { x: 0, y: 1000, w: 4, h: 3, isResizable: true },
    { x: 4, y: 1000, w: 4, h: 3, isResizable: true },
    { x: 8, y: 1000, w: 4, h: 3, isResizable: true }
  ]
];

export default function SaveChart(props) {
  const { onRef, chartName, boardType, detailObj } = props;
  const history = useHistory();
  const [form] = Form.useForm();
  const [form2] = Form.useForm();
  const [addBoardType, setAddBoardType] = useState(t('analysisCenter-nIFVbSXbwqiY'));

  const [allChartBoard, setAllChartBoard] = useState([]); // 所有未分组的数据看板
  const [allChartBoardCategory, setAllChartBoardCategory] = useState([]); // 所有文件夹

  const [chartBoards, setChartBoards] = useState([]); // 选中的数据看板
  const [menuList, setMunuList] = useState([]); // 文件夹List
  const [boardList, setBoardList] = useState([]); // 数据看板List

  useEffect(() => {
    setBoardList([]);
    form2.resetFields();
  }, [addBoardType]);

  const saveCharts = async () => {
    const formValue = await form.validateFields();
    const formValue2 = await form2.validateFields();
    const { addBoard, chartName } = formValue;
    const { boardName, folder } = formValue2;
    let findBoard;
    if (addBoard === t('analysisCenter-WfRxTiSV5yYy')) {
      // 根据chartBoards里面的的id去filter allChartBoard
      findBoard = allChartBoard.filter((w) => chartBoards.includes(`${w.id}`));
      findBoard.map((item) => {
        if (_.isEmpty(item.layouts)) {
          item.layouts = { xxs: layouts[0] };
          item.widgets = layouts[0];
        } else {
          item.layouts.xxs = [...item.layouts.xxs, ...layouts[chartBoards.length - 1]];
          item.widgets = [...item.widgets, ...layouts[chartBoards.length - 1]];
        }
        return item;
      });
    } else if (addBoard === t('analysisCenter-1lTwonSAnTaZ')) {
      findBoard = [
        {
          boardName,
          category: allChartBoardCategory.find((w) => w.id === parseInt(folder.replace('folder.', ''))),
          layouts: { xxs: layouts[0] },
          widgets: layouts[0]
        }
      ];
    }
    const saveChartValue = {
      ...formValue,
      ...formValue2,
      chartBoards: findBoard,
      name: chartName
    };
    return _.omit(saveChartValue, ['addBoard', 'boardName', 'chartName']);
  };

  useImperativeHandle(onRef, () => ({
    saveCharts
  }));

  const onValuesChange = async (changedValues) => {
    if (changedValues?.addBoard) setAddBoardType(changedValues.addBoard);
    if (
      changedValues.addBoard === t('analysisCenter-WfRxTiSV5yYy') ||
      (changedValues.addBoard === t('analysisCenter-1lTwonSAnTaZ') && _.isEmpty(boardList))
    ) {
      const allCategoryList = await DashboardService.getAllchartBoardCategory([
        {
          operator: 'EQ',
          propertyName: 'deptId',
          value: window.getDeptId()
        }
      ]);
      const chartBoardList = await DashboardService.getAllchartBoard([
        {
          operator: 'EQ',
          propertyName: 'deptId',
          value: window.getDeptId()
        }
      ]);

      setAllChartBoard(chartBoardList);
      setAllChartBoardCategory(allCategoryList);
      const menuList = getLevelData(chartBoardList, allCategoryList, 0);
      const boardList = getLevelData(chartBoardList, allCategoryList, 0, true);
      setMunuList(menuList);
      setBoardList(boardList);
    }
  };

  const getLevelData = (chartBoardList, data, parentId, isBoard) => {
    const itemArr = [];
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      if (node.parentId === parentId) {
        const newNode = {};
        newNode.value = `folder.${node.id}`;
        if (isBoard) newNode.selectable = false;
        newNode.title = node.name;
        newNode.isLeaf = false;
        const res = getLevelData(chartBoardList, data, node.id, isBoard);
        if (res.length > 0) {
          newNode.children = res;
        }
        if (isBoard) {
          const list = chartBoardList.filter((w) => parseInt(node.id) === w.category?.id);
          if (list.length > 0) {
            if (newNode.children) {
              list.forEach((n) => {
                newNode.children.push({
                  value: `${n.id}`,
                  title: n.boardName,
                  isLeaf: true
                });
              });
            } else {
              newNode.children = list.map((n) => ({
                value: `${n.id}`,
                title: n.boardName,
                isLeaf: true
              }));
            }
          }
        }
        itemArr.push(newNode);
      }
    }
    return itemArr;
  };

  const changeOne = (selectKeys) => {
    if (selectKeys.length > 3) {
      setChartBoards(selectKeys.slice(0, 3));
      return message.error(t('analysisCenter-jUdGV1kyeNgr'));
    } else {
      setChartBoards(selectKeys);
    }
  };

  const renderAddBoard = () => {
    if (addBoardType === t('analysisCenter-nIFVbSXbwqiY')) return null;
    else if (addBoardType === t('analysisCenter-WfRxTiSV5yYy')) {
      return (
        <TreeSelect
          multiple
          style={{ width: '100%' }}
          value={chartBoards}
          onChange={changeOne}
          treeData={boardList}
          placeholder={t('analysisCenter-9FDvdRqacWIC')}
        />
      );
    } else if (addBoardType === t('analysisCenter-1lTwonSAnTaZ')) {
      return (
        <Form form={form2} layout="vertical">
          <Form.Item
            label={t('analysisCenter-9zjaGlXbBBUw')}
            name="boardName"
            rules={[
              { required: true, message: t('analysisCenter-WPxaFasPBazM') },
              { max: 20, message: t('analysisCenter-wl4g8CDWeja0') }
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={t('analysisCenter-BzqvlDiXn6vK')}
            name="folder"
            rules={[{ required: true, message: t('analysisCenter-4q6iFTCHe7uq') }]}
          >
            <TreeSelect
              // multiple
              style={{ width: '100%' }}
              // value={chartBoards}
              // onChange={onChange}
              treeData={menuList}
            />
          </Form.Item>
        </Form>
      );
    }
  };

  return (
    <div>
      <Alert
        message={boardType ? t('analysisCenter-rR10r5Qvnig4') : t('analysisCenter-S0ZZO6mmpTs5')}
        showIcon
        type="info"
        className="mb-[16px] "
      />
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          chartName,
          displayType: 'COLUMN_CHART',
          addBoard: t('analysisCenter-nIFVbSXbwqiY')
        }}
        onValuesChange={onValuesChange}
      >
        <Form.Item
          label={t('analysisCenter-WmuxAiliklUc')}
          name="chartName"
          rules={[
            { required: true, message: t('analysisCenter-VRzo6dHx2Tgt') },
            { max: 32, message: t('analysisCenter-w8K9n1fPybfV') }
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label={t('analysisCenter-cq7e9FxcueJP')}
          name="displayType"
          rules={[{ required: true, message: t('analysisCenter-qkq6tEzvikm9') }]}
        >
          <Radio.Group>
            <Radio value="COLUMN_CHART">{t('analysisCenter-cr6e5w4yuAjN')}</Radio>
            <Radio value="PERCENT_COLUMN_CHART">{t('analysisCenter-OJs6hHI4EzO8')}</Radio>
            <Radio value="LINE_CHART">{t('analysisCenter-yyKc2HlW0r4w')}</Radio>
          </Radio.Group>
        </Form.Item>

        {boardType ? (
          <div className="mt-[16px]">
            <div className="mb-[8px]">{t('analysisCenter-yM3PIFBwWatT')}</div>
            <div className="cursor-pointer">
              {boardType === 'campaigns' ? (
                <a
                  onClick={() => history.push(`/aimarketer/home/<USER>/detail/${detailObj.id}`)}
                >{`[${detailObj.id}]${detailObj.name}`}</a>
              ) : (
                <a
                  onClick={() => history.push(`/aimarketer/home/<USER>/detail?id=${detailObj.id}`)}
                >{`[${detailObj.id}]${detailObj.name}`}</a>
              )}
            </div>
          </div>
        ) : (
          <Form.Item
            label={t('analysisCenter-pvTx2GV38PiS')}
            name="addBoard"
            rules={[{ required: true, message: t('analysisCenter-9FDvdRqacWIC') }]}
          >
            <Radio.Group>
              <Radio value={t('analysisCenter-nIFVbSXbwqiY')}>{t('analysisCenter-nIFVbSXbwqiY')}</Radio>
              <Radio value={t('analysisCenter-WfRxTiSV5yYy')}>{t('analysisCenter-WfRxTiSV5yYy')}</Radio>
              <Radio value={t('analysisCenter-1lTwonSAnTaZ')}>{t('analysisCenter-1lTwonSAnTaZ')}</Radio>
            </Radio.Group>
          </Form.Item>
        )}
      </Form>
      {renderAddBoard()}
    </div>
  );
}
