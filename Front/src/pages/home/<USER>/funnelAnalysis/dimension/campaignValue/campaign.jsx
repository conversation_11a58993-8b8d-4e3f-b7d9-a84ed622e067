import { Button, Input, Table, Tabs, Typography, message } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useContext, useEffect, useRef, useState } from 'react';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { useDebounce } from 'utils/customhooks';
import './campaign.scss';
// import icon from '../icon';
import { t } from 'utils/translation';
import { funnelAnalysisContext } from '../../funnelAnalysisContext';
import RenderBatch from './renderBatch';

const { Search } = Input;
const { Text } = Typography;

const phaseList = [
  {
    name: t('analysisCenter-41dztK7uOl3H'),
    text: t('analysisCenter-41dztK7uOl3H'),
    value: 'DRAFT',
    key: 'DRAFT',
    color: 'rgba(0, 0, 0, 0)'
  },
  {
    name: t('analysisCenter-f30ZGbRl3aA5'),
    text: t('analysisCenter-f30ZGbRl3aA5'),
    value: 'TESTING',
    key: 'TESTING',
    color: '#52c41a'
  },
  {
    name: t('analysisCenter-i56DNjAaAQGb'),
    text: t('analysisCenter-i56DNjAaAQGb'),
    value: 'TEST_SUC',
    key: 'TEST_SUC',
    color: '#1890ff'
  },
  {
    name: t('analysisCenter-uhVEPYwcWwFM'),
    text: t('analysisCenter-uhVEPYwcWwFM'),
    value: 'ENABLE',
    key: 'ENABLE',
    color: '#f5222d'
  },
  {
    name: t('analysisCenter-cbgOTX9PLjcB'),
    text: t('analysisCenter-cbgOTX9PLjcB'),
    value: 'STOPPED',
    key: 'STOPPED',
    color: '#faad14'
  }
];

/**
 * @description: 维度分组-营销活动
 * @param {*} type 营销活动选择的类型
 * @param {*} setVisible 关闭弹窗
 * @return {*}
 */
export default function RenderCampaign({ type, setVisible, setValueVisible }) {
  const { state, dispatch, dimensionIndex } = useContext(funnelAnalysisContext);
  const { scenarioId, dimensionGroup, scenarioList } = state;
  const [tabsKey, setTabsKey] = useState(0);
  const [activeKey, setActiveKey] = useState(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [loading, setLoading] = useState(false);
  const [campaignTable, setCampaignTable] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [editDimensionGroup, setEditDimensionGroup] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    search: [
      {
        connector: 'AND',
        propertyName: 'phase',
        operator: 'IN',
        value: 'ENABLE,STOPPED'
      },
      {
        connector: 'AND',
        propertyName: 'scenario.code',
        operator: 'EQ',
        value: (scenarioList && scenarioList.find((i) => i.id === scenarioId)?.code) || ''
      },
      {
        operator: 'EQ',
        propertyName: 'deptId',
        value: window.getDeptId()
      }
    ],
    size: 10,
    sorts: [{ direction: 'desc', propertyName: 'id' }]
  });
  const columns = [
    {
      title: t('analysisCenter-qbP1YiocCfQE'),
      dataIndex: 'name',
      render: (text) => {
        return (
          <Text style={{ width: '170px' }} ellipsis={{ tooltip: text }}>
            {text}
          </Text>
        );
      },
      width: 170,
      height: 100
    },
    {
      title: t('analysisCenter-L5SwcOwVhvRN'),
      dataIndex: 'phase',
      render: (text) => {
        return <div className="status">{text ? _.filter(phaseList, (v) => v.value === text)[0]?.name : '-'}</div>;
      },
      width: 120
    },
    {
      title: t('analysisCenter-EqwWjbwWjZ0J'),
      dataIndex: 'beginTime',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 200
    },
    {
      title: t('analysisCenter-5EiOi8gAizcT'),
      dataIndex: 'endTime',
      render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
      width: 200
    }
  ];

  useEffect(() => {
    const ini = async () => {
      const _dimensionGroup = _.cloneDeep(dimensionGroup);
      const campaignList = _dimensionGroup[dimensionIndex].filters[0].campaignGroup.filters;

      // 回显
      if (campaignList) {
        const defaultSelectedRowKeys = [];
        setActiveKey(`${campaignList[0]?.id}`);
        _.forEach(campaignList, (v) => defaultSelectedRowKeys.push(v.id));
        setSelectedRowKeys(defaultSelectedRowKeys);
        setEditDimensionGroup(campaignList);
      }
      setLoading(true);
    };
    ini();
    return () => {
      localStorage.removeItem('popOverPage');
    };
  }, []);

  useEffect(() => {
    init();
  }, [pagination]);

  const init = async () => {
    setLoading(true);
    const campaignTable = await FunnelAnalysis.query2(pagination);
    setCampaignTable(campaignTable.content);
    setTotalCount(campaignTable.totalElements);
    setLoading(false);
  };

  const handleTableChange = (page) => {
    setPagination({
      ...pagination,
      page: page.current,
      size: page.pageSize
    });
  };

  const selectTable = (rowValue, isTrue) => {
    const _selectedRowKeys = _.cloneDeep(selectedRowKeys);
    let campaignList = editDimensionGroup;
    if (isTrue) {
      campaignList.push(rowValue);
      setEditDimensionGroup(campaignList);
      setSelectedRowKeys(_.concat(_selectedRowKeys, rowValue.id));
      setActiveKey(`${rowValue.id}`);
    } else {
      campaignList = _.filter(campaignList, (v) => v.id !== rowValue.id);
      setEditDimensionGroup(campaignList);
      const newSelectRowKeys = _.without(_selectedRowKeys, rowValue.id);
      setSelectedRowKeys(newSelectRowKeys);
      setActiveKey(`${newSelectRowKeys[newSelectRowKeys.length - 1]}`);
    }
  };

  const changeTabs = (e) => {
    localStorage.setItem('popOverPage', '1');
    setActiveKey(e);
  };

  const saveSteps = () => {
    try {
      if (_.isEmpty(editDimensionGroup)) throw new Error(t('analysisCenter-TBNRz4qWA8De'));
      _.forEach(editDimensionGroup, (i) => {
        if (_.isEmpty(i.logList)) throw new Error(t('analysisCenter-2nnZVR3OMYbQ'));
        if (type === 'CAMPAIGN_NODE')
          if (_.isEmpty(i.logList[0].selectedFlows)) throw new Error(t('analysisCenter-GJHiVywuNN5q'));
      });
      const _dimensionGroup = _.cloneDeep(dimensionGroup);
      _dimensionGroup[dimensionIndex].filters[0].campaignGroup.filters = editDimensionGroup;
      dispatch({ dimensionGroup: _dimensionGroup });
      setVisible && setVisible(false);
      setValueVisible && setValueVisible(false);
    } catch (err) {
      message.error(err.message);
    }
  };

  const searchRef = useRef();

  const changeSearchName = useDebounce(() => {
    const getValue = async () => {
      let pageSearchName = _.cloneDeep(pagination);
      const config = {
        connector: 'AND',
        propertyName: 'name',
        operator: 'LIKE',
        value: searchRef.current.input.value
      };
      const searchConfig = pageSearchName.search;
      if (searchConfig.length === 3) {
        searchConfig.push(config);
      } else {
        searchConfig[searchConfig.length - 1] = config;
      }
      pageSearchName = {
        ...pageSearchName,
        page: 1,
        search: searchConfig
      };
      setPagination(pageSearchName);
    };
    getValue();
  }, 400);

  return (
    <div className="renderCampaign">
      <funnelAnalysisContext.Provider value={{ state, dispatch, editDimensionGroup, setEditDimensionGroup }}>
        <div className="campaignTableBox">
          <div className="table1">
            <Search
              placeholder={t('analysisCenter-3UI7RaTBKXy7')}
              allowClear
              onChange={changeSearchName}
              ref={searchRef}
            />
            <Table
              size="middle"
              columns={columns}
              dataSource={campaignTable || []}
              bordered={false}
              rowKey={(record) => record.id}
              loading={loading}
              rowSelection={{
                selectedRowKeys,
                // onChange: onSelectChange,
                onSelect: selectTable,
                hideSelectAll: true
              }}
              onChange={handleTableChange}
              pagination={{
                current: pagination.page,
                total: totalCount,
                defaultPageSize: 10,
                showQuickJumper: true,
                showSizeChanger: true,
                showLessItems: true,
                pageSizeOptions: ['10', '20', '50'],
                showTotal: (e) => `${t('analysisCenter-qdrIyuzqnle9')} ${e} ${t('analysisCenter-WFR2um5duftk')}`
              }}
            />
          </div>
          {!_.isEmpty(selectedRowKeys) && (
            <div className="batch">
              <div className="batchTitle">
                {type === 'CAMPAIGN_BATCH' ? t('analysisCenter-jTvZ60qPk7Vw') : t('analysisCenter-1TejwN5m33iK')}
              </div>
              <div className="batchContent">
                <Tabs
                  key={tabsKey}
                  hideAdd
                  // destroyInactiveTabPane
                  activeKey={activeKey}
                  onChange={changeTabs}
                  items={[
                    ...editDimensionGroup.map((item) => {
                      return {
                        label: item.name,
                        key: `${item.id}`,
                        children: (
                          <RenderBatch
                            setTabsKey={setTabsKey}
                            batchValue={item}
                            editDimensionGroup={editDimensionGroup}
                            type={type}
                          />
                        )
                      };
                    })
                  ]}
                />
              </div>
            </div>
          )}
        </div>
      </funnelAnalysisContext.Provider>
      <div className="buttons">
        {/* <Button>取消</Button> */}
        <Button type="primary" onClick={saveSteps}>
          {t('analysisCenter-kR4hfc3j6gJ2')}
        </Button>
      </div>
    </div>
  );
}
