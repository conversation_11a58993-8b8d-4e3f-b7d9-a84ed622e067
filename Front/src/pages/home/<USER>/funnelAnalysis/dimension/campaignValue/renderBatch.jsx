import { Button, Popover, Select, Table, message } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import CampaignV2Service from 'service/CampaignV2Service';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { t } from 'utils/translation';
import { FlowCanvas } from 'wolf-static-cpnt';
import { funnelAnalysisContext } from '../../funnelAnalysisContext';
import icon from '../../icon';

const campaignV2Service = new CampaignV2Service();
const { Option } = Select;

// eslint-disable-next-line no-unused-vars
const RenderBatch = ({ batchValue, setTabsKey, type }) => {
  const contextValue = useContext(funnelAnalysisContext);
  const { editDimensionGroup, setEditDimensionGroup } = contextValue;
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [batchList, setBatchList] = useState([]);
  const [findBatchIndex, setFindBatchIndex] = useState(null);
  const pageConfig = {
    search: [
      {
        propertyName: 'campaignId',
        operator: 'EQ',
        value: batchValue.id
      },
      {
        connector: 'AND',
        propertyName: 'faked',
        operator: 'EQ',
        value: 'false'
      }
    ],
    size: 1000,
    sorts: [{ direction: 'desc', propertyName: 'id' }]
  };
  const columns = [
    {
      title: t('analysisCenter-BNE7m4tBswbI'),
      dataIndex: 'id',
      width: 170,
      height: 100
    },
    {
      title: t('analysisCenter-kF2q5uftCsLk'),
      dataIndex: 'joinCount',
      width: 120
    },
    {
      title: t('analysisCenter-7ogVX8cNknH7'),
      dataIndex: 'text',
      width: 200,
      render: (text, record) => {
        const _logList = editDimensionGroup[findBatchIndex].logList;
        const findValue = _.find(_logList, (item) => item.id === record.id);
        let isShow = false;
        const findValueArr = [];
        if (!_.isEmpty(findValue?.selectedFlows)) {
          isShow = true;
          _.forEach(findValue?.selectedFlows, (item) => {
            findValueArr.push(item.name);
          });
        }
        return (
          // 如果过滤选择了批次
          type === 'CAMPAIGN_BATCH' ? null : (
            <Popover
              // key={Number}
              getPopupContainer={() => document.getElementsByClassName('batch')[0]}
              content={<RenderFlows id={record.id} />}
              title={t('analysisCenter-1ZKWG0jhMDaY')}
              trigger="click"
              destroyTooltipOnHide
              // overlayStyle={{ width: '300px', height: '300px', overflowY: 'auto' }}
              className="sketchcolorcom"
              open={record?.visible}
              onOpenChange={(e) => handleVisibleChange(e, record.id)}
            >
              {!isShow ? (
                <Select
                  mode="multiple"
                  maxTagCount={1}
                  style={{ width: '100%' }}
                  placeholder={t('analysisCenter-e8b7zG8AoIkl')}
                  dropdownRender={null}
                  dropdownStyle={{ display: 'none' }}
                />
              ) : (
                <Select
                  style={{ width: '100%' }}
                  mode="multiple"
                  maxTagCount={1}
                  placeholder={t('analysisCenter-e8b7zG8AoIkl')}
                  // disabled
                  dropdownRender={null}
                  dropdownStyle={{ display: 'none' }}
                  defaultValue={findValueArr}
                >
                  {findValueArr.map((item, index) => {
                    return (
                      <Option disabled key={index} value={item}>
                        {item}
                      </Option>
                    );
                  })}
                </Select>
              )}
              {/* <Input maxLength={7} readOnly placeholder={record.id} /> */}
            </Popover>
          )
        );
      }
    },
    {
      title: t('analysisCenter-0FgVY0juYe9j'),
      dataIndex: 'beginTime',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 200
    },
    {
      title: t('analysisCenter-DFxnT7lSEhXZ'),
      dataIndex: 'endTime',
      render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
      width: 200
    }
  ];

  const handleVisibleChange = (e, recordId) => {
    const _batchList = _.cloneDeep(batchList);
    const findIndex = _batchList.findIndex((item) => item.id === recordId);
    _batchList[findIndex].visible = e;
    setBatchList(_batchList);
  };

  const [selectedNodes, setSelectedNodes] = useState([]);

  // 回显每个批次流程节点
  useEffect(() => {
    const _logList = editDimensionGroup[findBatchIndex]?.logList;
    if (!_.isEmpty(_logList)) {
      const findLogListIndex = _.findIndex(_logList, (item) => item.id === 1);
      const _selectedNodes = _logList[findLogListIndex]?.selectedFlows || [];
      setSelectedNodes(_selectedNodes);
    }
  }, [batchList]);

  const RenderFlows = ({ id }) => {
    const onFlowCanvasClick = (data) => {
      if (selectedNodes.length >= 10) {
        message.warn(t('analysisCenter-8woFWEyYQwD6'), 1);
        return;
      }
      if (!_.find(selectedNodes, (item) => item.nodeId === data.nodeId)) {
        const _selectedNodes = _.cloneDeep(selectedNodes);
        _selectedNodes.push(data);
        setSelectedNodes(_selectedNodes);
      }
    };

    const delNode = (id) => {
      let _selectedNodes = _.cloneDeep(selectedNodes);
      _selectedNodes = _selectedNodes.filter((item) => item.nodeId !== id);
      setSelectedNodes(_selectedNodes);
    };

    /**
     * @description: 保存节点
     * @return {*}
     */
    const saveBatchId = () => {
      const _globalFilters = _.cloneDeep(editDimensionGroup);
      const _selectedNodes = _.cloneDeep(selectedNodes);
      const _logList = _globalFilters[findBatchIndex].logList;
      if (!_.isEmpty(_logList)) {
        const findLogListIndex = _.findIndex(_logList, (item) => item.id === id);
        if (findLogListIndex !== -1) {
          _logList[findLogListIndex].selectedFlows = _selectedNodes;
          _globalFilters[findBatchIndex].logList = _logList;
          setEditDimensionGroup(_globalFilters);
          setBatchList(() => {
            return _.cloneDeep(batchList);
          });
          message.success(t('analysisCenter-3hJu2wERbaQS'));
          setTabsKey(new Date().getTime());
          handleVisibleChange(false, id);
        } else {
          message.info(t('analysisCenter-eBzWcUppJOvT'));
        }
      } else {
        message.info(t('analysisCenter-eBzWcUppJOvT'));
      }
    };

    const findValue = _.find(batchList, (item) => item.id === id);
    return (
      <div className="popoverBatch">
        <div className="flowNodeTitle">
          {t('analysisCenter-zKe7L3VOjkZt')}[{selectedNodes.length}/10]
          <span>{t('analysisCenter-POefkEK09or9')}</span>
        </div>
        <div className="flowNode">
          {selectedNodes.map((item) => {
            return (
              <span className="flowNodeItem" key={item.nodeId}>
                {`[${item.nodeId}]${item.name}`} <span onClick={() => delNode(item.nodeId)}>{icon.CLOSE}</span>
              </span>
            );
          })}
        </div>
        <div className="renderFlow">
          <FlowCanvas
            dataProvider={dataProvider}
            value={findValue?.flows ?? []}
            onClickNode={(e) => onFlowCanvasClick(e, id)}
            mode="detail"
          />
        </div>
        <div className="buttons">
          <Button onClick={() => handleVisibleChange(false, id)}>{t('analysisCenter-8luMKTPWOOa3')}</Button>
          <Button type="primary" onClick={saveBatchId}>
            {t('analysisCenter-AiB7tX14bG7b')}
          </Button>
        </div>
      </div>
    );
  };

  const dataProvider = {
    getAtTimeNodesData: (flows) => {
      return campaignV2Service.getNextTimeInAtTimeNodes(flows);
    },
    getEventCountLogsByProjectId: async () =>
      FunnelAnalysis.getEventCountLogsByProjectId({
        projectId: localStorage.getItem('projectId')
      })
  };

  useEffect(() => {
    const init = async () => {
      setLoading(true);
      const _editGlobalFilter = _.cloneDeep(editDimensionGroup);
      let logList = null;
      const findBatchIndex = _.findIndex(_editGlobalFilter, {
        id: batchValue.id
      });
      setFindBatchIndex(findBatchIndex);
      logList = _editGlobalFilter[findBatchIndex].logList;

      // 回显
      const _selectedRowKeys = [];
      _.forEach(logList, (v) => _selectedRowKeys.push(v.id));
      setSelectedRowKeys(_selectedRowKeys);
      try {
        const batchList = await FunnelAnalysis.listCalcLogs(pageConfig);
        setBatchList(batchList.content);
        setLoading(false);
      } catch (err) {
        setLoading(false);
      }
    };
    init();
  }, []);

  const selectTable = (rowValue, isTrue) => {
    const _selectedRowKeys = _.cloneDeep(selectedRowKeys);
    const _editDimensionGroup = _.cloneDeep(editDimensionGroup);
    let _logList = _editDimensionGroup[findBatchIndex].logList || [];
    if (isTrue) {
      _logList.push(rowValue);
      setSelectedRowKeys(_.concat(_selectedRowKeys, rowValue.id));
    } else {
      _logList = _.filter(_logList, (item) => item.id !== rowValue.id);
      setSelectedRowKeys(_.without(_selectedRowKeys, rowValue.id));
    }
    // setTabsKey(new Date().getTime());
    _editDimensionGroup[findBatchIndex].logList = _logList;
    setEditDimensionGroup(_editDimensionGroup);
  };

  const rowSelection = {
    selectedRowKeys,
    // onChange: onSelectChange,
    onSelect: selectTable,
    hideSelectAll: true
  };

  return (
    <div className="renderBatch" style={{ overflow: 'auto' }}>
      <Table
        size="middle"
        columns={columns}
        dataSource={batchList || []}
        bordered={false}
        rowKey={(record) => record.id}
        loading={loading}
        rowSelection={rowSelection}
        scroll={{ x: 900 }}
        pagination={{
          position: 'bottomRight',
          defaultPageSize: 5,
          showTotal: (e) => `${t('analysisCenter-qdrIyuzqnle9')} ${e} ${t('analysisCenter-WFR2um5duftk')}`
        }}
      />
    </div>
  );
};
export default RenderBatch;
