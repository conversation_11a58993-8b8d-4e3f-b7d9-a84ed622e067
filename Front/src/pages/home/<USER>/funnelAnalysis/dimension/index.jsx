import { Button, message } from 'antd';
import _ from 'lodash';
import React, { useContext } from 'react';
import DimensionItem from './dimensionItem';
// import icon from '../icon';

import { t } from 'utils/translation';
import { funnelAnalysisContext } from '../funnelAnalysisContext';

export default function Dimension() {
  const { state, dispatch } = useContext(funnelAnalysisContext);
  const { dimensionGroup, isMultiStep } = state;

  const addDimensionGroup = () => {
    if (dimensionGroup.length >= 1) {
      return message.warning(t('analysisCenter-Q5as5pWEoWfr'));
    }
    const _dimensionGroup = _.cloneDeep(dimensionGroup);
    _dimensionGroup.push({});
    dispatch({ dimensionGroup: _dimensionGroup });
  };

  return (
    <funnelAnalysisContext.Provider value={{ state, dispatch }}>
      {dimensionGroup &&
        dimensionGroup.map((item, index) => {
          return <DimensionItem key={index} dimensionIndex={index} value={dimensionGroup} />;
        })}
      <Button
        disabled={isMultiStep}
        className="addButton"
        type="dashed"
        block
        onClick={addDimensionGroup}
        hidden={dimensionGroup.length >= 1}
      >
        +{t('analysisCenter-ct6uQDVwolrh')}
      </Button>
    </funnelAnalysisContext.Provider>
  );
}
