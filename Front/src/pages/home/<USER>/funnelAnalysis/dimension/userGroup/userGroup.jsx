import { Button, Select, Spin, message } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import UserGroupService from 'service/UserGroupService';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { t } from 'utils/translation';
import { funnelAnalysisContext } from '../../funnelAnalysisContext';
import './userGroup.scss';

const { Option } = Select;
const userGroupService = new UserGroupService();

export default ({ setFlagVisible }) => {
  const { state, dispatch, dimensionIndex } = useContext(funnelAnalysisContext);
  const { scenarioId, dimensionGroup } = state;
  const [userGroupQuery, setUserGroupQuery] = useState(null);
  const [selectValue, setSelectValue] = useState([]);
  const [loading, setLoading] = useState(false);
  const searchConfig = {
    size: 10000,
    page: 1,
    search: [
      { operator: 'LIKE', propertyName: 'name', value: '' },
      { operator: 'EQ', propertyName: 'status', value: 'NORMAL' },
      { operator: 'EQ', propertyName: 'calcStatus', value: 'SUC' },
      { operator: 'EQ', propertyName: 'scenario.id', value: scenarioId },
      {
        operator: 'IN',
        propertyName: 'approvalStatus',
        value: 'NONE,PASS,CANCEL'
      },
      {
        operator: 'EQ',
        propertyName: 'deptId',
        value: window.getDeptId()
      }
    ],
    sorts: [{ direction: 'desc', propertyName: 'updateTime' }]
  };

  useEffect(() => {
    init();
  }, []);

  const init = async () => {
    setLoading(true);
    try {
      const userGroupQuery = await FunnelAnalysis.getEventQuery(searchConfig);
      setUserGroupQuery(userGroupQuery);
      await userGroupService.list([
        {
          operator: 'EQ',
          propertyName: 'status',
          value: 'NORMAL'
        },
        {
          operator: 'EQ',
          propertyName: 'deptId',
          value: window.getDeptId()
        }
      ]);
      // 回显
      const nameArr = [];
      if (!_.isEmpty(dimensionGroup[dimensionIndex].filters)) {
        dimensionGroup[dimensionIndex].filters.forEach((n) => {
          nameArr.push(n.groupName);
        });
        const idArr = [];
        userGroupQuery.content.forEach((item) => {
          if (nameArr.includes(item.name)) {
            idArr.push(`${item.id}`);
          }
        });
        setSelectValue(idArr);
      }
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  };

  const changeSelect = (e) => {
    setSelectValue(e);
  };

  const save = () => {
    const filters = [];
    const _selectValue = _.cloneDeep(selectValue);
    if (_.isEmpty(_selectValue)) {
      return message.error(t('analysisCenter-Db1Yf9fZvP54'));
    }
    _selectValue.forEach((item) => {
      const value = userGroupQuery.content.find((i) => `${i.id}` === item);
      filters.push({
        groupName: value.name,
        segment: {
          connector: 'AND',
          filters: [
            {
              connector: 'AND',
              filters: [
                {
                  segment: {
                    id: value.id,
                    customerCount: value.customerCount,
                    name: value.name,
                    lastCalcTime: value.lastCalcTime
                  },
                  type: 'INCLUDE'
                }
              ]
            }
          ]
        }
      });
    });
    const _dimensionGroup = _.cloneDeep(dimensionGroup);
    _dimensionGroup[dimensionIndex] = {
      type: 'SEGMENT',
      // name: info.node.eventValue.fieldName,
      filterValue: [],
      group: dimensionIndex,
      filters
    };
    setFlagVisible(false);
    dispatch({ dimensionGroup: _dimensionGroup });
  };

  return (
    <div className="TabPaneUserGroup">
      <Spin spinning={loading}>
        <div className="userGroupBox">
          <Select
            placeholder={t('analysisCenter-Db1Yf9fZvP54')}
            mode="multiple"
            optionFilterProp="children"
            style={{ width: '100%' }}
            value={selectValue}
            onChange={(e) => changeSelect(e)}
            maxTagCount="responsive"
          >
            {userGroupQuery?.content.map((item, index) => {
              return (
                <Option key={index} value={JSON.stringify(item.id)}>
                  {item.name}
                </Option>
              );
            })}
          </Select>
        </div>
        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button type="primary" onClick={save}>
            {t('analysisCenter-AiB7tX14bG7b')}
          </Button>
        </div>
      </Spin>
    </div>
  );
};
