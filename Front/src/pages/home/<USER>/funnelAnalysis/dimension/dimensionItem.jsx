/*
 * @Author: Wxw
 * @Date: 2022-07-26 11:04:29
 * @LastEditTime: 2022-10-28 10:33:26
 * @LastEditors: Wxw
 * @Description:维度分组
 * @FilePath: \Front\src\pages\home\analysisCenter\funnelAnalysis\dimension\dimensionItem.jsx
 */
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Popover, Select, Tabs, Tooltip } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import TagAndTagValueService from 'service/tagAndTagValueService';
import { MyIconV2 } from 'utils/myIcon';
import Tag from '../filter/TabPane/Tag/tag';
import Campaign from '../filter/TabPane/campaign/campaign';
import Event from '../filter/TabPane/event/event';
import TableField from '../filter/TabPane/tableField/tableField';
import CampaignValue from './campaignValue/campaign';
import './dimensionItem.scss';
import UserGroup from './userGroup/userGroup';
// import icon from '../icon';

import { t } from 'utils/translation';
import { funnelAnalysisContext } from '../funnelAnalysisContext';

const { TabPane } = Tabs;

export default function Dimension(props) {
  const { dimensionIndex } = props;
  const { state, dispatch } = useContext(funnelAnalysisContext);
  const { stepList, dimensionGroup, scenarioId } = state;
  const [visible, setVisible] = useState(false);
  const [editDimensionGroup, setEditDimensionGroup] = useState(null);
  const [valueVisible, setValueVisible] = useState(false);
  const [tagValue, setTagValue] = useState([]);
  const RenderFilter = () => {
    const style = {
      marginRight: 0,
      fontSize: 16
    };
    const showTabPane = {
      event: [
        t('analysisCenter-tHt746irQHwp'),
        t('analysisCenter-HHAvHRuBc4rb'),
        t('analysisCenter-LnIVbeWlpH1g'),
        t('analysisCenter-tfKyeg9IiqkL')
      ],
      bizTable: [t('analysisCenter-HHAvHRuBc4rb'), t('analysisCenter-LnIVbeWlpH1g'), t('analysisCenter-tfKyeg9IiqkL')],
      campaignList: [t('analysisCenter-HHAvHRuBc4rb'), t('analysisCenter-LnIVbeWlpH1g')],
      all: [
        t('analysisCenter-tHt746irQHwp'),
        t('analysisCenter-HHAvHRuBc4rb'),
        t('analysisCenter-LnIVbeWlpH1g'),
        t('analysisCenter-d4DChjaNKUKY'),
        t('analysisCenter-tfKyeg9IiqkL')
      ],
      EVENT: [
        t('analysisCenter-tHt746irQHwp'),
        t('analysisCenter-HHAvHRuBc4rb'),
        t('analysisCenter-LnIVbeWlpH1g'),
        t('analysisCenter-tfKyeg9IiqkL')
      ],
      BIZ_TABLE: [t('analysisCenter-HHAvHRuBc4rb'), t('analysisCenter-LnIVbeWlpH1g'), t('analysisCenter-tfKyeg9IiqkL')],
      CAMPAIGN: [t('analysisCenter-HHAvHRuBc4rb'), t('analysisCenter-LnIVbeWlpH1g')]
    };
    // 本期bizTable 没有表字段

    // // 在这里处理格式, 判定是否是空数组或者是空对象的给过滤掉
    // let obj = flag ? globalFilters : isMulti ? stepList[index].multiStepList[ind] : stepList[index];
    // let newObj = Object.keys(obj).reduce((acc, cur) => {
    //   if (obj[cur] && obj[cur].length !== 0) {
    //     acc[cur] = obj[cur];
    //   }
    //   return acc;
    // }, {});
    // let keys = Object.keys(newObj);
    let typeArr = [];
    let showType = null;
    showType = 'all';
    stepList.forEach((item) => {
      typeArr.push(item.type);
    });
    typeArr = [...new Set(typeArr)];
    const type = [];
    typeArr.forEach((item) => {
      showTabPane[item].forEach((i) => {
        type.push(i);
      });
    });
    // 找到type里面出现了typeArr.length次的key
    const obj = {};
    type.forEach((item) => {
      if (obj[item]) {
        obj[item]++;
      } else {
        obj[item] = 1;
      }
    });
    const arr = [];
    for (const key in obj) {
      if (obj[key] === typeArr.length) {
        arr.push(key);
      }
    }
    showType = arr;
    const types = {
      USER_PROPERTIES: t('analysisCenter-tHt746irQHwp'),
      USER_LABEL: t('analysisCenter-HHAvHRuBc4rb'),
      SEGMENT: t('analysisCenter-LnIVbeWlpH1g'),
      TABLE_FIELD: t('analysisCenter-d4DChjaNKUKY'),
      CAMPAIGN: t('analysisCenter-tfKyeg9IiqkL')
    };
    return (
      <div>
        <Tabs defaultActiveKey={types[dimensionGroup[dimensionIndex].type]}>
          {showType.map((item) => {
            if (item === t('analysisCenter-tHt746irQHwp')) {
              return (
                <TabPane
                  tab={
                    <span>
                      {' '}
                      <MyIconV2 type="icon-icon-event" style={style} /> {t('analysisCenter-tHt746irQHwp')}
                    </span>
                  }
                  key={t('analysisCenter-tHt746irQHwp')}
                >
                  <Event setFlagVisible={setVisible} />
                </TabPane>
              );
            } else if (item === t('analysisCenter-HHAvHRuBc4rb')) {
              return (
                <TabPane
                  tab={
                    <span>
                      <MyIconV2 type="icon-icon-tag" style={style} />
                      <span style={{ marginRight: 5 }}>{t('analysisCenter-HHAvHRuBc4rb')}</span>
                      <Tooltip
                        overlayClassName="activity-board-remark-tooltip"
                        placement="top"
                        title={t('analysisCenter-D1a1AkgA40Bz')}
                      >
                        <QuestionCircleOutlined className="remarkTooltip-icon" />
                      </Tooltip>
                    </span>
                  }
                  key={t('analysisCenter-HHAvHRuBc4rb')}
                >
                  <Tag setFlagVisible={setVisible} />
                </TabPane>
              );
            } else if (item === t('analysisCenter-LnIVbeWlpH1g')) {
              return (
                <TabPane
                  tab={
                    <span>
                      <MyIconV2 type="icon-icon-users" style={style} />
                      <span style={{ marginRight: 5 }}>{t('analysisCenter-LnIVbeWlpH1g')}</span>
                      <Tooltip
                        overlayClassName="activity-board-remark-tooltip"
                        placement="top"
                        title={t('analysisCenter-CGduoHx8gvig')}
                      >
                        <QuestionCircleOutlined className="remarkTooltip-icon" />
                      </Tooltip>
                    </span>
                  }
                  key={t('analysisCenter-LnIVbeWlpH1g')}
                >
                  <UserGroup setFlagVisible={setVisible} />
                </TabPane>
              );
            } else if (item === t('analysisCenter-d4DChjaNKUKY')) {
              return (
                <TabPane
                  tab={
                    <span>
                      {' '}
                      <MyIconV2 type="icon-icon-datefile" style={style} /> {t('analysisCenter-d4DChjaNKUKY')}
                    </span>
                  }
                  key={t('analysisCenter-d4DChjaNKUKY')}
                >
                  <TableField setFlagVisible={setVisible} />
                </TabPane>
              );
            } else if (item === t('analysisCenter-tfKyeg9IiqkL')) {
              return (
                <TabPane
                  tab={
                    <span>
                      {' '}
                      <MyIconV2 type="icon-icon-camping" style={style} /> {t('analysisCenter-tfKyeg9IiqkL')}
                    </span>
                  }
                  key={t('analysisCenter-tfKyeg9IiqkL')}
                >
                  <Campaign setFlagVisible={setVisible} setValueVisible={setValueVisible} />
                </TabPane>
              );
            }
            return null;
          })}
        </Tabs>
      </div>
    );
  };

  const RenderTitle = () => {
    let title = t('analysisCenter-prtYhcfIhvnH');
    const _dimensionGroup = dimensionGroup;
    const type = _dimensionGroup[dimensionIndex].type;
    const icons = {
      USER_PROPERTIES: 'icon-icon-event',
      USER_LABEL: 'icon-icon-tag',
      SEGMENT: 'icon-icon-users',
      TABLE_FIELD: 'icon-icon-datefile',
      CAMPAIGN: 'icon-icon-camping'
    };
    switch (type) {
      case 'SEGMENT':
        title = `${_dimensionGroup[dimensionIndex].filters.length}${t('analysisCenter-iCGYPeumjCDb')}`;
        break;
      case 'TABLE_FIELD':
        title = _dimensionGroup[dimensionIndex].name;
        break;
      case 'CAMPAIGN':
        const filterTypeName = {
          // CAMPAIGN: '按营销活动',
          CAMPAIGN_BATCH: t('analysisCenter-I1DspKkasU60'),
          CAMPAIGN_NODE: t('analysisCenter-JhSu15IabDiv')
        };
        title = filterTypeName[_dimensionGroup[dimensionIndex].filters[0].campaignGroup.filterType];
        break;
      case 'USER_LABEL':
        title = _dimensionGroup[dimensionIndex].name;
        break;
      case 'USER_PROPERTIES':
        title = _dimensionGroup[dimensionIndex].name;
        break;
      default:
    }
    return (
      <div className="dimensionTitle">
        {icons[type] ? (
          <div className="mouse">
            <MyIconV2 type={icons[type]} />
          </div>
        ) : (
          <div className="mouse">
            <MyIconV2 type="icon-icon-event" />
          </div>
        )}
        <div className="dimensionContent" style={{ opacity: title === t('analysisCenter-prtYhcfIhvnH') ? 0.4 : 1 }}>
          {title}
        </div>
        <div className="icons" onClick={deleteDimension}>
          <MyIconV2 type="icon-icon-close" />
        </div>
      </div>
    );
  };

  const deleteDimension = () => {
    const _dimensionGroup = _.cloneDeep(dimensionGroup);
    _dimensionGroup.splice(dimensionIndex, 1);
    // // 重新排序id
    // _dimensionGroup.forEach((item, index) => {
    //   item.id = index;
    // });
    dispatch({ dimensionGroup: _dimensionGroup });
  };

  const handleChange = (value) => {
    // debugger;
    const _dimensionGroup = _.cloneDeep(dimensionGroup);

    if (_dimensionGroup[dimensionIndex].type === 'USER_LABEL') {
      const _value = _.cloneDeep(value);
      const filterArrayRes = _value.map((item) => {
        const itemRes = item.match(/\[(.+?)\]/g);
        item = itemRes ? RegExp.$1 : item;
        return item;
      });

      _dimensionGroup[dimensionIndex].showValue = value;
      _dimensionGroup[dimensionIndex].filterValue = filterArrayRes || value;
      _dimensionGroup[dimensionIndex].filters?.forEach((item) => {
        item.userLabel.filters[0].filters[0].showValue = value;
      });
    } else {
      _dimensionGroup[dimensionIndex].filterValue = value;
    }

    dispatch({ dimensionGroup: _dimensionGroup });
  };

  const renderCampaiginFilterValue = () => {
    return (
      <div>
        <CampaignValue
          // setVisible={setVisible}
          setValueVisible={setValueVisible}
          type={dimensionGroup[dimensionIndex].filters[0].campaignGroup.filterType}
        />
      </div>
    );
  };

  const getTagValue = async () => {
    const id = dimensionGroup[dimensionIndex].filters[0].userLabel.filters[0].filters[0].id;
    const tagValue = await TagAndTagValueService.getTagValuesById({ labelId: id });
    return tagValue;
  };

  useEffect(() => {
    (async () => {
      if (dimensionGroup[dimensionIndex]?.type === 'USER_LABEL') {
        const _tagValue = await getTagValue();
        setTagValue(_tagValue);
      }
    })();
  }, [dimensionGroup[dimensionIndex]?.type, dimensionGroup[dimensionIndex]?.name]);

  const renderFilterValue = () => {
    let title = t('analysisCenter-HbV8hfTPAr98');
    let opacity;
    if (dimensionGroup[dimensionIndex].type === 'CAMPAIGN') {
      let namelength = null;
      const type = dimensionGroup[dimensionIndex].filters[0].campaignGroup.filterType;
      const filter = dimensionGroup[dimensionIndex].filters[0].campaignGroup.filters;
      if (type === 'CAMPAIGN_BATCH') {
        // 把filters每一项的logList的length加起来
        namelength = filter.reduce((total, item) => {
          return total + item.logList.length;
        }, 0);
        if (namelength === 0) {
          title = t('analysisCenter-ribqL7FQwm67');
          opacity = true;
        } else {
          title = `${namelength}${t('analysisCenter-q6JGuZqzO9mf')}`;
        }
      } else if (type === 'CAMPAIGN_NODE') {
        // 把filters每一项的logList里每一项的selectedFlows的length加起来
        namelength = filter.reduce((total, item) => {
          return (
            total +
            item.logList.reduce((total1, item1) => {
              return total1 + item1.selectedFlows.length;
            }, 0)
          );
        }, 0);
        if (namelength === 0) {
          title = t('analysisCenter-nbbOY44siao2');
          opacity = true;
        } else {
          title = `${namelength}${t('analysisCenter-SXu8LmpvK49g')}`;
        }
      } else {
        return (title = t('analysisCenter-9FDvdRqacWIC'));
      }
    }

    if (dimensionGroup[dimensionIndex].type === 'SEGMENT') {
      return null;
    } else if (dimensionGroup[dimensionIndex].type === 'CAMPAIGN') {
      return (
        <Popover
          getPopupContainer={() => document.getElementsByClassName('content')[0]}
          content={renderCampaiginFilterValue()}
          trigger="click"
          destroyTooltipOnHide
          overlayStyle={{ minWidth: '320px' }}
          autoAdjustOverflow
          open={valueVisible}
          onOpenChange={setValueVisible}
          placement="bottom"
          className="conversion"
        >
          <div className="stepTitle" style={{ opacity: opacity ? 0.4 : 1 }}>
            {title}
          </div>
        </Popover>
      );
    }
    return (
      <div className="option">
        <div style={{ width: '95%' }}>
          <Select
            bordered={false}
            mode="tags"
            value={
              dimensionGroup[dimensionIndex].type === 'USER_LABEL'
                ? dimensionGroup[dimensionIndex].filters?.map((item) => item.userLabel.filters[0].filters[0])[0]
                    ?.showValue
                : dimensionGroup[dimensionIndex].filterValue
            }
            size="middle"
            placeholder={t('analysisCenter-qyCFEbP0NJbU')}
            maxTagCount={6}
            maxTagTextLength={20}
            onChange={handleChange}
            style={{
              width: '100%'
            }}
          >
            {!_.isEmpty(tagValue) &&
              tagValue
                .filter((n) => !!n.value)
                .map((item, i) => (
                  <Select.Option
                    value={item?.priorityShow === 2 && item.displayValue ? item.valueAndDisplayValue : item.value}
                    label={item?.priorityShow === 2 && item.displayValue ? item.valueAndDisplayValue : item.value}
                    key={i.id}
                  >
                    {item?.priorityShow === 2 && item.displayValue
                      ? item.valueAndDisplayValue
                      : item.value
                        ? item.value
                        : item}
                  </Select.Option>
                ))}
          </Select>
        </div>
      </div>
    );
  };
  return (
    <funnelAnalysisContext.Provider
      value={{
        state,
        dispatch,
        isDimension: true,
        dimensionIndex,
        setVisible,
        scenarioId,
        editDimensionGroup,
        setEditDimensionGroup
      }}
    >
      <div className="dimensionItem">
        <Popover
          getPopupContainer={() => document.getElementsByClassName('content')[0]}
          // content={RenderSetStep(item, index)}
          content={RenderFilter()}
          trigger="click"
          destroyTooltipOnHide
          overlayStyle={{ minWidth: '320px' }}
          autoAdjustOverflow
          open={visible}
          onOpenChange={setVisible}
          placement="bottomRight"
          className="conversion"
        >
          <div className="stepTitle">{RenderTitle()}</div>
        </Popover>
        {renderFilterValue()}
      </div>
    </funnelAnalysisContext.Provider>
  );
}
