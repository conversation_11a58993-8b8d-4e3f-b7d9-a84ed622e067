import { LinkOutlined } from '@ant-design/icons';
import { Alert, Form, Input, message, Modal, Radio, Select } from 'antd';
import CustomRangePicker from 'components/featurecoms/rangepicker/index';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import CampaignV2Service from 'service/CampaignV2Service';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import UserGroupService from 'service/UserGroupService';
import UserService from 'service/UserService';
import { t } from 'utils/translation';
import { funnelAnalysisContext } from '../funnelAnalysisContext';

const { Option } = Select;
const { confirm } = Modal;

const type = {
  CONVERT: t('analysisCenter-XRkJND50p4SA'),
  LOSS: t('analysisCenter-93fOVf0roxxN')
};

const timeTerm = [
  {
    value: 'DAY',
    label: t('analysisCenter-82qJCQ5QjMyQ'),
    unit: 'days'
  },
  {
    value: 'WEEK',
    label: t('analysisCenter-rOmciXTXtyZh'),
    unit: 'weeks'
  },
  {
    value: 'MONTH',
    label: t('analysisCenter-1pGYMHzFRs31'),
    unit: 'months'
  }
];

const isPassObj = {
  true: t('analysisCenter-IDuNMeMliSEx'),
  false: t('analysisCenter-6Zyand8dv4yi')
};

const getString = (obj, showTime) => {
  let str = '';
  if (typeof obj === 'object') {
    if (obj.type === 'ABSOLUTE') {
      const format = showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';
      str = `${dayjs(obj.timestamp).format(format)}`;
    } else if (obj.type === 'RELATIVE') {
      str = `${isPassObj[JSON.stringify(obj.isPast)]}${obj.times}${
        timeTerm.find((n) => n.value === obj.timeTerm)?.label
      }`;
    } else if (obj.type === 'NOW') {
      str = t('analysisCenter-TK8zrKZWW5lw');
    }
  }

  return str;
};

const dateType = {
  MINUTE: t('analysisCenter-DYUgyLUF5Cxb'),
  HOUR: t('analysisCenter-nvOH8cY57GAe'),
  DAY: t('analysisCenter-82qJCQ5QjMyQ')
};

const campaignV2Service = new CampaignV2Service();
const userGroupService = new UserGroupService();
const userService = new UserService();

const handleValidator = async (rule, val) => {
  if (val) {
    const res = await campaignV2Service.ensureUnique({ name: val });
    if (!res) {
      return Promise.reject(new Error(rule.message));
    }
  }
  return Promise.resolve();
};

const SaveFunnelGroup = () => {
  const { state, dispatch } = useContext(funnelAnalysisContext);
  const { scenarioId, name, storageType, defaultFunnel, dateRange2 } = state;
  const [form] = Form.useForm();
  const [validDateType, setValidDateType] = useState('FOREVER');
  const [date, setDate] = useState({
    validBeginTime: new Date().getTime(),
    validEndTime: null
  });

  useEffect(() => {
    const { nowValue } = state.saveGroupValue;
    form.setFieldsValue({
      scenarioId,
      segmentName: `${name}_step${nowValue.stepGroup}_${type[storageType]}_${
        nowValue?.groupName ? nowValue?.groupName : t('analysisCenter-97XXpp8Nl8lM')
      }`
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const saveRecordInfo = async (id) => {
    const userInfo = await userService.getCurrentUser();
    await userGroupService.saveUserOperationRecord({
      targetId: id,
      targetType: 'SEGMENT',
      type: 'RECENT',
      createUserId: userInfo.id,
      updateUserId: userInfo.id,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });
  };

  const showPromiseConfirm = (data) => {
    confirm({
      title: t('analysisCenter-KRolgMKGeZDl'),
      className: 'processSaveModal',
      icon: <LinkOutlined />,
      okText: t('analysisCenter-2LwSg46tey0g'),
      cancelText: t('analysisCenter-dQ67yiUEySTo'),
      async onOk() {
        const userInfo = await userService.getCurrentUser();

        const processRes = await userGroupService.approvalProcessInfo({
          contentId: data.id,
          contentName: data.name,
          contentType: 'SEGMENT',
          promoterId: userInfo.id,
          projectId: localStorage.getItem('projectId')
        });

        const saveParams = {
          id: data.id,
          approvalNo: processRes.approvalNo,
          approvalStatus: 'RUNNING'
        };

        await userGroupService.updateApprovalNoAndStatus(saveParams);

        message.success(t('analysisCenter-qwNAuTaDUKp0'));
      }
    });
  };

  const handleOk = async () => {
    try {
      const formValue = await form.validateFields();
      const processAuth = await campaignV2Service.getProcessByType({
        type: 'SEGMENT'
      });
      const { nowValue } = state.saveGroupValue;
      let data = {
        ...defaultFunnel,
        ...formValue,
        approvalStatus: processAuth && processAuth.status === 'ENABLE' ? 'PENDING' : undefined,
        funnelStep: nowValue.stepGroup,
        groupIndex: state.saveGroupValue.ind,
        storageType,
        validDateType,
        validBeginTime: new Date().getTime(),
        validEndTime: date.validEndTime && date.validEndTime.valueOf()
      };
      if (data.validDateType === 'TEMPORARY' && !data.validEndTime) {
        throw new Error(t('analysisCenter-neFVvE4wRmvV'));
      }
      if (data.validDateType === 'FOREVER') data = _.omit(data, ['validBeginTime', 'validEndTime']);
      delete data?.id;
      const result = await FunnelAnalysis.saveFunnelChartSegment(data);
      if (processAuth && processAuth.status === 'ENABLE') {
        showPromiseConfirm(result);
      }
      saveRecordInfo(result.id);
      message.success(t('analysisCenter-3hJu2wERbaQS'));
      dispatch({ showSaveGroup: false });
    } catch (err) {
      window.console.log(err.message);
    }
  };

  const handleClose = () => {
    dispatch({
      showSaveGroup: false
    });
  };

  const renderMessage = () => {
    const str1 = getString(dateRange2[0], true);
    const str2 = getString(dateRange2[1], true);
    const { stepTerm, amount } = state.chartConfig.funnelDataQuery.conversion;
    return `${t('analysisCenter-l6bw6WiTUPMh')}“${str1} ~ ${str2}”${t('analysisCenter-vjXPD3x4hMnn')}${amount}${dateType[stepTerm]}${t('analysisCenter-Kb60rWILzAOA')}`;
  };

  const changeRangePicker = (dates) => {
    setDate({
      validBeginTime: dates[0],
      validEndTime: dates[1]
    });
  };

  return (
    <Modal
      className="saveFunnelGroup"
      title={t('analysisCenter-ZrmMstbgSCYe')}
      open
      onOk={handleOk}
      onCancel={handleClose}
    >
      <Alert style={{ marginBottom: 10 }} message={renderMessage()} type="warning" showIcon />

      <Form layout="vertical" form={form}>
        <Form.Item label={t('analysisCenter-Oe60fRFjzAcK')} name="scenarioId">
          <Select disabled>
            {state.scenarioList.map((n) => (
              <Option key={n.id} value={n.id}>
                {n.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={t('analysisCenter-93zhARezZPtp')}
          name="segmentName"
          rules={[
            { required: true, message: t('analysisCenter-Bfm8p84ljbGF') },
            { max: 60, message: t('analysisCenter-m0kYzoqyDhTh') },
            {
              pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
              message: t('analysisCenter-yiZf1B3x8eX3')
            },
            { validator: handleValidator, message: t('analysisCenter-iIEA93cXCPys') }
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item label={t('analysisCenter-2DCQI02kHSlQ')} name="memo">
          <Input.TextArea />
        </Form.Item>
      </Form>
      <div className="analysisGroupType">
        <span>{t('analysisCenter-S0WMurDvJnyg')}</span>
        <span>
          <Radio.Group onChange={(e) => setValidDateType(e.target.value)} defaultValue="FOREVER">
            <Radio value="FOREVER">{t('analysisCenter-Q8RfYVsXURSh')}</Radio>
            <Radio value="TEMPORARY">{t('analysisCenter-2DJVZIwBRNm5')}</Radio>
          </Radio.Group>
        </span>
        {validDateType === 'TEMPORARY' && (
          <CustomRangePicker
            limitTime
            className="userGroupPicker"
            value={[
              date.validBeginTime ? dayjs(date.validBeginTime) : null,
              date.validEndTime ? dayjs(date.validEndTime) : null
            ]}
            disableStartTime
            onChange={changeRangePicker}
            showTime={false}
            format="YYYY-MM-DD"
            style={{ marginTop: 12, width: '100%', marginLeft: 70 }}
            timeDiff={1}
            closeBefore
          />
        )}
      </div>
    </Modal>
  );
};

export default SaveFunnelGroup;
