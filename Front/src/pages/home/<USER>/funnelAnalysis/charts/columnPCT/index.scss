.columnPCT {
  // max-height: 600px;
  margin-top: 24px;
  // overflow: auto;
  padding-right: 10px;

  .column-conversionRate {
    .topTitile {
      width: 100%;
      display: flex;
      font-size: 12px;
      font-weight: bold;
      // justify-content: center;
      margin-bottom: 16px;
      overflow: hidden;
      padding-right: 20%;

      div {
        width: 24%;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        white-space: nowrap;

        &:last-child {
          margin-right: 0;
        }
      }
    }

  }

  .columnContent {
    .columnContentItem {
      // display: flex;
      margin-bottom: 22px;

      .columnContentItem-title {
        // width: 10%;
        // text-align: center;
        font-size: 14px;

        &>div {
          &>div {
            margin-right: 2px;
          }
        }
      }

      .cloumnCharts {
        width: 100%;
        margin-top: 8px;
        // margin-bottom: -20px;

        .cloumnChartsItem {
          display: flex;
          height: 20px;
          margin-bottom: 16px;

          .cloumnChartsItem-type {
            width: 10%;
            margin-right: 4px;
          }

          .cloumnChartsItem-value {
            width: 10%;
            margin-right: 4px;
            text-align: center;
          }

          .cloumnChartsItem-progress {
            width: 80%;
            background: #F5F5F5;

            .cloumnChartsItem-progress-bar {
              overflow: hidden;
              text-align: right;
              color: #fff;

              &:hover {
                //hover border的动画
                animation: hover-border 1s ease-in-out infinite;
                border: 1px solid rgba(0, 0, 0, 0.8);
              }
            }
          }

          .cloumnChartsItem-total {
            width: 10%;
            text-align: center;
          }
        }

        .cloumnChartsFooter {
          display: flex;
          width: 80px;
          height: 70px;
          position: relative;
          padding-left: 21%;

          .cloumnChartsFooterItem {
            margin-right: 16px;

            &:last-child {
              margin-right: 0;
            }

            .cloumnChartsFooter-arrow {
              width: 0px;
              height: 0px;
              border: 40px solid transparent;
              border-top: 6px solid transparent;
              border-top-color: #F5F5F5;
            }

            .cloumnChartsFooter-inner {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 80px;
              height: 24px;
              background: #F5F5F5;
              border-top: 3px solid #FF7A45;
              border-top-left-radius: 6px;
              border-top-right-radius: 6px;
              cursor: pointer;
            }
          }
        }
      }
    }
  }

  .scale {
    .line {
      width: 100%;
      height: 1px;
      background: rgba(0, 0, 0, 0.25)
    }

    .ratio {
      display: flex;
      justify-content: space-between;
    }
  }

  .titleColors {
    //span标签展示一个居中的小方块
    display: inline-block;
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 2px;
  }
}

.funnelChartsPopover {
  .content {
    div {
      margin: 16px 0 8px 0;
    }
  }
}