import { Button, Empty, Popover, Spin, Typography } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useRef, useState } from 'react';
import thousands from 'utils/thousands';
import { colors } from '../config';
import './index.scss';

import { funnelAnalysisContext } from '../../funnelAnalysisContext';

import { t } from 'utils/translation';

const { Text } = Typography;

export default function Column({ loading, chartList, readOn, selectedRowKeys }) {
  const [stepDataList, setStepDataList] = useState([]);
  const context = useContext(funnelAnalysisContext);

  useEffect(() => {
    if (!_.isEmpty(chartList)) {
      const _chartList = _.cloneDeep(chartList);
      let flag = false;
      _chartList.forEach((item) => {
        if (item.multiStep || item?.stepDataList[0]?.dimensionGroup) {
          flag = true;
        }
      });
      if (flag) {
        // 如果是多路径
        // 遍历_chartList每一项的stepDataList每一项的groupName等于selectedRowKeys的某一个值时,就删掉
        _chartList.forEach((item) => {
          item.stepDataList =
            (!_.isEmpty(selectedRowKeys) && item.stepDataList.filter((i) => selectedRowKeys.includes(i.groupName))) ||
            [];
        });
      }
      setStepDataList(_chartList);
    }
  }, [chartList, selectedRowKeys]);

  /**
   * @description: 点击柱状图 其他的赋给opcity 设置ref是重复点击回显总体柱状图
   * @param {*} ind 柱状图下标
   * @return {*}
   */
  // eslint-disable-next-line no-unused-vars
  const clickChart = (ind) => {
    const newStepDataList = _.cloneDeep(stepDataList);
    newStepDataList.map((v) => {
      v.stepDataList.map((b) => {
        b.isOpacity = ref.current !== ind;
        return b;
      });
      v.stepDataList[ind].isOpacity = false;
      return v;
    });
    ref.current = ind;
    setStepDataList(newStepDataList);
  };
  const ref = useRef(null);

  const handleHoverChange = (index, ind) => {
    const newStepDataList = _.cloneDeep(stepDataList);
    newStepDataList.map((v) => {
      return v.stepDataList.map((b) => {
        return (b.clickVisible = false);
      });
    });
    newStepDataList[index].stepDataList[ind].hoverVisible = !newStepDataList[index].stepDataList[ind].hoverVisible;
    setStepDataList(newStepDataList);
  };

  const handleClickChange = (index, ind) => {
    const newStepDataList = _.cloneDeep(stepDataList);
    newStepDataList[index].stepDataList[ind].clickVisible = !newStepDataList[index].stepDataList[ind].clickVisible;
    newStepDataList[index].stepDataList[ind].hoverVisible = false;
    setStepDataList(newStepDataList);
  };

  /**
   * @description: 点击柱状图展开
   * @param {*} index 下标
   * @param {*} ind 下标
   * @param {*} lastValue 上一个图的total值
   * @param {*} nowValue  当前图的total值
   * @return {*}
   */
  const renderHoverContent = (index, ind, lastValue, nowValue) => {
    let conversionValue = null;
    let conversionRatio = null;
    let drainValue = null;
    let drainRatio = null;
    const lastTotal = lastValue && lastValue[ind].total;
    const nowTotal = nowValue && nowValue.total;
    if (!lastValue) {
      conversionValue = nowValue.total;
      conversionRatio = 100;
      drainValue = 0;
      drainRatio = 0;
    } else {
      conversionValue = nowTotal;
      conversionRatio = ((nowTotal / lastTotal) * 100).toFixed(2);
      drainValue = lastTotal - nowTotal;
      drainRatio = (100 - conversionRatio).toFixed(2);
    }
    return (
      <div className="content">
        <div>
          {conversionValue}({conversionRatio}%) {t('analysisCenter-6wBpizaCiJ1q')}
        </div>
        <div>
          {drainValue}({drainRatio}%) {t('analysisCenter-unhPzLb0z4D7')}
        </div>
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            color: 'rgba(0,0,0,.25)'
          }}
        >
          <span>{t('analysisCenter-RkJX7IZWNLN4')}</span>
        </div>
      </div>
    );
  };

  /**
   * @description: 点击柱状图展开
   * @param {*} index 下标
   * @param {*} ind 下标
   * @param {*} lastValue 上一个图的total值
   * @param {*} nowValue  当前图的total值
   * @return {*}
   */
  const renderClickContent = (index, ind, lastValue, nowValue) => {
    const { dispatch } = context;
    let conversionValue = null;
    let conversionRatio = null;
    let drainValue = null;
    let drainRatio = null;
    const lastTotal = lastValue && lastValue[ind].total;
    const nowTotal = nowValue && nowValue.total;
    if (!lastValue) {
      conversionValue = nowValue.total;
      conversionRatio = 100;
      drainValue = 0;
      drainRatio = 0;
    } else {
      conversionValue = nowTotal;
      conversionRatio = ((nowTotal / lastTotal) * 100).toFixed(2);
      drainValue = lastTotal - nowTotal;
      drainRatio = (100 - conversionRatio).toFixed(2);
    }

    const saveGroup = (flag) => {
      const _stepDataList = _.cloneDeep(stepDataList);
      _stepDataList[index].stepDataList[ind].clickVisible = false;
      setStepDataList(_stepDataList);
      dispatch({
        storageType: flag ? 'CONVERT' : 'LOSS',
        showSaveGroup: true,
        saveGroupValue: { index, ind, lastValue, nowValue }
      });
    };
    return (
      <div className="content">
        <div>
          {conversionValue}({conversionRatio}%) {t('analysisCenter-6wBpizaCiJ1q')}
        </div>
        <Button type="primary" style={{ width: '100%' }} onClick={() => saveGroup(true)}>
          {t('analysisCenter-ZrmMstbgSCYe')}
        </Button>
        <div>
          {drainValue}({drainRatio}%) {t('analysisCenter-unhPzLb0z4D7')}
        </div>
        <Button type="primary" style={{ width: '100%' }} onClick={() => saveGroup(false)} disabled={!lastValue}>
          {t('analysisCenter-ZrmMstbgSCYe')}
        </Button>
      </div>
    );
  };

  const renderConversion = () => {
    if (!_.isEmpty(stepDataList)) {
      // 多路径渲染转化率 如果findMultiStep!==-1说明是多路径 循环渲染多路径
      // debugger;
      const findMultiStep = stepDataList.findIndex((v) => v.multiStep);
      let conversionList = null;
      const conversionArr = [];

      conversionList = findMultiStep !== -1 ? stepDataList[findMultiStep]?.stepDataList : stepDataList;
      const firstStepDataList = stepDataList[0].stepDataList;
      const lastStepDataList = stepDataList[stepDataList.length - 1].stepDataList;
      // debugger;
      firstStepDataList &&
        firstStepDataList.forEach((v, ind) => {
          const lastStepData = lastStepDataList[ind];
          const conversion = ((lastStepData.total / v.total) * 100).toFixed(2);
          conversionArr.push(conversion);
        });
      if (readOn) {
        // 如果只读把conversionList每一项的stepDataList都截取6个
        conversionList.map((v) => {
          v.stepDataList = v.stepDataList && v.stepDataList.slice(0, 6);
          return v;
        });
      }
      const groupName = (conversionList[0]?.stepDataList && conversionList[0]?.stepDataList[0]?.groupName) || null;
      return (
        <div
          style={{
            fontWeight: readOn ? '400px' : 'bold',
            fontSize: readOn ? '12px' : '16px'
          }}
          className="topTitile"
        >
          {/* 维度分组 */}
          {conversionList && groupName ? (
            conversionList[0]?.stepDataList.map((item, ind) => {
              return (
                <div key={ind}>
                  <Text
                    key={ind}
                    style={{ width: readOn ? '80%' : '85%' }}
                    ellipsis={{
                      tooltip: `${item?.groupName}${
                        conversionArr[ind] && `•${conversionArr[ind] === 'NaN' ? 0.0 : conversionArr[ind]}%`
                      }`
                    }}
                  >
                    <span className="titleColors" style={{ background: colors[ind] }} />
                    {`${item?.groupName}${
                      conversionArr[ind] && `•${conversionArr[ind] === 'NaN' ? 0.0 : conversionArr[ind]}%`
                    }`}
                  </Text>
                </div>
              );
            }) // 普通漏斗
          ) : findMultiStep === -1 ? (
            <div>
              {t('analysisCenter-vM6yQu2DdSrB')}
              {`•${conversionArr[0] === 'NaN' ? 0.0 : conversionArr[0]}%`}
            </div>
          ) : (
            conversionList.map((item, ind) => {
              // 多路径漏斗
              return (
                <div key={ind} style={{ marginRight: '15px' }}>
                  <span className="titleColors" style={{ background: colors[ind] }} />
                  {findMultiStep !== -1 ? item.stepNum : null}
                  {conversionArr[ind] && `•${conversionArr[ind] === 'NaN' ? 0.0 : conversionArr[ind]}%`}
                </div>
              );
            })
          )}
        </div>
      );
    } else {
      return null;
    }
  };

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        padding: _.isEmpty(stepDataList) || _.isEmpty(selectedRowKeys) ? '100px' : '0 5px',
        overflow: 'auto'
      }}
    >
      {loading ? (
        <div className="mainChartSpin">
          <Spin />
        </div>
      ) : _.isEmpty(stepDataList) || _.isEmpty(selectedRowKeys) ? (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      ) : (
        <div className="columnPCT">
          <div className="column-conversionRate">{renderConversion()}</div>
          <div className="columnContent">
            {_.map(stepDataList, (item, index) => {
              let title = '';
              return (
                <div className="columnContentItem" key={index}>
                  <div className="columnContentItem-title">
                    {/* 此处渲染图表左侧名称 */}
                    {item.multiStep ? (
                      <div style={{ display: 'flex' }}>
                        {item.stepDataList.forEach((v, ind) => {
                          // 如果是最后一个 没有或title += `${v.stepNum}或`
                          if (ind === item.stepDataList.length - 1) {
                            title += `${v.stepNum}`;
                          } else {
                            title += `${v.stepNum}${t('analysisCenter-3xczbdWJHrTM')}`;
                          }
                        })}
                        {item.stepDataList[0]?.stepGroup}、{title}
                      </div>
                    ) : (
                      <>
                        {' '}
                        {item.step}、{item.stepDataList[0]?.stepName}
                      </>
                    )}
                  </div>
                  <div className="cloumnCharts">
                    {_.map(item.stepDataList, (i, ind) => {
                      const max = stepDataList[0].stepDataList[ind].total;
                      let percent = index === 0 ? 100 : (i.total / max) * 100;
                      // 百分比等于0时为100%，否则是上一步的百分比
                      const percentageValue =
                        index === 0 ? 100 : (i.total / stepDataList[index - 1].stepDataList[ind].total) * 100;
                      // 寻找有没有多路径下标
                      const findIndex = stepDataList.findIndex((v) => v.multiStep);
                      let title = '';
                      if (findIndex !== -1) {
                        title = `${findIndex + 1}-${ind + 1}`;
                      } else {
                        title = item?.stepDataList[0]?.stepName;
                      }
                      // percent如果是NaN则为0
                      if (isNaN(percent)) percent = 0;
                      return (
                        <div className="cloumnChartsItem" key={ind}>
                          <div className="cloumnChartsItem-progress">
                            {readOn ? (
                              <div
                                className="cloumnChartsItem-progress-bar"
                                style={{
                                  // 宽度 要是percent等于false 并且ind等于0 就是100% 否则要是percent等于false就是0%
                                  width: percent ? `${percent}%` : index === 0 ? '100%' : '0%',
                                  height: '100%',
                                  background: colors[ind],
                                  opacity: i?.isOpacity ? 0.1 : 1,
                                  transition: 'opacity .4s'
                                }}
                              />
                            ) : (
                              <Popover
                                title={title}
                                trigger="hover"
                                content={renderHoverContent(index, ind, stepDataList[index - 1]?.stepDataList, i)}
                                open={i?.hoverVisible}
                                overlayClassName="funnelChartsPopover"
                                onOpenChange={() => handleHoverChange(index, ind)}
                              >
                                <Popover
                                  title={title}
                                  trigger="click"
                                  content={renderClickContent(index, ind, stepDataList[index - 1]?.stepDataList, i)}
                                  open={i?.clickVisible}
                                  overlayClassName="funnelChartsPopover"
                                  onOpenChange={() => handleClickChange(index, ind)}
                                >
                                  <div
                                    className="cloumnChartsItem-progress-bar"
                                    style={{
                                      // 宽度 要是percent等于false 并且ind等于0 就是100% 否则要是percent等于false就是0%
                                      width: percent ? `${percent}%` : index === 0 ? '100%' : '0%',
                                      height: '100%',
                                      background: colors[ind],
                                      opacity: i?.isOpacity ? 0.1 : 1,
                                      transition: 'opacity .4s'
                                    }}
                                  />
                                </Popover>
                              </Popover>
                            )}
                          </div>
                          <div className="cloumnChartsItem-value">
                            {percentageValue ? percentageValue.toFixed(2) : 0.0}%
                          </div>
                          <div className="cloumnChartsItem-total">
                            <Text ellipsis={{ tooltip: `${thousands(i.total)}${t('analysisCenter-g8RhTvwbkHwt')}` }}>
                              {thousands(i.total)}
                              {t('analysisCenter-g8RhTvwbkHwt')}
                            </Text>
                          </div>
                        </div>
                      );
                    })}
                    {/* <div className="cloumnChartsFooter" hidden={index === stepDataList.length - 1}>
                    {_.map(item.stepDataList, (i, ind) => {
                      // 计算到下一步的转化率
                      const nextStep = stepDataList[index + 1];
                      const nextStepValue = nextStep?.stepDataList[ind]?.total;
                      const lastPercent = (nextStepValue / i.total) * 100;
                      // debugger;
                      return (
                        <div className="cloumnChartsFooterItem" key={ind} onClick={() => clickChart(ind, i)}>
                          <div
                            className="cloumnChartsFooter-inner"
                            style={{ borderTop: `3px solid ${colors[ind]}` }}
                          >
                            {(lastPercent).toFixed(2)}%
                          </div>
                          <div className="cloumnChartsFooter-arrow" />
                        </div>
                      );
                    })}
                  </div> */}
                  </div>
                </div>
              );
            })}
          </div>
          <div className="scale" style={{ paddingRight: '20%' }}>
            <div className="line" />
            <div className="ratio">
              <div>0</div>
              <div>20%</div>
              <div>40%</div>
              <div>60%</div>
              <div>80%</div>
              <div>100%</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
