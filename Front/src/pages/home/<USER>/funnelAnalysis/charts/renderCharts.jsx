/*
 * @Author: Wxw
 * @Date: 2022-08-18 17:45:23
 * @LastEditTime: 2022-10-13 11:25:28
 * @LastEditors: Wxw
 * @Description:
 * @FilePath: \Front\src\pages\home\analysisCenter\funnelAnalysis\charts\renderCharts.jsx
 */
import React, { useContext } from 'react';
import { funnelAnalysisContext } from '../funnelAnalysisContext';
import Column from './column';
import ColumnPCT from './columnPCT';
import Line from './line';

export default ({ chartList, loading }) => {
  const { state } = useContext(funnelAnalysisContext);
  const { chartDisplayType, selectedRowKeys, tableColumn, lineChartList, tableList } = state;
  const renderAddCharts = () => {
    switch (chartDisplayType) {
      case 'COLUMN_CHART':
        return <Column chartList={chartList} selectedRowKeys={selectedRowKeys} readOn={false} loading={loading} />;
      case 'PERCENT_COLUMN_CHART':
        return <ColumnPCT chartList={chartList} selectedRowKeys={selectedRowKeys} loading={loading} />;
      case 'LINE_CHART':
        return (
          <Line
            lineChartList={lineChartList}
            tableColumn={tableColumn}
            chartList={chartList}
            selectedRowKeys={selectedRowKeys}
            readOn={false}
            loading={loading}
            tableList={tableList}
          />
        );
      default:
    }
  };

  return <div>{renderAddCharts()}</div>;
};
