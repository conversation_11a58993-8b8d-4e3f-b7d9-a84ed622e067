import { t } from 'utils/translation';

export const colors = ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#E86452', '#6DC8EC'];
export const mockData = [
  {
    step: '1',
    name: t('analysisCenter-fwMsu2o7jyVE'),
    dataList: [
      {
        date: '2022-07-01',
        type: t('analysisCenter-uvAeAfAcUFfU'),
        value: 12901,
        total: 100,
        ratio: 0.32
      },
      { date: '2022-07-01', type: '123', value: 12901, total: 100, ratio: 0.32 }
    ]
  },
  {
    step: '2',
    name: t('analysisCenter-NE0a6W8dxFNz'),
    dataList: [
      {
        date: '2022-07-01',
        type: t('analysisCenter-uvAeAfAcUFfU'),
        value: 11901,
        total: 100,
        ratio: 0.32
      }
      // { date: '2022-07-01', type: '12313', value: 12901, total: 100, ratio: 0.32 }
    ]
  },
  {
    step: '3',
    name: t('analysisCenter-iElfwQNHk7O7'),
    dataList: [
      { date: '2022-07-01', type: t('analysisCenter-uvAeAfAcUFfU'), value: 5000, total: 100, ratio: 0.32 }
      // { date: '2022-07-01', type: '132', value: 12901, total: 100, ratio: 0.32 }
    ]
  }
];
