import { Empty, Spin, Typography } from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { t } from 'utils/translation';
import Chart from './chart';
import './index.scss';

const { Text } = Typography;

const style = {
  color: 'rgba(0,0,0,0.85)',
  background: '#FAFAFA',
  borderTopColor: '#FAFAFA',
  transition: 'all 0.3s ease-in-out'
};

const checkStyle = {
  color: '#FFF',
  background: 'var(--ant-primary-color)',
  borderTopColor: 'var(--ant-primary-color)',
  transition: 'all 0.3s ease-in-out'
};

export default function Column({ loading, lineChartList, tableColumn, tableList, readOn, selectedRowKeys }) {
  const [filterChartList, setFilterChartList] = useState([]);
  const [currentNode, setCurrentNode] = useState(null);
  useEffect(() => {
    setAll();
  }, [lineChartList]);

  const setAll = () => {
    const _lineChartList = _.cloneDeep(lineChartList);
    const _filterChartList = _.filter(_lineChartList, (i) => i.step === 'all');
    setFilterChartList(_filterChartList);
    setCurrentNode('all');
  };

  const clickArrow = (item) => {
    const _lineChartList = _.cloneDeep(lineChartList);
    const _filterChartList = _.filter(_lineChartList, (i) => i.step === item.key);
    setFilterChartList(_filterChartList);
    setCurrentNode(item.key);
  };

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        padding: _.isEmpty(selectedRowKeys) ? '100px' : 0
      }}
    >
      {loading ? (
        <div className="mainchartSpin" style={{ display: 'flex', justifyContent: 'center' }}>
          <Spin />
        </div>
      ) : _.isEmpty(selectedRowKeys) ? (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      ) : (
        <div className="lineCharts">
          {!readOn && (
            <div className="lineChartsLeft">
              <div className="arrow arrowAll" onClick={setAll}>
                <div className="arrow-inner" style={currentNode === 'all' ? checkStyle : style}>
                  {t('analysisCenter-qvIeEfUFLCZf')}
                  {!_.isEmpty(tableList) &&
                    tableList[0]?.name === t('analysisCenter-97XXpp8Nl8lM') &&
                    `：${!_.isEmpty(tableList) && (tableList[0]?.all_ratio * 100).toFixed(2)}%`}
                </div>
                <div
                  className="arrow-arrow"
                  style={{
                    ...(currentNode === 'all' ? checkStyle : style),
                    background: null
                  }}
                />
              </div>
              {!_.isEmpty(tableColumn) &&
                tableColumn.map((item, index) => {
                  return (
                    <div className="stepItem" key={index}>
                      <div className="stepName">
                        <div>
                          <Text
                            // style={{ width: 150 }}
                            ellipsis={{ tooltip: `${item.key}.${item.title}` }}
                          >
                            {item.key}.{item.title}
                          </Text>
                        </div>
                        <div>{tableList[0][parseInt(item.key)]}人</div>
                      </div>
                      {/* 如果是最后一项 则不渲染 */}
                      {item.key !== tableColumn[tableColumn.length - 1]?.key && (
                        <div className="arrow" onClick={() => clickArrow(item)}>
                          <div className="arrow-inner" style={currentNode === item.key ? checkStyle : style}>
                            {!_.isEmpty(tableList) && tableList[0]?.name === t('analysisCenter-97XXpp8Nl8lM')
                              ? `${!_.isEmpty(tableList) && (tableList[0][`${item.key}_ratio`] * 100).toFixed(2)}%`
                              : `${t('analysisCenter-36y4ItDX42SY')}${item.key}${t('analysisCenter-SaKk8sGXT2iL')}${parseInt(item.key) + 1}`}
                          </div>
                          <div
                            className="arrow-arrow"
                            style={{
                              ...(currentNode === item.key ? checkStyle : style),
                              background: null
                            }}
                          />
                        </div>
                      )}
                    </div>
                  );
                })}
            </div>
          )}
          <div className="lineChartsRight" style={{ width: readOn && '98%' }}>
            <Chart filterChartList={filterChartList} />
          </div>
        </div>
      )}
    </div>
  );
}
