import { Line } from '@ant-design/charts';
import React from 'react';

export default (props) => {
  const { filterChartList } = props;

  const config = {
    data: filterChartList,
    xField: 'date',
    yField: 'ratio',
    seriesField: 'type',
    yAxis: {
      label: {
        formatter: (v) => `${(v * 100).toFixed(2)}%`
      }
    },
    legend: {
      radio: false,
      position: 'bottom'
    },
    // xAxis: {
    //   label: {
    //     formatter: (v) => {
    //       let date = new Date(parseInt(v));
    //       let year = date.getFullYear();
    //       let month = date.getMonth() + 1;
    //       let day = date.getDate();
    //       let time = `${year}/${month}/${day}`;
    //       return time;
    //     }
    //   }
    // },
    tooltip: {
      formatter: (datum) => {
        return {
          name: datum.type,
          value: `${(datum.ratio * 100).toFixed(2)}%`
        };
      },
      // follow: false, // 不跟鼠标划入
      enterable: true, // 允许鼠标划入
      showContent: true
      // customContent: (datum, defaultDom) => {
      //   return (
      //     <>
      //       <div>
      //         <div className="g2-tootip-title" style={{ margin: '12px 0' }}>{datum}</div>
      //         <div className="g2-tooltip-list">
      //           {defaultDom.map((item, index) => {
      //             return (
      //               <div className="g2-tooltip-list-item" key={index}>
      //                 <span
      //                   className="g2-tooltip-marker"
      //                   style={{ backgroundColor: item.color, width: '8px', height: '8px', borderRadius: '50%', display: 'inline-block', marginRight: '8px' }}
      //                 />
      //                 <span>{item.name}</span>：
      //                 <span
      //                   className="g2-tooltip-value"
      //                   style={{ display: 'inline-block', float: 'right', marginLeft: '30px' }}
      //                 >{item.value}</span>
      //               </div>
      //             );
      //           })}
      //         </div>
      //         <div onClick={() => clickChart(defaultDom)} style={{ marginBottom: '20px' }}>
      //           存为用户分群
      //         </div>
      //       </div>
      //     </>
      //   );
      // }
    }
  };

  // const clickChart = (value) => {
  //   window.console.log(value);
  // };

  return (
    <>
      <Line {...config} />
    </>
  );
};
