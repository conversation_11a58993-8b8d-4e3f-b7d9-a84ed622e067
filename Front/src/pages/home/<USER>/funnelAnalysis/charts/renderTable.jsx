/*
 * @Author: Wxw
 * @Date: 2022-08-18 17:45:23
 * @LastEditTime: 2022-12-07 15:05:10
 * @LastEditors: Wxw
 * @Description: 漏斗分析表格
 * @FilePath: \Front\src\pages\home\analysisCenter\funnelAnalysis\charts\renderTable.jsx
 */
import { Table, message } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import thousands from 'utils/thousands';
import { t } from 'utils/translation';
import { funnelAnalysisContext } from '../funnelAnalysisContext';
import './renderTable.scss';

export default (props) => {
  const { loading } = props;
  const { state, dispatch } = useContext(funnelAnalysisContext);
  const { selectedRowKeys } = state;
  const [cloumns, setCloumns] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const { tableColumn, tableList } = state;

  useEffect(() => {
    if (!_.isEmpty(tableColumn)) {
      const _tableColumn = _.cloneDeep(tableColumn);
      _tableColumn.map((item) => {
        item.dataIndex = item.key;
        item.width = 240;
        return item;
      });
      _tableColumn.unshift({
        title: t('analysisCenter-qvIeEfUFLCZf'),
        key: 'all_ratio',
        dataIndex: 'all_ratio',
        width: 180,
        render: (text) => {
          return <span>{(text * 100).toFixed(2)}%</span>;
        }
      });
      _tableColumn.unshift({
        title: t('analysisCenter-VTJEue5EEmaa'),
        key: 'name',
        dataIndex: 'name',
        width: 180,
        ellipsis: true
      });
      // _tableColumn除了name和all_ratio
      _tableColumn.map((item) => {
        if (item.dataIndex !== 'name' && item.dataIndex !== 'all_ratio') {
          item.render = (text, record) => {
            const ratio = text === 0 ? 0 : record[`${item.dataIndex}_ratio`];
            return (
              <span className="convers">
                <span>{thousands(text)}</span>
                <span>
                  <span className="ratio">{(ratio * 100).toFixed(2)}%</span>
                  <span className="ratioInner" />
                </span>
              </span>
            );
          };
        } else if (item.key !== 'all_ratio') {
          item.render = (text) => {
            return <span>{text}%</span>;
          };
        }
        // _tableColumn最后一个render等于null 或者第一个render等于null
        if (item.key === _tableColumn[_tableColumn.length - 1].key || item.key === _tableColumn[0].key) {
          item.render = null;
        }
        return item;
      });
      setCloumns(_tableColumn);
    }
  }, [tableColumn]);

  useEffect(() => {
    if (!_.isEmpty(tableList)) {
      const _tableList = _.cloneDeep(tableList);
      _tableList.map((item) => {
        item.key = item.name;
        item.children = item?.children?.map((child) => {
          child.key = Math.random();
          return child;
        });
        return item;
      });

      setDataSource(_tableList);
    }
  }, [tableList]);

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys) => {
      if (selectedRowKeys.length === 0) {
        return message.error(t('analysisCenter-b3DM9r3AhspQ'));
      }
      if (selectedRowKeys.length > 6) {
        return message.error(t('analysisCenter-jJy9yCPwpyyI'));
      }
      dispatch({ selectedRowKeys });
    },
    renderCell: (checked, record, index, originNode) => {
      // record里childen有值就是父级 子级不可选
      if (!record.children) {
        return null;
      }
      return originNode;
    },
    // 去去掉右上角的checkbox
    hideSelectAll: true
    // onSelect: (record, selected, selectedRows) => {
    //   console.log(record, selected, selectedRows);
    // },
    // onSelectAll: (selected, selectedRows, changeRows) => {
    //   console.log(selected, selectedRows, changeRows);
    // }
  };

  return (
    <div className="funnelTable">
      <Table
        loading={loading}
        size="middle"
        columns={cloumns}
        dataSource={dataSource || []}
        bordered={false}
        scroll={{ x: 300 }}
        pagination={false}
        rowSelection={{ ...rowSelection, checkStrictly: true }}
        rowKey={(record) => record.key}
      />
    </div>
  );
};
