import { But<PERSON>, Empty, Popover, Spin, Typography } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useRef, useState } from 'react';
import thousands from 'utils/thousands';
import { colors } from '../config';
import './index.scss';

import { t } from 'utils/translation';
import { funnelAnalysisContext } from '../../funnelAnalysisContext';

const { Text } = Typography;

/**
 * @description: 普通图表
 * @param {*} chartList 图表数据
 * @param {*} readOn 只读
 * @return {*}
 */
export default function Column({ loading, chartList, readOn, selectedRowKeys }) {
  const [stepDataList, setStepDataList] = useState([]);
  const context = useContext(funnelAnalysisContext);

  useEffect(() => {
    // setStepDataList(chartList);
    if (!_.isEmpty(chartList)) {
      const _chartList = _.cloneDeep(chartList);
      let flag = false;
      _chartList.forEach((item) => {
        if (item.multiStep || item?.stepDataList[0]?.dimensionGroup) {
          flag = true;
        }
      });
      if (flag) {
        // 多路径
        // 遍历_chartList每一项的stepDataList每一项的groupName等于selectedRowKeys的某一个值时,就删掉
        _chartList.forEach((item) => {
          item.stepDataList =
            (!_.isEmpty(selectedRowKeys) && item.stepDataList.filter((i) => selectedRowKeys.includes(i.groupName))) ||
            [];
        });
      }
      setStepDataList(_chartList);
    }
  }, [chartList, selectedRowKeys]);

  /**
   * @description: 点击柱状图 其他的赋给opcity 设置ref是重复点击回显总体柱状图
   * @param {*} ind 柱状图下标
   * @return {*}
   */
  const clickChart = (ind) => {
    const newStepDataList = _.cloneDeep(stepDataList);
    newStepDataList.map((v) => {
      v.stepDataList.map((b) => {
        b.isOpacity = ref.current !== ind;
        return b;
      });
      v.stepDataList[ind].isOpacity = false;
      return v;
    });
    ref.current = ind;
    setStepDataList(newStepDataList);
  };
  const ref = useRef(null);

  const handleHoverChange = (index, ind) => {
    const newStepDataList = _.cloneDeep(stepDataList);
    newStepDataList.map((v) => {
      return v.stepDataList.map((b) => {
        return (b.clickVisible = false);
      });
    });
    newStepDataList[index].stepDataList[ind].hoverVisible = !newStepDataList[index].stepDataList[ind].hoverVisible;
    setStepDataList(newStepDataList);
  };

  const handleClickChange = (index, ind) => {
    const newStepDataList = _.cloneDeep(stepDataList);
    newStepDataList[index].stepDataList[ind].clickVisible = !newStepDataList[index].stepDataList[ind].clickVisible;
    newStepDataList[index].stepDataList[ind].hoverVisible = false;
    setStepDataList(newStepDataList);
  };

  /**
   * @description: 点击柱状图展开
   * @param {*} index 下标
   * @param {*} ind 下标
   * @param {*} lastValue 上一个图的total值
   * @param {*} nowValue  当前图的total值
   * @return {*}
   */
  const renderHoverContent = (index, ind, lastValue, nowValue) => {
    let conversionValue = null;
    let conversionRatio = null;
    let drainValue = null;
    let drainRatio = null;
    const lastTotal = lastValue && lastValue[ind].total;
    const nowTotal = nowValue && nowValue.total;
    if (!lastValue) {
      conversionValue = nowValue.total;
      conversionRatio = 100;
      drainValue = 0;
      drainRatio = 0;
    } else {
      conversionValue = nowTotal;
      conversionRatio = ((nowTotal / lastTotal) * 100).toFixed(2);
      drainValue = lastTotal - nowTotal;
      drainRatio = (100 - conversionRatio).toFixed(2);
    }
    return (
      <div className="content">
        <div>
          {thousands(conversionValue)}({conversionRatio}%) {t('analysisCenter-6wBpizaCiJ1q')}
        </div>
        <div>
          {thousands(drainValue)}({drainRatio}%) {t('analysisCenter-unhPzLb0z4D7')}
        </div>
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            color: 'rgba(0,0,0,.25)'
          }}
        >
          <span>{t('analysisCenter-RkJX7IZWNLN4')}</span>
        </div>
      </div>
    );
  };

  /**
   * @description: 点击柱状图展开
   * @param {*} index 下标
   * @param {*} ind 下标
   * @param {*} lastValue 上一个图的total值
   * @param {*} nowValue  当前图的total值
   * @return {*}
   */
  const renderClickContent = (index, ind, lastValue, nowValue) => {
    const { dispatch } = context;
    let conversionValue = null;
    let conversionRatio = null;
    let drainValue = null;
    let drainRatio = null;
    const lastTotal = lastValue && lastValue[ind].total;
    const nowTotal = nowValue && nowValue.total;
    if (!lastValue) {
      conversionValue = nowValue.total;
      conversionRatio = 100;
      drainValue = 0;
      drainRatio = 0;
    } else {
      conversionValue = nowTotal;
      conversionRatio = ((nowTotal / lastTotal) * 100).toFixed(2);
      drainValue = lastTotal - nowTotal;
      drainRatio = (100 - conversionRatio).toFixed(2);
    }

    const saveGroup = (flag) => {
      const _stepDataList = _.cloneDeep(stepDataList);
      _stepDataList[index].stepDataList[ind].clickVisible = false;
      setStepDataList(_stepDataList);
      dispatch({
        storageType: flag ? 'CONVERT' : 'LOSS',
        showSaveGroup: true,
        saveGroupValue: { index, ind, lastValue, nowValue }
      });
    };

    return (
      <div className="content">
        <div>
          {thousands(conversionValue)}({conversionRatio}%) {t('analysisCenter-6wBpizaCiJ1q')}
        </div>
        <Button type="primary" style={{ width: '100%' }} onClick={() => saveGroup(true)}>
          {t('analysisCenter-ZrmMstbgSCYe')}
        </Button>
        <div>
          {thousands(drainValue)}({drainRatio}%) {t('analysisCenter-unhPzLb0z4D7')}
        </div>
        <Button type="primary" style={{ width: '100%' }} onClick={() => saveGroup(false)} disabled={!lastValue}>
          {t('analysisCenter-ZrmMstbgSCYe')}
        </Button>
      </div>
    );
  };

  const renderConversion = () => {
    if (!_.isEmpty(stepDataList)) {
      // 多路径渲染转化率 如果findMultiStep!==-1说明是多路径 循环渲染多路径
      const findMultiStep = stepDataList.findIndex((v) => v.multiStep);
      let conversionList = null;
      const conversionArr = [];

      conversionList = findMultiStep !== -1 ? stepDataList[findMultiStep]?.stepDataList : stepDataList;
      const firstStepDataList = stepDataList[0].stepDataList;
      const lastStepDataList = stepDataList[stepDataList.length - 1].stepDataList;
      firstStepDataList &&
        firstStepDataList.forEach((v, ind) => {
          const lastStepData = lastStepDataList[ind];
          const conversion = ((lastStepData?.total / v?.total) * 100).toFixed(2);
          conversionArr.push(conversion);
        });
      if (readOn) {
        // 如果只读把conversionList每一项的stepDataList都截取6个
        !_.isEmpty(conversionList) &&
          conversionList.map((v) => {
            v.stepDataList = (!_.isEmpty(v.stepDataList) && v.stepDataList.slice(0, 6)) || [];
            return v;
          });
      }
      const groupName = (conversionList[0]?.stepDataList && conversionList[0]?.stepDataList[0]?.groupName) || null;
      return (
        <div
          style={{
            fontWeight: readOn ? '400px' : 'bold',
            fontSize: readOn ? '12px' : '16px'
          }}
          className="topTitile"
        >
          {/* 维度分组 */}
          {conversionList && groupName ? (
            conversionList[0]?.stepDataList.map((item, ind) => {
              return (
                <div key={ind}>
                  <Text
                    key={ind}
                    style={{ width: readOn ? '78%' : '85%' }}
                    ellipsis={{
                      tooltip: `${item?.groupName}${
                        conversionArr[ind] && `•${conversionArr[ind] === 'NaN' ? 0.0 : conversionArr[ind]}%`
                      }`
                    }}
                  >
                    <span className="titleColors" style={{ background: colors[ind] }} />
                    {`${item?.groupName}${
                      conversionArr[ind] && `•${conversionArr[ind] === 'NaN' ? 0.0 : conversionArr[ind]}%`
                    }`}
                  </Text>
                </div>
              );
            }) // 普通漏斗
          ) : findMultiStep === -1 ? (
            <div>
              {t('analysisCenter-97XXpp8Nl8lM')}
              {`•${conversionArr[0] === 'NaN' ? 0.0 : conversionArr[0]}%`}
            </div>
          ) : (
            !_.isEmpty(conversionList) &&
            conversionList.map((item, ind) => {
              // 多路径漏斗
              return (
                <div key={ind} style={{ marginRight: '15px' }}>
                  <span className="titleColors" style={{ background: colors[ind] }} />
                  {findMultiStep !== -1 ? item.groupName : null}
                  {conversionArr[ind] && `•${conversionArr[ind] === 'NaN' ? 0.0 : conversionArr[ind]}%`}
                </div>
              );
            })
          )}
        </div>
      );
    } else {
      return null;
    }
  };

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        padding: _.isEmpty(stepDataList) || _.isEmpty(selectedRowKeys) ? '100px' : '0 5px',
        overflow: 'auto'
      }}
    >
      {loading ? (
        <div className="mainChartSpin">
          <Spin />
        </div>
      ) : _.isEmpty(stepDataList) || _.isEmpty(selectedRowKeys) ? (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      ) : (
        <div className="column">
          <div className="column-conversionRate">{renderConversion()}</div>
          <div className="columnContent">
            {_.map(stepDataList, (item, index) => {
              let title = '';
              return (
                <div className="columnContentItem" key={index}>
                  <div className="columnContentItem-title">
                    <span>
                      {/* 此处渲染图表左侧名称 */}
                      {item.multiStep ? (
                        <div style={{ display: 'flex' }}>
                          {item.stepDataList.forEach((v, ind) => {
                            // 如果是最后一个 没有或title += `${v.stepNum}或`
                            if (ind === item.stepDataList.length - 1) {
                              title += `${v?.groupName || v.stepName}`;
                            } else {
                              title += `${v?.groupName || v.stepName}或`;
                            }
                          })}
                          {item.stepDataList[0]?.stepGroup}、{title}
                        </div>
                      ) : (
                        <>
                          {item.step}、{item.stepDataList[0]?.stepName}
                        </>
                      )}
                    </span>
                  </div>
                  <div className="cloumnCharts">
                    {_.map(item.stepDataList, (i, ind) => {
                      const max = _.maxBy(stepDataList[0].stepDataList, 'total').total;
                      const percent = (i.total / max) * 100;
                      const title = i?.groupName || i.stepName;
                      return (
                        <div className="cloumnChartsItem" key={ind}>
                          {readOn ? (
                            <div className="cloumnChartsItem-progress">
                              <div
                                className="cloumnChartsItem-progress-bar"
                                style={{
                                  // 宽度 要是percent等于false 并且ind等于0 就是100% 否则要是percent等于false就是0%
                                  width: percent ? `${percent}%` : '0%',
                                  height: '100%',
                                  background: colors[ind],
                                  opacity: i?.isOpacity ? 0.1 : 1,
                                  transition: 'opacity .4s'
                                }}
                              />
                            </div>
                          ) : (
                            <Popover
                              title={title}
                              trigger="hover"
                              content={renderHoverContent(index, ind, stepDataList[index - 1]?.stepDataList, i)}
                              open={i?.hoverVisible}
                              overlayClassName="funnelChartsPopover"
                              onOpenChange={() => handleHoverChange(index, ind)}
                            >
                              <Popover
                                title={title}
                                trigger="click"
                                content={renderClickContent(
                                  index,
                                  ind,
                                  stepDataList[index - 1]?.stepDataList,
                                  i,
                                  stepDataList
                                )}
                                open={i?.clickVisible}
                                overlayClassName="funnelChartsPopover"
                                onOpenChange={() => handleClickChange(index, ind)}
                              >
                                <div className="cloumnChartsItem-progress">
                                  <div
                                    className="cloumnChartsItem-progress-bar"
                                    style={{
                                      width: percent ? `${percent}%` : '0%',
                                      height: '100%',
                                      background: colors[ind],
                                      opacity: i?.isOpacity ? 0.1 : 1,
                                      transition: 'opacity .4s'
                                    }}
                                  />
                                </div>
                              </Popover>
                            </Popover>
                          )}

                          <div className="cloumnChartsItem-total">
                            <Text ellipsis={{ tooltip: `${thousands(i.total)}人` }}>{thousands(i.total)}人</Text>
                          </div>
                        </div>
                      );
                    })}
                    <div className="cloumnChartsFooter" hidden={index === stepDataList.length - 1}>
                      {_.map(item.stepDataList, (i, ind) => {
                        // 计算到下一步的转化率
                        const nextStep = stepDataList[index + 1];
                        const nextStepValue = nextStep?.stepDataList[ind]?.total;
                        const lastPercent = (nextStepValue / i.total) * 100;
                        // debugger;
                        return (
                          <div
                            className="cloumnChartsFooterItem"
                            key={ind}
                            onClick={() => clickChart(ind, i)}
                            style={{ marginRight: readOn && '5px' }}
                          >
                            <div
                              className="cloumnChartsFooter-inner"
                              style={{
                                borderTop: `3px solid ${colors[ind]}`,
                                width: readOn && '64px'
                              }}
                            >
                              {lastPercent ? lastPercent.toFixed(2) : 0.0}%
                            </div>
                            <div
                              className="cloumnChartsFooter-arrow"
                              style={{
                                border: readOn && '28px solid transparent'
                              }}
                            />
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
