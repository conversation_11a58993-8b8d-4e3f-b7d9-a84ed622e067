import _ from 'lodash';
import React, { useContext } from 'react';
import './campaign.scss';

import { t } from 'utils/translation';
import { funnelAnalysisContext } from '../../../funnelAnalysisContext';

/**
 * @description:
 * @param {*} flag 是否是步骤过滤
 * @param {*} index 步骤下标
 * @param {*} ind 过滤的下标
 * @param {*} value 当前步骤过滤条件
 * @param {*} isMulti 是否是多路径
 * @return {*}
 */
export default ({ setFlagVisible }) => {
  const contextValue = useContext(funnelAnalysisContext);
  const {
    state,
    dispatch,
    flag,
    isDimension,
    index,
    ind,
    isMulti,
    filterIndex,
    setVisible,
    filterConnetor,
    dimensionIndex
  } = contextValue;
  const { stepList, globalFilters, dimensionGroup } = state;

  const save = (value) => {
    const data = {
      connector: filterConnetor,
      campaignGroup: {
        connector: filterConnetor,
        filterType: value,
        filters: []
      }
    };
    if (flag) {
      const _globalFilters = _.cloneDeep(globalFilters);
      _globalFilters[filterIndex] = data;
      dispatch({ globalFilters: _globalFilters });
    } else if (isDimension) {
      const _dimensionGroup = _.cloneDeep(dimensionGroup);
      _dimensionGroup[dimensionIndex] = {
        type: 'CAMPAIGN',
        filterValue: [],
        group: dimensionIndex,
        filters: [
          {
            groupName: null,
            campaignGroup: {
              connector: 'AND',
              filterType: value,
              filters: []
            }
          }
        ]
      };
      setFlagVisible(false);
      dispatch({ dimensionGroup: _dimensionGroup });
    } else {
      const _stepList = _.cloneDeep(stepList);
      if (isMulti) {
        _stepList[index].multiStepList[ind].filters[filterIndex] = data;
      } else {
        _stepList[index].filters[filterIndex] = data;
      }
      dispatch({ stepList: _stepList });
    }
    setVisible && setVisible(false);
    setFlagVisible && setFlagVisible(false);
  };

  return (
    <div className="TabPaneCampaign">
      {/* <div className="node" onClick={() => save('CAMPAIGN')}>按营销活动</div> */}
      <div className="node" onClick={() => save('CAMPAIGN_BATCH')}>
        {t('analysisCenter-I1DspKkasU60')}
      </div>
      <div className="node" onClick={() => save('CAMPAIGN_NODE')}>
        {t('analysisCenter-JhSu15IabDiv')}
      </div>
    </div>
  );
};
