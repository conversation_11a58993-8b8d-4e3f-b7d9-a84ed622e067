import { SearchOutlined } from '@ant-design/icons';
import { Input, Popover, Tree, message } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { t } from 'utils/translation';
import { funnelAnalysisContext } from '../../../funnelAnalysisContext';
import './event.scss';

const { DirectoryTree } = Tree;

/**
 * @description:
 * @param {*} flag 是否是步骤过滤
 * @param {*} index 步骤下标
 * @param {*} ind 过滤的下标
 * @param {*} value 当前步骤过滤条件
 * @param {*} isMulti 是否是多路径
 * @param {*} isDimension 是否是维度分组
 * @param {*} dimensionIndex 维度分组下标
 * @return {*}
 */
export default ({ setFlagVisible }) => {
  const contextValue = useContext(funnelAnalysisContext);
  const { state, dispatch, flag, isDimension, dimensionIndex, index, ind, isMulti, filterIndex, filterConnetor } =
    contextValue;
  const { stepList, dateRange2, globalFilters, dimensionGroup } = state;
  const [eventList, setEventList] = useState([]);
  const [treeData, setTreeData] = useState([]);
  const [eventValue, setEventValue] = useState('');

  useEffect(() => {
    const init = async () => {
      let eventList;
      if (flag || isDimension) {
        eventList = await FunnelAnalysis.findEventPropertyList({
          id: 0,
          projectId: localStorage.getItem('projectId')
        });
      } else {
        eventList = await FunnelAnalysis.getEventPropertyList({
          name: '',
          eventId: isMulti ? stepList[index].multiStepList[ind].event?.id : stepList[index]?.event?.id
        });
      }

      setEventList(eventList);
    };
    init();
  }, []);

  useEffect(() => {
    const renderTreeData = () => {
      let _eventList = _.cloneDeep(eventList);

      const regex = new RegExp(eventValue, 'i');
      _eventList = _eventList.filter((item) => regex.test(item.fieldName));

      const titleList = [];
      _eventList
        .filter((fitlerItem) => fitlerItem.fieldName)
        .forEach((item) => {
          titleList.push(item.level1);
        });
      const treeList = _.uniq(titleList).map((item, index) => {
        return {
          title: item,
          key: `${index}`
        };
      });
      // 把eventList每一项level1塞到对应的treeList.label里的children;

      treeList.forEach((item, index) => {
        item.children = _eventList
          .filter((event) => event.level1 === item.title)
          .map((event) => {
            return {
              key: `${index}.${event.fieldName}`,
              isLeaf: true,
              title: (
                <Popover
                  overlayClassName="tableField-popover"
                  trigger="hover"
                  placement="right"
                  content={renderPopover(event)}
                >
                  {event.fieldName}
                </Popover>
              ),
              eventValue: event
            };
          });
      });
      setTreeData(treeList);
    };

    renderTreeData();
  }, [eventValue, eventList]);

  const renderPopover = (event) => {
    return (
      <div className="renderPopover">
        <div>{event.fieldName}</div>
        <div>{event.field}</div>
        <div>{event.fieldType}</div>
      </div>
    );
  };

  const onSelect = (keys, info) => {
    // 判定 是否是叶子节点
    if (info.node.isLeaf) {
      if (flag) {
        const _globalFilters = _.cloneDeep(globalFilters);
        _globalFilters[filterIndex] = {
          connector: filterConnetor,
          eventFilterProperty: {
            connector: filterConnetor,
            filters: [
              {
                ...info.node.eventValue,
                operator: 'EQ', // 等于操作符
                value: null
              }
            ]
          }
        };
        setFlagVisible(false);
        dispatch({ globalFilters: _globalFilters });
      } else if (isDimension) {
        const _dimensionGroup = _.cloneDeep(dimensionGroup);
        _dimensionGroup[dimensionIndex] = {
          type: 'USER_PROPERTIES',
          name: info.node.eventValue.fieldName,
          filterValue: [],
          group: dimensionIndex,
          filters: [
            {
              groupName: null,
              eventFilterProperty: {
                connector: 'AND',
                filters: [
                  {
                    ...info.node.eventValue,
                    operator: 'EQ',
                    value: null
                  }
                ]
              }
            }
          ]
        };
        setFlagVisible(false);
        dispatch({ dimensionGroup: _dimensionGroup });
      } else {
        const _stepList = _.cloneDeep(stepList);
        if (isMulti) {
          if (_stepList[index]?.multiStepList[ind].event.id) {
            _stepList[index].multiStepList[ind].filters[filterIndex] = {
              connector: filterConnetor,
              eventGroup: {
                connector: filterConnetor,
                filters: [
                  {
                    connector: filterConnetor,
                    filters: [
                      {
                        dateRange: dateRange2,
                        eventInfo: {
                          id: _stepList[index]?.multiStepList[ind].event.id,
                          eventType: _stepList[index]?.multiStepList[ind].event.eventType,
                          displayName: _stepList[index]?.multiStepList[ind].event.name,
                          eventNameValue: _stepList[index]?.multiStepList[ind].event.eventNameValue,
                          specialPropertyMappingList:
                            _stepList[index]?.multiStepList[ind].event.specialPropertyMappingList
                        },
                        eventFilterProperty: {
                          connector: filterConnetor,
                          filters: [
                            {
                              connector: filterConnetor,
                              filters: [
                                {
                                  ...info.node.eventValue,
                                  operator: 'EQ', // 等于操作符
                                  value: null
                                }
                              ]
                            }
                          ]
                        },
                        eventAggregateProperty: {
                          fun: 'COUNT',
                          operator: 'IS_NOT_NULL',
                          property: {},
                          propertyType: 'TIMES',
                          value: null
                        }
                      }
                    ]
                  }
                ]
              }
            };
            setFlagVisible(false);
          }
        } else {
          if (_stepList[index]?.event?.id) {
            _stepList[index].filters[filterIndex] = {
              connector: filterConnetor,
              eventGroup: {
                connector: filterConnetor,
                filters: [
                  {
                    connector: filterConnetor,
                    filters: [
                      {
                        dateRange: dateRange2,
                        eventInfo: {
                          id: _stepList[index].event.id,
                          eventType: _stepList[index].event.eventType,
                          displayName: _stepList[index].event.name,
                          eventNameValue: _stepList[index].event.eventNameValue,
                          specialPropertyMappingList: _stepList[index].event.specialPropertyMappingList
                        },
                        eventFilterProperty: {
                          connector: filterConnetor,
                          filters: [
                            {
                              connector: filterConnetor,
                              filters: [
                                {
                                  ...info.node.eventValue,
                                  operator: 'EQ', // 等于操作符
                                  value: null
                                }
                              ]
                            }
                          ]
                        },
                        eventAggregateProperty: {
                          fun: 'COUNT',
                          operator: 'IS_NOT_NULL',
                          property: {},
                          propertyType: 'TIMES',
                          value: null
                        }
                      }
                    ]
                  }
                ]
              }
            };
            setFlagVisible(false);
          } else {
            return message.error(t('analysisCenter-UlLdrnhAOij8'));
          }
        }
        dispatch({ stepList: _stepList });
      }
    }
  };

  const onEventFilterSearch = (e) => {
    setEventValue(e.target.value);
  };

  return (
    <div className="TabPaneEvent">
      <Input
        onChange={onEventFilterSearch}
        placeholder={t('analysisCenter-qyCFEbP0NJbU')}
        suffix={<SearchOutlined className="text-[rgba(0,0,0,.65)]" />}
      />
      <DirectoryTree
        className="fieldDirectoryTree"
        onSelect={onSelect}
        // loadData={onLoadData}
        treeData={treeData}
      />
    </div>
  );
};
