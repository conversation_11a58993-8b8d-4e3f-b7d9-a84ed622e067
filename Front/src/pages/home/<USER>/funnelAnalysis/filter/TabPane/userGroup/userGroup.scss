.TabPaneUserGroup {
  .tip {
    height: 32px;
    line-height: 32px;
    margin-top: 17px;
    color: rgba(0, 0, 0, 0.45);
  }

  .userGroupBox {
    height: 300px;
    overflow-y: auto;

    .popItem {
      .userGroupItem {
        position: relative;
        line-height: 32px;
        width: 100%;
        height: 32px;
        cursor: pointer;

        &:hover {
          background: $active_color;
        }
      }

    }
  }

  .save {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}

.userGroupItemPopover {
  .ant-popover-inner-content {
    width: 560px;

    .renderUserGroupInfo {
      .FilterGroupPanel3 {
        white-space: nowrap;
        overflow: auto;
      }

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .content {
        &>div {
          font-size: 14px;
          display: inline-block;
          min-width: 230px;
          margin: 10px 10px 10px 0px;
        }
      }

      .count {
        font-size: 24px;
        font-weight: bold;
        margin: 10px 0;
      }

      .lastCalcTime {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
}