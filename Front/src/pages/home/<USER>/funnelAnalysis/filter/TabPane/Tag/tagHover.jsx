/*
 * @Author: 卜广硕 <EMAIL>
 * @Date: 2023-02-27 17:34:59
 * @LastEditors: 卜广硕 <EMAIL>
 * @LastEditTime: 2023-03-28 10:46:54
 * @FilePath: \Front\src\pages\home\analysisCenter\funnelAnalysis\filter\TabPane\Tag\tagHover.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Spin, Tag } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { t } from 'utils/translation';
import './tagHover.scss';

const typeMap = {
  EXTERNAL_IMPORT: t('analysisCenter-3hzb3umVPrqV'),
  SQL_CREATION: t('analysisCenter-LsrKGABR1HHO'),
  EX_SUBSCRIBE: t('analysisCenter-ZhFXeb5SFBvN')
};

export default (props) => {
  const { selectValue, labelHistoryList, loading } = props;
  const [tags, setTags] = useState([]);

  useEffect(() => {
    // 整理表格数据
    const _tags = [];
    _.forEach(labelHistoryList, (item) => {
      const showTagValue =
        item.valueAndDisplayValue.length <= 10
          ? item.priorityShow === 2 && item.displayValue
            ? item.valueAndDisplayValue
            : item.value
          : item.priorityShow === 2 && item.displayValue
            ? `${item.valueAndDisplayValue}`
            : `${item.value}`;

      _tags.push(showTagValue);
    });
    setTags(_.uniq(_tags));
  }, [labelHistoryList]);

  return (
    <Spin spinning={loading}>
      <div className="tagHover">
        <div className="label-name">{selectValue?.name}</div>
        <div className="label-attr">
          <div>{`${t('analysisCenter-tnChO8mkQDtS')}：${selectValue?.type ? typeMap[selectValue?.type] : ''}`}</div>
          <div>{`${t('analysisCenter-v5M32W3BuvSb')}：${
            selectValue?.validDate
              ? dayjs(selectValue?.validDate).format('YYYY-MM-DD')
              : t('analysisCenter-FwVIQic9eVj4')
          }`}</div>
          <div>{`${t('analysisCenter-Oe60fRFjzAcK')}：${selectValue?.scenario?.name}[${selectValue?.scenario?.code}]`}</div>
        </div>
        <div style={{ marginTop: '20px' }}>
          {t('analysisCenter-PQObn8pjs62U')}：
          {selectValue?.lastCalcTime ? dayjs(selectValue?.lastCalcTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </div>
        <div className="tagName">{t('analysisCenter-WCP2ei8HA8D5')}</div>
        <div className="tags">
          {!_.isEmpty(tags) &&
            tags.map((item, index) => {
              return <Tag key={index}>{item}</Tag>;
            })}
        </div>
      </div>
    </Spin>
  );
};
