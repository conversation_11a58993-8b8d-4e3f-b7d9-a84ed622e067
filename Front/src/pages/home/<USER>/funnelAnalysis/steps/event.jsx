/*
 * @Author: Wxw
 * @Date: 2022-07-26 11:04:29
 * @LastEditTime: 2022-10-12 10:25:52
 * @LastEditors: Wxw
 * @Description:漏斗流程步骤
 * @FilePath: \Front\src\pages\home\analysisCenter\funnelAnalysis\steps\event.jsx
 */
import { Line } from '@ant-design/charts';
import { Input, Popover, Spin, Table } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useContext, useEffect, useRef, useState } from 'react';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { useDebounce } from 'utils/customhooks';
import './index.scss';
// import icon from '../icon';
import { t } from 'utils/translation';
import { funnelAnalysisContext } from '../funnelAnalysisContext';

// const { TabPane } = Tabs;
const { Search } = Input;
const columns = [
  {
    title: t('analysisCenter-LwTu7T7GUOgK'),
    dataIndex: 'propertySchema'
  },
  {
    title: t('analysisCenter-PzqyRex3ZxH5'),
    dataIndex: 'displayName'
  },
  {
    title: t('analysisCenter-YGAqRbS1SBjW'),
    dataIndex: 'dataType'
  }
];
const nameAlias = {
  date: {
    alias: t('analysisCenter-rNaiS7qi0etP')
  },
  count: {
    alias: t('analysisCenter-PwTCRx7AcTTh')
  }
};

export default function Steps(props) {
  const { id, flag: isMultiStep, ind } = props;
  const { state, dispatch } = useContext(funnelAnalysisContext);
  const [eventConfig, setEventConfig] = useState({
    page: 1,
    search: [
      // { operator: 'EQ', propertyName: 'eventType', value: 'BURIED_POINT_EVENT' }
    ],
    totalElements: null,
    size: 10,
    sorts: [{ propertyName: 'updateTime', direction: 'desc' }]
  });
  const [eventValue, setEventValue] = useState([]);
  const [loading, setLoading] = useState(false);
  const [flag, setFlag] = useState(false);
  const [lineList, setLineList] = useState([]);
  const searchRef = useRef();
  const { stepList } = state;

  useEffect(() => {
    setLoading(true);
    const init = async () => {
      try {
        const eventValue = await FunnelAnalysis.queryEvent(eventConfig);
        const lineList = await FunnelAnalysis.getEventCountLogsByProjectId({
          projectId: localStorage.getItem('projectId')
        });
        setLineList(lineList);
        setEventValue(eventValue.content);
        setEventConfig({
          ...eventConfig,
          page: 1,
          totalElements: eventValue.totalElements
        });
        setLoading(false);
      } catch (err) {
        setLoading(false);
      }
    };
    init();
  }, []);

  // 监听eventBox触底
  const onEventScroll = async (e) => {
    if (eventConfig.totalElements > eventValue.length) {
      setLoading(true);
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      if (!flag && scrollTop + clientHeight >= scrollHeight) {
        const newData = { ...eventConfig, page: eventConfig.page + 1 };
        setEventConfig(newData);
        const newEventValue = await FunnelAnalysis.queryEvent(newData);
        setEventValue([...eventValue, ...newEventValue.content]);
      }
      setLoading(false);
    }
  };

  const onChange = useDebounce(() => {
    if (searchRef.current.input.value === '') {
      setEventConfig({ ...eventConfig, page: 1 });
    }
    const init = async () => {
      const data = {
        page: 1,
        search: [
          {
            operator: 'EQ',
            propertyName: 'eventType',
            value: 'BURIED_POINT_EVENT'
          },
          {
            operator: 'LIKE',
            propertyName: 'name',
            value: searchRef.current.input.value
          }
        ],
        size: 10,
        sorts: [{ propertyName: 'updateTime', direction: 'desc' }]
      };
      const eventValue = await FunnelAnalysis.queryEvent(data);

      await setEventValue(eventValue.content);
      setFlag(false); // 重置防止调用两次接口
    };
    init();
  }, 200);

  const renderEventInfo = (item) => {
    const findValue = _.find(lineList, (i) => i.eventId === item.id);
    if (
      _.some(findValue.eventCountLogList, (o) => {
        return !o;
      })
    ) {
      return;
    }
    const data = findValue.eventCountLogList.map((e) => {
      return {
        ...e,
        statTime: dayjs(e.statTime).format('MM-DD')
      };
    });
    const config = {
      data,
      padding: 'auto',
      xField: 'statTime',
      yField: 'count'
    };
    return (
      <div className="eventInfo">
        <div className="eventInfoTitle">{item.eventNameValue}</div>
        <div className="eventInfoContent">
          <div className="createInformation">
            {item.createUserName} {t('analysisCenter-CHIqI1mKlMWt')} {dayjs(item.createTime).format('YYYY-MM-DD')}
          </div>
          <div>
            {t('analysisCenter-BW1wrET0AByH')}:
            {item.eventType === 'CUSTOM' ? t('analysisCenter-J9k2f7WOS9RT') : t('analysisCenter-XOFOdZt0awOZ')}
          </div>
          <div className="eventTable">
            <Table
              size="small"
              rowKey="key"
              columns={columns}
              dataSource={_.map(item?.specialPropertyMappingList, (item, index) => {
                return { ...item, key: index };
              })}
              pagination={{
                defaultPageSize: 5,
                pageSizeOptions: ['5']
              }}
            />
          </div>
          <div className="line">
            <div className="lineTitle">{t('analysisCenter-MAafWr8ocf9F')}</div>
            <div className="lineChart">
              <Line {...config} meta={nameAlias} />
            </div>
          </div>
        </div>
      </div>
    );
  };

  /**
   * @description: 如果是多路径 就存储到当前步骤对应的ind下标中
   * @param {*} item
   * @return {*}
   */
  const clickEvent = (item) => {
    // todo 重复名
    // if (calculateDuplicateNames(stepList, item.name)) return message.error('漏斗步骤名称重复');
    const _steps = _.cloneDeep(stepList);
    if (isMultiStep) {
      const findStep = _.find(_steps, (i) => i.step === id);
      findStep.multiStepList[ind] = {
        step: findStep.multiStepList[ind].step,
        filters: [],
        visibleSetp: false,
        type: 'EVENT',
        event: item,
        name: item.name
      };
      dispatch({ stepList: _steps });
    } else {
      const value = _steps.map((i) => {
        if (i.step === id) {
          return {
            step: i.step,
            filters: [],
            visibleSetp: false,
            type: 'EVENT',
            event: item,
            name: item.name
          };
        }
        return i;
      });
      dispatch({ stepList: value });
    }
    dispatch({ dimensionGroup: [], globalFilters: [] });
  };

  return (
    <div className="renderEvent">
      <Spin spinning={loading}>
        <Search placeholder={t('analysisCenter-ALKG1eYuoDJc')} allowClear onChange={onChange} ref={searchRef} />
        <div className="eventBox" onScroll={onEventScroll}>
          {eventValue.map((item) => {
            return (
              <div className="popItem" key={item.id}>
                <Popover
                  placement="right"
                  autoAdjustOverflow
                  getPopupContainer={() => document.getElementsByClassName('content')[0]}
                  className="eventItemPopover"
                  content={() => renderEventInfo(item)}
                  trigger="hover"
                  overlayStyle={{ width: '512px' }}
                >
                  <div className="eventItem" onClick={() => clickEvent(item)}>
                    {item.name}
                  </div>
                </Popover>
              </div>
            );
          })}
        </div>
      </Spin>
    </div>
  );
}
