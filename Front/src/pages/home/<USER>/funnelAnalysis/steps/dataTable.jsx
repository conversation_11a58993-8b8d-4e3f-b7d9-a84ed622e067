/*
 * @Author: Wxw
 * @Date: 2022-07-26 11:04:29
 * @LastEditTime: 2022-10-17 17:48:03
 * @LastEditors: Wxw
 * @Description:漏斗流程步骤
 * @FilePath: \Front\src\pages\home\analysisCenter\funnelAnalysis\steps\dataTable.jsx
 */
import { Button, Form, Select } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import './dataTable.scss';
// import icon from '../icon';
import { t } from 'utils/translation';
import { funnelAnalysisContext } from '../funnelAnalysisContext';

const { Option } = Select;

export default function RenderDataTable(props) {
  const [form] = Form.useForm();
  const { id, stepIndex, flag, ind } = props;
  const { state, dispatch } = useContext(funnelAnalysisContext);
  const { stepList } = state;
  const [tableData, setTableData] = useState([]);

  useEffect(() => {
    const init = async () => {
      const tableData = await FunnelAnalysis.getTimeSequenceTable();
      setTableData(tableData);

      // 回显
      const _steps = _.cloneDeep(stepList);
      const value = flag ? _steps[stepIndex].multiStepList[ind].bizTable : _steps[stepIndex].bizTable;
      if (value) {
        form.setFieldsValue({
          tableId: value.tableId
        });
      }
    };
    init();
  }, []);

  const clickDataTable = async () => {
    const { tableId } = await form.validateFields();
    const findValue = _.find(tableData, { tableId });
    const _steps = _.cloneDeep(stepList);
    if (flag) {
      const findStep = _.find(_steps, (i) => i.step === id);
      findStep.multiStepList[ind] = {
        step: findStep.multiStepList[ind].step,
        filters: [],
        visibleSetp: false,
        type: 'BIZ_TABLE',
        bizTable: findValue,
        name: findValue.table.displayName
      };
      dispatch({ stepList: _steps });
    } else {
      const value = _steps.map((i) => {
        if (i.step === id) {
          return {
            step: i.step,
            filters: [],
            visibleSetp: false,
            type: 'BIZ_TABLE',
            bizTable: findValue,
            name: findValue.table.displayName
          };
        }
        return i;
      });
      dispatch({ stepList: value });
    }
    dispatch({ dimensionGroup: [], globalFilters: [] });
  };

  return (
    <div className="renderDataTable">
      <div className="dataTableBox">
        <Form form={form} layout="vertical">
          <Form.Item
            label={t('analysisCenter-qBWD0P4Tge7x')}
            name="tableId"
            rules={[{ required: true, message: t('analysisCenter-2YVuEMs73LVx') }]}
          >
            <Select
              placeholder={t('analysisCenter-2YVuEMs73LVx')}
              showSearch
              allowClear
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
            >
              {tableData.map((item) => {
                return (
                  <Option key={item.table.id} value={item.table.id}>
                    {item.table.displayName}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
        </Form>
      </div>
      <div className="buttons">
        {/* <Button >取消</Button> */}
        <Button type="primary" onClick={clickDataTable}>
          {t('analysisCenter-kR4hfc3j6gJ2')}
        </Button>
      </div>
    </div>
  );
}
