/*
 * @Author: Wxw
 * @Date: 2022-07-26 11:04:29
 * @LastEditTime: 2023-04-19 18:34:33
 * @LastEditors: 卜广硕 <EMAIL>
 * @Description:漏斗流程步骤
 * @FilePath: \Front\src\pages\home\analysisCenter\funnelAnalysis\steps\index.jsx
 */
import { Button, Dropdown, Input, message, Modal, Popover, Tabs, Typography } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
// import FunnelAnalysis from 'service/funnelAnalysis.js';
import _ from 'lodash';
import Sortable from 'sortablejs';
import { MyIconV2 } from 'utils/myIcon';
import { t } from 'utils/translation';
import FilterItem from '../filter/filterItem';
import { funnelAnalysisContext } from '../funnelAnalysisContext';
import icon from '../icon';
import Campaign from './campaign';
import DataTable from './dataTable';
import Event from './event';
import './index.scss';

const { Text } = Typography;
export default function Steps() {
  const { state, dispatch, scenarioList } = useContext(funnelAnalysisContext);
  const { stepList, dimensionGroup } = state;
  const [sort, setSort] = useState({ oldIndex: null, newIndex: null });
  const [showNameVisiable, setShowNameVisiable] = useState(false);
  const [editSteps, setEditSteps] = useState(stepList);
  const [editName, setEditName] = useState({
    name: null,
    step: null,
    flag: false,
    index: null,
    ind: null,
    defaultName: null
  });

  useEffect(() => {
    setEditSteps(stepList);
  }, [stepList]);

  useEffect(() => {
    const { oldIndex, newIndex } = sort;
    if (oldIndex !== null && newIndex !== null) {
      const _stepList = _.cloneDeep(stepList);
      _stepList.splice(newIndex, 0, _stepList.splice(oldIndex, 1)[0]);
      _stepList.map((item, index) => (item.step = `${index + 1}`));
      dispatch({ stepList: _stepList });
    }
    dispatch({ dimensionGroup: [], globalFilters: [] });
  }, [sort]);

  const addStep = () => {
    const id = `${stepList.length + 1}`;
    dispatch({
      stepList: [
        ...stepList,
        {
          name: t('analysisCenter-Rc0jBDYd8o4D'),
          step: id,
          visibleSetp: false,
          type: 'EVENT',
          filters: []
        }
      ],
      dimensionGroup: [],
      globalFilters: []
    });
  };

  /**
   * @description: 下拉栏操作
   * @param {*} stepId 步骤id
   * @param {*} flag 是否多路径对比
   * @param {*} index 步骤索引
   * @param {*} ind 对比索引
   * @return {*}
   */
  const menu = (stepId, flag, index, ind) => {
    return [
      {
        label: t('analysisCenter-eAWRYLDbQ19D'),
        onClick: () => rename(stepId, flag, index, ind),
        key: 'rename'
      },
      {
        label: t('analysisCenter-UcWtnbKfdmeQ'),
        onClick: () => multipath(stepId),
        key: 'multipath',
        disabled: !_.isEmpty(dimensionGroup)
      }
    ];
  };

  const rename = (stepId, flag, index, ind) => {
    setEditName({
      step: stepId,
      flag,
      index,
      ind,
      name: flag
        ? !_.isEmpty(editSteps[index].multiStepList[ind].displayName)
          ? editSteps[index].multiStepList[ind].displayName
          : editSteps[index].multiStepList[ind].name
        : !_.isEmpty(editSteps[index].displayName)
          ? editSteps[index].displayName
          : editSteps[index].name,
      defaultName: flag ? editSteps[index].multiStepList[ind].name : editSteps[index].name
    });
    setShowNameVisiable(true);
  };

  /**
   * @description: 多路径对比处理 首先检测是否有多路径
   * @param {*} stepId
   * @return {*}
   */
  const multipath = (stepId) => {
    const _stepList = _.cloneDeep(stepList);
    dispatch({ dimensionGroup: [], globalFilters: [] });
    const findMultiStep = _.find(_stepList, { multiStep: true });
    const findIndex = _.findIndex(_stepList, { step: stepId });

    if (!_stepList[findIndex]?.multiStep) {
      if (!_.isEmpty(findMultiStep)) {
        return message.warning(t('analysisCenter-vQrTdA30CnKx'));
      }
      const multipthStep = {
        step: stepId,
        multiStep: true,
        multiStepList: [
          {
            ..._stepList[findIndex],
            visibleSetp: false,
            step: '1'
          },
          {
            step: '2',
            name: t('analysisCenter-Rc0jBDYd8o4D'),
            visibleSetp: false,
            type: 'EVENT'
          }
        ]
      };
      _stepList[findIndex] = multipthStep;
      dispatch({ stepList: _stepList });
    } else {
      if (_stepList[findIndex].multiStepList.length >= 5) {
        return message.error(t('analysisCenter-HcED0OZUNlnl'));
      }
      _stepList[findIndex].multiStepList.push({
        step: `${_stepList[findIndex].multiStepList.length + 1}`,
        visibleSetp: false,
        name: t('analysisCenter-Rc0jBDYd8o4D'),
        type: 'EVENT',
        event: {}
      });
      dispatch({ stepList: _stepList });
    }
  };

  // 删除筛选
  const delSteps = (stepId) => {
    const newSteps = _.filter(stepList, (item) => item.step !== stepId);
    const newStepsSort = newSteps.map((item, index) => {
      return { ...item, step: index + 1 };
    });
    dispatch({ stepList: newStepsSort });
  };

  // 删除多路径对比
  const delMultiStep = (stepIndex, multiStepIndex) => {
    const _stepList = _.cloneDeep(stepList);
    _stepList[stepIndex].multiStepList.splice(multiStepIndex, 1);
    if (_stepList[stepIndex].multiStepList.length === 1) {
      _stepList[stepIndex] = {
        ..._stepList[stepIndex].multiStepList[0],
        multiStep: false
      };
      _stepList.map((item, index) => {
        return (item.step = `${index + 1}`);
      });
    } else {
      _stepList[stepIndex].multiStepList.map((item, index) => {
        return (item.step = `${index + 1}`);
      });
    }
    dispatch({ stepList: _stepList });
  };

  // 添加过滤条件
  const openDrawer = (index, ind) => {
    const _stepList = _.cloneDeep(stepList);
    if (!_.isNaN(parseInt(ind))) {
      if (_stepList[index].multiStepList[ind].filters) {
        _stepList[index].multiStepList[ind].filters = [
          ..._stepList[index].multiStepList[ind].filters,
          { id: _stepList[index].multiStepList[ind].filters.length + 1 }
        ];
      } else {
        _stepList[index].multiStepList[ind].filters = [{ id: 1 }];
      }
    } else {
      if (_stepList[index].filters) {
        _stepList[index].filters = [..._stepList[index].filters, { id: _stepList[index].filters.length + 1 }];
      } else {
        _stepList[index].filters = [{ id: 1 }];
      }
    }
    dispatch({ stepList: _stepList });
    // dispatch({
    //   stepList: stepList.map(item => {
    //     if (item.step === id) {
    //       return { ...item, open: !item.open };
    //     }
    //     return item;
    //   })
    // });
  };

  /**
   * @description: 点击步骤
   * @param {*} id 步骤id
   * @param {*} flag 是否是多路径对比
   * @param {*} flag 是否是多路径对比
   * @return {*}
   */
  const handleVisibleChange = async (stepIndex, flag, multiStepIndex) => {
    if (flag) {
      const _stepList = _.cloneDeep(stepList);
      _stepList[stepIndex].multiStepList[multiStepIndex].visibleSetp =
        !_stepList[stepIndex].multiStepList[multiStepIndex].visibleSetp;
      dispatch({ stepList: _stepList });
    } else {
      const _steps = _.cloneDeep(stepList);
      _steps[stepIndex].visibleSetp = !_steps[stepIndex].visibleSetp;
      dispatch({
        stepList: _steps
      });
    }
  };

  const changeTabs = (e, index, flag, i, ind) => {
    const _steps = _.cloneDeep(editSteps);
    const { step, visibleSetp } = _steps[index];
    // debugger;
    if (flag) {
      _steps[index].multiStepList[ind] = {
        type: e,
        step,
        name: t('analysisCenter-Rc0jBDYd8o4D'),
        visibleSetp,
        campaignList: [],
        event: {},
        bizTable: {}
      };
    } else {
      _steps[index] = {
        // ..._steps[index],
        type: e,
        step,
        name: t('analysisCenter-Rc0jBDYd8o4D'),
        visibleSetp,
        campaignList: [],
        event: {},
        bizTable: {}
      };
    }

    // debugger;
    setEditSteps(_steps);
  };

  /**
   * @description: 设置漏斗步骤
   * @param {*} item  漏斗步骤
   * @param {*} index 下标
   * @param {*} flag 是否是多路径对比
   * @param {*} i 对应的步骤
   * @param {*} ind 多路径下标
   * @return {*}
   */
  const RenderSetStep = (item, index, flag, i, ind) => {
    return (
      <div className="stepItem">
        <Tabs
          defaultActiveKey={flag ? item.multiStepList[ind].type : item.type}
          onChange={(e) => changeTabs(e, index, flag, i, ind)}
          destroyInactiveTabPane
          items={[
            {
              label: (
                <span>
                  {' '}
                  <MyIconV2 type="icon-icon-event" style={{ marginRight: 0, fontSize: 16 }} />{' '}
                  {t('analysisCenter-8jvAspN2rFBd')}
                </span>
              ),
              key: 'EVENT',
              children: <Event id={item.step} stepIndex={index} flag={flag} i={i} ind={ind} />
            },
            {
              label: (
                <span>
                  {' '}
                  <MyIconV2 type="icon-icon-datefile" style={{ marginRight: 0, fontSize: 16 }} />{' '}
                  {t('analysisCenter-qBWD0P4Tge7x')}
                </span>
              ),
              key: 'BIZ_TABLE',
              children: <DataTable id={item.step} stepIndex={index} flag={flag} i={i} ind={ind} />
            },
            {
              label: (
                <span>
                  {' '}
                  <MyIconV2 type="icon-icon-camping" style={{ marginRight: 0, fontSize: 16 }} />{' '}
                  {t('analysisCenter-tfKyeg9IiqkL')}
                </span>
              ),
              key: 'CAMPAIGN',
              children: <Campaign id={item.step} stepIndex={index} isMulti={flag} i={i} ind={ind} />
            }
          ]}
        />
      </div>
    );
  };

  const renderTitle = (name, displayName) => {
    const title = !_.isEmpty(displayName) ? displayName : name;
    return (
      // <Popover
      //   getPopupContainer={() => document.getElementsByClassName('content')[0]}
      //   content={<div>13321312</div>}
      //   trigger="hover"
      //   destroyTooltipOnHide
      //   overlayStyle={{ minWidth: '320px' }}
      //   autoAdjustOverflow
      //   placement="right"
      //   className="conversion"
      // >
      // </Popover>
      <Text
        style={{
          width: 'calc(100% - 20px)',
          color: title === t('analysisCenter-Rc0jBDYd8o4D') ? 'rgba(0,0,0,.25)' : 'rgba(0,0,0,.85)'
        }}
        ellipsis={{ tooltip: title }}
      >
        {title}
      </Text>
    );
  };

  const handleOk = (reset) => {
    const { name, flag, index, ind, defaultName } = editName;
    const _steps = _.cloneDeep(stepList);
    if (flag) {
      _steps[index].multiStepList[ind].displayName = !reset
        ? name === ''
          ? _steps[index].multiStepList[ind].name
          : name
        : defaultName;
    } else {
      _steps[index].displayName = !reset ? (name === '' ? _steps[index].name : name) : defaultName;
    }
    dispatch({ stepList: _steps });
    setShowNameVisiable(false);
  };

  const handleCancel = () => {
    setShowNameVisiable(false);
  };

  /**
   * @description:
   * @param {*} type 类型
   * @return {*}
   */
  const renderIcon = (type) => {
    if (type === 'EVENT') {
      return <MyIconV2 type="icon-icon-event" />;
    } else if (type === 'BIZ_TABLE') {
      return <MyIconV2 type="icon-icon-datefile" />;
    } else if (type === 'CAMPAIGN') {
      return <MyIconV2 type="icon-icon-camping" />;
    }
  };

  const sortableGroupDecorator = async () => {
    const tbody = document.getElementsByClassName('steps')[0];
    Sortable.create(tbody, {
      animation: 400,
      dataIdAttr: 'title',
      draggable: '.step',
      onEnd: async (evt) => {
        const { oldIndex, newIndex } = evt;
        setSort({ oldIndex, newIndex });
      }
    });
  };

  // hover 类名conversion 显示stepTitle
  // const handleHover = (id) => {
  // const showStep = document.getElementsByClassName(`showStep${id}`)[0];
  // if (showStep) showStep.style.display = 'block';
  // };

  // const handleonMouseOut = (id) => {
  // const showStep = document.getElementsByClassName(`showStep${id}`)[0];
  // if (showStep) showStep.style.display = 'none';
  // };

  return (
    <funnelAnalysisContext.Provider
      value={{
        state,
        dispatch,
        handleVisibleChange,
        editSteps,
        setEditSteps,
        scenarioList
      }}
    >
      <div className="steps" ref={sortableGroupDecorator}>
        {_.map(stepList, (item, index) => {
          if (item?.multiStep) {
            // return <div className="step" key={`${JSON.stringify(item)}`}>
            return (
              <div className="step" key={`${item.step}-${item.name}`}>
                {item.multiStepList.map((i, ind) => {
                  return (
                    <div key={ind}>
                      <div className="stepsItem">
                        <div className="sort">
                          {item.step}-{i.step}
                        </div>
                        <div className="mouse">{renderIcon(i.type)}</div>
                        <div className="stepContent">
                          <Popover
                            getPopupContainer={() => document.getElementsByClassName('content')[0]}
                            content={RenderSetStep(item, index, true, i, ind)}
                            trigger="click"
                            destroyTooltipOnHide
                            overlayStyle={{ minWidth: '320px' }}
                            autoAdjustOverflow
                            open={i.visibleSetp === true}
                            onOpenChange={() => handleVisibleChange(index, true, ind)}
                            placement="bottom"
                            className="conversion"
                          >
                            <div className="stepTitle">{renderTitle(i.name, i?.displayName)}</div>
                          </Popover>
                        </div>
                        <div className="icons">
                          <div className="stepIcon">
                            {/* <FilterOutlined /> */}
                            <span onClick={() => openDrawer(index, ind)}>
                              <MyIconV2 type="icon-icon-filter" />
                            </span>
                            <span onClick={() => delMultiStep(index, ind)}>
                              <MyIconV2 type="icon-icon-close" />
                            </span>
                          </div>
                          <div className="more">
                            <Dropdown
                              getPopupContainer={(triggerNode) => triggerNode.parentNode}
                              menu={{
                                items: menu(item.step, true, index, ind)
                              }}
                            >
                              {icon.MORE}
                            </Dropdown>
                          </div>
                        </div>
                      </div>
                      {item.multiStepList[ind].filters &&
                        item.multiStepList[ind].filters.map((filterItem, filterIndex) => {
                          return (
                            <div className="drawerItem" key={filterIndex}>
                              <FilterItem
                                index={index}
                                ind={ind}
                                filterIndex={filterIndex}
                                value={item.multiStepList[ind]?.filters}
                                isMulti
                              />
                            </div>
                          );
                        })}
                    </div>
                  );
                })}
              </div>
            );
          } else {
            // return <div className="step" key={`${JSON.stringify(item)}`}>
            return (
              <div className="step" key={`${item.step}-${item.name}`}>
                <div className="stepsItem">
                  <div className="sort">{item.step}</div>
                  <div className="mouse">{renderIcon(item.type)}</div>
                  <div
                    className="stepContent"
                    // onMouseOver={() => handleHover(item.step)}
                    // onMouseOut={() => handleonMouseOut(item.step)}
                    // onFocus={() => 0}
                    // onBlur={() => 0}
                  >
                    {/* <div className="stepTitle">{renderTitle(item)}</div> */}
                    <Popover
                      getPopupContainer={() => document.getElementsByClassName('content')[0]}
                      content={RenderSetStep(item, index)}
                      trigger="click"
                      destroyTooltipOnHide
                      overlayStyle={{ minWidth: '320px' }}
                      autoAdjustOverflow
                      open={item.visibleSetp === true}
                      onOpenChange={() => handleVisibleChange(index)}
                      placement="bottom"
                      className="conversion"
                    >
                      <div className="stepTitle">{renderTitle(item.name, item?.displayName)}</div>
                    </Popover>
                  </div>
                  <div className="icons">
                    <div className="stepIcon">
                      {/* <FilterOutlined /> */}
                      <span onClick={() => openDrawer(index)}>
                        <MyIconV2 type="icon-icon-filter" />
                      </span>
                      <span onClick={() => delSteps(item.step)}>
                        <MyIconV2 type="icon-icon-close" />
                      </span>
                    </div>
                    <div className="more">
                      <Dropdown
                        getPopupContainer={(triggerNode) => triggerNode.parentNode}
                        menu={{ items: menu(item.step, false, index) }}
                      >
                        {icon.MORE}
                      </Dropdown>
                    </div>
                  </div>
                  {/* <div className={`stepHover showStep${item.id}`}>假装是个弹窗</div> */}
                </div>
                {item.filters &&
                  item.filters.map((filterItem, filterIndex) => {
                    return (
                      <div className="drawerItem" key={filterIndex}>
                        <FilterItem index={index} filterIndex={filterIndex} value={item.filters} />
                      </div>
                    );
                  })}
              </div>
            );
          }
        })}
      </div>
      <Button className="addButton" onClick={addStep} type="dashed" block>
        +{t('analysisCenter-opQ63PmpzZEF')}
      </Button>
      <Modal
        title={<h3>{t('analysisCenter-eAWRYLDbQ19D')}</h3>}
        open={showNameVisiable}
        // onOk={handleOk}
        onCancel={() => setShowNameVisiable(false)}
        footer={[
          <Button key="reset" onClick={() => handleOk(true)}>
            {t('analysisCenter-UkhB5UNcLALw')}
          </Button>,
          <Button key="back" onClick={handleCancel}>
            {t('analysisCenter-8luMKTPWOOa3')}
          </Button>,
          <Button key="submit" type="primary" onClick={() => handleOk(false)}>
            {t('analysisCenter-AiB7tX14bG7b')}
          </Button>
        ]}
      >
        <Input
          value={editName.name}
          maxLength={30}
          onChange={(e) =>
            setEditName({
              ...editName,
              name: e.target.value
            })
          }
        />
      </Modal>
    </funnelAnalysisContext.Provider>
  );
}
