import { t } from 'utils/translation';

export default {
  maxFilterCount: 20,
  operatorList: [
    {
      name: t('analysisCenter-T966eKedwlSa'),
      operator: 'EQ'
    },
    {
      name: t('analysisCenter-ME8eWTpNAYGt'),
      operator: 'NE'
    },
    {
      name: t('analysisCenter-XNmRyIbFUzrz'),
      operator: 'GT'
    },
    {
      name: t('analysisCenter-qGazH6RkyZNY'),
      operator: 'GTE'
    },
    {
      name: t('analysisCenter-CioSjf29PpuY'),
      operator: 'LT'
    },
    {
      name: t('analysisCenter-gUaOuqbZnQeC'),
      operator: 'LTE'
    },
    {
      name: t('analysisCenter-OIxaQ4H6SPeX'),
      operator: 'BETWEEN'
    },
    {
      name: t('analysisCenter-h4MfH1A1vKQB'),
      operator: 'ADVANCED_BETWEEN'
    },
    {
      name: t('analysisCenter-A6QbtfYSebh6'),
      operator: 'IN'
    },
    {
      name: t('analysisCenter-nErXXFMSqPRa'),
      operator: 'NOT_IN'
    },
    {
      name: t('analysisCenter-OTdBbJuWJGYQ'),
      operator: 'IS_NOT_NULL'
    },
    {
      name: t('analysisCenter-Ze926L8fB9T5'),
      operator: 'IS_NULL'
    },
    {
      name: t('analysisCenter-QM4c0rEXGRBy'),
      operator: 'LIKE'
    },
    {
      name: t('analysisCenter-gQqkhBtPB0AE'),
      operator: 'NOT_LIKE'
    },
    {
      name: t('analysisCenter-QdO0XnKBZiDx'),
      operator: 'START_WITH'
    },
    {
      name: t('analysisCenter-S1aVwTnukHyD'),
      operator: 'NOT_START_WITH'
    },
    {
      name: t('analysisCenter-AiR4quM48nIE'),
      operator: 'END_WITH'
    },
    {
      name: t('analysisCenter-EdcBUsDlgALk'),
      operator: 'NOT_END_WITH'
    },
    {
      name: t('analysisCenter-1LeHuH2BW9mm'),
      operator: 'IS_TRUE'
    },
    {
      name: t('analysisCenter-OiCiDIVFUxpj'),
      operator: 'IS_FALSE'
    }
  ],
  typeOperator: {
    INT: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    LONG: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    DOUBLE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    HIVE_DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
    DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
    DATETIME: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
    HIVE_TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
    TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
    STRING: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'LIKE',
      'NOT_LIKE',
      'START_WITH',
      'NOT_START_WITH',
      'END_WITH',
      'NOT_END_WITH',
      'IN',
      'NOT_IN'
    ],
    BOOL: ['IS_TRUE', 'IS_FALSE']
  },
  typeFormatter: {
    DATE: 'YYYY-MM-DD',
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    TIMESTAMP: 'YYYY-MM-DD HH:mm:ss',
    HIVE_DATE: 'YYYY-MM-DD',
    HIVE_TIMESTAMP: 'YYYY-MM-DD HH:mm:ss'
  },
  connector: [
    {
      name: t('analysisCenter-GJmxUTeCyX42'),
      value: 'AND'
    },
    {
      name: t('analysisCenter-YO01TiFpUWiV'),
      value: 'OR'
    }
  ],
  validator: {
    STRING: {
      option: {
        required: true,
        maxLen: 500
      },
      message: {
        required: t('analysisCenter-JQkO0xZWlCiV'),
        maxLen: t('analysisCenter-XFozzBJ41VT4')
      }
    },
    INT: {
      option: {
        required: true,
        maxLen: 11,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: t('analysisCenter-JQkO0xZWlCiV'),
        maxLen: t('analysisCenter-Uv9ZPlwU16Jd'),
        regex: t('analysisCenter-Vv1BhJWTOor5')
      }
    },
    LONG: {
      option: {
        required: true,
        maxLen: 20,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: t('analysisCenter-JQkO0xZWlCiV'),
        maxLen: t('analysisCenter-N5jBKBAZm4Lc'),
        regex: t('analysisCenter-Vv1BhJWTOor5')
      }
    },
    DOUBLE: {
      option: {
        required: true,
        // regex: '^\\d*[.]?\\d*$',
        regex: '^[+-]?\\d+(\\.\\d+)?$',
        maxLen: 20
      },
      message: {
        required: t('analysisCenter-JQkO0xZWlCiV'),
        maxLen: t('analysisCenter-N5jBKBAZm4Lc'),
        regex: t('analysisCenter-LDtHhBxb2Okp')
      }
    },
    DATETIME: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-UUFXLh8J0tmQ')
      }
    },
    TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-UUFXLh8J0tmQ')
      }
    },
    DATE: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-DWLYxo36v23V')
      }
    },
    HIVE_TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-UUFXLh8J0tmQ')
      }
    },
    HIVE_DATE: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-DWLYxo36v23V')
      }
    },
    BOOL: {
      option: {
        required: false
      },
      message: {
        required: t('analysisCenter-qyCFEbP0NJbU')
      }
    }
  }
};
