import department from '@/service/department';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { message, Modal, Radio, Select, TreeSelect } from 'antd';
import _ from 'lodash';
import { idToName } from 'pages/home/<USER>/dataPermissions/config';
import React, { useEffect, useState } from 'react';
import CampaignV2Service from 'service/CampaignV2Service';
import { t } from 'utils/translation';
import './index.scss';

const { Option } = Select;
const campaignV2Service = new CampaignV2Service();

const UpdateDeptModal = (props) => {
  const { visible, defaultDeptId } = props;
  const [superAdminDeptList, setSuperAdminDeptList] = useState([]);
  const [departmentUserList] = useState(window.userDepartment[0]?.depts || []);
  const { getFieldDecorator } = props.form;
  useEffect(() => {
    (async () => {
      const params = {
        searches: [
          {
            operator: 'EQ',
            propertyName: 'companyId',
            value: localStorage.getItem('organizationId')
          }
        ]
      };
      const list = await department.findByCustom(params);
      const superAdminDeptList = renameChildsToChildren([list]);
      // setValue(filterValue(superAdminDeptList, defaultDeptId));
      props.form.setFieldsValue({ deptId: defaultDeptId });
      setSuperAdminDeptList(superAdminDeptList);
    })();
  }, []);
  const filterValue = (data, e) => {
    if (data?.id === e) {
      return data.routes || '';
    }
    if (data.children && Array.isArray(data.children)) {
      for (const child of data.children) {
        const result = filterValue(child, e);
        if (result) {
          return result; // 如果找到匹配项，则立即返回
        }
      }
    }
  };
  // const onSearch = _.debounce(async (e) => {
  //   const params = {
  //     searches: [
  //       {
  //         operator: 'EQ',
  //         propertyName: 'companyId',
  //         value: localStorage.getItem('organizationId')
  //       }
  //     ],
  //     idOrName: e
  //   };
  //   const list = await department.findByCustom(params);

  //   setSuperAdminDeptList(renameChildsToChildren([list || {}]));
  // }, 500);
  const renameChildsToChildren = (data) => {
    // 如果当前对象有 "childs" 属性，则重命名它为 "children"
    data.forEach((item) => {
      item.value = item.id;
      item.title = item.name;
      item.children = item.childs;
      delete item.childs;
      if (item.children) {
        renameChildsToChildren(item.children);
      }
    });
    return data;
  };

  const handleSubmit = () => {
    props.form.validateFieldsAndScroll(async (err, values) => {
      const { deptId } = values;
      if (!err) {
        const deptRoutes =
          localStorage.getItem('superAdmin') === 'true'
            ? filterValue(superAdminDeptList[0], deptId)
            : departmentUserList.filter((item) => item.id === deptId)[0]?.routes;
        const params = {
          deptRoutes,
          id: props.id,
          deptId
        };

        if (props.deptId === deptId) {
          message.error(t('operationCenter-cOdq6bqPHRVS'));
          return;
        }
        await campaignV2Service.updateDeptRoutes(params);
        if (props?.onOk) {
          props.onOk();
          return;
        }
        props.history.push('/aimarketer/home/<USER>');
        message.success(t('operationCenter-JqyLf15EssrK'));
      }
    });
  };
  return (
    <Modal
      title={t('operationCenter-rZxwuMHtksLd')}
      centered
      open={visible}
      onCancel={() => props.closeModal()}
      width={560}
      onOk={handleSubmit}
      className="updateDeptModal"
    >
      <Form colon={false} layout="vertical">
        <Form.Item label={t('operationCenter-ebayisyLPI3M')}>
          <Radio.Group defaultValue={1}>
            <Radio value={1}>{t('operationCenter-15LxkW651X3t')}</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label={t('operationCenter-eS1RAhRus9Bn')} style={{ marginBottom: 0 }}>
          {getFieldDecorator('deptId', {
            rules: [{ required: true, message: t('operationCenter-doSSdRRi57I3') }]
          })(
            localStorage.getItem('superAdmin') === 'true' ? (
              <TreeSelect
                showSearch
                style={{ width: '100%' }}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                placeholder={t('operationCenter-doSSdRRi57I3')}
                allowClear
                treeDefaultExpandAll={false} // 默认不展开所有节点
                treeDefaultExpandedKeys={[superAdminDeptList[0]?.id]}
                filterTreeNode={(value, TreeNode) => TreeNode.props.title.indexOf(value) > -1}
                // filterTreeNode={this.filterTreeNode}
                treeData={superAdminDeptList}
              />
            ) : (
              <Select
                showSearch
                style={{ width: '100%' }}
                placeholder={t('operationCenter-doSSdRRi57I3')}
                filterOption={(input, option) => {
                  // 自定义搜索逻辑
                  const label = option.props.label;
                  if (label !== undefined) {
                    return label.toString().toLowerCase().includes(input.toLowerCase());
                  }
                  return false;
                }}
              >
                {departmentUserList.map((item) => {
                  const _departmentListAll = JSON.parse(localStorage.getItem('departmentListAll'));
                  const value = _.find(_departmentListAll, {
                    id: parseInt(item?.id)
                  });
                  const deptKeyValue = JSON.parse(localStorage.getItem('deptKeyValue'));
                  const namePath = idToName(deptKeyValue, `${value.routes}${value.id}`).join(' / ');
                  return (
                    <Option key={item.id} value={item.id} label={namePath}>
                      {namePath}
                    </Option>
                  );
                })}
              </Select>
            )
          )}

          <span style={{ color: 'rgba(0, 0, 0, 0.45)' }}>{t('operationCenter-2dH8VV45CNKP')}</span>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default Form.create()(UpdateDeptModal);
