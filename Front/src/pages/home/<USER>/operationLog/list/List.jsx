/* eslint-disable react-hooks/exhaustive-deps */
import { FilterOutlined } from '@ant-design/icons';
import { Button, Descriptions, Drawer, Table } from 'antd';
import Query from 'components/query/Query';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import UserService from 'service/UserService';
import OperationLogService from 'service/operationLogService';
import { t } from 'utils/translation';
import config from '../config';
import './List.scss';

const userService = new UserService();

const grid = {
  leftCol: 24,
  rightCol: 24,
  childCol: 12
};

export default () => {
  const [param, setParam] = useState(_.cloneDeep(config.initParam));
  const [pagination, setPagination] = useState(_.cloneDeep(config.initPagination));
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isFold, setIsFold] = useState(true);
  const [userList, setUserList] = useState([]);
  const [opreateInfo, setOpreateInfo] = useState({});
  const [opreateOpen, setOpreateOpen] = useState(false);

  useEffect(() => {
    (async () => {
      const newuserList = await userService.listBy([]);
      setUserList(newuserList);
    })();
  }, []);

  useEffect(() => {
    const getList = async () => {
      try {
        setLoading(true);
        const finalParam = _.cloneDeep(param);
        finalParam.search.push({
          operator: 'EQ',
          propertyName: 'projectId',
          value: localStorage.getItem('projectId')
        });
        const result = await OperationLogService.operateLogList(finalParam);
        pagination.total = result.totalElements;
        pagination.current = param.page;
        setList(result.content);
        setLoading(false);
      } catch {
        setLoading(false);
      }
    };
    getList();
  }, [param]);

  const queryData = useCallback(
    (data) => {
      const newVal = {};
      data.forEach((n) => {
        if (n.value) newVal[n.propertyName] = n.value;
      });
      setParam({ ...param, search: data, page: 1 });
    },
    [param]
  );

  const handleTableChange = (lastpagination, filtersArg, sorter) => {
    const newParam = { ...param };
    newParam.page = lastpagination.current;
    newParam.size = lastpagination.pageSize;
    newParam.sorts = [
      {
        propertyName: Array.isArray(sorter.field) ? sorter.field.join('.') : sorter.field || 'timestamp',
        direction: sorter.order === 'ascend' ? 'asc' : 'desc'
      }
    ];
    setPagination({ ...pagination, ...lastpagination });
    setParam(newParam);
  };

  const onOperateView = (render) => {
    setOpreateOpen(true);
    setOpreateInfo(render);
  };

  const onClose = () => {
    setOpreateOpen(false);
    setOpreateInfo({});
  };

  /** 表头数据 */
  const columns = [
    // {
    //   title: 'ID',
    //   dataIndex: 'id',
    //   fixed: 'left',
    //   width: 60
    // },
    {
      title: t('setting-vA7jk19VR02B'),
      dataIndex: 'action',
      width: 200
    },
    {
      title: t('setting-q8XsnKAWecVB'),
      dataIndex: 'actionGroup',
      width: 200
    },
    {
      title: t('setting-qxorSnmzsSp9'),
      dataIndex: 'content',
      width: 200,
      render: (text) => <div className="whitespace-pre-line">{text}</div>
    },
    {
      title: t('setting-fJtcqWKse46T'),
      dataIndex: 'status',
      width: 80,
      render: (text) => <div>{text === 'SUCCESS' ? t('setting-p2UlKAysgk4K') : t('setting-jseapR7zgtxc')}</div>
    },
    {
      title: t('setting-Dl68muwyJYCP'),
      dataIndex: 'userName',
      width: 200
    },
    {
      title: t('setting-FMUUwXiNttF3'),
      dataIndex: 'timestamp',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 200,
      sorter: true,
      defaultSortOrder: 'descend'
    },
    {
      title: t('setting-VMkTPMeYPc3q'),
      width: 80,
      fixed: 'right',
      render: (text, render) => <a onClick={() => onOperateView(render)}>{t('setting-boymt6HqJgzR')}</a>
    }
  ];

  const configs = [
    {
      name: t('setting-vA7jk19VR02B'),
      nodeType: 'input',
      operator: 'LIKE',
      propertyName: 'action'
    },
    {
      name: t('setting-fJtcqWKse46T'),
      nodeType: 'select',
      operator: 'EQ',
      propertyName: 'status',
      options: [
        { name: t('setting-p2UlKAysgk4K'), value: 'SUCCESS' },
        { name: t('setting-jseapR7zgtxc'), value: 'FAIL' }
      ]
    },
    {
      name: t('setting-Dl68muwyJYCP'),
      nodeType: 'select',
      operator: 'EQ',
      options: userList.map((n) => ({ name: n.name, value: n.id })),
      propertyName: 'userId'
    },
    {
      name: t('setting-FMUUwXiNttF3'),
      nodeType: 'dateRange',
      operator: 'DATE_BETWEEN',
      propertyName: 'timestamp'
    },
    {
      name: t('setting-q8XsnKAWecVB'),
      nodeType: 'input',
      operator: 'LIKE',
      propertyName: 'actionGroup'
    },
    {
      name: t('setting-qxorSnmzsSp9'),
      nodeType: 'input',
      operator: 'LIKE',
      propertyName: 'content'
    }
  ];

  return (
    <div className="memberPermisstion">
      <header>
        <h1>{t('setting-K6sR3duNrgAN')}</h1>
        <div className="btnGroup">
          <Button onClick={() => setIsFold(!isFold)}>
            <FilterOutlined style={{ fontSize: '16px' }} />
            {t('setting-nLk4uLSTs9Ny')}
          </Button>
        </div>
      </header>
      <Query isFold={isFold} config={configs} grid={grid} queryData={queryData} />
      <div className="table1">
        <Table
          columns={columns}
          dataSource={list}
          bordered={false}
          loading={loading}
          onChange={handleTableChange}
          pagination={pagination}
          rowKey="id"
          scroll={{ x: 1300 }}
        />
      </div>

      <Drawer title={t('setting-gWlKghAae4AD')} width={600} onClose={onClose} open={opreateOpen}>
        <div>
          <Descriptions column={1}>
            <Descriptions.Item label={t('setting-vA7jk19VR02B')}>{opreateInfo.action}</Descriptions.Item>
            <Descriptions.Item label={t('setting-q8XsnKAWecVB')}>{opreateInfo.actionGroup}</Descriptions.Item>
            <Descriptions.Item label={t('setting-qxorSnmzsSp9')} className="whitespace-pre-line">
              {opreateInfo.content}
            </Descriptions.Item>
            <Descriptions.Item label={t('setting-TNfsPEJnlieo')}>{opreateInfo.menu1}</Descriptions.Item>
            <Descriptions.Item label={t('setting-fOr4D9x9gZrC')}>{opreateInfo.menu2}</Descriptions.Item>
            <Descriptions.Item label={t('setting-5Ysscwu0mps5')}>{opreateInfo.url}</Descriptions.Item>
            <Descriptions.Item label={t('setting-7wE7SJKVEWMU')}>{opreateInfo.referer}</Descriptions.Item>
            <Descriptions.Item label="IP">{opreateInfo.ip}</Descriptions.Item>
            <Descriptions.Item label={t('setting-fJtcqWKse46T')}>
              {opreateInfo.status === 'SUCCESS' ? t('setting-p2UlKAysgk4K') : t('setting-jseapR7zgtxc')}
            </Descriptions.Item>
            <Descriptions.Item label={t('setting-Dl68muwyJYCP')}>{opreateInfo.userName}</Descriptions.Item>
            <Descriptions.Item label={t('setting-FMUUwXiNttF3')}>
              {opreateInfo.timestamp && dayjs(opreateInfo.timestamp).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
          </Descriptions>
        </div>
      </Drawer>
    </div>
  );
};
