export default {
  cn: {
  "dataCenter-inzt6CphMHu8": "调度日志",
  "dataCenter-T4kAWotglEMp": " ID类型：",
  "dataCenter-SejvXQqVJR5l": "全部",
  "dataCenter-bbVgoBtHBiGd": "统计时间",
  "dataCenter-Tpzmf5qoYOlb": "数据范围： 起始时间",
  "dataCenter-jCYiZ3OfSKbT": "刷新",
  "dataCenter-2dDe8owTT1aO": "日期",
  "dataCenter-panewtmjrpNL": "覆盖人数",
  "dataCenter-m2SvayOJvrqm": "覆盖去重数",
  "dataCenter-QdierR2Aqw5s": "数据",
  "dataCenter-XBEcpssmjDo8": "覆盖人次趋势",
  "dataCenter-SyaX3Egd7uDz": "按每日进入起始节点的进行累计统计",
  "dataCenter-1OpJT4n7e2Az": "覆盖去重数趋势",
  "dataCenter-BfLOOYlj90O0": "按每日进入起始节点的进行去重统计，不同运营场景累加计算",
  "dataCenter-RBIpeAbxkk1f": "总数",
  "dataCenter-pM2GkvjKhcca": "草稿",
  "dataCenter-1LJMZ78R6GqA": "测试中",
  "dataCenter-8NidkwlGOAky": "测试完成",
  "dataCenter-e2Bw5PVb6n3o": "运行中",
  "dataCenter-JA1n7RoKPuff": "已结束",
  "dataCenter-Gf77WjzAkk6F": "总任务数",
  "dataCenter-dpVowZExvagC": "待运行",
  "dataCenter-44iNEwHFtUcQ": "运行中",
  "dataCenter-RrmSMKyKjJHV": "自动结束",
  "dataCenter-4hXRqyaQXEhX": "手动终止",
  "dataCenter-Zl3MiQ7nK1Uz": "运行失败",
  "dataCenter-ynV2ZSE5HEcz": "停止中",
  "dataCenter-7IJTHppwqCSM": "撤回中",
  "dataCenter-8VbWiooQVtiD": "撤回成功",
  "dataCenter-1ph7UZqfhHNe": "总览",
  "dataCenter-jTLCUNdekcQm": "覆盖人次",
  "dataCenter-NiBchCrqnCAb": "覆盖用户的次数，如一个用户参与多次将被计算多次",
  "dataCenter-bhdXtA48zo8u": "覆盖去重数",
  "dataCenter-rVqukJdKwX09": "覆盖用户的人数，如一个用户参与多次只会被计算一次",
  "dataCenter-Q613e47chwWG": "阶段",
  "dataCenter-x1woQQczUTrp": "任务运行状态",
  },
  en: {
 "dataCenter-inzt6CphMHu8": "Schedule Log",
  "dataCenter-T4kAWotglEMp": "ID Type:",
  "dataCenter-SejvXQqVJR5l": "All",
  "dataCenter-bbVgoBtHBiGd": "Statistical Time",
  "dataCenter-Tpzmf5qoYOlb": "Data Range: Start Time",
  "dataCenter-jCYiZ3OfSKbT": "Refresh",
  "dataCenter-2dDe8owTT1aO": "Date",
  "dataCenter-panewtmjrpNL": "Coverage Users",
  "dataCenter-m2SvayOJvrqm": "Coverage Distinct Users",
  "dataCenter-QdierR2Aqw5s": "Data",
  "dataCenter-XBEcpssmjDo8": "Coverage Users Trend",
  "dataCenter-SyaX3Egd7uDz": "Cumulative statistics by the number of users entering the starting node each day",
  "dataCenter-1OpJT4n7e2Az": "Coverage Distinct Users Trend",
  "dataCenter-BfLOOYlj90O0": "Cumulative statistics by the number of users entering the starting node each day, different operation scenarios are added and calculated",
  "dataCenter-RBIpeAbxkk1f": "Total",
  "dataCenter-pM2GkvjKhcca": "Draft",
  "dataCenter-1LJMZ78R6GqA": "Testing",
  "dataCenter-8NidkwlGOAky": "Test Completed",
  "dataCenter-e2Bw5PVb6n3o": "Running",
  "dataCenter-JA1n7RoKPuff": "Ended",
  "dataCenter-Gf77WjzAkk6F": "Total Tasks",
  "dataCenter-dpVowZExvagC": "Pending",
  "dataCenter-44iNEwHFtUcQ": "Running",
  "dataCenter-RrmSMKyKjJHV": "Automatic End",
  "dataCenter-4hXRqyaQXEhX": "Manual Termination",
  "dataCenter-Zl3MiQ7nK1Uz": "Running Failed",
  "dataCenter-ynV2ZSE5HEcz": "Stopping",
  "dataCenter-7IJTHppwqCSM": "Withdrawing",
  "dataCenter-8VbWiooQVtiD": "Withdrawal Success",
  "dataCenter-1ph7UZqfhHNe": "Overview",
  "dataCenter-jTLCUNdekcQm": "Coverage Users",
  "dataCenter-NiBchCrqnCAb": "The number of users covered, if a user participates multiple times will be calculated multiple times",
  "dataCenter-bhdXtA48zo8u": "Coverage Distinct Users",
  "dataCenter-rVqukJdKwX09": "Coverage Users",
  "dataCenter-Q613e47chwWG": "Stage",
  "dataCenter-x1woQQczUTrp": "Task Running Status",
  }
};
