import { QuestionCircleOutlined } from '@ant-design/icons';
import { Popover, Select, Tabs, Tooltip } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import TagAndTagValueService from 'service/tagAndTagValueService';
import { MyIconV2 } from 'utils/myIcon';
import { t } from 'utils/translation';
import Tag from '../filter/TabPane/Tag/tag';
import Campaign from '../filter/TabPane/campaign/campaign';
import Event from '../filter/TabPane/event/event';
import TableField from '../filter/TabPane/tableField/tableField';
import CampaignValue from './campaignValue/campaign';
import './dimensionItem.scss';
import UserGroup from './userGroup/userGroup';

import { retentionContext } from '../retentionContext';

export default function Dimension(props) {
  const { dimensionIndex } = props;
  const { state, dispatch } = useContext(retentionContext);
  const { stepList, dimensionGroup, scenarioId } = state;
  const [visible, setVisible] = useState(false);
  const [editDimensionGroup, setEditDimensionGroup] = useState(null);
  const [valueVisible, setValueVisible] = useState(false);
  const [tagValue, setTagValue] = useState([]);

  const RenderFilter = () => {
    const style = {
      marginRight: 0,
      fontSize: 16
    };
    const showTabPane = {
      event: [
        t('analysisCenter-Lx16QhyxC8aO'),
        t('analysisCenter-kfZOSErJDn9i'),
        t('analysisCenter-dKSR7Rrr2ZRK'),
        t('analysisCenter-IgB1zAFkeCfh')
      ],
      bizTable: [t('analysisCenter-kfZOSErJDn9i'), t('analysisCenter-dKSR7Rrr2ZRK'), t('analysisCenter-IgB1zAFkeCfh')],
      campaignList: [t('analysisCenter-kfZOSErJDn9i'), t('analysisCenter-dKSR7Rrr2ZRK')],
      all: [
        t('analysisCenter-NcyILORFWK67'),
        t('analysisCenter-kfZOSErJDn9i'),
        t('analysisCenter-dKSR7Rrr2ZRK'),
        t('analysisCenter-oqUeslY0V4Zy'),
        t('analysisCenter-IgB1zAFkeCfh')
      ],
      EVENT: [
        t('analysisCenter-Lx16QhyxC8aO'),
        t('analysisCenter-kfZOSErJDn9i'),
        t('analysisCenter-dKSR7Rrr2ZRK'),
        t('analysisCenter-IgB1zAFkeCfh')
      ],
      BIZ_TABLE: [t('analysisCenter-kfZOSErJDn9i'), t('analysisCenter-dKSR7Rrr2ZRK'), t('analysisCenter-IgB1zAFkeCfh')],
      CAMPAIGN: [t('analysisCenter-kfZOSErJDn9i'), t('analysisCenter-dKSR7Rrr2ZRK')]
    };
    // 本期bizTable 没有表字段

    // // 在这里处理格式, 判定是否是空数组或者是空对象的给过滤掉
    // let obj = flag ? globalFilters : isMulti ? stepList[index].multiStepList[ind] : stepList[index];
    // let newObj = Object.keys(obj).reduce((acc, cur) => {
    //   if (obj[cur] && obj[cur].length !== 0) {
    //     acc[cur] = obj[cur];
    //   }
    //   return acc;
    // }, {});
    // let keys = Object.keys(newObj);
    let typeArr = [];
    let showType = null;
    showType = 'all';
    stepList.forEach((item) => {
      typeArr.push(item.type);
    });
    typeArr = [...new Set(typeArr)];
    const type = [];
    typeArr.forEach((item) => {
      showTabPane[item].forEach((i) => {
        type.push(i);
      });
    });
    // 找到type里面出现了typeArr.length次的key
    const obj = {};
    type.forEach((item) => {
      if (obj[item]) {
        obj[item]++;
      } else {
        obj[item] = 1;
      }
    });
    const arr = [];
    for (const key in obj) {
      if (obj[key] === typeArr.length) {
        arr.push(key);
      }
    }
    showType = arr;
    const types = {
      USER_PROPERTIES: t('analysisCenter-Lx16QhyxC8aO'),
      USER_LABEL: t('analysisCenter-kfZOSErJDn9i'),
      SEGMENT: t('analysisCenter-dKSR7Rrr2ZRK'),
      TABLE_FIELD: t('analysisCenter-oqUeslY0V4Zy'),
      CAMPAIGN: t('analysisCenter-IgB1zAFkeCfh')
    };
    return (
      <div>
        <Tabs
          defaultActiveKey={types[dimensionGroup[dimensionIndex].type]}
          items={[
            ...showType.map((item) => {
              if (item === t('analysisCenter-Lx16QhyxC8aO')) {
                return {
                  label: (
                    <span>
                      {' '}
                      <MyIconV2 type="icon-icon-event" style={style} /> {t('analysisCenter-Lx16QhyxC8aO')}
                    </span>
                  ),
                  key: t('analysisCenter-Lx16QhyxC8aO'),
                  children: <Event setFlagVisible={setVisible} />
                };
              } else if (item === t('analysisCenter-kfZOSErJDn9i')) {
                return {
                  label: (
                    <span>
                      <MyIconV2 type="icon-icon-tag" style={style} />
                      <span style={{ marginRight: 5 }}>{t('analysisCenter-kfZOSErJDn9i')}</span>
                      <Tooltip
                        overlayClassName="activity-board-remark-tooltip"
                        placement="top"
                        title={t('analysisCenter-CzUbBZj4cfU9')}
                      >
                        <QuestionCircleOutlined className="remarkTooltip-icon" />
                      </Tooltip>
                    </span>
                  ),
                  key: t('analysisCenter-kfZOSErJDn9i'),
                  children: <Tag setFlagVisible={setVisible} />
                };
              } else if (item === t('analysisCenter-dKSR7Rrr2ZRK')) {
                return {
                  label: (
                    <span>
                      <MyIconV2 type="icon-icon-users" style={style} />
                      <span style={{ marginRight: 5 }}>{t('analysisCenter-dKSR7Rrr2ZRK')}</span>
                      <Tooltip
                        overlayClassName="activity-board-remark-tooltip"
                        placement="top"
                        title={t('analysisCenter-2qxA493Ep6I4')}
                      >
                        <QuestionCircleOutlined className="remarkTooltip-icon" />
                      </Tooltip>
                    </span>
                  ),
                  key: t('analysisCenter-dKSR7Rrr2ZRK'),
                  children: <UserGroup setFlagVisible={setVisible} />
                };
              } else if (item === t('analysisCenter-oqUeslY0V4Zy')) {
                return {
                  label: (
                    <span>
                      {' '}
                      <MyIconV2 type="icon-icon-datefile" style={style} /> {t('analysisCenter-oqUeslY0V4Zy')}
                    </span>
                  ),
                  key: t('analysisCenter-oqUeslY0V4Zy'),
                  children: <TableField setFlagVisible={setVisible} />
                };
              } else if (item === t('analysisCenter-IgB1zAFkeCfh')) {
                return {
                  label: (
                    <span>
                      {' '}
                      <MyIconV2 type="icon-icon-camping" style={style} /> {t('analysisCenter-IgB1zAFkeCfh')}
                    </span>
                  ),
                  key: t('analysisCenter-IgB1zAFkeCfh'),
                  children: <Campaign setFlagVisible={setVisible} />
                };
              }
              return null;
            })
          ]}
        />
      </div>
    );
  };

  const RenderTitle = () => {
    let title = t('analysisCenter-1AuCsRLsLsQF');
    const _dimensionGroup = dimensionGroup;
    const type = _dimensionGroup[dimensionIndex].type;
    const icons = {
      USER_PROPERTIES: 'icon-icon-event',
      USER_LABEL: 'icon-icon-tag',
      SEGMENT: 'icon-icon-users',
      TABLE_FIELD: 'icon-icon-datefile',
      CAMPAIGN: 'icon-icon-camping'
    };
    switch (type) {
      case 'SEGMENT':
        title = `${_dimensionGroup[dimensionIndex].filters.length}${t('analysisCenter-PlbsxOpIVgnu')}`;
        break;
      case 'TABLE_FIELD':
        title = _dimensionGroup[dimensionIndex].name;
        break;
      case 'CAMPAIGN':
        const filterTypeName = {
          // CAMPAIGN: '按流程画布',
          CAMPAIGN_BATCH: t('analysisCenter-hRyam7UG472b'),
          CAMPAIGN_NODE: t('analysisCenter-9bwS8OeFX684')
        };
        title = filterTypeName[_dimensionGroup[dimensionIndex].filters[0].campaignGroup.filterType];
        break;
      case 'USER_LABEL':
        title = _dimensionGroup[dimensionIndex].name;
        break;
      case 'USER_PROPERTIES':
        title = _dimensionGroup[dimensionIndex].name;
        break;
      default:
    }
    return (
      <div className="dimensionTitle">
        {icons[type] ? (
          <div className="mouse">
            <MyIconV2 type={icons[type]} />
          </div>
        ) : (
          <div className="mouse">
            <MyIconV2 type="icon-icon-event" />
          </div>
        )}
        <div className="dimensionContent" style={{ opacity: title === t('analysisCenter-1AuCsRLsLsQF') ? 0.4 : 1 }}>
          {title}
        </div>
        <div className="icons" onClick={deleteDimension}>
          <MyIconV2 type="icon-icon-close" />
        </div>
      </div>
    );
  };

  const deleteDimension = () => {
    const _dimensionGroup = _.cloneDeep(dimensionGroup);
    _dimensionGroup.splice(dimensionIndex, 1);
    // // 重新排序id
    // _dimensionGroup.forEach((item, index) => {
    //   item.id = index;
    // });
    dispatch({ dimensionGroup: _dimensionGroup });
  };

  const handleChange = (value) => {
    // debugger;
    const _dimensionGroup = _.cloneDeep(dimensionGroup);

    if (_dimensionGroup[dimensionIndex].type === 'USER_LABEL') {
      const _value = _.cloneDeep(value);
      const filterArrayRes = _value.map((item) => {
        const itemRes = item.match(/\[(.+?)\]/g);
        item = itemRes ? RegExp.$1 : item;
        return item;
      });

      _dimensionGroup[dimensionIndex].showValue = value;
      _dimensionGroup[dimensionIndex].filterValue = filterArrayRes || value;
      _dimensionGroup[dimensionIndex].filters?.forEach((item) => {
        item.userLabel.filters[0].filters[0].showValue = value;
      });
    } else {
      _dimensionGroup[dimensionIndex].filterValue = value;
    }

    dispatch({ dimensionGroup: _dimensionGroup });
  };

  const renderCampaiginFilterValue = () => {
    return (
      <div>
        <CampaignValue
          setVisible={setVisible}
          setValueVisible={setValueVisible}
          type={dimensionGroup[dimensionIndex].filters[0].campaignGroup.filterType}
        />
      </div>
    );
  };

  const getTagValue = async () => {
    const id = dimensionGroup[dimensionIndex].filters[0].userLabel.filters[0].filters[0].id;
    const tagValue = await TagAndTagValueService.getTagValuesById({ labelId: id });
    return tagValue;
  };

  useEffect(() => {
    (async () => {
      if (dimensionGroup[dimensionIndex]?.type === 'USER_LABEL') {
        const _tagValue = await getTagValue();
        setTagValue(_tagValue);
      }
    })();
  }, [dimensionGroup[dimensionIndex]?.type, dimensionGroup[dimensionIndex]?.name]);

  const renderFilterValue = () => {
    let title = t('analysisCenter-XnNj5QWJHF7z');
    let opacity;
    if (dimensionGroup[dimensionIndex].type === 'CAMPAIGN') {
      let namelength = null;
      const type = dimensionGroup[dimensionIndex].filters[0].campaignGroup.filterType;
      const filter = dimensionGroup[dimensionIndex].filters[0].campaignGroup.filters;
      if (type === 'CAMPAIGN_BATCH') {
        // 把filters每一项的logList的length加起来
        namelength = filter.reduce((total, item) => {
          return total + item.logList.length;
        }, 0);
        if (namelength === 0) {
          title = t('analysisCenter-rhVVhXZqOyIW');
          opacity = true;
        } else {
          title = `${namelength}${t('analysisCenter-PrZ62ocwDh2i')}`;
        }
      } else if (type === 'CAMPAIGN_NODE') {
        // 把filters每一项的logList里每一项的selectedFlows的length加起来
        namelength = filter.reduce((total, item) => {
          return (
            total +
            item.logList.reduce((total1, item1) => {
              return total1 + item1.selectedFlows.length;
            }, 0)
          );
        }, 0);
        if (namelength === 0) {
          title = t('analysisCenter-mN048i3AB6f6');
          opacity = true;
        } else {
          title = `${namelength}${t('analysisCenter-WCMy6KO66b09')}`;
        }
      } else {
        return (title = t('analysisCenter-XnNj5QWJHF7z'));
      }
    }

    if (dimensionGroup[dimensionIndex].type === 'SEGMENT') {
      return null;
    } else if (dimensionGroup[dimensionIndex].type === 'CAMPAIGN') {
      return (
        <Popover
          getPopupContainer={() => document.getElementsByClassName('content')[0]}
          content={renderCampaiginFilterValue()}
          trigger="click"
          destroyTooltipOnHide
          overlayStyle={{ minWidth: '320px' }}
          autoAdjustOverflow
          open={valueVisible}
          onOpenChange={setValueVisible}
          placement="bottom"
          className="conversion"
        >
          <div className="stepTitle" style={{ opacity: opacity ? 0.4 : 1 }}>
            {title}
          </div>
        </Popover>
      );
    }
    return (
      <div className="option">
        <div style={{ width: '95%' }}>
          <Select
            bordered={false}
            mode="tags"
            value={
              dimensionGroup[dimensionIndex].type === 'USER_LABEL'
                ? dimensionGroup[dimensionIndex].filters?.map((item) => item.userLabel.filters[0].filters[0])[0]
                    ?.showValue
                : dimensionGroup[dimensionIndex].filterValue
            }
            size="middle"
            placeholder={t('analysisCenter-Yof4MEvrITA1')}
            maxTagCount={6}
            maxTagTextLength={20}
            onChange={handleChange}
            style={{
              width: '100%'
            }}
          >
            {!_.isEmpty(tagValue) &&
              tagValue
                .filter((n) => !!n.value)
                .map((item, i) => (
                  <Select.Option
                    value={item?.priorityShow === 2 && item.displayValue ? item.valueAndDisplayValue : item.value}
                    label={item?.priorityShow === 2 && item.displayValue ? item.valueAndDisplayValue : item.value}
                    key={i.id}
                  >
                    {item?.priorityShow === 2 && item.displayValue
                      ? item.valueAndDisplayValue
                      : item.value
                        ? item.value
                        : item}
                  </Select.Option>
                ))}
          </Select>
        </div>
      </div>
    );
  };
  return (
    <retentionContext.Provider
      value={{
        state,
        dispatch,
        isDimension: true,
        dimensionIndex,
        setVisible,
        scenarioId,
        editDimensionGroup,
        setEditDimensionGroup
      }}
    >
      <div className="dimensionItem">
        <Popover
          getPopupContainer={() => document.getElementsByClassName('content')[0]}
          // content={RenderSetStep(item, index)}
          content={RenderFilter()}
          trigger="click"
          destroyTooltipOnHide
          overlayStyle={{ minWidth: '320px' }}
          autoAdjustOverflow
          open={visible}
          onOpenChange={setVisible}
          placement="bottomRight"
          className="conversion"
        >
          <div className="stepTitle">{RenderTitle()}</div>
        </Popover>
        {renderFilterValue()}
      </div>
    </retentionContext.Provider>
  );
}
