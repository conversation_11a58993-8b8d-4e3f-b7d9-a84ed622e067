import { Button, message } from 'antd';
import _ from 'lodash';
import React, { useContext } from 'react';
import DimensionItem from './dimensionItem';
// import icon from '../icon';

import { t } from 'utils/translation';
import { retentionContext } from '../retentionContext';

export default function Dimension() {
  const { state, dispatch } = useContext(retentionContext);
  const { dimensionGroup, isMultiStep } = state;

  const addDimensionGroup = () => {
    if (dimensionGroup.length >= 1) {
      return message.warning(t('analysisCenter-cWIFBBNKRDdw'));
    }
    const _dimensionGroup = _.cloneDeep(dimensionGroup);
    _dimensionGroup.push({});
    dispatch({ dimensionGroup: _dimensionGroup });
  };

  return (
    <retentionContext.Provider value={{ state, dispatch }}>
      {dimensionGroup &&
        dimensionGroup.map((item, index) => {
          return <DimensionItem key={index} dimensionIndex={index} value={dimensionGroup} />;
        })}
      <Button disabled={isMultiStep} className="addButton" type="dashed" block onClick={addDimensionGroup}>
        +{t('analysisCenter-QwRRBD6D7SJS')}
      </Button>
    </retentionContext.Provider>
  );
}
