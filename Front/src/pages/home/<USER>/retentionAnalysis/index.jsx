import { CloseOutlined, DownOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import {
  Button,
  Card,
  Checkbox,
  Divider,
  Drawer,
  Dropdown,
  Empty,
  Input,
  Modal,
  Select,
  Space,
  Spin,
  Tooltip,
  Tree,
  message
} from 'antd';
import NumericInput from 'components/bussinesscoms/numbericInput/index';
import dayjs from 'dayjs';
import _ from 'lodash';
import { getDeptPath } from 'pages/home/<USER>/dataPermissions/config';
import React, { useEffect, useReducer, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import CampaignV2Service from 'service/CampaignV2Service';
import CampaignsService from 'service/CampaignsService';
import ScenarioService from 'service/ScenarioService';
import UserService from 'service/UserService';
import AnalysisCenterService from 'service/analysisCenterService';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { useDebounce, useQuery } from 'utils/customhooks';
import { MyIcon, MyIconV2 } from 'utils/myIcon';
import { t } from 'utils/translation';
import { SelectTime } from 'wolf-static-cpnt';
import RenderChart from './charts/renderCharts';
import RenderTable from './charts/renderTable';
import { getTime } from './config';
import Dimension from './dimension';
import GlobalFilter from './filter';
import './index.scss';
import { retentionContext } from './retentionContext';
import SaveChart from './saveChart/saveChart';
import SaveGroup from './saveGroup/index';
import Steps from './steps';
import UpdateChart from './updateChart/updateChart';

const querystring = require('querystring');

const { Option } = Select;

const reducer = (state, action) => ({ ...state, ...action });

const campaignsService = new CampaignsService();
const campaignV2Service = new CampaignV2Service();
const userService = new UserService();

export default function FunnelAnalysisDom(props) {
  const parmas = querystring.parse(window.location.search.substr(1));
  const { id } = useParams();

  const queryParams = useQuery();
  const campaignId = queryParams.get('campaignId');
  const boardType = queryParams.get('boardType');
  const scenario = queryParams.get('scenarioId');
  const version = queryParams.get('version');
  const saveId = queryParams.get('saveId');
  const deptId = queryParams.get('deptId');

  const [state, dispatch] = useReducer(reducer, {
    name: t('analysisCenter-2xstUn8otesW'),
    loading: false,
    scenarioId: null,
    chartResult: null,
    dateRange2: [
      {
        type: 'RELATIVE',
        timeTerm: 'DAY',
        isPast: true,
        times: 7,
        truncateAsDay: true
      },
      { type: 'NOW', timeTerm: 'DAY', isPast: true, truncateAsDay: true }
    ],
    timeTerm: 'DAY',
    stepList: [],
    dimensionGroup: [],
    globalFilters: [],
    scenarioList: [],
    chartDisplayType: 'CHANGE_CHART',
    isMultiStep: false, // 是否禁用维度分组
    offSave: true,
    open: true,
    defaultFunnel: null,
    showSaveGroup: false,
    saveGroupValue: null, // 保存分群的值
    timeValue: { startValue: 0, endValue: 7 },
    chartConfig: {},
    showTotal: false,
    analysisType: 'RETAINED',
    visibleDrawer: false,
    updateChart: false,
    displayType: 'CHANGE_CHART', // 最终图表展示的类型 TREND_CHART, CHANGE_CHART, RETAINED_TABLE;
    updateChartLoading: false,
    chartSelect: t('analysisCenter-XIZgqWk2M2rm')
  });
  const [scenarioList, setScenarioList] = useState([]);
  const [filterConfig, setFilterConfig] = useState({});
  const [chartConfigData, setChartConfigData] = useState([]);
  const [userId, setUserId] = useState(null);
  const [boardDetail, setBoardDetail] = useState({});
  const [campaignTreeData, setCampaignTreeData] = useState([]);
  const [deptPath, setDeptPath] = useState(getDeptPath());
  const [campaignList, setCampaignList] = useState([]);
  const timer = useRef(null);
  const onRef = useRef(null);

  useEffect(() => {
    dispatch({ offSave: true });
  }, [state.stepList, state.globalFilters, state.dimensionGroup, state.timeTerm, state.dateRange2, state.analysisType]);

  // 遍历维度分组是否禁用
  useEffect(() => {
    const isFlag = state.stepList.some((item) => item.multiStep);
    if (isFlag) {
      dispatch({ isMultiStep: true });
    } else {
      dispatch({ isMultiStep: false });
    }
  }, [state.stepList]);

  const GetQueryString = (name) => {
    const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
    const r = window.location.search.substr(1).match(reg); // search,查询？后面的参数，并匹配正则
    if (r != null) return unescape(r[2]);
    return null;
  };

  const saveRecordInfo = async (id, userId) => {
    await campaignsService.saveUserOperationRecord({
      targetId: id,
      id: state.defaultFunnel?.recentUserOperationRecord?.id,
      targetType: 'CHART',
      type: 'RECENT',
      createUserId: userId,
      updateUserId: userId,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });
  };

  useEffect(() => {
    init();
    return () => {
      timer.current && clearTimeout(timer.current);
      localStorage.removeItem('isActivityAnalysis');
      // localStorage.removeItem('timefilter');
    };
  }, []);

  useEffect(() => {
    const getBoardInfo = async () => {
      if (campaignId) {
        if (boardType === 'campaigns') {
          const result = await campaignsService.getCampaignsV2({
            id: Number(campaignId),
            deptId: window.getDeptId()
          });

          // const res = await campaignsService.findCampaigns([
          //   {
          //     operator: 'EQ',
          //     propertyName: 'campaignsId',
          //     value: Number(props.location.state?.campaignId)
          //   }
          // ]);

          if (!_.isNil(id)) {
            const treeResult = [
              {
                title: `${t('analysisCenter-zbYFR6adeMp7')}[${result.id}] ${result.name}`,
                key: result.id,
                children: state.chartConfig.campaignFilters
                  ? state.chartConfig.campaignFilters.map((item) => {
                      return {
                        title: `${t('analysisCenter-3jBhI9hJlgAb')}[${item.id}] ${item.name}`,
                        key: item.id
                      };
                    })
                  : []
              }
            ];
            setCampaignTreeData(treeResult);
          } else {
            const res = await campaignsService.getCampaignFilter({
              type: 'CAMPAIGNS',
              id: Number(campaignId)
            });

            setCampaignList(res);

            const treeResult = [
              {
                title: `${t('analysisCenter-zbYFR6adeMp7')}[${result.id}] ${result.name}`,
                key: result.id,
                children:
                  res && res.length
                    ? res.map((item) => {
                        return {
                          title: `${t('analysisCenter-3jBhI9hJlgAb')}[${item.id}] ${item.name}`,
                          key: item.id
                        };
                      })
                    : []
              }
            ];

            setCampaignTreeData(treeResult);
          }

          setBoardDetail(result);
        } else {
          if (!_.isNil(id)) {
            const treeResult = state.chartConfig.campaignFilters
              ? state.chartConfig.campaignFilters.map((item) => {
                  return {
                    title: `${t('analysisCenter-3jBhI9hJlgAb')}[${item.id}] ${item.name}`,
                    key: item.id
                  };
                })
              : [];
            setBoardDetail(state.chartConfig.campaignFilters && state.chartConfig.campaignFilters[0]);
            setCampaignTreeData(treeResult);
          } else {
            const result = await campaignsService.getCampaignFilter({
              type: 'FLOW_CANVAS',
              id: Number(campaignId)
            });

            setCampaignList(result);
            const treeResult = [
              {
                title: `${t('analysisCenter-3jBhI9hJlgAb')}[${result[0].id}] ${result[0].name}`,
                key: result[0].id
              }
            ];
            setBoardDetail(result[0]);
            setCampaignTreeData(treeResult);
          }
        }
      }
    };

    getBoardInfo();
  }, [state.chartConfig]);

  /**
   * @param {object} result 刷新传入
   */
  const init = async (result) => {
    if (campaignId) {
      localStorage.setItem('activityCache', true);
    }
    let _scenarioList = await ScenarioService.scenarioList([]);
    _scenarioList = _scenarioList.sort((a, b) => a.orderNum - b.orderNum);
    const userInfo = await userService.getCurrentUser();
    setUserId(userInfo.id);
    setScenarioList(_scenarioList);
    dispatch({
      scenarioList: _scenarioList
    });
    if (!_.isNil(id)) {
      dispatch({
        loading: true
      });
      // let info = await AnalysisCenterService.getChartConfig(id);
      let info = await AnalysisCenterService.getChartConfigV2({
        id: Number(id),
        deptId: deptId || window.getDeptId()
      });
      if (info) {
        setDeptPath(getDeptPath(info?.deptId));
        setChartConfigData(info);
        const _dimensionGroup = info?.chartConfig?.retentionAnalysisDataQuery?.dimensionGroup;
        _dimensionGroup.map((item) => {
          const arr = [];
          item.filters &&
            item.filters.forEach((i) => {
              arr.push(i?.groupName);
            });
          item.filterValue = arr;
          return item;
        });

        const timeFilter = GetQueryString('timeFilter');
        // 设置状态来判定是否第一次进入页面
        if (timeFilter && !localStorage.getItem('timeFilter')) {
          const timeFilterArr = timeFilter.split(',');
          localStorage.setItem('timeFilter', JSON.stringify(timeFilter));
          info = {
            ...info,
            dateRange2: [
              {
                type: 'ABSOLUTE',
                timestamp: parseInt(timeFilterArr[0]),
                times: 30,
                timeTerm: 'DAY',
                isPast: true
              },
              {
                type: 'ABSOLUTE',
                timestamp: parseInt(timeFilterArr[1]),
                times: 0,
                timeTerm: 'DAY',
                isPast: true
              }
            ]
          };
          setFilterConfig({
            startTime: timeFilterArr[0],
            endTime: timeFilterArr[1]
          });
        }

        const calcTime = parmas.calcState ? JSON.parse(localStorage.getItem('dateRange')) : info.dateRange2;
        await dispatch({
          name: result?.name || info.name,
          chartConfig: info.chartConfig,
          dateRange2: calcTime,
          stepList: info.chartConfig.retentionAnalysisDataQuery.stepList,
          scenarioId: info.scenario.id,
          globalFilters: info.chartConfig.retentionAnalysisDataQuery.globalFilters,
          dimensionGroup: _dimensionGroup,
          timeTerm: info.chartConfig.retentionAnalysisDataQuery.analysisDate.timeTerm,
          displayType: info.displayType === 'RETAINED_TABLE' ? 'CHANGE_CHART' : info.displayType,
          defaultFunnel: info,
          analysisType: info.chartConfig.retentionAnalysisDataQuery.analysisType,
          timeValue: {
            startValue: info.chartConfig.retentionAnalysisDataQuery.analysisDate.beginDate,
            endValue: info.chartConfig.retentionAnalysisDataQuery.analysisDate.endDate
          },
          chartDisplayType: info.displayType === 'RETAINED_TABLE' ? 'CHANGE_CHART' : info.displayType
        });
        await doRequest(info, calcTime);
      }
    } else {
      const _stepList = _.cloneDeep(state.stepList);
      _stepList.push(
        {
          step: '1',
          visibleSetp: false,
          name: t('analysisCenter-6BJP2FDZLsUh'),
          type: 'EVENT',
          event: {}
        },
        {
          step: '2',
          visibleSetp: false,
          name: t('analysisCenter-LAFsyfblsvvU'),
          type: 'EVENT',
          event: {}
        }
      );
      dispatch({
        stepList: _stepList,
        scenarioId:
          campaignId && boardType === 'flowCanvas'
            ? Number(scenario)
            : _scenarioList.find((item) => item.isDefault)?.id || _scenarioList[0].id
      });
    }
  };

  const exitQuit = () => {
    props.history.go(-1);
  };

  const changeName = (e) => {
    let name;
    if (!_.isEmpty(e)) {
      if (e.target.value.length > 32) {
        tip();
      }
      name = e.target.value.substring(0, 32);
      dispatch({ name });
    }
  };

  const tip = useDebounce(() => {
    message.error(t('analysisCenter-4cs1IOEMUpFp'));
  }, 500);

  const change = (value) => {
    dispatch({
      dateRange2: value
    });
  };

  const changeScenario = (value) => {
    const oldValue = _.cloneDeep(state.scenarioId);
    const _stepList = _.cloneDeep(state.stepList);
    const flag = _.every(
      _stepList,
      (item) => item.name === t('analysisCenter-6BJP2FDZLsUh') || item.name === t('analysisCenter-LAFsyfblsvvU')
    );

    const onOk = () => {
      dispatch({
        scenarioId: value,
        stepList: [
          {
            step: '1',
            visibleSetp: false,
            name: t('analysisCenter-6BJP2FDZLsUh'),
            type: 'EVENT',
            event: {}
          },
          {
            step: '2',
            visibleSetp: false,
            name: t('analysisCenter-LAFsyfblsvvU'),
            type: 'EVENT',
            event: {}
          }
        ],
        dimensionGroup: [],
        globalFilters: []
      });
    };
    if (!flag) {
      Modal.confirm({
        icon: <ExclamationCircleOutlined />,
        content: t('analysisCenter-P7caadVAWBRY'),
        okText: t('analysisCenter-4FfFqmIgBc8f'),
        cancelText: t('analysisCenter-YfV068uerocm'),
        onOk,
        onCancel() {
          dispatch({ scenarioId: oldValue });
        }
      });
    } else {
      onOk();
    }
  };

  const saveFunnel = async (obj) => {
    const { type, forceCalc } = obj;
    if (type === 'newChart') {
      return dispatch({ updateChart: false, visibleDrawer: true });
    }
    dispatch({ loading: true });
    try {
      const {
        name,
        chartConfig,
        dateRange2,
        stepList,
        scenarioId,
        timeValue: { startValue, endValue }
      } = state;
      if (
        stepList[0].name === t('analysisCenter-6BJP2FDZLsUh') ||
        stepList[1].name === t('analysisCenter-LAFsyfblsvvU')
      )
        throw new Error(t('analysisCenter-lKT7BLaVltvg'));
      if (getTime(dateRange2[1]) - getTime(dateRange2[0]) > 15552000000)
        throw new Error(t('analysisCenter-jke1RXRqL4jC'));
      else if (endValue - startValue > 30 || startValue - endValue > 30)
        throw new Error(t('analysisCenter-aQ83cuuDbs3S'));
      else if (state.timeTerm === 'DAY' && endValue < startValue) throw new Error(t('analysisCenter-SoRqfTAIgeiq'));

      const findScenarioId = scenarioList.find((item) => item.id === scenarioId);

      const _stepList = _.cloneDeep(stepList);

      _stepList.forEach((item) => {
        item.filters.forEach((item2) => {
          const keyList = Object.keys(item2);
          const key = keyList.find((keyItem) => keyItem === 'userLabel');
          if (key === 'userLabel') {
            const _itemValue = _.cloneDeep(item2.userLabel.filters[0].filters[0].value);
            if (_.isArray(_itemValue) && typeof _itemValue[0] === 'string') {
              const filterArrayRes = _itemValue.map((item3) => {
                const itemRes = item3.match(/\[(.+?)\]/g);
                item3 = itemRes ? RegExp.$1 : item3;
                return item3;
              });
              item2.userLabel.filters[0].filters[0].showValue =
                item2.userLabel.filters[0].filters[0].showValue || item2.userLabel.filters[0].filters[0].value;
              item2.userLabel.filters[0].filters[0].value = filterArrayRes;
            } else {
              const result = _itemValue && !_.isArray(_itemValue) && _itemValue.toString().match(/\[(.+?)\]/g);

              item2.userLabel.filters[0].filters[0].showValue =
                item2.userLabel.filters[0].filters[0].showValue || item2.userLabel.filters[0].filters[0].value;
              item2.userLabel.filters[0].filters[0].value = result
                ? RegExp.$1
                : item2.userLabel.filters[0].filters[0].value;
            }
          }
        });
      });

      const _dimensionGroup = _.cloneDeep(state.dimensionGroup);
      const format = {
        USER_PROPERTIES: 'eventFilterProperty', // 事件
        USER_LABEL: 'userLabel', // 用户标签
        SEGMENT: 'segment', // 用户分群
        TABLE_FIELD: 'userProperty', // 表字段
        CAMPAIGN: 'campaign' // 活动
      };
      _dimensionGroup.map((item) => {
        const { type, filterValue } = item;
        if (type === 'USER_PROPERTIES') {
          const arr = [];
          filterValue.forEach((i) => {
            const _filters = _.cloneDeep(item.filters[0]);
            _filters.groupName = i;
            _filters[format[type]].filters[0].value = i;
            arr.push(_filters);
          });
          item.filters = arr;
        } else if (type === 'USER_LABEL' || type === 'TABLE_FIELD') {
          const arr = [];
          filterValue.forEach((i) => {
            const _filters = _.cloneDeep(item.filters[0]);
            _filters.groupName = i;
            _filters[format[type]].filters[0].filters[0].value = i;
            _filters[format[type]].filters[0].filters[0].showValue =
              item.showValue || _filters[format[type]].filters[0].filters[0].showValue;
            arr.push(_filters);
          });
          item.filters = arr;
        }
        return item;
      });

      const _globalFilters = _.cloneDeep(state.globalFilters);

      _globalFilters.forEach((item) => {
        const keyList = Object.keys(item);
        const key = keyList.find((keyItem) => keyItem === 'userLabel');
        if (key === 'userLabel') {
          const _itemValue = _.cloneDeep(item.userLabel.filters[0].filters[0].value);

          if (_.isArray(_itemValue) && typeof _itemValue[0] === 'string') {
            const filterArrayRes = _itemValue.map((item2) => {
              const itemRes = item2.match(/\[(.+?)\]/g);
              item2 = itemRes ? RegExp.$1 : item2;
              return item2;
            });
            item.userLabel.filters[0].filters[0].showValue =
              item.userLabel.filters[0].filters[0].showValue || item.userLabel.filters[0].filters[0].value;
            item.userLabel.filters[0].filters[0].value = filterArrayRes;
          } else {
            const result = _itemValue && !_.isArray(_itemValue) && _itemValue.toString().match(/\[(.+?)\]/g);

            item.userLabel.filters[0].filters[0].showValue =
              item.userLabel.filters[0].filters[0].showValue || item.userLabel.filters[0].filters[0].value;
            item.userLabel.filters[0].filters[0].value = result
              ? RegExp.$1
              : item.userLabel.filters[0].filters[0].value;
          }
        }
      });

      let data = {
        name,
        extendChartType: boardType || localStorage.getItem('isActivityAnalysis') ? 'CREATE_CAMPAIGN_CHART' : undefined,
        chartConfig: {
          ...chartConfig,
          retentionAnalysisDataQuery: {
            ...chartConfig?.retentionAnalysisDataQuery,
            stepList: _stepList,
            globalFilters: _globalFilters,
            dimensionGroup: _dimensionGroup,
            analysisType: state.analysisType,
            analysisDate: {
              beginDate: state.timeTerm === 'DAY' ? state.timeValue.startValue : 0,
              endDate: state.timeTerm === 'DAY' ? state.timeValue.endValue : 0,
              timeTerm: state.timeTerm
            }
          }
        },
        scenario: findScenarioId,
        dateRange2: dateRange2.map((item) => {
          return {
            ...item,
            truncateAsDay: true
          };
        }),
        chartType: 'RETENTION_ANALYSIS',
        projectId: localStorage.getItem('projectId'),
        id: !_.isNil(id) ? id : null,
        displayType: state.displayType !== state.chartDisplayType ? state.displayType : state.chartDisplayType,
        deptId: state.defaultFunnel?.deptId || window.getDeptId(),
        ...(forceCalc ? { argChange: true } : {}),
        isBusinessTable: 0
      };

      if (!data.chartConfig.campaignFilters && campaignId) {
        data.chartConfig = {
          ...data.chartConfig,
          campaignFilters: campaignList
        };
      }

      if (forceCalc) {
        // 如果手动计算就把table表格选项清空
        data = {
          ...data,
          ...obj
        };
        data.chartConfig.retentionAnalysisDataQuery.displayGroupNames = [];
        await doRequest(_.cloneDeep(data));
      } else {
        const childValue = await onRef?.current?.saveCharts();
        data = {
          ...data,
          ...childValue,
          isBusinessTable: 0,
          id: type === 'updateChart' ? id : null,
          deptId: type === 'updateChart' ? data?.deptId : window.getDeptId()
        };

        if (boardType === 'flowCanvas') {
          if (type === 'updateChart') {
            if (scenarioId !== chartConfigData.scenario.id) {
              throw new Error(t('analysisCenter-ktOemYkHUu58'));
            }
          } else {
            if (scenarioId !== Number(scenario)) {
              throw new Error(t('analysisCenter-ktOemYkHUu58'));
            }
          }
        }

        const result = await AnalysisCenterService.saveChartConfig(data);
        if (campaignId) {
          if (_.isNil(id)) {
            const selectValue = await campaignV2Service.campaignV2ChartBoardListBy([
              {
                operator: 'EQ',
                propertyName: 'campaignId',
                value: campaignId
              }
            ]);

            const widgets = selectValue.length ? selectValue[0].widgets : [];
            // const version = props.location.state.version;

            const _widgets = _.cloneDeep(widgets);

            _widgets.push({
              x: (_widgets.length * 4) % 12,
              y: widgets[widgets?.length - 1]?.y + 100 || 0,
              w: 4,
              h: 3,
              isResizable: true,
              i: `${result.id}`
            });

            await campaignV2Service.saveCampaignV2ChartBoard({
              widgets: _widgets,
              campaignId,
              version: Number(version),
              type: boardType === 'campaigns' ? 'CAMPAIGNS' : 'FLOW_CANVAS',
              id: Number(saveId)
            });
          }
          messageDom(type, data.chartBoards, result, campaignId, boardDetail);
        } else {
          messageDom(type, data.chartBoards, result);
        }
        saveRecordInfo(result.id, userId, result);
        // props.history.push('/aimarketer/home/<USER>/database');
      }
    } catch (err) {
      err.message && message.error(err.message);
      dispatch({
        loading: false
      });
    }
  };

  const { refresh } = useRequest(() => init(), {
    manual: true
  });

  const messageDom = (type, chartBoards, result, state, detailObj) => {
    if (parseInt(result?.id) === parseInt(id)) {
      dispatch({ name: result.name });
    }
    dispatch({
      loading: false,
      updateChart: false,
      visibleDrawer: false
    });
    if (type === 'updateChart') {
      dispatch({ updateChart: false });
      init();

      if (state) {
        if (boardType === 'campaigns') {
          message.success(
            <div style={{ display: 'inline-block' }}>
              <span>【{result.name}】</span>
              {t('analysisCenter-rGG33ujfgRhB')}
              <span
                style={{
                  color: 'var(--ant-primary-color)',
                  cursor: 'pointer'
                }}
                onClick={() => props.history.push(`/aimarketer/home/<USER>/detail/${campaignId}`)}
              >
                【{detailObj.name}】
              </span>
              {t('analysisCenter-zbYFR6adeMp7')}
            </div>
          );
        } else {
          message.success(
            <div style={{ display: 'inline-block' }}>
              <span>【{result.name}】</span>
              {t('analysisCenter-rGG33ujfgRhB')}
              <span
                style={{
                  color: 'var(--ant-primary-color)',
                  cursor: 'pointer'
                }}
                onClick={() => props.history.push(`/aimarketer/home/<USER>/detail?id=${campaignId}`)}
              >
                【{detailObj.name}】
              </span>
              {t('analysisCenter-3jBhI9hJlgAb')}
            </div>
          );
        }
      } else {
        return message.success(`【${result.name}】${t('analysisCenter-C7v6cg8XSWDb')}`);
      }
    } else {
      if (!_.isEmpty(chartBoards)) {
        message.success(
          <div style={{ display: 'inline-block' }}>
            【{result.name}】{t('analysisCenter-zFSrx8BQsC5H')}
            {result.chartBoards.map((item) => {
              return (
                <a onClick={() => window.open(`/aimarketer/home/<USER>/dataDashboard/${item.id}`)}>
                  【{item.boardName}】
                </a>
              );
            })}
            {t('analysisCenter-5Cl3ZYV3DNA6')}
          </div>
        );
        if (!id)
          props.history.push(`/aimarketer/home/<USER>/analysisCenter/database/retentionAnalysis/${result.id}`);
        dispatch({ updateChart: false });
        refresh();
      } else {
        if (state) {
          if (boardType === 'campaigns') {
            message.success(
              <div style={{ display: 'inline-block' }}>
                <span>【{result.name}】</span>
                {t('analysisCenter-hvG2hYpSTAwL')}
                <span
                  style={{
                    color: 'var(--ant-primary-color)',
                    cursor: 'pointer'
                  }}
                  onClick={() => props.history.push(`/aimarketer/home/<USER>/detail/${campaignId}`)}
                >
                  【{detailObj.name}】
                </span>
                {t('analysisCenter-zbYFR6adeMp7')}
              </div>
            );
          } else {
            message.success(
              <div style={{ display: 'inline-block' }}>
                <span>【{result.name}】</span>
                {t('analysisCenter-hvG2hYpSTAwL')}
                <span
                  style={{
                    color: 'var(--ant-primary-color)',
                    cursor: 'pointer'
                  }}
                  onClick={() => props.history.push(`/aimarketer/home/<USER>/detail?id=${campaignId}`)}
                >
                  【{detailObj.name}】
                </span>
                {t('analysisCenter-3jBhI9hJlgAb')}
              </div>
            );
          }
        } else {
          message.success(`【${result.name}】${t('analysisCenter-BMnPrYLHR4T8')}`);
        }

        if (id) {
          init(parseInt(result?.id) === parseInt(id) ? result : null);
          dispatch({ updateChart: false });
          return false;
        } else {
          if (state) {
            localStorage.setItem('isActivityAnalysis', true);
            props.history.push(
              `/aimarketer/home/<USER>/analysisCenter/database/retentionAnalysis/${result.id}?campaignId=${campaignId}&boardType=${boardType}&scenarioId=${scenario}&version=${version}&saveId=${saveId}`
            );
          } else {
            props.history.push(`/aimarketer/home/<USER>/analysisCenter/database/retentionAnalysis/${result.id}`);
          }

          init();
          // dispatch({ name: result.name });
          refresh();
        }
      }
    }

    dispatch({ updateChart: false });
  };

  /**
   * @description: 异步计算接口
   * @param {*} data 需要深克隆data
   * @param {*} isTable 是否是表格
   * @return {*}
   */
  const doRequest = async (data, calcTime) => {
    try {
      if (calcTime) data.dateRange2 = calcTime;
      const res = await FunnelAnalysis.calcFunnelChartResult(data);

      if (!res) {
        dispatch({
          loading: true
        });
      } else if (res.header.code === 0) {
        if (!_.isEmpty(res.body.chartResult)) {
          dispatch({ chartResult: res.body.chartResult });
        }
        res.body.chartResult.dataList.map((item, index) => {
          return (item.key = index);
        });
        dispatch({
          chartResult: res.body.chartResult,
          loading: false,
          offSave: !!state.loading,
          defaultFunnel: data,
          displayType: data.displayType === 'RETAINED_TABLE' ? 'CHANGE_CHART' : data.displayType,
          chartType: data.displayType === 'RETAINED_TABLE' ? 'CHANGE_CHART' : data.displayType,
          chartSelect:
            data.displayType === 'RETAINED_TABLE'
              ? t('analysisCenter-XIZgqWk2M2rm')
              : data.displayType === 'CHANGE_CHART'
                ? t('analysisCenter-XIZgqWk2M2rm')
                : res.body.chartResult?.columnList[0]?.value || t('analysisCenter-b1KLOc0YsrCC')
        });
      } else if (res.header.code === 210) {
        // dispatch({loading: false});
        timer.current = setTimeout(async () => {
          data.forceCalc = false;
          await doRequest(data);
        }, 3000);
      } else if (res.header.code === 1) {
        if (res.header.message) {
          message.info(res.header.message);
        }

        dispatch({
          loading: false
        });
      }
    } catch (error) {
      // responseData[selectedChart] = {rowList: [], columnList: []};
      dispatch({
        loading: false
      });
    }
  };

  const inputRef = useRef();

  const onBlurName = () => {
    if (inputRef.current.input.value === '') {
      const name = t('analysisCenter-2xstUn8otesW');
      dispatch({ name });
    }
  };

  const setChartType = (type) => {
    if (type === state.chartDisplayType) return;
    const chartSelect =
      type === 'CHANGE_CHART'
        ? t('analysisCenter-XIZgqWk2M2rm')
        : state.chartResult?.columnList[0]?.value || t('analysisCenter-b1KLOc0YsrCC');
    dispatch({ chartDisplayType: type, chartSelect });
  };

  const clickIcon = () => {
    setFilterConfig({});
    init();
  };

  const clickOpen = (flag) => {
    dispatch({ open: flag });
  };

  const changeTime = (value, type) => {
    dispatch({ timeValue: { ...state.timeValue, [type]: value } });
  };

  const editShowTotal = (e) => {
    dispatch({ showTotal: e.target.checked });
  };

  useEffect(() => {
    if (state.analysisType === 'LOSS' && parseInt(state.timeValue.startValue) < 1) {
      dispatch({
        timeValue: {
          ...state.timeValue,
          startValue: 1
        }
      });
    }
  }, [state.analysisType]);

  return (
    <div className="retentionAnalysis">
      <Spin spinning={false}>
        <header>
          <div className="left">
            <span className="titleBack" onClick={exitQuit}>
              <MyIconV2 type="icon-arrowleft" style={{ fontSize: 20 }} />
            </span>
            <div className="leftHandleWrapper">
              <Input bordered={false} value={state.name} onChange={changeName} ref={inputRef} onBlur={onBlurName} />
            </div>
          </div>
          <div className="right">
            <Space className="btnGroup">
              <Button type="primary" loading={state.loading} onClick={() => saveFunnel({ forceCalc: true })}>
                {t('analysisCenter-FIbFQSpPqRps')}
              </Button>
              {boardType || localStorage.getItem('isActivityAnalysis') ? (
                <Button
                  onClick={() => (!_.isNil(id) ? dispatch({ updateChart: true }) : dispatch({ visibleDrawer: true }))}
                  disabled={state.offSave}
                  // loading={state.initLoading}
                  buttonsRender={([leftButton, rightButton]) => [
                    React.cloneElement(leftButton, {
                      loading: state.initLoading,
                      key: '11'
                    }),
                    React.cloneElement(rightButton, {
                      loading: state.initLoading,
                      key: '22'
                    })
                  ]}
                  type="primary"
                >
                  {!_.isNil(id) ? t('analysisCenter-nhrDmCSP6yxe') : t('analysisCenter-fYURWX6SDYK3')}
                </Button>
              ) : (
                <Dropdown.Button
                  icon={<DownOutlined />}
                  onClick={() => (!_.isNil(id) ? dispatch({ updateChart: true }) : dispatch({ visibleDrawer: true }))}
                  disabled={state.offSave}
                  // loading={state.initLoading}
                  buttonsRender={([leftButton, rightButton]) => [
                    React.cloneElement(leftButton, {
                      loading: state.initLoading,
                      key: '11'
                    }),
                    React.cloneElement(rightButton, {
                      loading: state.initLoading,
                      key: '22'
                    })
                  ]}
                  type="primary"
                  menu={{
                    items: [
                      {
                        label: t('analysisCenter-eIvyOieZNo05'),
                        key: '1',
                        onClick: () => {
                          dispatch({ visibleDrawer: true });
                        }
                      }
                    ]
                  }}
                >
                  {!_.isNil(id) ? t('analysisCenter-nhrDmCSP6yxe') : t('analysisCenter-fYURWX6SDYK3')}
                </Dropdown.Button>
              )}
            </Space>
          </div>
        </header>
        <retentionContext.Provider value={{ state, dispatch, scenarioList }}>
          <div className="content">
            <div className="left" style={{ display: !state.open && 'none' }}>
              <div className="resize-bar" />
              <div className="resize-line" />
              <div className="resize-save">
                <div className="screen">
                  <div>
                    {t('analysisCenter-tns5dOcSYj5o')}
                    <Select
                      style={{ minWidth: '100px' }}
                      value={state.scenarioId}
                      bordered={false}
                      placeholder={t('analysisCenter-aY58jKYKjo1f')}
                      onChange={changeScenario}
                    >
                      {state.scenarioList.map((n) => (
                        <Option key={n.id} value={n.id}>
                          {n.name}
                        </Option>
                      ))}
                    </Select>
                  </div>
                  <div className="overflow-hidden whitespace-nowrap text-ellipsis">
                    {t('analysisCenter-v9i6G8llccqq')}
                    <Tooltip title={deptPath} placement="topLeft">
                      <span>{deptPath}</span>
                    </Tooltip>
                  </div>
                </div>
                <div className="process">
                  <div className="setUpSteps">
                    <div className="title">{t('analysisCenter-Hx7CBf0adtOA')}</div>
                    <Steps />
                  </div>
                  <div className="setGlobalFilter">
                    <div className="title">{t('analysisCenter-4pga2l9zq9sb')}</div>
                    <GlobalFilter />
                  </div>
                  <div className="setDimension">
                    <div className="title">{t('analysisCenter-7WSqeSLLsi7S')}</div>
                    <Dimension />
                  </div>
                  {campaignId && (
                    <div>
                      <div className="title">{t('analysisCenter-mnCjcK2JRgau')}</div>
                      <Tree
                        treeData={campaignTreeData}
                        selectable={false}
                        className={boardType === 'campaigns' ? '' : 'treeNoopNone'}
                      />
                    </div>
                  )}
                </div>
              </div>
              <div className="open" onClick={() => clickOpen(false)}>
                <MyIconV2 type="icon-icon-page" />
              </div>
            </div>
            <div className="right">
              {!state.open && (
                <div className="open" onClick={() => clickOpen(true)}>
                  <MyIconV2 type="icon-icon-page" />
                </div>
              )}
              <div className="rightScreen" style={{ paddingLeft: !state.open && '80px' }}>
                <Select
                  value={state.timeTerm}
                  onChange={(e) => {
                    dispatch({ timeTerm: e });
                    if (e === 'DAY') {
                      dispatch({
                        timeValue: {
                          ...state.timeValue,
                          startValue: state.analysisType === 'LOSS' ? 1 : 0,
                          endValue: 7
                        }
                      });
                    }
                  }}
                  style={{ marginRight: 8 }}
                >
                  <Option value="DAY">{t('analysisCenter-z96TSJzUMDMI')}</Option>
                  <Option value="WEEK">{t('analysisCenter-fa5iHbOpqNf9')}</Option>
                  <Option value="MONTH">{t('analysisCenter-YJAMQbsg61Jk')}</Option>
                </Select>

                <SelectTime
                  data={state.dateRange2}
                  isAnalysis
                  style={{
                    maxWidth: 'auto'
                  }}
                  onChange={change}
                />
                <div className="inputs" hidden={state.timeTerm !== 'DAY'}>
                  <div className="setTime">
                    {t('analysisCenter-PY7m5dWKopuv')}
                    <NumericInput
                      value={state.timeValue.startValue}
                      bordered={false}
                      onChange={(e) => changeTime(e, 'startValue')}
                      min={state.analysisType === 'LOSS' ? 1 : 0}
                      defaultValue="1"
                    />
                    {t('analysisCenter-obyRbg9gnlwV')}
                  </div>
                  <div className="division"> - </div>
                  <div className="setTime">
                    {t('analysisCenter-PY7m5dWKopuv')}
                    <NumericInput
                      value={state.timeValue.endValue}
                      bordered={false}
                      onChange={(e) => changeTime(e, 'endValue')}
                    />
                    {t('analysisCenter-obyRbg9gnlwV')}
                  </div>
                </div>
                <div className="retentionType">
                  <Select
                    value={state.analysisType}
                    style={{ width: 100 }}
                    onChange={(e) => dispatch({ analysisType: e })}
                  >
                    <Option value="RETAINED">{t('analysisCenter-99dMmDzYxDCM')}</Option>
                    <Option value="LOSS">{t('analysisCenter-0S2Z2S8ZyVSB')}</Option>
                  </Select>
                </div>
                <div className="chartSelect">
                  {state.displayType === 'CHANGE_CHART' ? (
                    <Select
                      style={{ width: 128 }}
                      value={state.chartSelect}
                      onChange={(chartSelect) => dispatch({ chartSelect })}
                    >
                      <Option value="全部">{t('analysisCenter-XIZgqWk2M2rm')}</Option>
                      {state.chartResult?.dataList[0]?.children.map((item) => {
                        return (
                          <Option key={item.name} value={item.name}>
                            {item.name}
                          </Option>
                        );
                      })}
                    </Select>
                  ) : (
                    <Select
                      style={{ width: 128 }}
                      value={state.chartSelect}
                      onChange={(chartSelect) => dispatch({ chartSelect })}
                    >
                      {state.chartResult?.columnList?.map((item) => {
                        return (
                          <Option key={item.key} value={item.value}>
                            {item.value}
                          </Option>
                        );
                      })}
                    </Select>
                  )}
                </div>
                <div className="chartType">
                  <Button
                    type={state.displayType === 'CHANGE_CHART' && 'primary'}
                    onClick={() => {
                      setChartType('CHANGE_CHART');
                      dispatch({ displayType: 'CHANGE_CHART' });
                    }}
                  >
                    <Tooltip title={t('analysisCenter-oRGCWDbCFjMY')}>
                      <MyIcon type="icon-icon-retain-horizontal" style={{ fontSize: 20 }} />
                    </Tooltip>
                  </Button>
                  <Button
                    type={state.displayType === 'TREND_CHART' && 'primary'}
                    onClick={() => {
                      setChartType('TREND_CHART');
                      dispatch({ displayType: 'TREND_CHART' });
                    }}
                  >
                    <Tooltip title={t('analysisCenter-OOdrTzEUDqJ1')}>
                      <MyIcon type="icon-icon-retain-vertical" style={{ fontSize: 20 }} />
                    </Tooltip>
                  </Button>
                </div>
              </div>
              {!_.isEmpty(filterConfig) && (
                <div className="term">
                  <span className="title">{t('analysisCenter-uB4pnFB7ce4V')}</span>
                  <Card style={{ width: '348px', height: '32px' }}>
                    <p>
                      {dayjs(parseInt(filterConfig.startTime)).format('YYYY-MM-DD HH:mm:ss')} ~{' '}
                      {dayjs(parseInt(filterConfig.endTime)).format('YYYY-MM-DD HH:mm:ss')}{' '}
                      <CloseOutlined onClick={clickIcon} />
                    </p>
                  </Card>
                </div>
              )}

              {!_.isEmpty(state?.chartResult?.dataList) ? (
                <>
                  <div className="chart">
                    <RenderChart
                      loading={state.loading}
                      chartSelect={state.chartSelect}
                      chartDisplayType={state.chartDisplayType}
                      chartResult={state.chartResult}
                      dimensionGroup={state.dimensionGroup}
                    />
                  </div>
                  <div style={{ padding: '0 80px' }}>
                    <Divider />
                  </div>
                  <div className="table1">
                    <div className="tableTitle">
                      <span>{t('analysisCenter-IPgylfSAnwFQ')}</span>
                      <span>
                        <Checkbox onChange={editShowTotal}>{t('analysisCenter-pjg6b12XbP1y')}</Checkbox>
                      </span>
                    </div>
                    <RenderTable loading={state.loading} />
                  </div>
                </>
              ) : (
                <div className="empty">
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                </div>
              )}
            </div>
          </div>
          {state.showSaveGroup && <SaveGroup />}
          <Drawer
            title={t('analysisCenter-fYURWX6SDYK3')}
            placement="right"
            width={600}
            onClose={() => dispatch({ visibleDrawer: false })}
            destroyOnClose
            footer={
              <div className="buttomFooter">
                <Button onClick={() => dispatch({ visibleDrawer: false })}>{t('analysisCenter-1m5zHzWZUKJp')}</Button>
                <Button type="primary" onClick={saveFunnel}>
                  {t('analysisCenter-6NQW4ZBXalpp')}
                </Button>
              </div>
            }
            open={state.visibleDrawer}
          >
            <SaveChart
              onRef={onRef}
              chartName={state.name}
              boardType={boardType}
              campaignId={campaignId}
              detailObj={boardDetail}
            />
          </Drawer>
          <Drawer
            open={state.updateChart}
            title={t('analysisCenter-nhrDmCSP6yxe')}
            width={560}
            destroyOnClose
            onCancel={() => dispatch({ updateChart: false })}
            onClose={() => dispatch({ updateChart: false })}
            footer={
              <div className="buttomFooter">
                <Button onClick={() => dispatch({ updateChart: false })} key="1">
                  {t('analysisCenter-1m5zHzWZUKJp')}
                </Button>
                <Button
                  type="primary"
                  key="3"
                  onClick={() => saveFunnel({ type: 'updateChart' })}
                  loading={state.loading}
                >
                  {t('analysisCenter-nhrDmCSP6yxe')}
                </Button>
              </div>
            }
          >
            <UpdateChart boardType={boardType} detailObj={boardDetail} />
          </Drawer>
        </retentionContext.Provider>
      </Spin>
    </div>
  );
}
