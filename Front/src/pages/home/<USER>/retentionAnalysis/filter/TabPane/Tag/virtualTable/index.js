import { Table } from 'antd';
import classNames from 'classnames';
import ResizeObserver from 'rc-resize-observer';
import React, { useRef, useState } from 'react';
import { VariableSizeGrid as Grid } from 'react-window';
import './index.scss';

function VirtualTable(props) {
  const { columns, scroll } = props;
  const [tableWidth, setTableWidth] = useState(0);
  const widthColumnCount = columns.filter(({ width }) => !width).length;
  const mergedColumns = columns.map((column) => {
    if (column.width) {
      return column;
    }

    return { ...column, width: Math.floor(tableWidth / widthColumnCount) };
  });
  const gridRef = useRef();
  const [connectObject] = useState(() => {
    const obj = {};
    Object.defineProperty(obj, 'scrollLeft', {
      get: () => null,
      set: (scrollLeft) => {
        if (gridRef.current) {
          gridRef.current.scrollTo({
            scrollLeft
          });
        }
      }
    });
    return obj;
  });

  const renderTableCell = ({ rawData, rowIndex, mergedColumns, columnIndex }) => {
    if (columnIndex !== 0) {
      if (!rawData[0][mergedColumns[columnIndex].dataIndex]?.customers) {
        return '0(0.00%)';
      }
      if (!rawData[rowIndex][mergedColumns[columnIndex].dataIndex]?.customers) {
        return '0(0.00%)';
      }
      return `${0}(${(
        (rawData[rowIndex][mergedColumns[columnIndex].dataIndex]?.customers /
          rawData[0][mergedColumns[columnIndex].dataIndex]?.customers) *
        100
      ).toFixed(2)}%)`;
    }
    return rawData[rowIndex].valueName;
  };

  const renderVirtualList = (rawData, { scrollbarSize, ref, onScroll }) => {
    ref.current = connectObject;
    const totalHeight = rawData.length * 54;
    return (
      <Grid
        ref={gridRef}
        className="virtual-grid"
        columnCount={mergedColumns.length}
        columnWidth={(index) => {
          const { width } = mergedColumns[index];
          return totalHeight > scroll.y && index === mergedColumns.length - 1 ? width - scrollbarSize - 1 : width;
        }}
        height={scroll.y}
        rowCount={rawData.length}
        rowHeight={() => 54}
        width={tableWidth}
        onScroll={({ scrollLeft }) => {
          onScroll({
            scrollLeft
          });
        }}
      >
        {({ columnIndex, rowIndex, style }) => (
          <div
            className={classNames('virtual-table-cell', {
              'virtual-table-cell-last': columnIndex === mergedColumns.length - 1
            })}
            style={style}
          >
            {/* {rawData[rowIndex][mergedColumns[columnIndex].dataIndex]} */}
            {renderTableCell({ rawData, rowIndex, mergedColumns, columnIndex })}
          </div>
        )}
      </Grid>
    );
  };

  return (
    <div className="viTable">
      <ResizeObserver
        onResize={({ width }) => {
          setTableWidth(width);
        }}
      >
        <Table
          {...props}
          className="virtual-table"
          columns={mergedColumns}
          pagination={false}
          components={{
            body: renderVirtualList
          }}
        />
      </ResizeObserver>
    </div>
  );
}

export default VirtualTable;
