import { Spin, Tag } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { t } from 'utils/translation';
import './tagHover.scss';

const typeMap = {
  EXTERNAL_IMPORT: t('analysisCenter-cRpoi88UctFw'),
  SQL_CREATION: t('analysisCenter-YwVwow7fI02g'),
  EX_SUBSCRIBE: t('analysisCenter-HBfGh9hurZsn')
};

export default (props) => {
  const { selectValue, labelHistoryList, loading } = props;
  const [tags, setTags] = useState([]);

  useEffect(() => {
    // 整理表格数据
    const _tags = [];
    _.forEach(labelHistoryList, (item) => {
      const showTagValue =
        item.valueAndDisplayValue.length <= 10
          ? item.priorityShow === 2 && item.displayValue
            ? item.valueAndDisplayValue
            : item.value
          : item.priorityShow === 2 && item.displayValue
            ? `${item.valueAndDisplayValue}`
            : `${item.value}`;

      _tags.push(showTagValue);
    });
    setTags(_.uniq(_tags));
  }, [labelHistoryList]);

  return (
    <Spin spinning={loading}>
      <div className="tagHover">
        <div className="label-name">{selectValue?.name}</div>
        <div className="label-attr">
          <div>{`${t('analysisCenter-5XH4VjEiaux9')}${selectValue?.type ? typeMap[selectValue?.type] : ''}`}</div>
          <div>{`${t('analysisCenter-FvZW28nAmX4j')}${
            selectValue?.validDate
              ? dayjs(selectValue?.validDate).format('YYYY-MM-DD')
              : t('analysisCenter-73Aa74SHDj1R')
          }`}</div>
          <div>{`${t('analysisCenter-iKRZ1nLiBXVu')}${selectValue?.scenario?.name}[${selectValue?.scenario?.code}]`}</div>
        </div>
        <div style={{ marginTop: '20px' }}>
          {t('analysisCenter-p287BGsx5pVP')}
          {selectValue?.lastCalcTime ? dayjs(selectValue?.lastCalcTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </div>
        <div className="tagName">{t('analysisCenter-GulVBXcC9AuD')}</div>
        <div className="tags">
          {!_.isEmpty(tags) &&
            tags.map((item, index) => {
              return <Tag key={index}>{item}</Tag>;
            })}
        </div>
      </div>
    </Spin>
  );
};
