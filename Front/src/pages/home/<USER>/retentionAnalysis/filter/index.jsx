/*
 * @Author: Wxw
 * @Date: 2022-07-26 11:04:29
 * @LastEditTime: 2023-04-20 15:23:29
 * @LastEditors: 卜广硕 <EMAIL>
 * @Description:全局过滤步骤
 * @FilePath: \Front\src\pages\home\analysisCenter\retentionAnalysis\filter\index.jsx
 */
import { Button } from 'antd';
import _ from 'lodash';
import React, { useContext } from 'react';
import FilterItem from './filterItem';
import './index.scss';
// import icon from '../icon';

import { t } from 'utils/translation';
import { retentionContext } from '../retentionContext';

export default function GlobalFilter() {
  const { state, dispatch } = useContext(retentionContext);
  const { globalFilters } = state;

  const addGlobalFilters = () => {
    const _globalFilters = _.cloneDeep(globalFilters);
    _globalFilters.push({});
    dispatch({ globalFilters: _globalFilters });
  };

  return (
    <retentionContext.Provider value={{ state, dispatch }}>
      <div className="globalFilter" style={{ border: _.isEmpty(globalFilters) && 'none' }}>
        {!_.isEmpty(globalFilters) &&
          globalFilters.map((item, index) => {
            // return <FilterItem key={`${JSON.stringify(item)}${index}`} flag filterIndex={index} value={globalFilters} isFilter />;
            return <FilterItem key={index} flag filterIndex={index} value={globalFilters} isFilter />;
          })}
      </div>
      <Button className="addButton" type="dashed" block onClick={addGlobalFilters}>
        +{t('analysisCenter-doe4OgdrTb5o')}
      </Button>
    </retentionContext.Provider>
  );
}
