import { Popover } from 'antd';
import _ from 'lodash';
import React, { useContext, useState } from 'react';
import { t } from 'utils/translation';
import { retentionContext } from '../../retentionContext';
import Campaign from '../../steps/campaign';

/**
 * @description: 选择流程画布节点
 * @param {*} flag 是否是步骤过滤
 * @param {*} index 步骤下标
 * @param {*} ind 过滤的下标
 * @param {*} value 当前步骤过滤条件
 * @param {*} isMulti 是否是多路径
 * @return {*}
 */
export default ({ index, ind, filterIndex, isMulti }) => {
  const contextValue = useContext(retentionContext);
  const { state, flag } = contextValue;
  const { stepList, globalFilters } = state;
  const [visible, setVisible] = useState(false);

  const renderCampaiginFilterValue = () => {
    return (
      <div>
        <Campaign
          isMulti={isMulti}
          flag={flag}
          filterIndex={filterIndex}
          isFilter
          stepIndex={index}
          ind={ind}
          setVisible={setVisible}
          type={
            flag
              ? globalFilters[filterIndex].campaignGroup.filterType
              : isMulti
                ? stepList[index].multiStepList[ind].filters[filterIndex].campaignGroup.filterType
                : stepList[index].filters[filterIndex].campaignGroup.filterType
          }
        />
      </div>
    );
  };

  const renderTitle = () => {
    let title = '';
    let opacity = false;
    let namelength = null;
    let type;
    let filter;
    if (flag) {
      const _globalFilters = _.cloneDeep(globalFilters);
      const { filterType, filters } = _globalFilters[filterIndex].campaignGroup;
      type = filterType;
      filter = filters;
    } else {
      const _stepList = _.cloneDeep(stepList);
      const _campaignGroup = !isMulti
        ? _stepList[index].filters[filterIndex].campaignGroup
        : _stepList[index].multiStepList[ind].filters[filterIndex].campaignGroup;
      const { filterType, filters } = _campaignGroup;
      type = filterType;
      filter = filters;
    }
    if (type === 'CAMPAIGN_BATCH') {
      // 把filters每一项的logList的length加起来
      namelength = filter.reduce((total, item) => {
        return total + item.logList.length;
      }, 0);
      if (namelength === 0) {
        title = t('analysisCenter-DbM3Ist00Jqd');
        opacity = true;
      } else {
        title = `${namelength} ${t('analysisCenter-JgNcqtgW3PXp')}`;
      }
    } else if (type === 'CAMPAIGN_NODE') {
      // 把filters每一项的logList里每一项的selectedFlows的length加起来
      namelength = filter.reduce((total, item) => {
        return (
          total +
          item.logList.reduce((total1, item1) => {
            return total1 + item1.selectedFlows.length;
          }, 0)
        );
      }, 0);
      if (namelength === 0) {
        title = t('analysisCenter-dMz9Z5bVqNUR');
        opacity = true;
      } else {
        title = `${namelength} ${t('analysisCenter-qimwrbVJ6Yps')}`;
      }
    } else {
      return t('analysisCenter-JyVBYnKKzdjJ');
    }

    return <div style={{ opacity: opacity ? 0.4 : 1 }}>{title}</div>;
  };

  return (
    <div className="campaignFilterValue" style={{ width: '100%', paddingLeft: '5px' }}>
      <Popover
        getPopupContainer={() => document.getElementsByClassName('content')[0]}
        content={renderCampaiginFilterValue()}
        trigger="click"
        destroyTooltipOnHide
        overlayStyle={{ minWidth: '320px' }}
        autoAdjustOverflow
        open={visible}
        onOpenChange={setVisible}
        placement="bottom"
        className="conversion"
      >
        <div className="stepTitle">{renderTitle()}</div>
      </Popover>
    </div>
  );
};
