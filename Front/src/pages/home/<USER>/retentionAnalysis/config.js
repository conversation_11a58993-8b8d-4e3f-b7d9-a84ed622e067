import dayjs from 'dayjs';
import { t } from 'utils/translation';

const timeTerm = [
  {
    value: 'DAY',
    label: t('analysisCenter-jwSRjZA3Hoi1'),
    unit: 'days'
  },
  {
    value: 'WEEK',
    label: t('analysisCenter-tFFIPY9stXaW'),
    unit: 'weeks'
  },
  {
    value: 'MONTH',
    label: t('analysisCenter-lBLtIUYoYIDN'),
    unit: 'months'
  }
];

export const connectorConfig = {
  maxFilterCount: 20,
  operatorList: [
    {
      name: t('analysisCenter-vRmcaNhGopSx'),
      operator: 'EQ'
    },
    {
      name: t('analysisCenter-VMEOFncXp48O'),
      operator: 'NE'
    },
    {
      name: t('analysisCenter-V2L7T2CIM8vN'),
      operator: 'GT'
    },
    {
      name: t('analysisCenter-Zp6SUm4wLrRT'),
      operator: 'GTE'
    },
    {
      name: t('analysisCenter-S5IP3X9wn10e'),
      operator: 'LT'
    },
    {
      name: t('analysisCenter-iERR4NhMO0Kg'),
      operator: 'LTE'
    },
    {
      name: t('analysisCenter-h4UrTGSI5do0'),
      operator: 'BETWEEN'
    },
    {
      name: t('analysisCenter-1sheWwIok5tw'),
      operator: 'ADVANCED_BETWEEN'
    },
    {
      name: t('analysisCenter-5igHC14xBobq'),
      operator: 'IN'
    },
    {
      name: t('analysisCenter-u4oH8gawdUWv'),
      operator: 'NOT_IN'
    },
    {
      name: t('analysisCenter-KvAqvQn8DhZk'),
      operator: 'IS_NOT_NULL'
    },
    {
      name: t('analysisCenter-yl3wQaw9LPuy'),
      operator: 'IS_NULL'
    },
    {
      name: t('analysisCenter-6WUcsmLQxsba'),
      operator: 'LIKE'
    },
    {
      name: t('analysisCenter-S8Ji3IN7esUV'),
      operator: 'NOT_LIKE'
    },
    {
      name: t('analysisCenter-Gjxk9eoNFIvT'),
      operator: 'START_WITH'
    },
    {
      name: t('analysisCenter-WuTuvr7vooI2'),
      operator: 'NOT_START_WITH'
    },
    {
      name: t('analysisCenter-z3S9UEkYL1It'),
      operator: 'END_WITH'
    },
    {
      name: t('analysisCenter-E5EfAHTwb9wa'),
      operator: 'NOT_END_WITH'
    },
    {
      name: t('analysisCenter-4FfFqmIgBc8f'),
      operator: 'IS_TRUE'
    },
    {
      name: t('analysisCenter-YfV068uerocm'),
      operator: 'IS_FALSE'
    }
  ],
  typeOperator: {
    INT: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    LONG: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    DOUBLE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    HIVE_DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
    DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
    DATETIME: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
    HIVE_TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
    TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
    STRING: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'LIKE',
      'NOT_LIKE',
      'START_WITH',
      'NOT_START_WITH',
      'END_WITH',
      'NOT_END_WITH',
      'IN',
      'NOT_IN'
    ],
    BOOL: ['IS_TRUE', 'IS_FALSE']
  },
  typeFormatter: {
    DATE: 'YYYY-MM-DD',
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    TIMESTAMP: 'YYYY-MM-DD HH:mm:ss',
    HIVE_DATE: 'YYYY-MM-DD',
    HIVE_TIMESTAMP: 'YYYY-MM-DD HH:mm:ss'
  },
  connector: [
    {
      name: t('analysisCenter-yG0mcGRlwa6R'),
      value: 'AND'
    },
    {
      name: t('analysisCenter-GK1tkOy8GcH1'),
      value: 'OR'
    }
  ],
  validator: {
    STRING: {
      option: {
        required: true,
        maxLen: 500
      },
      message: {
        required: t('analysisCenter-Yof4MEvrITA1'),
        maxLen: t('analysisCenter-Lv53CErIO9tj')
      }
    },
    INT: {
      option: {
        required: true,
        maxLen: 11,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: t('analysisCenter-Yof4MEvrITA1'),
        maxLen: t('analysisCenter-XUCumvNo3qF6'),
        regex: t('analysisCenter-Rgoaxv7HEHhg')
      }
    },
    LONG: {
      option: {
        required: true,
        maxLen: 20,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: t('analysisCenter-Yof4MEvrITA1'),
        maxLen: t('analysisCenter-ByLHOmfr729V'),
        regex: t('analysisCenter-Rgoaxv7HEHhg')
      }
    },
    DOUBLE: {
      option: {
        required: true,
        // regex: '^\\d*[.]?\\d*$',
        regex: '^[+-]?\\d+(\\.\\d+)?$',
        maxLen: 20
      },
      message: {
        required: t('analysisCenter-Yof4MEvrITA1'),
        maxLen: t('analysisCenter-ByLHOmfr729V'),
        regex: t('analysisCenter-pkagAfvVTcOd')
      }
    },
    DATETIME: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-VFW1cqIWZeix')
      }
    },
    TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-VFW1cqIWZeix')
      }
    },
    DATE: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-DMhZbPyuLi7P')
      }
    },
    HIVE_TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-VFW1cqIWZeix')
      }
    },
    HIVE_DATE: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-DMhZbPyuLi7P')
      }
    },
    BOOL: {
      option: {
        required: false
      },
      message: {
        required: t('analysisCenter-Yof4MEvrITA1')
      }
    }
  }
};
export const getTime = (obj) => {
  let time = dayjs();
  if (obj.type === 'ABSOLUTE') {
    time = dayjs(obj.timestamp);
  } else if (obj.type === 'RELATIVE') {
    const info = timeTerm.find((n) => n.value === obj.timeTerm);
    if (obj.isPast) {
      time = dayjs().subtract(obj.times, info.unit);
    } else {
      time = dayjs().add(obj.times, info.unit);
    }
  }
  return time.valueOf();
};
