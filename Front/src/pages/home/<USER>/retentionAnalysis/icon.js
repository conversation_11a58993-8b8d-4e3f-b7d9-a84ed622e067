import React from 'react';

/**
 * @description: 漏斗图图标
 * MOUSE : 鼠标
 */
const iconSvg = {
  MOUSE: (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.93378 10.8712L9.19132 13.0572L6.43021 6.42972L13.0577 9.19083L10.8717 9.93329C10.4303 10.0832 10.0837 10.4298 9.93378 10.8712ZM5.69402 5.03969C5.28191 4.868 4.86849 5.28142 5.04018 5.69353L8.7774 14.6639C8.95464 15.0893 9.56418 15.0688 9.71239 14.6324L10.7905 11.4581C10.8142 11.5068 10.8463 11.5524 10.8867 11.5928L13.7197 14.4263C13.9149 14.6216 14.2315 14.6216 14.4268 14.4263C14.6221 14.2311 14.6221 13.9145 14.4268 13.7192L11.5938 10.8857C11.5535 10.8454 11.508 10.8134 11.4595 10.7898L14.6329 9.7119C15.0693 9.56369 15.0898 8.95415 14.6644 8.77692L5.69402 5.03969Z"
        fill="black"
        fillOpacity="0.65"
      />
    </svg>
  ),
  FILTTER: (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M3.4 2.7L3.25 2.5H3H2.5V1.5H13.5V2.5H13H12.75L12.6 2.7L9.7 6.56667C9.57018 6.73976 9.5 6.9503 9.5 7.16667V12.691L6.5 14.191V7.16667C6.5 6.9503 6.42982 6.73976 6.3 6.56667L3.4 2.7Z"
        stroke="black"
        strokeOpacity="0.45"
      />
    </svg>
  ),
  CLOSE: (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_8140_264637)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.74849 3.05021L3.04139 3.75732L7.28455 8.00048L3.04236 12.2427L3.74946 12.9498L7.99166 8.70759L12.2338 12.9497L12.9409 12.2426L8.69876 8.00048L12.9419 3.75739L12.2347 3.05029L7.99166 7.29338L3.74849 3.05021Z"
          fill="black"
          fillOpacity="0.45"
        />
      </g>
      <defs>
        <clipPath id="clip0_8140_264637">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  MORE: (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="8" cy="3" r="1" fill="black" fillOpacity="0.45" />
      <circle cx="8" cy="8" r="1" fill="black" fillOpacity="0.45" />
      <circle cx="8" cy="13" r="1" fill="black" fillOpacity="0.45" />
    </svg>
  ),
  EVENT: (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7 1.5C7 1.22386 6.77614 1 6.5 1C6.22386 1 6 1.22386 6 1.5V3.5C6 3.77614 6.22386 4 6.5 4C6.77614 4 7 3.77614 7 3.5V1.5ZM3.3184 2.6108C3.12313 2.41554 2.80655 2.41554 2.61129 2.6108C2.41603 2.80606 2.41603 3.12265 2.61129 3.31791L4.0255 4.73212C4.22077 4.92738 4.53735 4.92738 4.73261 4.73212C4.92787 4.53686 4.92787 4.22028 4.73261 4.02502L3.3184 2.6108ZM10.3887 2.61056C10.584 2.80582 10.584 3.1224 10.3887 3.31766L8.9745 4.73188C8.77923 4.92714 8.46265 4.92714 8.26739 4.73188C8.07213 4.53662 8.07213 4.22003 8.26739 4.02477L9.6816 2.61056C9.87687 2.4153 10.1934 2.4153 10.3887 2.61056ZM4.73246 8.97489C4.92772 8.77963 4.92772 8.46305 4.73246 8.26778C4.5372 8.07252 4.22061 8.07252 4.02535 8.26778L2.61114 9.682C2.41588 9.87726 2.41588 10.1938 2.61114 10.3891C2.8064 10.5844 3.12298 10.5844 3.31825 10.3891L4.73246 8.97489ZM4 6.49976C4 6.7759 3.77614 6.99976 3.5 6.99976L1.5 6.99976C1.22386 6.99976 1 6.7759 1 6.49976C1 6.22361 1.22386 5.99976 1.5 5.99976L3.5 5.99976C3.77614 5.99976 4 6.22361 4 6.49976ZM9.73936 10.677L9.04219 12.7296L6.80376 6.80352L12.7299 9.04195L10.6773 9.73911C10.2359 9.88903 9.88927 10.2356 9.73936 10.677ZM6.12153 5.47686C5.71889 5.32478 5.32502 5.71865 5.4771 6.12128L8.59943 14.3874C8.76522 14.8264 9.38973 14.8158 9.54062 14.3716L10.6862 10.9986C10.7362 10.8515 10.8517 10.736 10.9989 10.686L14.3718 9.54037C14.8161 9.38948 14.8266 8.76498 14.3877 8.59919L6.12153 5.47686Z"
        fill="black"
      />
    </svg>
  ),
  CAMPAIGN: (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.78004 3.59408L7.99934 2.39983L9.21864 3.59408C9.49515 3.86491 9.86564 4.01838 10.2527 4.02239L11.9593 4.0401L11.977 5.74674C11.981 6.13377 12.1345 6.50426 12.4053 6.78077L13.5996 8.00007L12.4053 9.21937C12.1345 9.49588 11.981 9.86637 11.977 10.2534L11.9593 11.96L10.2527 11.9777C9.86564 11.9818 9.49515 12.1352 9.21864 12.4061L7.99934 13.6003L6.78004 12.4061C6.50353 12.1352 6.13304 11.9818 5.74601 11.9777L4.03937 11.96L4.02166 10.2534C4.01764 9.86637 3.86418 9.49588 3.59335 9.21937L2.3991 8.00007L3.59335 6.78077C3.86418 6.50426 4.01764 6.13377 4.02166 5.74674L4.03937 4.0401L5.74601 4.02239C6.13303 4.01838 6.50353 3.86491 6.78004 3.59408ZM7.64947 1.34275C7.84386 1.15235 8.15481 1.15235 8.3492 1.34275L9.91837 2.87968C10.0105 2.96995 10.134 3.02111 10.263 3.02245L12.4594 3.04524C12.7315 3.04806 12.9513 3.26794 12.9542 3.54002L12.977 5.73636C12.9783 5.86537 13.0295 5.98887 13.1197 6.08104L14.6567 7.6502C14.8471 7.84459 14.8471 8.15555 14.6567 8.34994L13.1197 9.9191C13.0295 10.0113 12.9783 10.1348 12.977 10.2638L12.9542 12.4601C12.9513 12.7322 12.7315 12.9521 12.4594 12.9549L10.263 12.9777C10.134 12.979 10.0105 13.0302 9.91837 13.1205L8.3492 14.6574C8.15481 14.8478 7.84386 14.8478 7.64947 14.6574L6.08031 13.1205C5.98814 13.0302 5.86464 12.979 5.73563 12.9777L3.53929 12.9549C3.26721 12.9521 3.04733 12.7322 3.04451 12.4601L3.02171 10.2638C3.02037 10.1348 2.96922 10.0113 2.87894 9.9191L1.34202 8.34994C1.15162 8.15555 1.15162 7.84459 1.34202 7.6502L2.87894 6.08104C2.96922 5.98887 3.02037 5.86537 3.02171 5.73636L3.04451 3.54002C3.04733 3.26794 3.26721 3.04806 3.53929 3.04524L5.73563 3.02245C5.86464 3.02111 5.98814 2.96995 6.08031 2.87968L7.64947 1.34275ZM5.647 9.6465L9.647 5.6465L10.354 6.3535L6.354 10.3535L5.647 9.6465ZM6.92426 5.61724C6.97456 5.73858 7.00047 5.86865 7.0005 6C7.00057 6.26528 6.89525 6.51973 6.70771 6.70736C6.52017 6.89499 6.26578 7.00043 6.0005 7.0005C5.73522 7.00057 5.48077 6.89525 5.29314 6.70771C5.10551 6.52017 5.00007 6.26578 5 6.0005C4.99997 5.86915 5.02581 5.73907 5.07604 5.6177C5.12628 5.49633 5.19993 5.38605 5.29279 5.29314C5.38565 5.20024 5.4959 5.12653 5.61724 5.07624C5.73858 5.02594 5.86865 5.00003 6 5C6.13135 4.99997 6.26143 5.02581 6.3828 5.07604C6.50417 5.12628 6.61445 5.19993 6.70736 5.29279C6.80026 5.38565 6.87397 5.4959 6.92426 5.61724ZM9.61774 9.07623C9.73908 9.02594 9.86915 9.00003 10.0005 9C10.2658 8.99993 10.5202 9.10525 10.7079 9.29279C10.8955 9.48033 11.0009 9.73472 11.001 10C11.0011 10.2653 10.8957 10.5197 10.7082 10.7074C10.5207 10.895 10.2663 11.0004 10.001 11.0005C9.86965 11.0005 9.73957 10.9747 9.6182 10.9245C9.49683 10.8742 9.38655 10.8006 9.29364 10.7077C9.20074 10.6149 9.12703 10.5046 9.07674 10.3833C9.02644 10.2619 9.00053 10.1319 9.0005 10.0005C9.00047 9.86915 9.02631 9.73907 9.07654 9.6177C9.12678 9.49633 9.20043 9.38605 9.29329 9.29314C9.38615 9.20024 9.4964 9.12653 9.61774 9.07623Z"
        fill="black"
      />
    </svg>
  ),
  BIZ_TABLE: (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.5 3H12.5V13H3.5L3.5 3ZM2.5 3C2.5 2.44772 2.94772 2 3.5 2H12.5C13.0523 2 13.5 2.44772 13.5 3V13C13.5 13.5523 13.0523 14 12.5 14H3.5C2.94771 14 2.5 13.5523 2.5 13V3ZM5 6H11V5H5V6ZM5 8.5H11V7.5H5V8.5ZM9 11H5V10H9V11Z"
        fill="black"
      />
    </svg>
  ),
  tag: (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.0397 8.75308L9.25974 13.5331C9.13591 13.657 8.98886 13.7554 8.827 13.8225C8.66513 13.8896 8.49163 13.9241 8.31641 13.9241C8.14119 13.9241 7.96769 13.8896 7.80582 13.8225C7.64396 13.7554 7.49691 13.657 7.37308 13.5331L2.35352 8.52018V2.35352C2.35352 2.07737 2.57737 1.85352 2.85352 1.85352H9.02018L14.0397 6.87308C14.2881 7.12289 14.4275 7.46083 14.4275 7.81308C14.4275 8.16532 14.2881 8.50326 14.0397 8.75308ZM8.07971 12.8255L3.35352 8.10558V2.85352H8.60597L13.3305 7.57808L13.3312 7.57873C13.3929 7.64112 13.4275 7.72532 13.4275 7.81308C13.4275 7.90083 13.3929 7.98503 13.3312 8.04742L13.3305 8.04808L8.55263 12.826L8.55224 12.8264C8.52128 12.8574 8.48452 12.8819 8.44406 12.8987C8.40359 12.9155 8.36022 12.9241 8.31641 12.9241C8.2726 12.9241 8.22923 12.9155 8.18876 12.8987C8.1483 12.8819 8.11153 12.8574 8.08058 12.8264L8.07971 12.8255ZM6.5 7C7.05228 7 7.5 6.55228 7.5 6C7.5 5.44772 7.05228 5 6.5 5C5.94772 5 5.5 5.44772 5.5 6C5.5 6.55228 5.94772 7 6.5 7Z"
        fill="black"
      />
    </svg>
  ),
  down: (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.99998 10.707L3.64648 6.35348L4.35348 5.64648L7.99998 9.29298L11.6465 5.64648L12.3535 6.35348L7.99998 10.707Z"
        fill="black"
      />
    </svg>
  ),
  up: (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M11.6465 10.3535L7.99998 6.70697L4.35348 10.3535L3.64648 9.64647L7.99998 5.29297L12.3535 9.64647L11.6465 10.3535Z"
        fill="black"
      />
    </svg>
  ),
  CHANGE_CHART: (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <mask id="path-1-inside-1_2583_254674" fill="white">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M2 2C1.44772 2 1 2.44772 1 3V5C1 5.55228 1.44772 6 2 6H4C4.55228 6 5 5.55228 5 5V3C5 2.44772 4.55228 2 4 2H2ZM2 7C1.44772 7 1 7.44772 1 8V10C1 10.5523 1.44772 11 2 11H4C4.55228 11 5 10.5523 5 10V8C5 7.44772 4.55228 7 4 7H2ZM1 13C1 12.4477 1.44772 12 2 12H4C4.55228 12 5 12.4477 5 13V15C5 15.5523 4.55228 16 4 16H2C1.44772 16 1 15.5523 1 15V13ZM7 2C6.44772 2 6 2.44772 6 3V5C6 5.55228 6.44772 6 7 6H9C9.55229 6 10 5.55228 10 5V3C10 2.44772 9.55229 2 9 2H7ZM6 8C6 7.44772 6.44772 7 7 7H9C9.55229 7 10 7.44772 10 8V10C10 10.5523 9.55229 11 9 11H7C6.44772 11 6 10.5523 6 10V8ZM12 2C11.4477 2 11 2.44772 11 3V5C11 5.55228 11.4477 6 12 6H14C14.5523 6 15 5.55228 15 5V3C15 2.44772 14.5523 2 14 2H12Z"
        />
      </mask>
      <path
        d="M2 3V1C0.895431 1 0 1.89543 0 3H2ZM2 5V3H0V5H2ZM2 5H0C0 6.10457 0.89543 7 2 7V5ZM4 5H2V7H4V5ZM4 5V7C5.10457 7 6 6.10457 6 5H4ZM4 3V5H6V3H4ZM4 3H6C6 1.89543 5.10457 1 4 1V3ZM2 3H4V1H2V3ZM2 8V6C0.89543 6 0 6.89543 0 8H2ZM2 10V8H0V10H2ZM2 10H0C0 11.1046 0.895431 12 2 12V10ZM4 10H2V12H4V10ZM4 10V12C5.10457 12 6 11.1046 6 10H4ZM4 8V10H6V8H4ZM4 8H6C6 6.89543 5.10457 6 4 6V8ZM2 8H4V6H2V8ZM2 11C0.895431 11 0 11.8954 0 13H2V11ZM4 11H2V13H4V11ZM6 13C6 11.8954 5.10457 11 4 11V13H6ZM6 15V13H4V15H6ZM4 17C5.10457 17 6 16.1046 6 15H4V17ZM2 17H4V15H2V17ZM0 15C0 16.1046 0.895431 17 2 17V15H0ZM0 13V15H2V13H0ZM7 3V1C5.89543 1 5 1.89543 5 3H7ZM7 5V3H5V5H7ZM7 5H5C5 6.10457 5.89543 7 7 7V5ZM9 5H7V7H9V5ZM9 5V7C10.1046 7 11 6.10457 11 5H9ZM9 3V5H11V3H9ZM9 3H11C11 1.89543 10.1046 1 9 1V3ZM7 3H9V1H7V3ZM7 6C5.89543 6 5 6.89543 5 8H7V6ZM9 6H7V8H9V6ZM11 8C11 6.89543 10.1046 6 9 6V8H11ZM11 10V8H9V10H11ZM9 12C10.1046 12 11 11.1046 11 10H9V12ZM7 12H9V10H7V12ZM5 10C5 11.1046 5.89543 12 7 12V10H5ZM5 8V10H7V8H5ZM12 3V1C10.8954 1 10 1.89543 10 3H12ZM12 5V3H10V5H12ZM12 5H10C10 6.10457 10.8954 7 12 7V5ZM14 5H12V7H14V5ZM14 5V7C15.1046 7 16 6.10457 16 5H14ZM14 3V5H16V3H14ZM14 3H16C16 1.89543 15.1046 1 14 1V3ZM12 3H14V1H12V3Z"
        fill="black"
        fillOpacity="0.85"
        mask="url(#path-1-inside-1_2583_254674)"
      />
      <rect x="1" width="14" height="1" fill="black" fillOpacity="0.85" />
    </svg>
  ),
  TREND_CHART: (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <mask id="path-1-inside-1_2583_254685" fill="white">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3 1C2.44772 1 2 1.44772 2 2V4C2 4.55228 2.44772 5 3 5H5C5.55228 5 6 4.55228 6 4V2C6 1.44772 5.55228 1 5 1H3ZM3 6C2.44772 6 2 6.44772 2 7V9C2 9.55229 2.44772 10 3 10H5C5.55228 10 6 9.55229 6 9V7C6 6.44772 5.55228 6 5 6H3ZM2 12C2 11.4477 2.44772 11 3 11H5C5.55228 11 6 11.4477 6 12V14C6 14.5523 5.55228 15 5 15H3C2.44772 15 2 14.5523 2 14V12ZM8 1C7.44772 1 7 1.44772 7 2V4C7 4.55228 7.44772 5 8 5H10C10.5523 5 11 4.55228 11 4V2C11 1.44772 10.5523 1 10 1H8ZM7 7C7 6.44772 7.44772 6 8 6H10C10.5523 6 11 6.44772 11 7V9C11 9.55229 10.5523 10 10 10H8C7.44772 10 7 9.55229 7 9V7ZM13 1C12.4477 1 12 1.44772 12 2V4C12 4.55228 12.4477 5 13 5H15C15.5523 5 16 4.55228 16 4V2C16 1.44772 15.5523 1 15 1H13Z"
        />
      </mask>
      <path
        d="M3 2V0C1.89543 0 1 0.895431 1 2H3ZM3 4V2H1V4H3ZM3 4H1C1 5.10457 1.89543 6 3 6V4ZM5 4H3V6H5V4ZM5 4V6C6.10457 6 7 5.10457 7 4H5ZM5 2V4H7V2H5ZM5 2H7C7 0.89543 6.10457 0 5 0V2ZM3 2H5V0H3V2ZM3 7V5C1.89543 5 1 5.89543 1 7H3ZM3 9V7H1V9H3ZM3 9H1C1 10.1046 1.89543 11 3 11V9ZM5 9H3V11H5V9ZM5 9V11C6.10457 11 7 10.1046 7 9H5ZM5 7V9H7V7H5ZM5 7H7C7 5.89543 6.10457 5 5 5V7ZM3 7H5V5H3V7ZM3 10C1.89543 10 1 10.8954 1 12H3V10ZM5 10H3V12H5V10ZM7 12C7 10.8954 6.10457 10 5 10V12H7ZM7 14V12H5V14H7ZM5 16C6.10457 16 7 15.1046 7 14H5V16ZM3 16H5V14H3V16ZM1 14C1 15.1046 1.89543 16 3 16V14H1ZM1 12V14H3V12H1ZM8 2V0C6.89543 0 6 0.89543 6 2H8ZM8 4V2H6V4H8ZM8 4H6C6 5.10457 6.89543 6 8 6V4ZM10 4H8V6H10V4ZM10 4V6C11.1046 6 12 5.10457 12 4H10ZM10 2V4H12V2H10ZM10 2H12C12 0.895431 11.1046 0 10 0V2ZM8 2H10V0H8V2ZM8 5C6.89543 5 6 5.89543 6 7H8V5ZM10 5H8V7H10V5ZM12 7C12 5.89543 11.1046 5 10 5V7H12ZM12 9V7H10V9H12ZM10 11C11.1046 11 12 10.1046 12 9H10V11ZM8 11H10V9H8V11ZM6 9C6 10.1046 6.89543 11 8 11V9H6ZM6 7V9H8V7H6ZM13 2V0C11.8954 0 11 0.895431 11 2H13ZM13 4V2H11V4H13ZM13 4H11C11 5.10457 11.8954 6 13 6V4ZM15 4H13V6H15V4ZM15 4V6C16.1046 6 17 5.10457 17 4H15ZM15 2V4H17V2H15ZM15 2H17C17 0.895431 16.1046 0 15 0V2ZM13 2H15V0H13V2Z"
        fill="black"
        fillOpacity="0.85"
        mask="url(#path-1-inside-1_2583_254685)"
      />
      <rect y="1" width="1" height="14" fill="black" fillOpacity="0.85" />
    </svg>
  )
};

export default iconSvg;
