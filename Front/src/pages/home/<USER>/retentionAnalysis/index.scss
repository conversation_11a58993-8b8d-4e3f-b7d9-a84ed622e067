.retentionAnalysis {
  header {
    display: flex;
    height: 60px;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    margin: 0 -24px;
    padding: 0 24px;
    border-bottom: 1px solid #f0f0f0;

    .left {
      display: flex;
      align-items: center;
      margin: 0;

      .titleBack {
        display: flex;
        cursor: pointer;
      }

      .leftHandleWrapper {
        min-width: 400px;
        margin-left: 8px;

        .ant-input {
          font-size: 20px;
          font-weight: 700;
          box-sizing: border-box;
        }

        &:hover {
          border: 1px solid $primary_color;
          // box-shadow: 0px 0px 0px 2px rgba(255, 104, 0, 0.25);
          border-radius: 6px;
        }
      }
    }

    .right {
      .ant-btn {
        // border-radius: 6px;
      }
    }
  }

  .content {
    height: calc(100vh - 120px);
    margin: 0 -24px;
    display: flex;
    overflow: hidden;
    border-right: 1px solid #f0f0f0;

    .ant-spin-nested-loading {
      width: 100%;

    }

    .anticon {
      //图标
      margin-right: 0;
    }

    .stepItem {
      .renderEvent {
        // position: relative;

        .eventBox {
          margin-top: 16px;
          height: 240px;
          overflow-y: auto;

          .popItem {
            .eventItem {
              position: relative;
              line-height: 32px;
              width: 100%;
              height: 32px;
              cursor: pointer;

              &:hover {
                background: $active_color;
              }
            }

          }
        }

        .eventTip {
          display: none;
          position: absolute;
          top: 0;
          right: -40px;
        }

        .eventInfo {
          .eventInfoTitle {
            font-weight: bold;
          }
        }
      }
    }

    .eventInfo {

      //子元素挂载到contend的样式
      .eventInfoTitle {
        font-size: 16px;
        font-weight: bold;
      }

      .eventInfoContent {
        .createInformation {
          color: rgba(0, 0, 0, 0.45);
        }

        .eventTable {
          margin-top: 16px;
          margin-bottom: 16px;
        }

        .line {
          .lineTitle {
            font-weight: bold;
            margin-bottom: 18px;
          }

          .lineChart {
            height: 280px;
          }
        }
      }
    }

    .left {
      height: calc(100vh - 120px);
      background-color: #fff;
      position: relative;

      .open {
        position: absolute;
        bottom: 20px;
        right: 20px;
        cursor: pointer;
      }

      .resize-save {
        position: absolute;
        top: 0;
        right: 5px;
        bottom: 0;
        left: 0;
        // padding: 24px;
        overflow-x: hidden;

        .screen {
          padding: 24px;
          border-bottom: 1px solid #f0f0f0;

          .ant-popover {
            .ant-popover-content>.ant-popover-inner {
              border-radius: 6px;

              .ant-popover-inner-content {
                padding: 12px 16px 3px 16px;

                .conversion-title {
                  font-size: 14px;
                  color: #8c8c8c;
                  margin-bottom: 24px;
                }

                .conversion-content {
                  display: flex;
                  justify-content: space-between;

                  .ant-input-number,
                  .ant-select {
                    width: 48%;
                    border-radius: 6px;

                    &>.ant-select-selector {
                      border-radius: 6px;
                    }
                  }
                }

                .tip {
                  font-size: 14px;
                  color: #FAAD14;
                }
              }
            }
          }

          .conversion {
            padding: 0 11px;
            cursor: pointer;
            color: #000;

            .svg {
              margin-left: 6px;
              font-size: 12px;
            }
          }

          &>div {
            color: #595959;
            height: 32px;
            line-height: 32px;

            .anticon-down {
              color: #000;
            }
          }
        }

        .process {
          padding: 24px;

          .title {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 20px;
          }

          .ant-tree {
            .ant-tree-node-content-wrapper {
              padding: 0;
              cursor: text;
            }

            .ant-tree-title {
              display: inline-block;
              width: 300px;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
          }

          .treeNoopNone {
            .ant-tree-switcher-noop {
              display: none;
            }
          }
        }
      }

      .resize-bar {
        height: inherit;
        resize: horizontal;
        opacity: 0;
        cursor: ew-resize;
        width: 440px;
        min-width: 320px;
        /* 最小宽度 320px */
        max-width: 640px;
        /* 最大宽度 640px */
        overflow: scroll;
      }

      .resize-line {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        border-right: 1px solid #f0f0f0;
        border-left: 1px solid #f0f0f0;
        pointer-events: none;
      }

      .resize-bar:hover~.resize-line,
      .resize-bar:active~.resize-line {
        border-left: 2px solid $primary_color;
      }

      .resize-bar::-webkit-scrollbar {
        width: 300px;
        height: inherit;
      }
    }

    .right {
      height: calc(100vh - 120px);
      padding: 16px 32px 16px 16px;
      flex: 1 1 auto;
      background-color: #fff;
      box-sizing: border-box;
      // overflow: hidden;
      overflow-y: auto;

      .open {
        position: absolute;
        bottom: 20px;
        left: 10px;
        cursor: pointer;
      }

      .rightScreen {
        width: 100%;
        height: 48px;
        display: -webkit-inline-box;

        .inputs {
          height: 32px;
          line-height: 32px;
          display: flex;

          .division {
            margin: 0 4px;
          }

          .setTime {
            padding: 0 12px;
            display: flex;
            border-radius: 6px;
            border: 1px solid #d9d9d9;

            input {
              width: 50px;
              height: 32px;
              text-align: center;
            }
          }
        }

        .retentionType {
          margin-left: 16px;

          .ant-select-selector {
            border-radius: 6px;
          }
        }

        .site-input-group-wrapper1 {

          .ant-input-group {
            .ant-input {
              &:first-child {
                // border-left: none;
                // border-radius: 6px 0 0 6px;
              }

              &:last-child {
                border-radius: 0 6px 6px 0;
              }
            }

          }

          .shortcutTime {
            .ant-popover {
              width: 253px !important;

              .shortcutOptions-item {
                width: 100px;
                height: 32px;
                line-height: 32px;
                border-radius: 6px;
              }
            }
          }
        }

        // margin-bottom: 16px;
        &>.ant-select {
          margin-left: 8px;

          .ant-select-selector {
            border-radius: 6px;
          }
        }

        .selectTimeV2 .selectTimeV2-content {
          width: auto;

          .contentLeft {
            border-radius: 6px;
          }

          .shortcutOptions {
            .shortcutOptions-item {
              width: 90px;
            }
          }
        }
      }

      .chartSelect {
        margin: 0 8px 0 auto;

        .ant-select-selector {
          border-radius: 6px;
        }
      }

      .chartType {
        .ant-btn-primary {
          background: var(--primary-hover-bg);
          border-color: var(--primary-hover-bg) !important;
          color: var(--ant-primary-color);
          ;
        }

        .ant-btn {
          width: 32px;
          height: 32px;
          line-height: 32px;
          border-radius: 6px;
          position: relative;
          border-color: aliceblue;

          span {
            position: absolute;
            top: 4px;
            left: 1px;
            bottom: 0;
            right: 0;
          }
        }

        &>:last-child {
          margin-left: 2px;
        }
      }

      .chart {
        margin-top: 16px;
        padding: 0 80px;
      }

      .table1 {
        padding: 0 80px;

        .tableTitle {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;

          &>:first-child {
            font-size: 16px;
            font-weight: bold;
          }

        }
      }

      .empty {
        &>.ant-empty {
          line-height: 300px;
        }
      }
    }
  }

  // .ant-btn {
  //   border-radius: 6px;
  // }

  .addButton {
    margin-top: 16px;
    margin-bottom: 40px;
    border-radius: 6px;
    // color: rgba(0, 0, 0, 0.25);

    // &:hover {
    //   color: $primary_color;
    // }
  }

  .term {
    margin-left: 24px;
    display: flex;

    .title {
      line-height: 32px;
    }

    .ant-card>.ant-card-body {
      padding: 0;
      line-height: 32px;
      padding-left: 8px;

      .anticon-close {
        font-size: 12px;
        margin-left: 9px;
        cursor: pointer;
      }
    }
  }

  .ant-select-selection-overflow {
    padding-right: 33px;
  }

  .ant-input-group-compact {
    width: 260px !important;

    &>:first-child {
      width: 110px !important;
    }

    &>:last-child {
      width: 110px !important;
    }
  }
}