.steps {

  .title {
    margin-bottom: 16px;
  }

  .step {
    border: 1px solid #E9E9E9;
    margin-bottom: 16px;
    border-radius: 6px;

    &>div {
      border-top: 1px solid #E9E9E9;

      &:first-child {
        border-top: none;
      }
    }

    .stepsItem {
      position: relative;
      // width: 392px;
      height: 40px;
      display: flex;
      // background-color: antiquewhite;
      // margin-top: 16px;
      border-radius: 6px;
      // border: 1px solid #E9E9E9;
      padding: 10px 12px;

      .ant-popover {}

      .sort {
        text-align: center;
        position: relative;
        width: 22px;
        height: 22px;
        border-radius: 4px;
        background: $primary_color;
        color: #fff;
        font-size: 12px;
        line-height: 22px;

        span {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }

      .mouse {
        margin: 0 8px;
        font-size: 16px;
      }

      .stepContent {
        width: calc(100% - 130px);
        // min-width: 46%;
        margin: -8px 0;
        align-items: center;
        cursor: pointer;

        &:hover {
          background-color: $active_color;
          border-radius: 6px;
        }

        .stepTitle {

          line-height: 36px;
          padding-left: 8px;
          color: rgba(0, 0, 0, 0.85);
          //定一个宽度,不换行 超出自动省略
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px;
          font-weight: 500;
          width: 100%;
        }
      }

      .icons {
        display: flex;
        margin-left: auto;

        .stepIcon {
          font-size: 16px;
          display: none;
          color: rgba(0, 0, 0, 0.45);

          span {
            margin-right: 4px;
            cursor: pointer;
          }
        }

        .more {
          padding-top: 2px;
          cursor: pointer;
        }
      }

      .stepHover {
        position: absolute;
        display: none;
        right: 0;
      }

      //hover的时候 显示.stepIcon
      &:hover {
        .stepIcon {
          display: flex;
        }
      }
    }

    .drawer {
      // border: 1px solid #e9e9e9;
    }
  }
}