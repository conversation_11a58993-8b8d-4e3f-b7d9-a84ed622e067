import { Button, Dropdown, Input, Modal, Popover, Tabs, Typography } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
// import FunnelAnalysis from 'service/funnelAnalysis.js';
import _ from 'lodash';
import { MyIconV2 } from 'utils/myIcon';
import { t } from 'utils/translation';
import FilterItem from '../filter/filterItem';
import icon from '../icon';
import { retentionContext } from '../retentionContext';
import Campaign from './campaign';
import DataTable from './dataTable';
import Event from './event';
import './index.scss';

const { Text } = Typography;
export default function Steps() {
  const { state, dispatch, scenarioList } = useContext(retentionContext);
  const { stepList } = state;
  const [showNameVisiable, setShowNameVisiable] = useState(false);
  const [editSteps, setEditSteps] = useState(stepList);
  const [editName, setEditName] = useState({
    name: null,
    step: null,
    flag: false,
    index: null,
    ind: null,
    defaultName: null
  });

  useEffect(() => {
    setEditSteps(stepList);
  }, [stepList]);

  const rename = (stepId, flag, index) => {
    setEditName({
      step: stepId,
      flag,
      index,
      defaultName: editSteps[index].name,
      name: !_.isEmpty(editSteps[index].displayName) ? editSteps[index].displayName : editSteps[index].name
    });
    setShowNameVisiable(true);
  };

  // 添加过滤条件
  const openDrawer = (index) => {
    const _stepList = _.cloneDeep(stepList);
    if (_stepList[index].filters) {
      _stepList[index].filters = [..._stepList[index].filters, { id: _stepList[index].filters.length + 1 }];
    } else {
      _stepList[index].filters = [{ id: 1 }];
    }
    dispatch({ stepList: _stepList });
  };

  /**
   * @description: 点击步骤
   * @param {*} id 步骤id
   * @return {*}
   */
  const handleVisibleChange = async (stepIndex) => {
    const _steps = _.cloneDeep(stepList);
    _steps[stepIndex].visibleSetp = !_steps[stepIndex].visibleSetp;
    dispatch({
      stepList: _steps
    });
  };

  const changeTabs = (e, index) => {
    const _steps = _.cloneDeep(editSteps);
    const { step, visibleSetp } = _steps[index];
    _steps[index] = {
      type: e,
      step,
      name: t('analysisCenter-2Kf99UsdISJl'),
      visibleSetp,
      campaignList: [],
      event: {},
      bizTable: {}
    };
    setEditSteps(_steps);
  };

  /**
   * @description: 设置漏斗步骤
   * @param {*} item  漏斗步骤
   * @param {*} index 下标
   * @param {*} flag 是否是多路径对比
   * @param {*} i 对应的步骤
   * @param {*} ind 多路径下标
   * @return {*}
   */
  const RenderSetStep = (item, index) => {
    return (
      <div className="stepItem">
        <Tabs
          defaultActiveKey={item.type}
          onChange={(e) => changeTabs(e, index)}
          destroyInactiveTabPane
          items={[
            {
              label: (
                <span>
                  {' '}
                  <MyIconV2 type="icon-icon-event" style={{ marginRight: 0, fontSize: 16 }} />{' '}
                  {t('analysisCenter-G4b5fcEXAWkP')}
                </span>
              ),
              key: 'EVENT',
              children: <Event id={item.step} stepIndex={index} />
            },
            {
              label: (
                <span>
                  {' '}
                  <MyIconV2 type="icon-icon-datefile" style={{ marginRight: 0, fontSize: 16 }} />{' '}
                  {t('analysisCenter-8EXdqvKt3Eao')}
                </span>
              ),
              key: 'BIZ_TABLE',
              children: <DataTable id={item.step} stepIndex={index} />
            },
            {
              label: (
                <span>
                  {' '}
                  <MyIconV2 type="icon-icon-camping" style={{ marginRight: 0, fontSize: 16 }} />{' '}
                  {t('analysisCenter-x3OTdDdp6xJg')}
                </span>
              ),
              key: 'CAMPAIGN',
              children: <Campaign id={item.step} stepIndex={index} />
            }
          ]}
        />
      </div>
    );
  };

  const renderTitle = (name, displayName) => {
    const title = !_.isEmpty(displayName) ? displayName : name;
    return (
      // <Popover
      //   getPopupContainer={() => document.getElementsByClassName('content')[0]}
      //   content={<div>13321312</div>}
      //   trigger="hover"
      //   destroyTooltipOnHide
      //   overlayStyle={{ minWidth: '320px' }}
      //   autoAdjustOverflow
      //   placement="right"
      //   className="conversion"
      // >
      // </Popover>
      <Text
        style={{
          width: 'calc(100% - 20px)',
          color:
            title === t('analysisCenter-jAs8u1kVajLr') || title === t('analysisCenter-H7xEFojnZ0FU')
              ? 'rgba(0,0,0,.25)'
              : 'rgba(0,0,0,.85)'
        }}
        ellipsis={{ tooltip: title }}
      >
        {title}
      </Text>
    );
  };

  const handleOk = (reset) => {
    const { name, index, defaultName } = editName;
    const _steps = _.cloneDeep(stepList);
    _steps[index].displayName = !reset ? (name === '' ? _steps[index].name : name) : defaultName;
    dispatch({ stepList: _steps });
    setShowNameVisiable(false);
  };

  const handleCancel = () => {
    setShowNameVisiable(false);
  };

  /**
   * @description:
   * @param {*} type 类型
   * @return {*}
   */
  const renderIcon = (type) => {
    if (type === 'EVENT') {
      return <MyIconV2 type="icon-icon-event" />;
    } else if (type === 'BIZ_TABLE') {
      return <MyIconV2 type="icon-icon-datefile" />;
    } else if (type === 'CAMPAIGN') {
      return <MyIconV2 type="icon-icon-camping" />;
    }
  };

  return (
    <retentionContext.Provider
      value={{
        state,
        dispatch,
        handleVisibleChange,
        editSteps,
        setEditSteps,
        scenarioList
      }}
    >
      <div className="steps">
        {_.map(stepList, (item, index) => {
          // return <div key={`${JSON.stringify(item)}`}>
          return (
            <div key={index}>
              <div className="step" style={{ marginBottom: 40 }}>
                <div className="stepsItem">
                  <div className="mouse">{renderIcon(item.type)}</div>
                  <div className="stepContent">
                    {/* <div className="stepTitle">{renderTitle(item)}</div> */}
                    <Popover
                      getPopupContainer={() => document.getElementsByClassName('content')[0]}
                      content={RenderSetStep(item, index)}
                      trigger="click"
                      destroyTooltipOnHide
                      overlayStyle={{ minWidth: '320px' }}
                      autoAdjustOverflow
                      open={item.visibleSetp === true}
                      onOpenChange={() => handleVisibleChange(index)}
                      placement="bottom"
                      className="conversion"
                    >
                      <div className="stepTitle">{renderTitle(item.name, item?.displayName)}</div>
                    </Popover>
                  </div>
                  <div className="icons">
                    <div className="stepIcon">
                      <span onClick={() => openDrawer(index)}>
                        <MyIconV2 type="icon-icon-filter" />
                      </span>
                    </div>
                    <div className="more">
                      <Dropdown
                        getPopupContainer={(triggerNode) => triggerNode.parentNode}
                        menu={{
                          items: [
                            {
                              label: t('analysisCenter-FdP6oebTwnZC'),
                              onClick: () => rename(item.step, false, index),
                              key: 'rename'
                            }
                          ]
                        }}
                      >
                        {icon.MORE}
                      </Dropdown>
                    </div>
                  </div>
                </div>
                {item.filters &&
                  item.filters.map((filterItem, filterIndex) => {
                    return (
                      <div className="drawerItem" key={filterIndex}>
                        <FilterItem index={index} filterIndex={filterIndex} value={item.filters} />
                      </div>
                    );
                  })}
              </div>
              <div className="setUpSteps" hidden={index === 1}>
                <div className="title">{t('analysisCenter-EpWErjQm0Ntt')}</div>
              </div>
            </div>
          );
        })}
      </div>
      <Modal
        title={<h3>{t('analysisCenter-FdP6oebTwnZC')}</h3>}
        open={showNameVisiable}
        // onOk={handleOk}
        onCancel={() => setShowNameVisiable(false)}
        footer={[
          <Button key="reset" onClick={() => handleOk(true)}>
            {t('analysisCenter-16TTlbT5VEEr')}
          </Button>,
          <Button key="back" onClick={handleCancel}>
            {t('analysisCenter-Ax8AXGGI7ftu')}
          </Button>,
          <Button key="submit" type="primary" onClick={() => handleOk(false)}>
            {t('analysisCenter-cyEPTBfZuF9J')}
          </Button>
        ]}
      >
        <Input
          value={editName.name}
          maxLength={30}
          onChange={(e) =>
            setEditName({
              ...editName,
              name: e.target.value
            })
          }
        />
      </Modal>
    </retentionContext.Provider>
  );
}
