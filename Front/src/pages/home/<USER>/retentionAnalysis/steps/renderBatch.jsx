import { Table } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { t } from 'utils/translation';
import { retentionContext } from '../retentionContext';

const RenderBatch = ({ flag, batchValue, id, isMulti, ind, isFilter }) => {
  const contextValue = useContext(retentionContext);
  const { editSteps, setEditSteps, editGlobalFilter, setEditGlobalFilter } = contextValue;
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [batchList, setBatchList] = useState([]);
  const [findIndex, setFindIndex] = useState(null);
  const [page, setPage] = useState(localStorage.getItem('popOverPage') || 1);

  useEffect(() => {
    localStorage.setItem('popOverPage', page);
  }, [page]);
  const pageConfig = {
    search: [
      {
        propertyName: 'campaignId',
        operator: 'EQ',
        value: batchValue.id
      },
      {
        connector: 'AND',
        propertyName: 'faked',
        operator: 'EQ',
        value: 'false'
      }
    ],
    size: 1000,
    sorts: [{ direction: 'desc', propertyName: 'id' }]
  };
  const columns = [
    {
      title: t('analysisCenter-OR90661A0ipb'),
      dataIndex: 'id',
      width: 170,
      height: 100
    },
    {
      title: t('analysisCenter-1DWb8n8cBdQd'),
      dataIndex: 'joinCount',
      width: 120
    },
    {
      title: t('analysisCenter-pbymtw2quWuw'),
      dataIndex: 'beginTime',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 200
    },
    {
      title: t('analysisCenter-GyAZ1rksOXFI'),
      dataIndex: 'endTime',
      render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
      width: 200
    }
  ];

  useEffect(() => {
    const init = async () => {
      // console.log(batchValue, 'batchValue');
      setLoading(true);
      const _steps = _.cloneDeep(editSteps);
      const _editGlobalFilter = _.cloneDeep(editGlobalFilter);
      let logList;
      if (flag || isFilter) {
        const findBatchIndex = _.findIndex(_editGlobalFilter, {
          id: batchValue.id
        });
        logList = _editGlobalFilter[findBatchIndex].logList;
      } else {
        // 获取当前步骤的索引
        const findIndex = _.findIndex(_steps, { step: id });
        setFindIndex(findIndex);
        // 获取当前批次的索引
        const findBatchIndex = isMulti
          ? _.findIndex(_steps[findIndex].multiStepList[ind].campaignList, {
              id: batchValue.id
            })
          : _.findIndex(_steps[findIndex].campaignList, { id: batchValue.id });
        logList = isMulti
          ? _steps[findIndex].multiStepList[ind].campaignList[findBatchIndex].logList
          : _steps[findIndex].campaignList[findBatchIndex].logList;
      }

      // 回显
      const _selectedRowKeys = [];
      _.forEach(logList, (v) => _selectedRowKeys.push(v.id));
      setSelectedRowKeys(_selectedRowKeys);
      try {
        const batchList = await FunnelAnalysis.listCalcLogs(pageConfig);
        setBatchList(batchList.content);
        setLoading(false);
      } catch (err) {
        setLoading(false);
      }
    };
    init();
    return () => {
      localStorage.removeItem('popOverPage');
    };
  }, []);

  const selectTable = (rowValue, isTrue) => {
    const _selectedRowKeys = _.cloneDeep(selectedRowKeys);
    if (flag || isFilter) {
      // 过滤
      const _globalFilters = _.cloneDeep(editGlobalFilter);
      const findBatchIndex = _.findIndex(_globalFilters, { id: batchValue.id });
      let _logList = _globalFilters[findBatchIndex].logList || [];
      if (isTrue) {
        _logList.push(rowValue);
        setSelectedRowKeys(_.concat(_selectedRowKeys, rowValue.id));
      } else {
        _logList = _.filter(_logList, (item) => item.id !== rowValue.id);
        setSelectedRowKeys(_.without(_selectedRowKeys, rowValue.id));
      }
      // setTabsKey(new Date().getTime());
      _globalFilters[findBatchIndex].logList = _logList;
      setEditGlobalFilter(_globalFilters);
    } else {
      const _steps = _.cloneDeep(editSteps);
      const findBatchIndex = isMulti
        ? _.findIndex(_steps[findIndex].multiStepList[ind].campaignList, {
            id: batchValue.id
          })
        : _.findIndex(_steps[findIndex].campaignList, { id: batchValue.id });
      if (isMulti) {
        let _logList = _steps[findIndex].multiStepList[ind].campaignList[findBatchIndex].logList || [];
        if (isTrue) {
          _logList.push(rowValue);
          setSelectedRowKeys(_.concat(_selectedRowKeys, rowValue.id));
        } else {
          _logList = _.filter(_logList, (item) => item.id !== rowValue.id);
          setSelectedRowKeys(_.without(_selectedRowKeys, rowValue.id));
        }
        // setTabsKey(new Date().getTime());
        _steps[findIndex].multiStepList[ind].campaignList[findBatchIndex].logList = _logList;
        setEditSteps(_steps);
      } else {
        let _logList = _steps[findIndex].campaignList[findBatchIndex]?.logList || [];
        if (isTrue) {
          _logList.push(rowValue);
          setSelectedRowKeys(_.concat(_selectedRowKeys, rowValue.id));
        } else {
          _logList = _.filter(_logList, (item) => item.id !== rowValue.id);
          setSelectedRowKeys(_.without(_selectedRowKeys, rowValue.id));
        }
        // setTabsKey(new Date().getTime());
        _steps[findIndex].campaignList[findBatchIndex].logList = _logList;
        setEditSteps(_steps);
      }
    }
  };

  const rowSelection = {
    selectedRowKeys,
    // onChange: onSelectChange,
    onSelect: selectTable,
    hideSelectAll: true
  };

  return (
    <div className="renderBatch" style={{ overflow: 'auto' }}>
      <Table
        size="middle"
        columns={columns}
        dataSource={batchList || []}
        bordered={false}
        rowKey={(record) => record.id}
        loading={loading}
        rowSelection={rowSelection}
        scroll={{ x: 640, y: 300 }}
        pagination={{
          position: 'bottomRight',
          current: parseInt(page) || 1,
          defaultPageSize: 20,
          total: batchList?.length || 0,
          onChange: (page) => {
            localStorage.setItem('popOverPage', page);
            setPage(page);
          },
          pageSizeOptions: ['20', '50', '100'],
          showQuickJumper: true,
          showSizeChanger: true,
          showLessItems: true,
          showTotal: (e) => `${t('analysisCenter-Q6tTGeKBgtYW')} ${e} ${t('analysisCenter-LcyQcDdkUo5e')}`
        }}
      />
    </div>
  );
};
export default RenderBatch;
