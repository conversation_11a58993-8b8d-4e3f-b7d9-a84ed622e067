import { Button, Input, Table, Tabs, Typography, message } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useContext, useEffect, useRef, useState } from 'react';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { useDebounce } from 'utils/customhooks';
import './campaign.scss';
// import icon from '../icon';
import { t } from 'utils/translation';
import { retentionContext } from '../retentionContext';
import RenderBatch from './renderBatch';

const { Search } = Input;
const { Text } = Typography;

const phaseList = [
  {
    name: t('analysisCenter-Jhu6bI9jtJsW'),
    text: t('analysisCenter-Jhu6bI9jtJsW'),
    value: 'DRAFT',
    key: 'DRAFT',
    color: 'rgba(0, 0, 0, 0)'
  },
  {
    name: t('analysisCenter-v0ngG1Xp8t3O'),
    text: t('analysisCenter-v0ngG1Xp8t3O'),
    value: 'TESTING',
    key: 'TESTING',
    color: '#52c41a'
  },
  {
    name: t('analysisCenter-XtZj01qfCxUk'),
    text: t('analysisCenter-XtZj01qfCxUk'),
    value: 'TEST_SUC',
    key: 'TEST_SUC',
    color: '#1890ff'
  },
  {
    name: t('analysisCenter-l6sPrHEX95z9'),
    text: t('analysisCenter-l6sPrHEX95z9'),
    value: 'ENABLE',
    key: 'ENABLE',
    color: '#f5222d'
  },
  {
    name: t('analysisCenter-HJ4omOrPtNh6'),
    text: t('analysisCenter-HJ4omOrPtNh6'),
    value: 'STOPPED',
    key: 'STOPPED',
    color: '#faad14'
  }
];

/**
 * @description: 流程画布步骤过滤
 * @param {*} id
 * @param {*} stepIndex
 * @param {*} isMulti
 * @param {*} ind
 * @param {*} flag 是否是全局过滤
 * @param {*} filterIndex 过滤下标
 * @return {*}
 */
export default function RenderCampaign({ id, stepIndex, isMulti, ind, flag, filterIndex, isFilter, type, setVisible }) {
  const { state, dispatch, editSteps, setEditSteps } = useContext(retentionContext);
  const { stepList, scenarioId, globalFilters, scenarioList } = state;
  const [tabsKey, setTabsKey] = useState(0);
  const [activeKey, setActiveKey] = useState(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [loading, setLoading] = useState(false);
  const [campaignTable, setCampaignTable] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [editGlobalFilter, setEditGlobalFilter] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    search: [
      {
        connector: 'AND',
        propertyName: 'phase',
        operator: 'IN',
        value: 'ENABLE,STOPPED'
      },
      {
        connector: 'AND',
        propertyName: 'scenario.code',
        operator: 'EQ',
        value: (scenarioList && scenarioList.find((i) => i.id === scenarioId)?.code) || ''
      },
      {
        operator: 'EQ',
        propertyName: 'deptId',
        value: window.getDeptId()
      }
    ],
    size: 10,
    sorts: [{ direction: 'desc', propertyName: 'id' }]
  });
  const columns = [
    {
      title: t('analysisCenter-woHaCq0Jm75M'),
      dataIndex: 'name',
      render: (text) => {
        return (
          <Text style={{ width: '170px' }} ellipsis={{ tooltip: text }}>
            {text}
          </Text>
        );
      },
      width: 170,
      height: 100
    },
    {
      title: t('analysisCenter-m142oEoeUOt6'),
      dataIndex: 'phase',
      render: (text) => {
        return <div className="status">{text ? _.filter(phaseList, (v) => v.value === text)[0]?.name : '-'}</div>;
      },
      width: 80
    },
    {
      title: t('analysisCenter-dVbu37IsnGvo'),
      dataIndex: 'beginTime',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 160
    },
    {
      title: t('analysisCenter-eqyJBlur6ZED'),
      dataIndex: 'endTime',
      render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
      width: 160
    }
  ];

  useEffect(() => {
    const ini = async () => {
      if (flag && isFilter) {
        const _globalFilters = _.cloneDeep(globalFilters);
        const campaignList = _globalFilters[filterIndex]?.campaignGroup.filters;
        if (campaignList) {
          const defaultSelectedRowKeys = [];
          setActiveKey(`${campaignList[0]?.id}`);
          _.forEach(campaignList, (v) => defaultSelectedRowKeys.push(v.id));
          setSelectedRowKeys(defaultSelectedRowKeys);
          setEditGlobalFilter(campaignList);
        }
      } else if (isFilter) {
        const _steps = _.cloneDeep(stepList);
        const campaignList = _steps[stepIndex].filters[filterIndex]?.campaignGroup.filters;
        if (campaignList) {
          const defaultSelectedRowKeys = [];
          setActiveKey(`${campaignList[0]?.id}`);
          _.forEach(campaignList, (v) => defaultSelectedRowKeys.push(v.id));
          setSelectedRowKeys(defaultSelectedRowKeys);
          setEditGlobalFilter(campaignList);
        }
      } else {
        const _steps = _.cloneDeep(stepList);
        const campaignList = isMulti
          ? _steps[stepIndex].multiStepList[ind].campaignList
          : _steps[stepIndex].campaignList;
        // 回显
        if (campaignList) {
          const defaultSelectedRowKeys = [];
          setActiveKey(`${campaignList[0]?.id}`);
          _.forEach(campaignList, (v) => defaultSelectedRowKeys.push(v.id));
          setSelectedRowKeys(defaultSelectedRowKeys);
        }
      }
      setLoading(true);
    };
    ini();
    return () => {
      localStorage.removeItem('popOverPage');
    };
  }, []);

  useEffect(() => {
    init();
  }, [pagination]);

  const init = async () => {
    setLoading(true);
    const campaignTable = await FunnelAnalysis.query2(pagination);
    setCampaignTable(campaignTable.content);
    setTotalCount(campaignTable.totalElements);
    setLoading(false);
  };

  const handleTableChange = (page) => {
    setPagination({
      ...pagination,
      page: page.current,
      size: page.pageSize
    });
  };

  // const onSelectChange = (newSelectedRowKeys, List) => {
  // console.log(newSelectedRowKeys, List);
  // let _steps = _.cloneDeep(editSteps);
  // let findIndex = _.findIndex(_steps, { step: id });
  // let list = isMulti ? _steps[findIndex].multiStepList[ind].campaignList : _steps[findIndex].campaignList;
  // let campaignList = [];
  // _.forEach(List, (v) => {
  //   if (_.find(list, { id: v.id })) {
  //     campaignList.push(_.find(list, { id: v.id }));
  //   } else {
  //     campaignList.push(v);
  //   }
  // });
  // if (isMulti) {
  //   _steps[findIndex].multiStepList[ind] = {
  //     step: _steps[findIndex].multiStepList[ind].step,
  //     campaignList,
  //     type: 'CAMPAIGN'
  //   };
  //   setEditSteps(_steps);
  // } else {
  //   _steps[findIndex] = {
  //     step: id,
  //     type: 'CAMPAIGN',
  //     campaignList
  //   };
  //   setEditSteps(_steps);
  // }
  // setActiveKey(`${newSelectedRowKeys[newSelectedRowKeys.length - 1]}`);
  // setSelectedRowKeys(newSelectedRowKeys);
  // dispatch({})
  // };

  const selectTable = (rowValue, isTrue) => {
    const _selectedRowKeys = _.cloneDeep(selectedRowKeys);
    if (flag || isFilter) {
      let campaignList = editGlobalFilter;
      if (isTrue) {
        campaignList.push(rowValue);
        setEditGlobalFilter(campaignList);
        setSelectedRowKeys(_.concat(_selectedRowKeys, rowValue.id));
        setActiveKey(`${rowValue.id}`);
      } else {
        campaignList = _.filter(campaignList, (v) => v.id !== rowValue.id);
        setEditGlobalFilter(campaignList);
        const newSelectRowKeys = _.without(_selectedRowKeys, rowValue.id);
        setSelectedRowKeys(newSelectRowKeys);
        setActiveKey(`${newSelectRowKeys[newSelectRowKeys.length - 1]}`);
      }
    } else {
      const _steps = _.cloneDeep(editSteps);
      const findIndex = _.findIndex(_steps, { step: id });
      let campaignList = isMulti
        ? _steps[findIndex].multiStepList[ind]?.campaignList
        : _steps[findIndex]?.campaignList || [];
      if (isTrue) {
        campaignList.push(rowValue);
        if (isMulti) {
          _steps[findIndex].multiStepList[ind] = {
            step: _steps[findIndex].multiStepList[ind].step,
            campaignList,
            type: 'CAMPAIGN'
          };
          setEditSteps(_steps);
        } else {
          _steps[findIndex] = {
            step: id,
            type: 'CAMPAIGN',
            campaignList
          };
          setEditSteps(_steps);
        }
        setSelectedRowKeys(_.concat(_selectedRowKeys, rowValue.id));
        setActiveKey(`${rowValue.id}`);
      } else {
        campaignList = _.filter(campaignList, (v) => v.id !== rowValue.id);
        if (isMulti) {
          _steps[findIndex].multiStepList[ind] = {
            step: _steps[findIndex].multiStepList[ind].step,
            campaignList,
            type: 'CAMPAIGN'
          };
          setEditSteps(_steps);
        } else {
          _steps[findIndex] = {
            step: id,
            type: 'CAMPAIGN',
            campaignList
          };
          setEditSteps(_steps);
        }
        const newSelectRowKeys = _.without(_selectedRowKeys, rowValue.id);
        setSelectedRowKeys(newSelectRowKeys);
        setActiveKey(`${newSelectRowKeys[newSelectRowKeys.length - 1]}`);
      }
    }
  };

  const changeTabs = (e) => {
    localStorage.setItem('popOverPage', '1');
    setActiveKey(e);
  };

  const saveSteps = () => {
    try {
      const arrTitle = [];
      if (flag) {
        if (_.isEmpty(editGlobalFilter)) throw new Error(t('analysisCenter-bnLMA3bP6KrW'));
        _.forEach(editGlobalFilter, (i) => {
          if (_.isEmpty(i.logList)) throw new Error(t('analysisCenter-4HaK8ggL42HS'));
          if (type === 'CAMPAIGN_NODE')
            if (_.isEmpty(i.logList[0].selectedFlows)) throw new Error(t('analysisCenter-kFVPlwrQoKUk'));
        });
        const _globalFilters = _.cloneDeep(globalFilters);
        _globalFilters[filterIndex].campaignGroup.filters = editGlobalFilter;
        dispatch({ globalFilters: _globalFilters });
        setVisible(false);
      } else if (isFilter) {
        const _stepList = _.cloneDeep(stepList);
        if (_.isEmpty(editGlobalFilter)) throw new Error(t('analysisCenter-bnLMA3bP6KrW'));
        _.forEach(editGlobalFilter, (i) => {
          if (_.isEmpty(i.logList)) throw new Error(t('analysisCenter-4HaK8ggL42HS'));
          if (type === 'CAMPAIGN_NODE')
            if (_.isEmpty(i.logList[0].selectedFlows)) throw new Error(t('analysisCenter-kFVPlwrQoKUk'));
        });
        if (!isMulti) {
          _stepList[stepIndex].filters[filterIndex].campaignGroup.filters = editGlobalFilter;
        } else {
          _stepList[stepIndex].multiStepList[ind].filters[0].campaignGroup.filters = editGlobalFilter;
        }
        _stepList[stepIndex].visibleSetp = false;
        // debugger
        dispatch({ stepList: _stepList });
      } else {
        if (isMulti) {
          const _steps = editSteps.map((item) => {
            if (item.step === id) {
              if (_.isEmpty(item.multiStepList[ind].campaignList)) throw new Error(t('analysisCenter-bnLMA3bP6KrW'));
              _.forEach(item.multiStepList[ind].campaignList, (i) => {
                if (_.isEmpty(i.logList)) throw new Error(t('analysisCenter-4HaK8ggL42HS'));
                arrTitle.push(i.name);
              });
              item.multiStepList[ind] = {
                ...item.multiStepList[ind],
                visibleSetp: false,
                name: arrTitle.join(', '),
                filters: [],
                step: ind + 1
              };
            }
            return item;
          });

          dispatch({ stepList: _steps });
        } else {
          const _steps = editSteps.map((item) => {
            if (item.step === id) {
              if (_.isEmpty(item?.campaignList)) throw new Error(t('analysisCenter-pvnQEzbqB9Ub'));
              _.forEach(item.campaignList, (i) => {
                if (_.isEmpty(i.logList)) throw new Error(t('analysisCenter-4HaK8ggL42HS'));
                arrTitle.push(i.name);
              });
              return {
                ...item,
                visibleSetp: false,
                name: arrTitle.join(', '),
                filters: []
              };
            }
            return item;
          });
          dispatch({
            stepList: _steps
          });
        }
      }
      if (!flag) dispatch({ dimensionGroup: [], globalFilters: [] });
      setVisible && setVisible(false);
      // setValueVisible && setValueVisible(false);
    } catch (err) {
      message.error(err.message);
    }
  };

  const searchRef = useRef();

  const changeSearchName = useDebounce(() => {
    const getValue = async () => {
      let pageSearchName = _.cloneDeep(pagination);
      const config = {
        connector: 'AND',
        propertyName: 'name',
        operator: 'LIKE',
        value: searchRef.current.input.value
      };
      const searchConfig = pageSearchName.search;
      if (searchConfig.length === 3) {
        searchConfig.push(config);
      } else {
        searchConfig[searchConfig.length - 1] = config;
      }
      pageSearchName = {
        ...pageSearchName,
        page: 1,
        search: searchConfig
      };
      setPagination(pageSearchName);
    };
    getValue();
  }, 400);

  return (
    <div className="renderCampaign">
      <retentionContext.Provider
        value={{
          state,
          dispatch,
          editSteps,
          setEditSteps,
          editGlobalFilter,
          setEditGlobalFilter
        }}
      >
        <div className="campaignTableBox">
          <div className="table1">
            <Search
              placeholder={t('analysisCenter-A93zGBEeqHcl')}
              allowClear
              onChange={changeSearchName}
              ref={searchRef}
            />
            <Table
              size="middle"
              columns={columns}
              dataSource={campaignTable || []}
              bordered={false}
              rowKey={(record) => record.id}
              loading={loading}
              rowSelection={{
                selectedRowKeys,
                // onChange: onSelectChange,
                onSelect: selectTable,
                hideSelectAll: true
              }}
              onChange={handleTableChange}
              pagination={{
                current: pagination.page,
                total: totalCount,
                defaultPageSize: 10,
                showQuickJumper: true,
                showSizeChanger: true,
                showLessItems: true,
                pageSizeOptions: ['10', '20', '50'],
                showTotal: (e) => `${t('analysisCenter-Q6tTGeKBgtYW')} ${e} ${t('analysisCenter-LcyQcDdkUo5e')}`
              }}
            />
          </div>
          {!_.isEmpty(selectedRowKeys) && (
            <div className="batch">
              <div className="batchTitle">
                {type === 'CAMPAIGN_BATCH' ? t('analysisCenter-4omDs3d6Q2kE') : t('analysisCenter-bWhljuNWvyNP')}
              </div>
              <div className="batchContent">
                <Tabs
                  key={tabsKey}
                  hideAdd
                  // destroyInactiveTabPane
                  activeKey={activeKey}
                  onChange={changeTabs}
                  items={
                    flag || isFilter
                      ? editGlobalFilter.map((item) => {
                          return {
                            label: item.name,
                            key: `${item.id}`,
                            children: (
                              <RenderBatch
                                setTabsKey={setTabsKey}
                                batchValue={item}
                                flag={flag}
                                filterIndex={filterIndex}
                                editGlobalFilter={editGlobalFilter}
                                isFilter
                                type={type}
                              />
                            )
                          };
                        })
                      : !isMulti
                        ? (editSteps[stepIndex]?.campaignList || []).map((item) => {
                            return {
                              label: item.name,
                              key: `${item.id}`,
                              children: (
                                <RenderBatch
                                  isMulti={isMulti}
                                  ind={ind}
                                  batchValue={item}
                                  id={id}
                                  setTabsKey={setTabsKey}
                                  editSteps={editSteps}
                                  editGlobalFilter={editGlobalFilter}
                                  type={type}
                                />
                              )
                            };
                          })
                        : (editSteps[stepIndex]?.multiStepList[ind].campaignList || [[]]).map((item) => {
                            return {
                              label: item.name,
                              key: `${item.id}`,
                              children: (
                                <RenderBatch
                                  isMulti={isMulti}
                                  ind={ind}
                                  batchValue={item}
                                  id={id}
                                  setTabsKey={setTabsKey}
                                  editSteps={editSteps}
                                  editGlobalFilter={editGlobalFilter}
                                  type={type}
                                />
                              )
                            };
                          })
                  }
                />
              </div>
            </div>
          )}
        </div>
      </retentionContext.Provider>
      <div className="buttons">
        {/* <Button>取消</Button> */}
        <Button type="primary" onClick={saveSteps}>
          {t('analysisCenter-LzWvGabJUIsb')}
        </Button>
      </div>
    </div>
  );
}
