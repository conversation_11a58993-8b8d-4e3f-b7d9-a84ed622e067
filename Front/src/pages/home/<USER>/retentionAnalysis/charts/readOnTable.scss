.retentionReadOnTable {
  width: 100%;
  height: 100%;
  overflow: auto;

  .ant-table-content {
    overflow: auto;
  }

  .tableCell {
    display: flex;
    flex-direction: column;
  }

  .ant-table-cell {
    text-align: center;
  }

  .convers {
    display: flex;

    &>:last-child {
      margin: 0 25px 0 auto;
    }

    .ratio {
      width: 64px;
      height: 22px;
      // margin-left: 40px;
      display: inline-block;
      background: #e9e9e9;
      border-radius: 2px;
      text-align: center;
    }

    .ratioInner {
      width: 11px;
      height: 11px;
      margin-left: -5px;
      display: inline-block;
      border-top: 11px solid #e9e9e9;
      border-right: 11px solid #e9e9e9;
      transform: rotate(135deg);
    }
  }
}