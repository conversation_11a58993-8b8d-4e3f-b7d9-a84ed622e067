import { Table, Tooltip } from 'antd';
import _, { isNil } from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import thousands from 'utils/thousands';
import { t } from 'utils/translation';
import { retentionContext } from '../retentionContext';
import { tableColors } from './config';
import './renderTable.scss';

export default (props) => {
  const { loading } = props;
  const { state, dispatch } = useContext(retentionContext);
  const { showTotal } = state;
  const [cloumns, setCloumns] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [count, setCount] = useState({
    max: 0,
    min: 0
  });
  const {
    chartResult: { columnList, dataList },
    stepList
  } = state;

  /**
   * @description: 计算对应颜色 把max和min分成6份，每份的值对应ratio
   * @param {*} max 最大值
   * @param {*} min  最小值
   * @param {*} ratio  比例
   * @return {*}
   */
  const ratioColors = (max, min, ratio) => {
    const step = (max - min) / 6;
    let index = Math.floor((ratio - min) / step);
    if (ratio === `${max.toFixed(2)}`) index = tableColors.length - 1;
    return tableColors[index];
  };

  useEffect(() => {
    if (!_.isEmpty(columnList)) {
      const _tableColumn = _.cloneDeep(columnList);
      _tableColumn.map((item) => {
        item.dataIndex = item.key;
        item.width = _tableColumn.length < 6 ? 'auto' : 100;
        item.title = item.value;
        return item;
      });
      _tableColumn.unshift(
        {
          title: t('analysisCenter-QUiFRYG9x1iq'),
          key: 'name',
          dataIndex: 'name',
          width: 200,
          ellipsis: true,
          fixed: 'left'
        },
        {
          title: t('analysisCenter-CE5Metls8gB1'),
          key: 'total',
          dataIndex: 'total',
          width: 100,
          fixed: 'left',
          render: (text) => <>{thousands(text)}人</>
        }
      );
      // _tableColumn除了name和all_ratio
      _tableColumn.map((item) => {
        if (item.dataIndex !== 'name' && item.dataIndex !== 'total') {
          item.onCell = (record) => {
            const ratio = (record[`${item.dataIndex}_ratio`] * 100).toFixed(2) || 0;
            const backgroundColor = ratioColors(
              count.max,
              count.min,
              (record[`${item.dataIndex}_ratio`] * 100).toFixed(2)
            );
            return {
              style: {
                backgroundColor: record[item.dataIndex] > 0 && backgroundColor,
                border: '1px solid #fff'
              },
              onClick: () => {
                // 如果是维度,全部 则不能存储用户分群
                if (record[item.dataIndex] >= 0 && record.name !== record.groupName) {
                  dispatch({
                    showSaveGroup: true,
                    saveGroupValue: {
                      date: record?.name,
                      title: item.title,
                      ratio,
                      endDateTime: record[`${item?.dataIndex}_date`],
                      groupName: record.groupName
                    }
                  });
                }
              }
            };
          };
          item.render = (text, record) => {
            const ratio = (record[`${item.dataIndex}_ratio`] * 100).toFixed(2) || 0;
            const startDate = (!_.isEmpty(record?.children) && record?.children[0]?.name) || '';
            const endDate = (!_.isEmpty(record?.children) && record?.children[record.children.length - 1]?.name) || '';
            if (record.name === record.groupName) {
              const renderDom = (
                <div>
                  <div>
                    {t('analysisCenter-q2CKyVF335qG')}
                    {ratio}%
                  </div>
                  <div>
                    ({startDate}
                    {t('analysisCenter-PZ0vig8RaFVk')}
                    {endDate}
                    {t('analysisCenter-HdmebAxnFUw4')}
                    {item.title}
                    {t('analysisCenter-hl46RJM5IIeP')}
                    {startDate}
                    {t('analysisCenter-PZ0vig8RaFVk')}
                    {endDate}
                    {t('analysisCenter-GSIMldrNnbfV')})
                  </div>
                </div>
              );
              return (
                <Tooltip title={renderDom} overlayInnerStyle={{ width: '400px' }}>
                  <div className="tableCell">
                    <span>{parseInt(text) === 0 ? 0 : ratio}% </span>
                  </div>
                </Tooltip>
              );
            } else {
              const newDate = record[`${item.dataIndex}_date`];
              const renderDom = (
                <div>
                  <div>
                    {t('analysisCenter-ScF4dku6CxeQ')}
                    {record?.name}
                    {t('analysisCenter-9O2LCUNT9Nfk')}
                    {stepList[0].displayName}
                    {t('analysisCenter-HdmebAxnFUw4')}
                    {record?.total}
                    {t('analysisCenter-5PM70AQeVIUl')}
                  </div>

                  <div>
                    {t('analysisCenter-axblqnUWz0cI')}
                    {record[item.dataIndex]}
                    {t('analysisCenter-P4bML0IzRVhP')}
                    {item.title}({newDate}){t('analysisCenter-9O2LCUNT9Nfk')}
                    {stepList[1].displayName}
                    {t('analysisCenter-tFBxtM45uUw2')}
                  </div>
                  <div>{t('analysisCenter-T6RCUz6cc5bY')}</div>
                </div>
              );
              return (
                <Tooltip title={renderDom} overlayInnerStyle={{ width: '400px' }}>
                  <div className="tableCell" hidden={isNil(text)}>
                    <span>
                      {parseInt(text) === 0 && record[`${item.dataIndex}_ratio`]
                        ? 0
                        : (record[`${item.dataIndex}_ratio`] * 100).toFixed(2) || 0}
                      %
                    </span>
                    <span hidden={!showTotal}>
                      {text || 0}
                      {t('analysisCenter-4Q7mn1AMb8sJ')}
                    </span>
                  </div>
                </Tooltip>
              );
            }
          };
        }
        // _tableColumn最后一个render等于null 或者第一个render等于null
        if (item.key === _tableColumn[0].key) {
          item.render = null;
        }
        return item;
      });

      if (_tableColumn.length < 9) {
        _tableColumn[_tableColumn.length - 1].width = null;
      }
      setCloumns(_tableColumn);
    } else {
      setCloumns([]);
    }
  }, [columnList, count, showTotal]);

  useEffect(() => {
    let ratio = [];
    if (!_.isEmpty(dataList)) {
      const _dataList = _.cloneDeep(dataList);
      // 递归_mockDataList把把每一项的dataMap删掉
      const recursion = (data) => {
        data.map((item) => {
          item.key = Math.random();
          _.map(item.dataMap, (value, key) => {
            item[key] = value;
          });
          if (item.dataMap) ratio = _.concat(ratio, item.dataMap);
          delete item.dataMap;
          if (item.children) {
            recursion(item.children);
          }
          return item;
        });
      };
      recursion(_dataList);
      setDataSource(_dataList);
      const newRatio = [];
      // ratio是数组 每一项是对象，遍历每一项的key,如果包含‘_ratio’就把value push到newRatio
      ratio.forEach((item) => {
        _.map(item, (value, key) => {
          if (key.includes('_ratio')) {
            newRatio.push((value * 100).toFixed(2));
          }
        });
      });
      setCount({
        max: Math.max(...newRatio),
        min: Math.min(...newRatio)
      });
    }
  }, [dataList]);

  return (
    <div className="retentionTable">
      <Table
        loading={loading}
        size="middle"
        columns={cloumns}
        dataSource={dataSource || []}
        bordered={false}
        scroll={{ x: columnList.length < 6 ? 'auto' : 1500 }}
        pagination={false}
        rowKey={(record) => record.key}
      />
    </div>
  );
};
