import { Line } from '@ant-design/charts';
import { Spin } from 'antd';
import _ from 'lodash';
import React, { memo, useEffect, useState } from 'react';
import { t } from 'utils/translation';
import './index.scss';

export default memo(({ loading, readOn, chartSelect, chartDisplayType, chartResult, dimensionGroup }) => {
  const [data, setData] = useState([]);

  useEffect(() => {
    const list =
      chartDisplayType === 'TREND_CHART' ? chartResult?.trendChartDataList : chartResult?.changeChartDataList;
    const select = readOn && chartDisplayType === 'TREND_CHART' ? list && list[0]?.type : chartSelect;
    const filterData =
      select === t('analysisCenter-XIZgqWk2M2rm')
        ? list?.filter((item) => item.type === t('analysisCenter-XIZgqWk2M2rm'))
        : list?.filter((item) => item.type === select);
    setData(filterData || []);
  }, [chartSelect, chartDisplayType, chartResult]);

  const config = {
    data,
    xField: 'xAxis',
    yField: 'yAxis',
    seriesField: 'groupName',
    yAxis: {
      label: {
        formatter: (v) => `${(v * 100).toFixed(2)}%`
      }
    },
    tooltip: {
      formatter: (datum) => {
        return {
          name: _.isEmpty(dimensionGroup) ? chartSelect : datum.groupName,
          value: `${(datum.yAxis * 100 || 0).toFixed(2)}%`
        };
      },
      // follow: false, // 不跟鼠标划入
      enterable: true, // 允许鼠标划入
      showContent: true
    },
    legend: {
      radio: false,
      position: 'bottom'
    }
  };

  return (
    <div className="retentionLine" style={{ width: '100%', height: '100%' }}>
      {loading ? (
        <div className="mainchartSpin">
          <Spin />
        </div>
      ) : (
        <div className="lineChartsRight" style={{ width: readOn && '98%' }}>
          <Line {...config} />
        </div>
      )}
    </div>
  );
});
