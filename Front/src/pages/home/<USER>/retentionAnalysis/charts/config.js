import { t } from 'utils/translation';

export const colors = ['#FF7A45', '#40A9FF', '#5CDBD3', '#9254DE', '#FF4D4F', '#FFC53D'];
export const mockData = [
  {
    step: '1',
    name: t('analysisCenter-vM6yQu2DdSrB'),
    dataList: [
      {
        date: '2022-07-01',
        type: t('analysisCenter-3xczbdWJHrTM'),
        value: 12901,
        total: 100,
        ratio: 0.32
      },
      { date: '2022-07-01', type: '123', value: 12901, total: 100, ratio: 0.32 }
    ]
  },
  {
    step: '2',
    name: t('analysisCenter-g8RhTvwbkHwt'),
    dataList: [
      {
        date: '2022-07-01',
        type: t('analysisCenter-3xczbdWJHrTM'),
        value: 11901,
        total: 100,
        ratio: 0.32
      }
      // { date: '2022-07-01', type: '12313', value: 12901, total: 100, ratio: 0.32 }
    ]
  },
  {
    step: '3',
    name: t('analysisCenter-YL2FdT6qj8Kh'),
    dataList: [
      { date: '2022-07-01', type: t('analysisCenter-3xczbdWJHrTM'), value: 5000, total: 100, ratio: 0.32 }
      // { date: '2022-07-01', type: '132', value: 12901, total: 100, ratio: 0.32 }
    ]
  }
];

// 多路径
export const stepDataList = [
  {
    step: '1',
    multiStep: false,
    stepDataList: [
      {
        stepNum: '1-1',
        stepName: 'pageview',
        total: 49629,
        stepGroup: '1',
        multiStep: false
      },
      {
        stepNum: '1-2',
        stepName: 'pageview',
        total: 49629,
        stepGroup: '1',
        multiStep: false
      },
      {
        stepNum: '1-3',
        stepName: 'pageview',
        total: 49629,
        stepGroup: '1',
        multiStep: false
      }
    ]
  },
  {
    step: '2',
    multiStep: true,
    stepDataList: [
      {
        stepNum: '2-1',
        stepName: 'pv123',
        total: 33333,
        stepGroup: '2',
        multiStep: true
      },
      {
        stepNum: '2-2',
        stepName: 'gaoyl_test0',
        total: 22222,
        stepGroup: '2',
        multiStep: true
      },
      {
        stepNum: '2-3',
        stepName: 'pageview1',
        total: 22111,
        stepGroup: '2',
        multiStep: true
      }
    ]
  },
  {
    step: '3',
    multiStep: false,
    stepDataList: [
      {
        stepNum: '3-1',
        stepName: t('analysisCenter-Ql3TLSZ0kPhb'),
        total: 22222,
        stepGroup: '3',
        multiStep: false
      },
      {
        stepNum: '3-2',
        stepName: t('analysisCenter-Ql3TLSZ0kPhb'),
        total: 11111,
        stepGroup: '3',
        multiStep: false
      },
      {
        stepNum: '3-3',
        stepName: t('analysisCenter-Ql3TLSZ0kPhb'),
        total: 999,
        stepGroup: '3',
        multiStep: false
      }
    ]
  }
];

export const tableColors = ['#F0F7FF', '#D9E9FF', '#B0D0FF', '#87B3FF', '#5B8FF9', '#446FD4'];
