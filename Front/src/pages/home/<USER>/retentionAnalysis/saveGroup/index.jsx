import { LinkOutlined } from '@ant-design/icons';
import { Form, Input, message, Modal, Radio, Select } from 'antd';
import CustomRangePicker from 'components/featurecoms/rangepicker/index';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import CampaignV2Service from 'service/CampaignV2Service';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import UserGroupService from 'service/UserGroupService';
import UserService from 'service/UserService';
import { t } from 'utils/translation';
import { retentionContext } from '../retentionContext';

const { Option } = Select;
const { confirm } = Modal;

const campaignV2Service = new CampaignV2Service();
const userGroupService = new UserGroupService();
const userService = new UserService();

const handleValidator = async (rule, val) => {
  if (val) {
    const res = await campaignV2Service.ensureUnique({ name: val });
    if (!res) {
      return Promise.reject(new Error(rule.message));
    }
  }
  return Promise.resolve();
};

const SaveFunnelGroup = () => {
  const { state, dispatch } = useContext(retentionContext);
  const {
    scenarioId,
    name,
    defaultFunnel,
    chartResult: { analysisType }
  } = state;
  const [form] = Form.useForm();
  const [validDateType, setValidDateType] = useState('FOREVER');
  const [date, setDate] = useState({
    validBeginTime: new Date().getTime(),
    validEndTime: null
  });

  useEffect(() => {
    const { date, title, ratio } = state.saveGroupValue;
    const map = { RETAINED: t('analysisCenter-9rUzqp9zYGfT'), LOSS: t('analysisCenter-iGpLuylsdhxi') };
    form.setFieldsValue({
      scenarioId,
      segmentName: `[${name}] ${date} ${title}${map[analysisType]}（${ratio}%） `
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const saveRecordInfo = async (id) => {
    const userInfo = await userService.getCurrentUser();
    await userGroupService.saveUserOperationRecord({
      targetId: id,
      targetType: 'SEGMENT',
      type: 'RECENT',
      createUserId: userInfo.id,
      updateUserId: userInfo.id,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });
  };

  const showPromiseConfirm = (data) => {
    confirm({
      title: t('analysisCenter-7aFJqVxO5sz0'),
      className: 'processSaveModal',
      icon: <LinkOutlined />,
      okText: t('analysisCenter-l4b1j0ENu8mw'),
      cancelText: t('analysisCenter-7fzmx6mRlXRN'),
      async onOk() {
        const userInfo = await userService.getCurrentUser();
        const processRes = await userGroupService.approvalProcessInfo({
          contentId: data.id,
          contentName: data.name,
          contentType: 'SEGMENT',
          promoterId: userInfo.id,
          projectId: localStorage.getItem('projectId')
        });

        const saveParams = {
          id: data.id,
          approvalNo: processRes.approvalNo,
          approvalStatus: 'RUNNING'
        };

        await userGroupService.updateApprovalNoAndStatus(saveParams);

        message.success(t('analysisCenter-ePK8TGiWOqgc'));
      }
    });
  };

  const handleOk = async () => {
    try {
      const formValue = await form.validateFields();
      const processAuth = await campaignV2Service.getProcessByType({
        type: 'SEGMENT'
      });
      let data = {
        ...defaultFunnel,
        ...formValue,
        validDateType,
        approvalStatus: processAuth && processAuth.status === 'ENABLE' ? 'PENDING' : undefined,
        validBeginTime: new Date().getTime(),
        validEndTime: date.validEndTime && date.validEndTime.valueOf(),
        storageType: analysisType,
        initDateTime: dayjs(state.saveGroupValue.date).valueOf(),
        endDateTime: dayjs(state.saveGroupValue.endDateTime).valueOf(),
        groupName: state.saveGroupValue.groupName
      };
      if (data.validDateType === 'TEMPORARY' && !data.validEndTime) {
        throw new Error(t('analysisCenter-WclP2enFznqf'));
      }
      if (data.validDateType === 'FOREVER') data = _.omit(data, ['validBeginTime', 'validEndTime']);
      delete data?.id;
      const result = await FunnelAnalysis.saveRetentionChartSegment(data);
      if (processAuth && processAuth.status === 'ENABLE') {
        showPromiseConfirm(result);
      }
      saveRecordInfo(result.id);
      message.success(t('analysisCenter-vgoC3oNz7yG7'));
      dispatch({ showSaveGroup: false });
    } catch (err) {
      if (err.message) message.error(err.message);
    }
  };

  const handleClose = () => {
    dispatch({
      showSaveGroup: false
    });
  };

  const changeRangePicker = (dates) => {
    setDate({
      validBeginTime: dates[0],
      validEndTime: dates[1]
    });
  };

  return (
    <Modal
      className="saveFunnelGroup"
      title={t('analysisCenter-7rS0qqK2aacU')}
      open
      onOk={handleOk}
      onCancel={handleClose}
    >
      <Form layout="vertical" form={form}>
        <Form.Item label={t('analysisCenter-iKRZ1nLiBXVu')} name="scenarioId">
          <Select disabled>
            {state.scenarioList.map((n) => (
              <Option key={n.id} value={n.id}>
                {n.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={t('analysisCenter-Y6COfdcR9Z5Q')}
          name="segmentName"
          rules={[
            { required: true, message: t('analysisCenter-sWqx9a1OPQFz') },
            { max: 60, message: t('analysisCenter-UR5rYfxeCaYO') },
            {
              pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
              message: t('analysisCenter-T7xzLNh8EuGY')
            },
            { validator: handleValidator, message: t('analysisCenter-qnQL7yVlFDhJ') }
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item label={t('analysisCenter-bT6VsFhxNxcJ')} name="memo">
          <Input.TextArea />
        </Form.Item>
      </Form>
      <div className="analysisGroupType">
        <span>{t('analysisCenter-DPqPCjAeJ4lf')}</span>
        <span>
          <Radio.Group onChange={(e) => setValidDateType(e.target.value)} defaultValue="FOREVER">
            <Radio value="FOREVER">{t('analysisCenter-5ZNekBrWxPS7')}</Radio>
            <Radio value="TEMPORARY">{t('analysisCenter-dcpN4CEQRoo6')}</Radio>
          </Radio.Group>
        </span>
        {validDateType === 'TEMPORARY' && (
          <CustomRangePicker
            limitTime
            className="userGroupPicker"
            value={[
              date.validBeginTime ? dayjs(date.validBeginTime) : null,
              date.validEndTime ? dayjs(date.validEndTime) : null
            ]}
            disableStartTime
            onChange={changeRangePicker}
            showTime={false}
            format="YYYY-MM-DD"
            style={{ marginTop: 12, width: '100%', marginLeft: 70 }}
            timeDiff={1}
            closeBefore
          />
        )}
      </div>
    </Modal>
  );
};

export default SaveFunnelGroup;
