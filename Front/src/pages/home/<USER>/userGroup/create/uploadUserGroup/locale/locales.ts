export default {
  cn: {
    // uploadUserGroup 翻译 - Front/src/pages/home/<USER>/userGroup/create/uploadUserGroup/uploadUserGroup.jsx
    'portraitCenter-CGs2eETOlreN': '数据表',
    'portraitCenter-rsr4Dh9odfcj': '表字段',
    'portraitCenter-MpJ5cjYBT5c7': '字段值',
    'portraitCenter-3QJThzTEBtzS': '无法上传，文件大小超过限制',
    'portraitCenter-8E3D3Mn28lIJ': '请选择有效期',
    'portraitCenter-sXQrgtvwXpGV': '启用失败：超过失效时间 {{time}}',
    'portraitCenter-fVQT5PlMYKYF': '上传失败,请重试',
    'portraitCenter-5ePVFBaTtW3m': '请上传文件！',
    'portraitCenter-AEYKnqn5A50q': '保存成功',
    'portraitCenter-MpO9YRrLWqql': '上传成功',
    'portraitCenter-QoBeukhvymUg': '创建成功',
    'portraitCenter-8urZQQ9xwUpA': '字段权限',
    'portraitCenter-2h6bzpuQgPmO': '脱敏字段',
    'portraitCenter-Bfsh6QVYc4Ya': '无限制',
    'portraitCenter-sKuT0KnEkvv4': '用户ID类型：{{type}}',
    'portraitCenter-uUaBjRpbVR2x': '从当前用户 ID 类型中计算符合数据权限并符合条件的用户',
    'portraitCenter-fEu8ySvG92LS': '计算人数',
    'portraitCenter-3yUZMb6awNl1': '数据权限',
    'portraitCenter-AjqLQUJnEJz1': '数据权限详情',
    'portraitCenter-xFvdHs1sN3wE': '拖动文件到这里',
    'portraitCenter-XoFp9X7ONpQ5': '或',
    'portraitCenter-gbgBhh1vPoGq': '从电脑选择文件',
    'portraitCenter-q3EuWz6TM9HQ': '支持csv文件，文件最大不超过200M',
    'portraitCenter-JHCvxO5i6RHn': '请上传单列、字符串类型且不包含表头的用户id，多列将忽略首列外数据。',
    'portraitCenter-zN0tmG3X0KYg': '上传中...',
    'portraitCenter-6ubdoW2vlF7O': '重新上传',
    'portraitCenter-bfGyDQgh3tmH': '下一步',
    'portraitCenter-2bOyQiUum3bf': '上一步',
    'portraitCenter-YOwXMYWyj9vW': '创建完成',
    'portraitCenter-QtgyPXmcnBes': '取消创建',
    'portraitCenter-96lJlDlKVLvb': '是否确认取消创建分群，当前步骤数据会丢失哦',
    'portraitCenter-HOdB1suoGnIV': '否',
    'portraitCenter-URRGtPuuF0G7': '是',
    'portraitCenter-zp1YOzDrk4Ao': '基本信息',
    'portraitCenter-QwribLvXUU6G': '上传文件',
    
    // uploadUserGroup/Step1 翻译 - Front/src/pages/home/<USER>/userGroup/create/uploadUserGroup/Step1/index.js
    'portraitCenter-9jrlEmCQjOJr': '归属部门',
    'portraitCenter-igNzKJFsgmEA': 'ID类型',
    'portraitCenter-Thdb8sR6IPu8': '请选择ID类型编码!',
    'portraitCenter-CdtyhfgxzxXa': '请选择',
    'portraitCenter-Y28FoO6A3YcG': '分群名称:',
    'portraitCenter-l58t7cZfbLAw': '请输入分群名称!',
    'portraitCenter-GagrICotetQT': '分群名称不要超出60个字符',
    'portraitCenter-rMbGx5b5AvI0': '请输入字母、数字、字符(._-)或者汉字',
    'portraitCenter-7J7VGPhOu4un': '分群名称不能为空',
    'portraitCenter-p2h1zNJBB8pg': '分群名称已存在',
    'portraitCenter-u9njZ9gLjIzV': '请输入分群名称',
    'portraitCenter-kEiIheqIbK2p': '有效时间',
    'portraitCenter-3kaUoggj20NB': '请选择有效时间!',
    'portraitCenter-IU2CzPwT4uH6': '永久有效',
    'portraitCenter-rqn4dQy0BrkX': '失效时间',
    'portraitCenter-1CbrSCjr0yB6': '备注描述',
    'portraitCenter-CW5DJgCmYstg': '分群描述请不要超出150个字符',
    'portraitCenter-4TkbgPZEJ5iV': '请输入分群描述，不超过150字',
    'portraitCenter-rQu8xnkAWTPF': '标记为测试分群',
    'portraitCenter-yHaxCw5DNgJN': '测试分群可以作为普通分群参与所有活动。运营活动的"测试流程"中，仅可选择测试分群',
    'portraitCenter-VKvXyvz9agYR': '标记为黑（白）名单分群',
    'portraitCenter-yfPWMAuD9o9H': '不标记',
    'portraitCenter-afsdUwo0keLH': '黑名单',
    'portraitCenter-dCoFFtp6jr8H': '白名单',
    'portraitCenter-4phc7bWiOcx0': '默认{{type}}名单分群'
  },
  en: {
    // uploadUserGroup translation - Front/src/pages/home/<USER>/userGroup/create/uploadUserGroup/uploadUserGroup.jsx
    'portraitCenter-CGs2eETOlreN': 'Data Table',
    'portraitCenter-rsr4Dh9odfcj': 'Table Field',
    'portraitCenter-MpJ5cjYBT5c7': 'Field Value',
    'portraitCenter-3QJThzTEBtzS': 'Cannot upload, file size exceeds limit',
    'portraitCenter-8E3D3Mn28lIJ': 'Please select validity period',
    'portraitCenter-sXQrgtvwXpGV': 'Activation failed: exceeds expiration time {{time}}',
    'portraitCenter-fVQT5PlMYKYF': 'Upload failed, please try again',
    'portraitCenter-5ePVFBaTtW3m': 'Please upload file!',
    'portraitCenter-AEYKnqn5A50q': 'Save successful',
    'portraitCenter-MpO9YRrLWqql': 'Upload successful',
    'portraitCenter-QoBeukhvymUg': 'Create successful',
    'portraitCenter-8urZQQ9xwUpA': 'Field Permissions',
    'portraitCenter-2h6bzpuQgPmO': 'Sensitive Fields',
    'portraitCenter-Bfsh6QVYc4Ya': 'No restrictions',
    'portraitCenter-sKuT0KnEkvv4': 'User ID Type: {{type}}',
    'portraitCenter-uUaBjRpbVR2x': 'Calculate users who meet data permissions and conditions from the current user ID type',
    'portraitCenter-fEu8ySvG92LS': 'Calculate Count',
    'portraitCenter-3yUZMb6awNl1': 'Data Permissions',
    'portraitCenter-AjqLQUJnEJz1': 'Data Permission Details',
    'portraitCenter-xFvdHs1sN3wE': 'Drag files here',
    'portraitCenter-XoFp9X7ONpQ5': 'or',
    'portraitCenter-gbgBhh1vPoGq': 'Select files from computer',
    'portraitCenter-q3EuWz6TM9HQ': 'Supports CSV files, maximum file size 200M',
    'portraitCenter-JHCvxO5i6RHn': 'Please upload single-column, string-type user IDs without headers. Multi-column data will ignore columns other than the first.',
    'portraitCenter-zN0tmG3X0KYg': 'Uploading...',
    'portraitCenter-6ubdoW2vlF7O': 'Re-upload',
    'portraitCenter-bfGyDQgh3tmH': 'Next Step',
    'portraitCenter-2bOyQiUum3bf': 'Previous Step',
    'portraitCenter-YOwXMYWyj9vW': 'Create Complete',
    'portraitCenter-QtgyPXmcnBes': 'Cancel Create',
    'portraitCenter-96lJlDlKVLvb': 'Are you sure to cancel creating the user group? Current step data will be lost.',
    'portraitCenter-HOdB1suoGnIV': 'No',
    'portraitCenter-URRGtPuuF0G7': 'Yes',
    'portraitCenter-zp1YOzDrk4Ao': 'Basic Information',
    'portraitCenter-QwribLvXUU6G': 'Upload File',
    
    // uploadUserGroup/Step1 translation - Front/src/pages/home/<USER>/userGroup/create/uploadUserGroup/Step1/index.js
    'portraitCenter-9jrlEmCQjOJr': 'Department',
    'portraitCenter-igNzKJFsgmEA': 'ID Type',
    'portraitCenter-Thdb8sR6IPu8': 'Please select ID type!',
    'portraitCenter-CdtyhfgxzxXa': 'Please select',
    'portraitCenter-Y28FoO6A3YcG': 'Group Name:',
    'portraitCenter-l58t7cZfbLAw': 'Please enter group name!',
    'portraitCenter-GagrICotetQT': 'Group name should not exceed 60 characters',
    'portraitCenter-rMbGx5b5AvI0': 'Please enter letters, numbers, characters (._-) or Chinese characters',
    'portraitCenter-7J7VGPhOu4un': 'Group name cannot be empty',
    'portraitCenter-p2h1zNJBB8pg': 'Group name already exists',
    'portraitCenter-u9njZ9gLjIzV': 'Please enter group name',
    'portraitCenter-kEiIheqIbK2p': 'Validity Period',
    'portraitCenter-3kaUoggj20NB': 'Please select validity period!',
    'portraitCenter-IU2CzPwT4uH6': 'Permanent',
    'portraitCenter-rqn4dQy0BrkX': 'Expiration Time',
    'portraitCenter-1CbrSCjr0yB6': 'Description',
    'portraitCenter-CW5DJgCmYstg': 'Group description should not exceed 150 characters',
    'portraitCenter-4TkbgPZEJ5iV': 'Please enter group description, no more than 150 characters',
    'portraitCenter-rQu8xnkAWTPF': 'Mark as Test Group',
    'portraitCenter-yHaxCw5DNgJN': 'Test groups can participate in all activities as regular groups. In the "test process" of operational activities, only test groups can be selected',
    'portraitCenter-VKvXyvz9agYR': 'Mark as Black/White List Group',
    'portraitCenter-yfPWMAuD9o9H': 'No Mark',
    'portraitCenter-afsdUwo0keLH': 'Blacklist',
    'portraitCenter-dCoFFtp6jr8H': 'Whitelist',
    'portraitCenter-4phc7bWiOcx0': 'Default {{type}}list Group'
  }
};
