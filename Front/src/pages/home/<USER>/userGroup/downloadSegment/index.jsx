import { CaretRightOutlined, CloseOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Checkbox, Collapse, Divider, Modal, Tooltip, message } from 'antd';
import _ from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import ProjectMangerService from 'service/projectMangerService';
import { t } from '@/utils/translation';
import './index.scss';
import Tag from './TagSelector';

const _projectMangerService = new ProjectMangerService();
const { Panel } = Collapse;

const DownloadSegment = (props) => {
  const [segmentList, setSegmentList] = useState([]);
  const [activeKey, setActiveKey] = useState(['userInfo', 'userLabel']);
  const [tagOptions, setTagOptions] = useState([]);
  const [dateType, setDateType] = useState(['userInfo', 'userLabel']);

  useEffect(() => {
    let ignore = false;
    const init = async () => {
      const reData = await _projectMangerService.getDownloadSegment({});
      !ignore && setSegmentList(_.filter(reData?.schemaList, (item) => item.selected));
      // !ignore && setSelectedList(_.filter(reData?.schemaList, item => item.selected) || []);
    };
    init();
    return () => {
      ignore = true;
    };
  }, []);

  const handleOk = () => {
    const selectedList = _.filter(segmentList, (item) => item.selected);
    const labelList = tagOptions.map((item) => item.tagValue);
    if (_.isEmpty(dateType)) {
      return message.error(t('portraitCenter-7FClFdqAnZ1d'));
    }
    if (_.isEmpty(selectedList) && _.isEmpty(labelList)) {
      return message.error(t('portraitCenter-gbrYyYJTUtQW'));
    }
    props.handleExportSegmentOk && props.handleExportSegmentOk(selectedList, labelList, dateType);
  };

  const handleCancel = () => {
    props.handleExportSegmentCancel && props.handleExportSegmentCancel();
  };

  const onListItemChange = (data) => {
    const findIndex = _.findIndex(segmentList, (item) => item.schemaId === data.schemaId);
    const _segmentList = _.cloneDeep(segmentList);
    _segmentList[findIndex] = {
      ..._segmentList[findIndex],
      selected: !_segmentList[findIndex].selected
    };
    setSegmentList(_segmentList);
  };

  const onChangeSelectAll = () => {
    if (_.find(segmentList, (item) => item.selected === false)) {
      // 全选
      setSegmentList(_.map(segmentList, (item) => ({ ...item, selected: true })));
    } else {
      // 全不选
      setSegmentList(_.map(segmentList, (item) => ({ ...item, selected: false })));
    }
  };

  const handleClose = (data) => {
    const _segmentList = _.cloneDeep(segmentList);
    const findIndex = _.findIndex(_segmentList, (item) => item.schemaId === data.schemaId);
    _segmentList[findIndex] = {
      ..._segmentList[findIndex],
      selected: false
    };
    setSegmentList(_segmentList);
  };

  const selectedSegment = useMemo(() => {
    return _.filter(segmentList, (item) => item.selected);
  }, [segmentList]);

  const onCancelTag = (title) => {
    let _value = _.cloneDeep(tagOptions);
    _value = _value.filter((item) => item.title !== title);
    setTagOptions(_value);
  };

  const changeDateType = (value) => {
    if (dateType.length > value.length) {
      const find = _.find(dateType, (item) => !value.includes(item));

      if (
        (find === 'userInfo' && !_.some(segmentList, (item) => item.selected)) ||
        (find === 'userLabel' && _.isEmpty(tagOptions))
      ) {
        return setDateType(value);
      }

      return Modal.confirm({
        title: t('portraitCenter-x1OZg8FJMmnf'),
        content: t('portraitCenter-VFDR0B8HQwcU'),
        onCancel: () => {
          //  callback(false);
        },
        onOk: () => {
          if (find === 'userInfo') {
            setSegmentList(_.map(segmentList, (item) => ({ ...item, selected: false })));
          } else if (find === 'userLabel') {
            setTagOptions([]);
          }
          setDateType(value);
        }
      });
    }
    setDateType(value);
  };

  return (
    <Modal
      className="downloadSegmentModal"
      title={
        <>
          {t('portraitCenter-PCNS11JCzkVj')}
          <Tooltip
            placement="top"
            title={
              <>
                <span>{t('portraitCenter-V7o42SKd19f6', { count: props.downloadSegmentMaxCount })}</span>
                <br />
                <span>
                  {t('portraitCenter-2KmIcp9lqrp3', {
                    count: props.downloadSegmentMaxCount,
                    maxCount: props.downloadSegmentMaxCount
                  })}
                </span>
              </>
            }
          >
            <QuestionCircleOutlined style={{ marginLeft: 5 }} />
          </Tooltip>
        </>
      }
      width={1000}
      open
      onOk={handleOk}
      onCancel={handleCancel}
    >
      <div style={{ marginBottom: 16 }}>{t('portraitCenter-vIbNcx9GvoMW', { name: props.segmentInfo?.name })}</div>
      <div style={{ marginBottom: 16 }}>
        <span>{t('portraitCenter-4NRGPgOGsLnU')}</span>
        <Checkbox.Group
          value={dateType}
          options={[
            { label: t('portraitCenter-pZcLSV7fdoyn'), value: 'userInfo' },
            { label: t('portraitCenter-hPdZEdvZbbfO'), value: 'userLabel' }
          ]}
          onChange={changeDateType}
        />
      </div>
      <div className="wrapper">
        <Collapse
          expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
          defaultActiveKey={activeKey}
          onChange={setActiveKey}
          style={{ width: '49%', height: '100vh' }}
          bordered={false}
          // ghost
        >
          <Panel
            header={t('portraitCenter-pZcLSV7fdoyn')}
            key="userInfo"
            style={{
              overflow: 'auto',
              height:
                activeKey.length === 2
                  ? '50%'
                  : activeKey.length === 1 && activeKey.includes('userInfo')
                    ? '50%'
                    : 'auto',
              transition: 'height 0.2s ease-in-out'
            }}
          >
            <div className="selectArea bg-[#fff]">
              {/* <div className="title">设置导出字段</div> */}
              <div className="header">
                <Checkbox
                  checked={!_.find(segmentList, (item) => item.selected === false)}
                  onChange={onChangeSelectAll}
                >
                  {/* <span>字段</span> */}
                  <span>{t('portraitCenter-Q4iZk9dqWDoa')}</span>
                </Checkbox>
              </div>
              <div
                className="listWrapper"
                style={{
                  height: 'calc(100% - 48px)',
                  maxHeight: '400px',
                  transition: 'height 0.2s ease-in-out'
                }}
              >
                {_.map(segmentList, (item) => {
                  return (
                    <div className="listItem" key={item.name}>
                      <Checkbox checked={item.selected} onChange={() => onListItemChange(item)}>
                        {/* <div title={item.name}>{item.name}</div> */}
                        <div title={item.displayName}>{item.displayName}</div>
                      </Checkbox>
                    </div>
                  );
                })}
              </div>
            </div>
          </Panel>
          <Panel
            header={
              <div className="flex gap-8 items-center">
                {t('portraitCenter-hPdZEdvZbbfO')}
                <Tooltip placement="top" title={t('portraitCenter-7pRq41lqzIet')}>
                  <QuestionCircleOutlined />
                </Tooltip>
              </div>
            }
            style={{
              overflow: 'auto',
              height: activeKey.length === 1 && activeKey.includes('userLabel') ? 'auto' : '50%'
            }}
            key="userLabel"
          >
            <Tag
              scenario={props.segmentInfo?.scenario}
              onChange={setTagOptions}
              value={tagOptions}
              listWrapperStyle={{
                // todo 高度需要调整
                height: 'calc(100vh - 48px)',
                // maxHeight: '600px',
                transition: 'height 0.2s ease-in-out',
                background: '#fff'
              }}
            />
          </Panel>
        </Collapse>
        <Divider type="vertical" />
        <div className="selectedArea">
          <div className="userInfo">
            <div className="title">
              {t('portraitCenter-SMbM5CBV8aaf', { selected: selectedSegment?.length || 0, total: segmentList.length })}
            </div>
            <div className="header">
              {/* <span>字段</span> */}
              <span>{t('portraitCenter-Q4iZk9dqWDoa')}</span>
              <CloseOutlined
                onClick={() => {
                  handleClose();
                }}
              />
            </div>
            <div className="rightListWrapper" style={{ height: '300px' }}>
              {_.map(selectedSegment, (item) => {
                return (
                  <div className="listItem" key={item.name}>
                    {/* <div title={item.name}>{item.name}</div> */}
                    <div title={item.displayName}>{item.displayName}</div>
                    <CloseOutlined
                      onClick={() => {
                        handleClose(item);
                      }}
                    />
                  </div>
                );
              })}
            </div>
          </div>
          <div className="userTag">
            <div className="title">{t('portraitCenter-1kay6uQRRoh6', { count: tagOptions?.length || 0 })}</div>
            <div className="header">
              <span>{t('portraitCenter-iz60kLw6YJzA')}</span>
              <CloseOutlined
                onClick={() => {
                  handleClose();
                }}
              />
            </div>
            <div className="rightListWrapper">
              {_.map(tagOptions, (item) => {
                return (
                  <div className="listItem" title={item.title} key={item.title} style={{ cursor: 'pointer' }}>
                    {item.title}
                    <CloseOutlined style={{ margin: '15px 15px 0 auto' }} onClick={() => onCancelTag(item.title)} />
                  </div>
                );
              })}
              {/* {_.map(selectedSegment, (item) => {
                return (
                  <div className="listItem" key={item.name}>
                    <div title={item.name}>{item.name}</div>
                    <div title={item.displayName}>{item.displayName}</div>
                    <CloseOutlined
                      onClick={() => {
                        handleClose(item);
                      }}
                    />
                  </div>
                );
              })} */}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default DownloadSegment;
