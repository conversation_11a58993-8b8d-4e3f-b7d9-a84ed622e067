export default {
  cn: {
    'portraitCenter-userGroup-ruleList-once-name': '单次计算',
    'portraitCenter-userGroup-ruleList-once-text': '单次计算',
    'portraitCenter-userGroup-ruleList-schedule-name': '定期计算',
    'portraitCenter-userGroup-ruleList-schedule-text': '定期计算',
    'portraitCenter-userGroup-validTime-forever': '永久',
    'portraitCenter-userGroup-statusList-draft': '草稿',
    'portraitCenter-userGroup-statusList-normal': '启用中',
    'portraitCenter-userGroup-statusList-disable': '已停用',
    'portraitCenter-userGroup-approveStatusList-pending': '待提交审批',
    'portraitCenter-userGroup-approveStatusList-pass': '审批通过',
    'portraitCenter-userGroup-approveStatusList-none': '无审批',
    'portraitCenter-userGroup-approveStatusList-reject': '审批驳回',
    'portraitCenter-userGroup-approveStatusList-running': '审批中',
    'portraitCenter-userGroup-approveStatusList-backout': '已撤销',
    'portraitCenter-userGroup-approveStatusList-cancel': '已取消',
    'portraitCenter-userGroup-calcStatusList-notrun': '未开始',
    'portraitCenter-userGroup-calcStatusList-calcing': '计算中',
    'portraitCenter-userGroup-calcStatusList-fail': '计算失败',
    'portraitCenter-userGroup-calcStatusList-suc': '计算成功',
    'portraitCenter-userGroup-typeList-conditional': '自定义筛选',
    'portraitCenter-userGroup-typeList-ai': 'AI智能分群',
    'portraitCenter-userGroup-typeList-complex': '复合分群',
    'portraitCenter-userGroup-typeList-upload': '上传分群',
    'portraitCenter-userGroup-typeList-campaign': '流程分群',
    'portraitCenter-userGroup-typeList-conditionAggregate': '行为聚合分群',
    'portraitCenter-userGroup-typeList-shortLink': '短链分群',
    'portraitCenter-userGroup-typeList-campaignLog': '日志分群',
    'portraitCenter-userGroup-typeList-funnelChart': '漏斗转化分群',
    'portraitCenter-userGroup-typeList-retentionChart': '留存转化分群',
    'portraitCenter-userGroup-typeList-exSubscribe': '外部导入',
    'portraitCenter-userGroup-proessStatusList-pending': '待提交审批',
    'portraitCenter-userGroup-proessStatusList-running': '审批中',
    'portraitCenter-userGroup-proessStatusList-pass': '审批通过',
    'portraitCenter-userGroup-proessStatusList-none': '无审批',
    'portraitCenter-userGroup-proessStatusList-reject': '审批驳回',
    'portraitCenter-userGroup-proessStatusList-backout': '已撤销',
    'portraitCenter-userGroup-queryConfig-name': '分群名称',
    'portraitCenter-userGroup-queryConfig-customerCount': '人数',
    'portraitCenter-userGroup-queryConfig-validTime': '有效时间',
    'portraitCenter-userGroup-queryConfig-type': '分群类型',
    'portraitCenter-userGroup-queryConfig-status': '分群状态',
    'portraitCenter-userGroup-queryConfig-calcRule': '计算规则',
    'portraitCenter-userGroup-queryConfig-createTime': '创建时间',
    'portraitCenter-userGroup-queryConfig-user': '创建人',
    'portraitCenter-userGroup-queryConfig-lastCalcTime': '最近计算时间',
    'portraitCenter-userGroup-queryConfig-createUserId': '仅查看我创建的',
    'portraitCenter-userGroup-tableConfig-edit': '继续编辑',
    'portraitCenter-userGroup-tableConfig-view': '查看分群',
    'portraitCenter-userGroup-tableConfig-delete': '删除分群',
    'portraitCenter-userGroup-tableConfig-deleteConfirm': '删除分群可能影响其他使用此分群参与的计算，是否删除？',
    'portraitCenter-userGroup-list-status': '分群状态',
    'portraitCenter-userGroup-list-idType': 'ID类型',
    'portraitCenter-userGroup-list-calcStatus': '计算状态',
    'portraitCenter-userGroup-list-approvalStatus': '审批状态',
    'portraitCenter-userGroup-list-type': '分群类型',
    'portraitCenter-userGroup-list-testGroup': '测试分群',
    'portraitCenter-userGroup-list-yes': '是',
    'portraitCenter-userGroup-list-no': '否',
    'portraitCenter-userGroup-list-calcRule': '计算规则',
    'portraitCenter-userGroup-list-creator': '创建人',
    'portraitCenter-userGroup-list-createTime': '创建时间',
    'portraitCenter-userGroup-list-updater': '更新人',
    'portraitCenter-userGroup-list-updateTime': '更新时间',
    'portraitCenter-userGroup-list-operations': '操作',
    'portraitCenter-userGroup-tableConfig-createUser': '创建人',
    'portraitCenter-userGroup-tableConfig-createTime': '创建时间',
    'portraitCenter-userGroup-tableConfig-updateUser': '更新人',
    'portraitCenter-userGroup-recentMenu-week': '一周',
    'portraitCenter-userGroup-recentMenu-month': '一个月',
    'portraitCenter-userGroup-recentMenu-threeMonths': '三个月',
    'portraitCenter-userGroup-recentMenu-sixMonths': '六个月',
    'portraitCenter-userGroup-queryConfig-all': '全部',
    'portraitCenter-userGroup-queryConfig-mine': '我的',
    'portraitCenter-userGroup-queryConfig-recent': '最近',
    'portraitCenter-userGroup-queryConfig-favorite': '收藏',
    'portraitCenter-userGroup-list-more': '更多',
    'portraitCenter-userGroup-list-edit': '编辑',
    'portraitCenter-userGroup-list-filter': '筛选',
    'portraitCenter-userGroup-list-create': '新建分群',
    'portraitCenter-userGroup-list-share': '与我分享',
    'portraitCenter-userGroup-list-recycle': '回收站',
    'portraitCenter-userGroup-create-title': '选择创建分群方式',
    'portraitCenter-userGroup-typeList-conditionAggregate-desc': '行为为主，聚合属性、标签、分群等条件建立分群',
    'portraitCenter-userGroup-typeList-conditional-desc': '自定义标签、属性筛选出特定的分群',
    'portraitCenter-userGroup-typeList-ai-desc': '通过AI算法对不同的活动场景推荐最优的分群',
    'portraitCenter-userGroup-typeList-upload-desc': '上传唯一识别号，建立分群',
    'portraitCenter-userGroup-typeList-complex-desc': '通过叠加、排除组合现有分群所产生的分群',
    'portraitCenter-userGroup-typeList-campaign-desc': '选择过去参加过流程的作为分群',
    'portraitCenter-userGroup-delete-confirmation': '您将删除选项："{{name}}"',
    'portraitCenter-userGroup-delete-impact': '删除分群可能影响其他使用此分群参与的计算，是否删除？',
    'portraitCenter-userGroup-collect-success': '收藏成功',
    'portraitCenter-userGroup-cancel-collect-success': '取消收藏成功',
    'portraitCenter-userGroup-delete-success': '删除成功',
    'portraitCenter-userGroup-enable-success': '启用成功',
    'portraitCenter-userGroup-enable-enable': '启用',
    'portraitCenter-userGroup-enable-disable': '停用',
    'portraitCenter-userGroup-disable-success': '停用成功',
    'portraitCenter-userGroup-recalc-success': '重新计算成功',
    'portraitCenter-userGroup-recycle-success': '操作成功',
    'portraitCenter-userGroup-edit-success': '编辑成功',
    'portraitCenter-userGroup-enable-fail': '启用失败：超过失效时间{{time}}',
    'portraitCenter-userGroup-ownership-edit-success': '修改归属成功',
    'portraitCenter-userGroup-delete-title': '删除',
    'portraitCenter-userGroup-delete-okText': '删除',
    'portraitCenter-userGroup-delete-cancelText': '取消',
    'portraitCenter-userGroup-enable-confirm': '是否要{{status}}分群',
    'portraitCenter-userGroup-status-confirm-onText': '确定',
    'portraitCenter-userGroup-status-confirm-cancelText': '取消',
    'portraitCenter-userGroup-reCalc-confirm-title': '确定重新计算',
    'portraitCenter-userGroup-reCalc-confirm-content': '重新计算将生产新的计算结果',
    'portraitCenter-userGroup-reCalc-confirm-onText': '确定',
    'portraitCenter-userGroup-reCalc-confirm-cancelText': '取消',
    'portraitCenter-userGroup-recycle-confirm-content': '您将把 "{{name}}" 此条数据移入到回收站中，是否继续？移入后，所有依赖该客户分群创建的将无法继续正常使用，后续您也可以从回收站进行还原。',
    'portraitCenter-userGroup-recycle-confirm-title': '移入回收站',
    'portraitCenter-userGroup-recycle-confirm-onText': '确定移入',
    'portraitCenter-userGroup-recycle-confirm-cancelText': '取消',
    'portraitCenter-userGroup-customerCount-error': '请填写完整区间',
    'portraitCenter-userGroup-export-loading': '用户分群数据准备中，请耐心等待...',
    'portraitCenter-userGroup-list-recycling': '移入回收站',
    'portraitCenter-userGroup-list-updateDept': '修改归属',
    'portraitCenter-userGroup-list-view': '查看',
    'portraitCenter-userGroup-list-collect': '收藏',
    'portraitCenter-userGroup-list-cancelCollect': '取消收藏',
    'portraitCenter-userGroup-list-delete': '删除',
    'portraitCenter-userGroup-list-disable': '停用',
    'portraitCenter-userGroup-list-download': '下载',
    'portraitCenter-userGroup-list-reCalc': '重新计算',
    'portraitCenter-userGroup-list-share2': '分享协作',
    'portraitCenter-userGroup-list-shareManage': '协作管理',
    'portraitCenter-userGroup-elements-id': '分群ID',
    'portraitCenter-userGroup-elements-name': '分群名称',
    'portraitCenter-userGroup-elements-type': '分群类型',
    'portraitCenter-userGroup-elements-status': '分群状态',
    'portraitCenter-userGroup-elements-approvalStatus': '审批状态',
    'portraitCenter-userGroup-elements-calcStatus': '计算状态',
    'portraitCenter-userGroup-elements-calcRule': '计算规则',
    'portraitCenter-userGroup-elements-scenarioId': 'ID类型',
    'portraitCenter-userGroup-elements-createTime': '创建时间',
    'portraitCenter-userGroup-elements-createUser': '创建人',
    'portraitCenter-userGroup-elements-lastCalcTime': '最近计算时间',
    'portraitCenter-userGroup-elements-whetherTest': '测试分群',
    'portraitCenter-userGroup-elements-genre': '分群标记类型',
    'portraitCenter-userGroup-elements-customerCount': '人数',
    'portraitCenter-userGroup-elements-validDateType': '有效时间',
    'portraitCenter-userGroup-elements-calcRule2': '更新规则',
    'portraitCenter-userGroup-elements-genre-GENERAL': '不标记',
    'portraitCenter-userGroup-elements-genre-BLACK': '黑名单',
    'portraitCenter-userGroup-elements-genre-WHITE': '白名单',
    'portraitCenter-userGroup-elements-whetherTest-0': '不限制',
    'portraitCenter-userGroup-elements-whetherTest-noLimit': '不限',
    'portraitCenter-akqZVzkmOR3E': '行为聚合分群',
    'portraitCenter-Hz1NyF0SYvES': '行为为主，聚合属性、标签、分群等条件建立分群',
    'portraitCenter-Cwt5Y1hGrnqe': '自定义筛选',
    'portraitCenter-BLaTPIrVC7v2': '自定义标签、属性筛选出特定的分群',
    'portraitCenter-604nUNNyfwVF': 'AI智能分群',
    'portraitCenter-QiR3ftYTdVCh': '通过AI算法对不同的活动场景推荐最优的分群',
    'portraitCenter-zIF6EFp48OfM': '上传分群',
    'portraitCenter-vl9t7zoTFytT': '上传唯一识别号，建立分群',
    'portraitCenter-0WqZNPRzUZv9': '复合分群',
    'portraitCenter-YsXN2Upmfk4Q': '通过叠加、排除组合现有分群所产生的分群',
    'portraitCenter-J9fOcMHqaWbA': '流程分群',
    'portraitCenter-RvYiCycSG5Qf': '选择过去参加过流程的作为分群',
    'portraitCenter-dRD0rq2ZC6J9': '启用失败：超过失效时间{{time}}',
    'portraitCenter-7vgSk74z0uf4': '是否要{{action}}分群',
    'portraitCenter-uW91wRsZlrGd': '启用',
    'portraitCenter-QhWA3hrz1eu8': '停用',
    'portraitCenter-2ypEfDD0MjkO': '确认',
    'portraitCenter-Dn10Lbhvrxuv': '取消',
    'portraitCenter-FhoqYUAkSX7M': '{{action}}成功',
    'portraitCenter-Lvc66fBk1gbf': '确定重新计算',
    'portraitCenter-5ORiWllTfepK': '重新计算将生产新的计算结果',
    'portraitCenter-IptepwhXYJbL': '请填写完整区间',
    'portraitCenter-HwdyPhWmbeKa': '用户分群数据准备中，请耐心等待...',
    'portraitCenter-0S42r47PYGlt': '分群名称',
    'portraitCenter-tuDeaBiSWJtk': '分享人',
    'portraitCenter-Ld8pB09MdR02': '分享人部门',
    'portraitCenter-k4SmnTvgW1EL': '人数',
    'portraitCenter-T8t2j2uddo0E': '最近计算时间',
    'portraitCenter-5SVPqbDgDmWS': '有效时间',
    'portraitCenter-6r5qA1IVkzpj': '永久',
    'portraitCenter-gULZDcID33pN': '分群状态',
    'portraitCenter-aioVwHX9LFNt': 'ID类型',
    'portraitCenter-ovgu2gAVzgFd': '计算状态',
    'portraitCenter-bUfiTbxW8xtV': '审批状态',
    'portraitCenter-0r4DEwNZj5rr': '分群类型',
    'portraitCenter-MQskFIsGfy8L': '测试分群',
    'portraitCenter-68Q8PSrKjoVt': '是',
    'portraitCenter-Q5lNBs6t6sqA': '否',
    'portraitCenter-kYlkekoGHCip': '计算规则',
    'portraitCenter-xo1Bf97Jl1oL': '创建人',
    'portraitCenter-1H7oDz8Gakpt': '创建时间',
    'portraitCenter-TScEoMrVPTzh': '更新人',
    'portraitCenter-w9OOXQ6C19yr': '更新时间',
    'portraitCenter-tt5pDiXHbhnn': '操作',
    'portraitCenter-vgJQmWI4z9pe': '编辑',
    'portraitCenter-7dAvuFuX7ewX': '更多',
    'portraitCenter-KC4NBKy7Q71r': '查看',
    'portraitCenter-jq8h7i8PHF6v': '删除',
    'portraitCenter-U40FAnOgTj1F': '下载',
    'portraitCenter-Hl3iv5yzCGjo': '重新计算',
    'portraitCenter-dc01FBnxJZBZ': '复制链接',
    'portraitCenter-jJ00Det1IicL': '编辑成功',
    'portraitCenter-FUuxdmBylNxo': '用户分群',
    'portraitCenter-ytWkgmsHnFgS': '与我分享',
    'portraitCenter-yHRQQUrfatFO': '筛选',
    'portraitCenter-nPe74KJz11BX': '选择创建分群方式',
    'portraitCenter-jGezJv0SByo1': 'id只能输入纯数字',
    'portraitCenter-80CQpsJXzGEd': 'id不能超过19位',
    'portraitCenter-qTuBFk7zVJp3': '共 {{count}} 条',
    'portraitCenter-yi37lSU0d7tc': '删除分群可能影响其他使用此分群参与的计算，是否删除？',
    'portraitCenter-3w2w2DPhr9R5': '删除成功',
    'portraitCenter-4ApQCI3FF8Av': '您将彻底删除选项："{{name}}"',
    'portraitCenter-1RAAyeVPqSuP': '此操作不可恢复，是否继续？',
    'portraitCenter-xUScS2SJPxaA': '彻底删除',
    'portraitCenter-lGdiUVrHxHXb': '您将还原选项："{{name}}" 还原，是否继续？',
    'portraitCenter-yk6w5csD5HBb': '确定还原',
    'portraitCenter-9ihX4N7HOaKO': '还原',
    'portraitCenter-F1fukdr7dip2': '还原成功',
    'portraitCenter-VgyRtwQaoTSQ': '移入人',
    'portraitCenter-7u4ZnXhK13A9': '移入时间',
    'portraitCenter-o4y2hrRmvmKQ': '回收站',
    'portraitCenter-Zf4l749nQa8G': '取消收藏',
    'portraitCenter-3TMxqK2qgbEM': '收藏',
    'portraitCenter-8TKULQI4IQ7Z': '协作管理',
    'portraitCenter-zL7K0FJFTFQc': '您将彻底删除选项："{{name}}"',
    'portraitCenter-Kswfi0zaYCqB': '此操作不可恢复，是否继续？',
    'portraitCenter-dAvuYbg6nKdY': '彻底删除',
    'portraitCenter-yrz698LzkUU0': '您将还原选项："{{name}}" 还原，是否继续？',
    'portraitCenter-UHlYeX54MlEu': '确定还原',
    'portraitCenter-5tL0EDdCuVob': '还原',
    'portraitCenter-MTgv42nzbWj8': '还原成功',
    'portraitCenter-NOevIpVzi7Ut': '共 {{count}} 条',
    'portraitCenter-KYGPWHEGkx1C': '选择创建分群方式',
    'portraitCenter-YUirWuPOboWB': '移入人',
    'portraitCenter-wdHnbo3duWbh': '移入时间',
    'portraitCenter-UtHT2aPCgWrg': '您将彻底删除选项',
    'portraitCenter-BzZVD95KwK0g': '您将还原选项 "{{name}}" 还原, 是否继续',
    // downloadSegment 翻译
    'portraitCenter-7FClFdqAnZ1d': '未选择导出数据',
    'portraitCenter-gbrYyYJTUtQW': '不能下载空数据',
    'portraitCenter-x1OZg8FJMmnf': '提示',
    'portraitCenter-VFDR0B8HQwcU': '取消勾选，将清除已选数据',
    'portraitCenter-PCNS11JCzkVj': '下载用户分群',
    'portraitCenter-V7o42SKd19f6': '最大支持下载用户量为 {{count}}',
    'portraitCenter-kai': '若分群超 {{count}} 用户，将随机下载 {{maxCount}} ',
    'portraitCenter-vIbNcx9GvoMW': '分群名称: {{name}}',
    'portraitCenter-4NRGPgOGsLnU': '导出数据:',
    'portraitCenter-pZcLSV7fdoyn': '用户属性',
    'portraitCenter-hPdZEdvZbbfO': '用户标签',
    'portraitCenter-Q4iZk9dqWDoa': '字段显示名',
    'portraitCenter-7pRq41lqzIet': '使用标签表最新数据',
    'portraitCenter-SMbM5CBV8aaf': '用户属性 (已选: {{selected}}/{{total}})',
    'portraitCenter-1kay6uQRRoh6': '用户标签 (已选: {{count}})',
    'portraitCenter-iz60kLw6YJzA': '标签名称',
    // editAiGroup 翻译
    'portraitCenter-VJwc75eXV2ar': '短链分群',
    'portraitCenter-FKgbkPMHvApQ': 'AI智能分群',
    'portraitCenter-zeal8YrWYAxm': '留存分群',
    'portraitCenter-ezqOxjEeLbXg': '漏斗分群',
    'portraitCenter-H9eJdiDv5UYA': '日志分群',
    'portraitCenter-2fV2MkvXRFeC': '外部导入',
    'portraitCenter-OhPsAUQO6APb': '编辑 {{type}}',
    'portraitCenter-oURpCDFEew8D': '用户ID类型',
    'portraitCenter-XD7aCbSK49GG': '分群名称',
    'portraitCenter-TzFLKjKDSO5k': '请输入分群名称!',
    'portraitCenter-rIYjdjjj8PhA': '分群名称不能超过60个字符',
    'portraitCenter-wCPsffxLNode': '请输入字母、数字、字符(._-)或者汉字',
    'portraitCenter-xukyVj4DtIIJ': '分群名称不能为空',
    'portraitCenter-fs1PhOQg08MT': '分群名称已存在',
    'portraitCenter-dmY0t2ik2Um8': '请输入分群名称',
    'portraitCenter-2ZqmFD1YmeOz': '有效时间',
    'portraitCenter-igJD7wRYQYFP': '请选择有效时间',
    'portraitCenter-qlnKRa6nzC3S': '永久有效',
    'portraitCenter-Dcw0s0ARlJ0t': '失效时间',
    'portraitCenter-vf5VzaVFRDzv': '备注描述',
    'portraitCenter-zm4P5PXeZw5C': '备注描述不能超过150个字符',
    'portraitCenter-6M77bwINZxXw': '请输入备注描述, 不超过150个字符(可选)'
  },
  en: {
    'portraitCenter-userGroup-ruleList-once-name': 'Once Calculation',
    'portraitCenter-userGroup-ruleList-once-text': 'Once Calculation',
    'portraitCenter-userGroup-ruleList-schedule-name': 'Scheduled Calculation',
    'portraitCenter-userGroup-ruleList-schedule-text': 'Scheduled Calculation',
    'portraitCenter-userGroup-validTime-forever': 'Forever',
    'portraitCenter-userGroup-statusList-draft': 'Draft',
    'portraitCenter-userGroup-statusList-normal': 'Enabled',
    'portraitCenter-userGroup-statusList-disable': 'Disabled',
    'portraitCenter-userGroup-approveStatusList-pending': 'Pending Approval',
    'portraitCenter-userGroup-approveStatusList-pass': 'Approved',
    'portraitCenter-userGroup-approveStatusList-none': 'No Approval',
    'portraitCenter-userGroup-approveStatusList-reject': 'Rejected',
    'portraitCenter-userGroup-approveStatusList-running': 'In Approval',
    'portraitCenter-userGroup-approveStatusList-backout': 'Revoked',
    'portraitCenter-userGroup-approveStatusList-cancel': 'Cancelled',
    'portraitCenter-userGroup-calcStatusList-notrun': 'Not Started',
    'portraitCenter-userGroup-calcStatusList-calcing': 'Calculating',
    'portraitCenter-userGroup-calcStatusList-fail': 'Calculation Failed',
    'portraitCenter-userGroup-calcStatusList-suc': 'Calculation Successful',
    'portraitCenter-userGroup-typeList-conditional': 'Custom Filter',
    'portraitCenter-userGroup-typeList-ai': 'AI Smart Grouping',
    'portraitCenter-userGroup-typeList-complex': 'Complex Grouping',
    'portraitCenter-userGroup-typeList-upload': 'Upload Grouping',
    'portraitCenter-userGroup-typeList-campaign': 'Campaign Grouping',
    'portraitCenter-userGroup-typeList-conditionAggregate': 'Behavior Aggregate Grouping',
    'portraitCenter-userGroup-typeList-shortLink': 'Short Link Grouping',
    'portraitCenter-userGroup-typeList-campaignLog': 'Campaign Log Grouping',
    'portraitCenter-userGroup-typeList-funnelChart': 'Funnel Conversion Grouping',
    'portraitCenter-userGroup-typeList-retentionChart': 'Retention Conversion Grouping',
    'portraitCenter-userGroup-typeList-exSubscribe': 'External Import',
    'portraitCenter-userGroup-proessStatusList-pending': 'Pending Approval',
    'portraitCenter-userGroup-proessStatusList-running': 'In Approval',
    'portraitCenter-userGroup-proessStatusList-pass': 'Approved',
    'portraitCenter-userGroup-proessStatusList-none': 'No Approval',
    'portraitCenter-userGroup-proessStatusList-reject': 'Rejected',
    'portraitCenter-userGroup-proessStatusList-backout': 'Revoked',
    'portraitCenter-userGroup-queryConfig-name': 'Group Name',
    'portraitCenter-userGroup-queryConfig-customerCount': 'Number of People',
    'portraitCenter-userGroup-queryConfig-validTime': 'Valid Time',
    'portraitCenter-userGroup-queryConfig-type': 'Group Type',
    'portraitCenter-userGroup-queryConfig-status': 'Group Status',
    'portraitCenter-userGroup-queryConfig-calcRule': 'Calculation Rule',
    'portraitCenter-userGroup-queryConfig-createTime': 'Creation Time',
    'portraitCenter-userGroup-queryConfig-user': 'Creator',
    'portraitCenter-userGroup-queryConfig-lastCalcTime': 'Last Calculation Time',
    'portraitCenter-userGroup-queryConfig-createUserId': 'Only View My Created',
    'portraitCenter-userGroup-tableConfig-edit': 'Continue Editing',
    'portraitCenter-userGroup-tableConfig-view': 'View Group',
    'portraitCenter-userGroup-tableConfig-delete': 'Delete Group',
    'portraitCenter-userGroup-tableConfig-deleteConfirm': 'Deleting the group may affect other calculations that use this group. Do you want to delete?',
    'portraitCenter-userGroup-list-status': 'Group Status',
    'portraitCenter-userGroup-list-idType': 'ID Type',
    'portraitCenter-userGroup-list-calcStatus': 'Calculation Status',
    'portraitCenter-userGroup-list-approvalStatus': 'Approval Status',
    'portraitCenter-userGroup-list-type': 'Group Type',
    'portraitCenter-userGroup-list-testGroup': 'Test Group',
    'portraitCenter-userGroup-list-yes': 'Yes',
    'portraitCenter-userGroup-list-no': 'No',
    'portraitCenter-userGroup-list-calcRule': 'Calculation Rule',
    'portraitCenter-userGroup-list-creator': 'Creator',
    'portraitCenter-userGroup-list-createTime': 'Creation Time',
    'portraitCenter-userGroup-list-updater': 'Updater',
    'portraitCenter-userGroup-list-updateTime': 'Update Time',
    'portraitCenter-userGroup-list-operations': 'Operations',
    'portraitCenter-userGroup-tableConfig-createUser': 'Creator',
    'portraitCenter-userGroup-tableConfig-createTime': 'Creation Time',
    'portraitCenter-userGroup-tableConfig-updateUser': 'Updater',
    'portraitCenter-userGroup-recentMenu-week': 'One Week',
    'portraitCenter-userGroup-recentMenu-month': 'One Month',
    'portraitCenter-userGroup-recentMenu-threeMonths': 'Three Months',
    'portraitCenter-userGroup-recentMenu-sixMonths': 'Six Months',
    'portraitCenter-userGroup-queryConfig-all': 'All',
    'portraitCenter-userGroup-queryConfig-mine': 'Mine',
    'portraitCenter-userGroup-queryConfig-recent': 'Recent',
    'portraitCenter-userGroup-queryConfig-favorite': 'Favorite',
    'portraitCenter-userGroup-list-more': 'More',
    'portraitCenter-userGroup-list-edit': 'Edit',
    'portraitCenter-userGroup-list-filter': 'Filter',
    'portraitCenter-userGroup-list-create': 'Create Group',
    'portraitCenter-userGroup-list-share': 'Shared with Me',
    'portraitCenter-userGroup-list-recycle': 'Recycle Bin',
    'portraitCenter-userGroup-create-title': 'Select Group Creation Method',
    'portraitCenter-userGroup-typeList-conditionAggregate-desc': 'Create groups based on behavior, aggregated attributes, tags, and other conditions',
    'portraitCenter-userGroup-typeList-conditional-desc': 'Filter specific groups by custom tags and attributes',
    'portraitCenter-userGroup-typeList-ai-desc': 'Recommend the best groups for different activity scenarios through AI algorithms',
    'portraitCenter-userGroup-typeList-upload-desc': 'Create groups by uploading unique identifiers',
    'portraitCenter-userGroup-typeList-complex-desc': 'Create groups by combining or excluding existing groups',
    'portraitCenter-userGroup-typeList-campaign-desc': 'Select those who participated in past campaigns as a group',
    'portraitCenter-userGroup-delete-confirmation': 'You will delete the option: "{{name}}"',
    'portraitCenter-userGroup-delete-impact': 'Deleting the group may affect other calculations that use this group. Do you want to delete?',
    'portraitCenter-userGroup-collect-success': 'Collection Successful',
    'portraitCenter-userGroup-cancel-collect-success': 'Cancel Collection Successful',
    'portraitCenter-userGroup-delete-success': 'Delete Successful',
    'portraitCenter-userGroup-enable-success': 'Enable Successful',
    'portraitCenter-userGroup-enable-enable': 'Enable',
    'portraitCenter-userGroup-enable-disable': 'Disable',
    'portraitCenter-userGroup-disable-success': 'Disable Successful',
    'portraitCenter-userGroup-recalc-success': 'Recalculation Successful',
    'portraitCenter-userGroup-recycle-success': 'Operation Successful',
    'portraitCenter-userGroup-edit-success': 'Edit Successful',
    'portraitCenter-userGroup-enable-fail': 'Enable Failed: Exceeded expiration time {{time}}',
    'portraitCenter-userGroup-ownership-edit-success': 'Ownership Modification Successful',
    'portraitCenter-userGroup-delete-title': 'Delete',
    'portraitCenter-userGroup-delete-okText': 'Delete',
    'portraitCenter-userGroup-delete-cancelText': 'Cancel',
    'portraitCenter-userGroup-enable-confirm': 'Do you want to {{status}} the group',
    'portraitCenter-userGroup-status-confirm-onText': 'Confirm',
    'portraitCenter-userGroup-status-confirm-cancelText': 'Cancel',
    'portraitCenter-userGroup-reCalc-confirm-title': 'Confirm Recalculation',
    'portraitCenter-userGroup-reCalc-confirm-content': 'Recalculation will produce new calculation results',
    'portraitCenter-userGroup-reCalc-confirm-onText': 'Confirm',
    'portraitCenter-userGroup-reCalc-confirm-cancelText': 'Cancel',
    'portraitCenter-userGroup-recycle-confirm-content': 'You will move the data "{{name}}" to the recycle bin. Do you want to continue? After moving, all User groups that rely on this group will not be able to continue to be used normally. You can also restore it from the recycle bin later.',
    'portraitCenter-userGroup-recycle-confirm-title': 'Move to Recycle Bin',
    'portraitCenter-userGroup-recycle-confirm-onText': 'Confirm Move',
    'portraitCenter-userGroup-recycle-confirm-cancelText': 'Cancel',
    'portraitCenter-userGroup-customerCount-error': 'Please fill in the complete range',
    'portraitCenter-userGroup-export-loading': 'User group data is being prepared, please wait patiently...',
    'portraitCenter-userGroup-list-recycling': 'Move to Recycle Bin',
    'portraitCenter-userGroup-list-updateDept': 'Modify Ownership',
    'portraitCenter-userGroup-list-view': 'View',
    'portraitCenter-userGroup-list-collect': 'Collect',
    'portraitCenter-userGroup-list-cancelCollect': 'Cancel Collect',
    'portraitCenter-userGroup-list-delete': 'Delete',
    'portraitCenter-userGroup-list-disable': 'Disable',
    'portraitCenter-userGroup-list-download': 'Download',
    'portraitCenter-userGroup-list-reCalc': 'Recalculate',
    'portraitCenter-userGroup-list-share2': 'Collaborative Sharing',
    'portraitCenter-userGroup-list-shareManage': 'Collaboration Management',
    'portraitCenter-userGroup-elements-id': 'Group ID',
    'portraitCenter-userGroup-elements-name': 'Group Name',
    'portraitCenter-userGroup-elements-type': 'Group Type',
    'portraitCenter-userGroup-elements-status': 'Group Status',
    'portraitCenter-userGroup-elements-approvalStatus': 'Approval Status',
    'portraitCenter-userGroup-elements-calcStatus': 'Calculation Status',
    'portraitCenter-userGroup-elements-calcRule': 'Calculation Rule',
    'portraitCenter-userGroup-elements-scenarioId': 'ID Type',
    'portraitCenter-userGroup-elements-createTime': 'Creation Time',
    'portraitCenter-userGroup-elements-createUser': 'Creator',
    'portraitCenter-userGroup-elements-lastCalcTime': 'Last Calculation Time',
    'portraitCenter-userGroup-elements-whetherTest': 'Whether Test',
    'portraitCenter-userGroup-elements-genre': 'Group Marking Type',
    'portraitCenter-userGroup-elements-customerCount': 'Number of People',
    'portraitCenter-userGroup-elements-validDateType': 'Valid Time',
    'portraitCenter-userGroup-elements-calcRule2': 'Update Rule',
    'portraitCenter-userGroup-elements-genre-GENERAL': 'Unmarked',
    'portraitCenter-userGroup-elements-genre-BLACK': 'Blacklist',
    'portraitCenter-userGroup-elements-genre-WHITE': 'Whitelist',
    'portraitCenter-userGroup-elements-whetherTest-0': 'No Limit',
    'portraitCenter-userGroup-elements-whetherTest-noLimit': 'Unlimited',
    'portraitCenter-akqZVzkmOR3E': 'Behavior Aggregate Grouping',
    'portraitCenter-Hz1NyF0SYvES': 'Behavior-based, aggregating attributes, tags, groups and other conditions to create groups',
    'portraitCenter-Cwt5Y1hGrnqe': 'Custom Filter',
    'portraitCenter-BLaTPIrVC7v2': 'Filter specific groups through custom tags and attributes',
    'portraitCenter-604nUNNyfwVF': 'AI Smart Grouping',
    'portraitCenter-QiR3ftYTdVCh': 'Recommend optimal groups for different activity scenarios through AI algorithms',
    'portraitCenter-zIF6EFp48OfM': 'Upload Grouping',
    'portraitCenter-vl9t7zoTFytT': 'Upload unique identifiers to create groups',
    'portraitCenter-0WqZNPRzUZv9': 'Complex Grouping',
    'portraitCenter-YsXN2Upmfk4Q': 'Groups generated by combining existing groups through overlay and exclusion',
    'portraitCenter-J9fOcMHqaWbA': 'Campaign Grouping',
    'portraitCenter-RvYiCycSG5Qf': 'Select those who participated in campaigns in the past as groups',
    'portraitCenter-dRD0rq2ZC6J9': 'Enable failed: Exceeded expiration time {{time}}',
    'portraitCenter-7vgSk74z0uf4': 'Do you want to {{action}} the group',
    'portraitCenter-uW91wRsZlrGd': 'Enable',
    'portraitCenter-QhWA3hrz1eu8': 'Disable',
    'portraitCenter-2ypEfDD0MjkO': 'Confirm',
    'portraitCenter-Dn10Lbhvrxuv': 'Cancel',
    'portraitCenter-FhoqYUAkSX7M': '{{action}} successful',
    'portraitCenter-Lvc66fBk1gbf': 'Confirm recalculation',
    'portraitCenter-5ORiWllTfepK': 'Recalculation will generate new calculation results',
    'portraitCenter-IptepwhXYJbL': 'Please fill in the complete range',
    'portraitCenter-HwdyPhWmbeKa': 'User group data is being prepared, please wait patiently...',
    'portraitCenter-0S42r47PYGlt': 'Group Name',
    'portraitCenter-tuDeaBiSWJtk': 'Sharer',
    'portraitCenter-Ld8pB09MdR02': 'Sharer Department',
    'portraitCenter-k4SmnTvgW1EL': 'Count',
    'portraitCenter-T8t2j2uddo0E': 'Last Calculation Time',
    'portraitCenter-5SVPqbDgDmWS': 'Valid Time',
    'portraitCenter-6r5qA1IVkzpj': 'Permanent',
    'portraitCenter-gULZDcID33pN': 'Group Status',
    'portraitCenter-aioVwHX9LFNt': 'ID Type',
    'portraitCenter-ovgu2gAVzgFd': 'Calculation Status',
    'portraitCenter-bUfiTbxW8xtV': 'Approval Status',
    'portraitCenter-0r4DEwNZj5rr': 'Group Type',
    'portraitCenter-MQskFIsGfy8L': 'Test Group',
    'portraitCenter-68Q8PSrKjoVt': 'Yes',
    'portraitCenter-Q5lNBs6t6sqA': 'No',
    'portraitCenter-kYlkekoGHCip': 'Calculation Rule',
    'portraitCenter-xo1Bf97Jl1oL': 'Creator',
    'portraitCenter-1H7oDz8Gakpt': 'Create Time',
    'portraitCenter-TScEoMrVPTzh': 'Updater',
    'portraitCenter-w9OOXQ6C19yr': 'Update Time',
    'portraitCenter-tt5pDiXHbhnn': 'Operation',
    'portraitCenter-vgJQmWI4z9pe': 'Edit',
    'portraitCenter-7dAvuFuX7ewX': 'More',
    'portraitCenter-KC4NBKy7Q71r': 'View',
    'portraitCenter-jq8h7i8PHF6v': 'Delete',
    'portraitCenter-U40FAnOgTj1F': 'Download',
    'portraitCenter-Hl3iv5yzCGjo': 'Recalculate',
    'portraitCenter-dc01FBnxJZBZ': 'Copy Link',
    'portraitCenter-jJ00Det1IicL': 'Edit successful',
    'portraitCenter-FUuxdmBylNxo': 'User Groups',
    'portraitCenter-ytWkgmsHnFgS': 'Shared with Me',
    'portraitCenter-yHRQQUrfatFO': 'Filter',
    'portraitCenter-nPe74KJz11BX': 'Select Group Creation Method',
    'portraitCenter-jGezJv0SByo1': 'ID can only contain numbers',
    'portraitCenter-80CQpsJXzGEd': 'ID cannot exceed 19 digits',
    'portraitCenter-qTuBFk7zVJp3': 'Total {{count}} items',
    'portraitCenter-yi37lSU0d7tc': 'Deleting the group may affect other calculations that use this group. Do you want to delete?',
    'portraitCenter-3w2w2DPhr9R5': 'Delete successful',
    'portraitCenter-4ApQCI3FF8Av': 'You will permanently delete option: "{{name}}"',
    'portraitCenter-1RAAyeVPqSuP': 'This operation cannot be undone, do you want to continue?',
    'portraitCenter-xUScS2SJPxaA': 'Permanently Delete',
    'portraitCenter-lGdiUVrHxHXb': 'You will restore option: "{{name}}", do you want to continue?',
    'portraitCenter-yk6w5csD5HBb': 'Confirm Restore',
    'portraitCenter-9ihX4N7HOaKO': 'Restore',
    'portraitCenter-F1fukdr7dip2': 'Restore successful',
    'portraitCenter-VgyRtwQaoTSQ': 'Moved In By',
    'portraitCenter-7u4ZnXhK13A9': 'Moved In Time',
    'portraitCenter-o4y2hrRmvmKQ': 'Recycle Bin',
    'portraitCenter-Zf4l749nQa8G': 'Unfavorite',
    'portraitCenter-3TMxqK2qgbEM': 'Favorite',
    'portraitCenter-8TKULQI4IQ7Z': 'Collaboration Management',
    'portraitCenter-zL7K0FJFTFQc': 'You will permanently delete option: "{{name}}"',
    'portraitCenter-Kswfi0zaYCqB': 'This operation cannot be undone, do you want to continue?',
    'portraitCenter-dAvuYbg6nKdY': 'Permanently Delete',
    'portraitCenter-yrz698LzkUU0': 'You will restore option: "{{name}}", do you want to continue?',
    'portraitCenter-UHlYeX54MlEu': 'Confirm Restore',
    'portraitCenter-5tL0EDdCuVob': 'Restore',
    'portraitCenter-MTgv42nzbWj8': 'Restore successful',
    'portraitCenter-NOevIpVzi7Ut': 'Total {{count}} items',
    'portraitCenter-KYGPWHEGkx1C': 'Select Group Creation Method',
    'portraitCenter-YUirWuPOboWB': 'Moved By',
    'portraitCenter-wdHnbo3duWbh': 'Move Time',
    'portraitCenter-UtHT2aPCgWrg': 'You will permanently delete option',
    'portraitCenter-BzZVD95KwK0g': 'You will restore option "{{name}}", do you want to continue',
    // downloadSegment 翻译
    'portraitCenter-7FClFdqAnZ1d': 'No export data selected',
    'portraitCenter-gbrYyYJTUtQW': 'Cannot download empty data',
    'portraitCenter-x1OZg8FJMmnf': 'Tips',
    'portraitCenter-VFDR0B8HQwcU': 'Unchecking will clear selected data',
    'portraitCenter-PCNS11JCzkVj': 'Download User Group',
    'portraitCenter-V7o42SKd19f6': 'Maximum supported download count is {{count}}',
    'portraitCenter-2KmIcp9lqrp3': 'If group exceeds {{count}} users, {{count}} will be randomly downloaded',
    'portraitCenter-vIbNcx9GvoMW': 'Group Name: {{name}}',
    'portraitCenter-4NRGPgOGsLnU': 'Export Data:',
    'portraitCenter-pZcLSV7fdoyn': 'User Properties',
    'portraitCenter-hPdZEdvZbbfO': 'User Tags',
    'portraitCenter-Q4iZk9dqWDoa': 'Field Display Name',
    'portraitCenter-7pRq41lqzIet': 'Use latest data from tag table',
    'portraitCenter-SMbM5CBV8aaf': 'User Properties (Selected: {{selected}}/{{total}})',
    'portraitCenter-1kay6uQRRoh6': 'User Tags (Selected: {{count}})',
    'portraitCenter-iz60kLw6YJzA': 'Tag Name',
    // editAiGroup 翻译
    'portraitCenter-VJwc75eXV2ar': 'Short Link Group',
    'portraitCenter-FKgbkPMHvApQ': 'AI Smart Group',
    'portraitCenter-zeal8YrWYAxm': 'Retention Group',
    'portraitCenter-ezqOxjEeLbXg': 'Funnel Group',
    'portraitCenter-H9eJdiDv5UYA': 'Log Group',
    'portraitCenter-2fV2MkvXRFeC': 'External Import',
    'portraitCenter-OhPsAUQO6APb': 'Edit {{type}}',
    'portraitCenter-oURpCDFEew8D': 'User ID Type',
    'portraitCenter-XD7aCbSK49GG': 'Group Name',
    'portraitCenter-TzFLKjKDSO5k': 'Please enter group name!',
    'portraitCenter-rIYjdjjj8PhA': 'Group name should not exceed 60 characters',
    'portraitCenter-wCPsffxLNode': 'Please enter letters, numbers, characters (._-) or Chinese characters',
    'portraitCenter-xukyVj4DtIIJ': 'Group name cannot be empty',
    'portraitCenter-fs1PhOQg08MT': 'Group name already exists',
    'portraitCenter-dmY0t2ik2Um8': 'Please enter group name',
    'portraitCenter-2ZqmFD1YmeOz': 'Valid Time',
    'portraitCenter-igJD7wRYQYFP': 'Please select valid time!',
    'portraitCenter-qlnKRa6nzC3S': 'Permanently Valid',
    'portraitCenter-Dcw0s0ARlJ0t': 'Expiration Time',
    'portraitCenter-vf5VzaVFRDzv': 'Remark Description',
    'portraitCenter-zm4P5PXeZw5C': 'Group description should not exceed 150 characters',
    'portraitCenter-6M77bwINZxXw': 'Please enter remark description, no more than 150 characters (optional)'
  }
};
