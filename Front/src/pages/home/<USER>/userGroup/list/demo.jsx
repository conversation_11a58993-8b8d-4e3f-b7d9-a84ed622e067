import { But<PERSON>, Modal } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { Component } from 'react';
import { connect } from 'react-redux';
// import Datatist from 'datatist-react';
import { FilterOutlined } from '@ant-design/icons';
import AIIcon from 'assets/images/group-type-ai.png';
import setIcon from 'assets/images/group-type1.png';
import BaseTable from 'components/baseTable/baseTable';
import Query from 'components/query/Query';
import { ruleList, statusList, typeList } from 'pages/home/<USER>/config';
import UserService from 'service/UserService';
import ArrayUtil from 'utils/arrayUtil';
import Log from 'utils/log';
import { t } from 'utils/translation';
import { queryConfig, tableConfig } from '../config';
import './UserGroupList.scss';

// const { DtQuery } = Datatist;

/** 表头数据 */
const columns = [
  {
    title: t('portraitCenter-userGroup-elements-name'),
    dataIndex: 'name',
    className: 'name',
    render: (text, val) => (
      <div>
        {val.src ? <img src={val.src} style={{ width: 28, height: 28, marginRight: 5 }} alt="png" /> : null}
        <span>{text}</span>
      </div>
    ),
    width: 200
  },
  {
    title: t('portraitCenter-userGroup-elements-customerCount'),
    dataIndex: 'customerCount',
    sorter: true
  },
  {
    title: t('portraitCenter-userGroup-elements-lastCalcTime'),
    dataIndex: 'lastCalcTime',
    render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
    sorter: true
  },
  {
    title: t('portraitCenter-userGroup-elements-status'),
    dataIndex: 'status',
    render: (text) => (
      <div className="status">
        <span className={`circle ${text === 'NORMAL' ? 'green' : null}`} />
        {_.filter(statusList, (v) => v.value === text)[0].name}
      </div>
    ),
    sorter: true
  },
  {
    title: t('portraitCenter-userGroup-elements-type'),
    dataIndex: 'type',
    render: (text) => _.filter(typeList, (v) => v.value === text)[0].name
  },
  {
    title: t('portraitCenter-userGroup-elements-calcRule'),
    dataIndex: 'calcRule',
    className: 'maxWidth',
    render: (text) => (text ? _.filter(ruleList, (v) => v.value === text)[0].name : '-')
  },
  {
    title: t('portraitCenter-userGroup-elements-createUserName'),
    dataIndex: 'createUserName'
  },
  {
    title: t('portraitCenter-userGroup-elements-createTime'),
    dataIndex: 'createTime',
    render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    sorter: true
  },
  {
    title: t('portraitCenter-userGroup-elements-operation'),
    dataIndex: 'set',
    className: 'td-set'
  }
];
tableConfig.columns = columns;

const grid = {
  leftCol: 24,
  rightCol: 24,
  childCol: 12
};

const log = Log.getLogger('userGroupList');
const userService = new UserService();

const stateToProps = (state) => {
  return {
    loginUser: state.loginUser
  };
};

const mapDispatchToProps = () => {
  return {};
};

class List extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      isFold: false,
      value: {},
      config: [],
      tableParams: {
        search: []
      }
    };

    this.toggle = this.toggle.bind(this);
    this.queryData = this.queryData.bind(this);
    this.hideModal = this.hideModal.bind(this);
    this.selectSegmentType = this.selectSegmentType.bind(this);
  }

  async componentDidMount() {
    log.debug('componentDidMount', this.state);
    await this.findAllName('');
  }

  // 筛选
  toggle() {
    const { isFold } = this.state;
    this.setState({ isFold: !isFold });
  }

  // 子组件查询数组
  queryData(data) {
    log.debug('子组件传过来的数据', data);
    const { loginUser } = this.props;
    _.forEach(data, (value) => {
      if (value.propertyName === 'createUserId') {
        if (value.value === true) {
          value.value = loginUser.id;
        } else {
          value.value = '';
        }
      } else if (value.value === undefined) {
        value.value = '';
      }
    });

    const userObj = _.find(data, (item) => item.propertyName === 'createUserName');
    const userIdObj = _.find(data, (item) => item.propertyName === 'createUserId');

    if (userObj.value) {
      userIdObj.value = userObj.value;
      userObj.value = '';
    }

    this.setState({
      tableParams: {
        ...this.state.tableParams,
        search: data
      }
    });
  }

  // 查询用户
  async findAllName(data) {
    let userList;
    try {
      userList = await userService.findAllName(data);
    } catch (error) {
      log.debug('userGroupService.findAllName', error);
      userList = [];
    }
    const arr = ArrayUtil.changeArrByKey(userList, 'name', 'name', 'value', 'id');
    const config = ArrayUtil.toArrByPropAndValue(queryConfig, 'propertyName', 'createUserId', 'options', arr);
    this.setState({ config });
  }

  // 隐藏modal
  hideModal() {
    this.setState({ visible: false });
  }

  // 选择分群方式
  selectSegmentType(value) {
    if (value.type === 'CONDITIONAL') {
      this.props.history.push(`/aimarketer/home/<USER>/userGroup/create?type=${value.type}`);
    }
  }

  render() {
    const { isFold, visible, config, tableParams } = this.state;

    const modalData = [
      {
        title: t('portraitCenter-userGroup-typeList-conditional'),
        des: t('portraitCenter-userGroup-typeList-conditional-desc'),
        src: setIcon,
        type: 'CONDITIONAL'
      },
      {
        title: t('portraitCenter-userGroup-typeList-ai'),
        des: t('portraitCenter-userGroup-typeList-ai-desc'),
        src: AIIcon,
        type: 'AI'
      }
    ];

    return (
      <div className="userGroup1">
        <header>
          <h1>{t('portraitCenter-userGroup-list-title')}</h1>
          <div className="btnGroup">
            <Button onClick={this.toggle}>
              <FilterOutlined />
              {t('portraitCenter-userGroup-list-filter')}
            </Button>
            <Button type="primary" onClick={() => this.setState({ visible: true })}>
              {t('portraitCenter-userGroup-list-create')}
            </Button>
          </div>
        </header>
        {/* <Skeleton
          className="query"
          active
          paragraph={{ rows: 8 }}
          loading={!config.length}
          title={false}
        >
        </Skeleton> */}
        {/* {config.length ? <DtQuery isFold={isFold} config={config} grid={grid} queryData={this.queryData} /> : null} */}
        {config.length ? <Query isFold={isFold} config={config} grid={grid} queryData={this.queryData} /> : null}
        <div className="table1">
          <BaseTable tableParams={tableParams} tableConfig={tableConfig} />
        </div>
        <Modal
          open={visible}
          onCancel={this.hideModal}
          footer={null}
          width={976}
          getContainer={document.getElementsByClassName('userGroup')[0]}
        >
          <div className="userGroupCreateModal">
            <p className="title">{t('portraitCenter-userGroup-create-title')}</p>
            <div className="container">
              {_.map(modalData, (item, index) => {
                return (
                  <div
                    key={index}
                    className={`item ${item.type === 'AI' ? 'disabled' : null}`}
                    onClick={() => this.selectSegmentType(item)}
                  >
                    <img src={item.src} alt="png" />
                    <div className="right">
                      <h2>{item.title}</h2>
                      <p>{item.des}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </Modal>
      </div>
    );
  }
}

export default connect(stateToProps, mapDispatchToProps)(List);
