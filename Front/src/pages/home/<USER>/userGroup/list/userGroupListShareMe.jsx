import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Divider, Dropdown, Modal, Table, Tooltip, message } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link, withRouter } from 'react-router-dom';
// import Datatist from 'datatist-react';
import QueryForList from 'components/bussinesscoms/queryforlist/index';
import CampaignV2Service from 'service/CampaignV2Service';
import UserGroupService from 'service/UserGroupService';
import UserService from 'service/UserService';
import ArrayUtil from 'utils/arrayUtil';
import Log from 'utils/log';
import { t } from 'utils/translation';
import './UserGroupList.scss';

import usergroupicon from 'assets/images/user-group-list-icon.png';
import scenarioService from 'service/ScenarioService';

import { useStore } from '@/store/share';
import CheckAuth from 'utils/checkAuth';
import { DatatistIcon } from 'utils/myIcon';
import { copyToClipboard, transformUrl } from 'utils/universal';

import { getDeptPath } from '@/pages/home/<USER>/dataPermissions/config';
import { FilterOutlined } from '@ant-design/icons';
import {
  calcStatusList,
  elementsWithoutApprove,
  proessStatusList,
  elements as queryElements,
  ruleList,
  statusList,
  typeList
} from '../config';
import DownloadSegment from '../downloadSegment/index';
import EditAiGroup from '../editAiGroup/index';

const SIZE = 10;
const searchAdd = [];
const log = Log.getLogger('userGroupList');
const userGroupService = new UserGroupService();
const campaignV2Service = new CampaignV2Service();

const { confirm } = Modal;

const modalData = [
  {
    title: t('portraitCenter-akqZVzkmOR3E'),
    des: t('portraitCenter-Hz1NyF0SYvES'),
    src: <DatatistIcon type="icon-Throng-Behavior" />,
    type: 'CONDITION_AGGREGATE'
  },
  {
    title: t('portraitCenter-userGroup-typeList-conditional'),
    des: t('portraitCenter-userGroup-typeList-conditional-desc'),
    src: <DatatistIcon type="icon-Throng-Custom" />,
    type: 'CONDITIONAL'
  },
  {
    title: t('portraitCenter-userGroup-typeList-ai'),
    des: t('portraitCenter-userGroup-typeList-ai-desc'),
    src: <DatatistIcon type="icon-Throng-Ai" />,
    type: 'AI'
  },
  {
    title: t('portraitCenter-userGroup-typeList-upload'),
    des: t('portraitCenter-userGroup-typeList-upload-desc'),
    src: <DatatistIcon type="icon-Throng-Upload" />,
    type: 'UPLOAD'
  },
  {
    title: t('portraitCenter-userGroup-typeList-complex'),
    des: t('portraitCenter-userGroup-typeList-complex-desc'),
    src: <DatatistIcon type="icon-Throng-Compose" />,
    type: 'COMPLEX'
  },
  {
    title: t('portraitCenter-userGroup-typeList-campaign'),
    des: t('portraitCenter-userGroup-typeList-campaign-desc'),
    src: <DatatistIcon type="icon-Throng-Campaign" />,
    type: 'CAMPAIGN'
  }
];

const stateToProps = (state) => {
  return {
    userList: ArrayUtil.changeArrByKey(state.userList, 'name', 'name', 'value', 'id'),
    loginUser: state.loginUser,
    isFold: state.toggle.userGroup,
    meunData: state.meunData.menus.main
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    toggle: (isFold) => {
      dispatch({ type: 'USERGROUP', isFold: !isFold });
    }
  };
};

const withStore = (BaseComponent) => (props) => {
  const store = useStore();
  return <BaseComponent {...props} store={store} />;
};

const isCheckAuth = CheckAuth.checkAuth;

class ShareGroupList extends Component {
  constructor(props) {
    super(props);
    this.userService = new UserService();
    this.state = {
      visible: false,
      loading: false,
      value: {},
      proessStatus: false,
      tableParams: {
        size: SIZE,
        page: +localStorage.getItem('shareUserGroupPage') || 1,
        search: [],
        sorts: [
          {
            direction: 'desc',
            propertyName: 'operationTime'
          }
        ]
      },
      totalCount: null,
      data: [],
      elements: queryElements,
      defaultFormData: {},
      downloadSegmentMaxCount: 0,
      showDownloadModal: false,
      currentDownloadSegment: null,
      scenarioOptions: [],
      editVisible: false,
      editValue: null,
      editLoading: false,
      // sorterState: { idSort: false, countSort: false, recentTimeSort: false, statusSort: false, createTimeSort: false, updateTimeSort: false },
      sorterInfo: { columnKey: null, order: null },
      idSort: null,
      countSort: null,
      userId: null,
      infoId: null,
      isCollect: false,
      recentRange: null
    };

    this.queryData = this.queryData.bind(this);
    this.handleTableChange = this.handleTableChange.bind(this);
    this.hideModal = this.hideModal.bind(this);
    this.selectSegmentType = this.selectSegmentType.bind(this);
    this.timer = null;
    this.dispatch = this.props.store.dispatch;
  }

  UNSAFE_componentWillMount() {
    window.addEventListener('beforeunload', this.beforeunload);
  }

  async componentDidMount() {
    // const  {search} = this.props.location;

    // const {Authorization} = qs.parse(search.slice(1))
    // console.log("ss",Authorization)

    const { tableParams } = this.state;

    let _tableParams = _.cloneDeep(tableParams);

    if (localStorage.getItem('shareUserGroupFilter') && localStorage.getItem('shareUserGroupCache')) {
      const defaultFilter = {};
      let isRangeList;
      const _userGroupFilter = JSON.parse(localStorage.getItem('shareUserGroupFilter'));

      for (const item of _userGroupFilter) {
        if (item.value !== '' && item.propertyName !== 'operationTime') {
          if (item.propertyName === 'customerCount') {
            if (!isRangeList) {
              isRangeList = item;
              defaultFilter[`${item.propertyName}`] = `${item.operator},${item.value}`;
            } else {
              defaultFilter[`${item.propertyName}`] = `DATE_BETWEEN,${isRangeList.value},${item.value}`;
            }
          } else if (item.propertyName === 'validDateType') {
            defaultFilter.validDateType = item.value === 'FOREVER' ? item.value : 'DATE';
          } else {
            defaultFilter[`${item.propertyName}`] = item.value;
          }

          if (item.operator === 'DATE_BETWEEN') {
            if (item.propertyName === 'validEndTime') {
              const validEndTime = _userGroupFilter.find((filterItem) => filterItem.propertyName === 'validEndTime');

              const dateArr = validEndTime.value.split(',');

              defaultFilter.validDateType = `${defaultFilter.validDateType},${dateArr.join(',')}`;
            } else {
              const dateArr = item.value.split(',');
              defaultFilter[`${item.propertyName}`] = dateArr.map((item) => dayjs(parseInt(item)));
            }
          }
        }
      }

      this.setState({
        defaultFormData: defaultFilter
      });

      const _filters = _.map(JSON.parse(localStorage.getItem('shareUserGroupFilter')), (item) => {
        if (item.propertyName === 'id') {
          return {
            connector: 'AND',
            propertyName: item.propertyName,
            operator: item.operator,
            value: _.join(item.value, ',')
          };
        }
        if (item.operator === 'IN') {
          return {
            connector: 'AND',
            propertyName: item.propertyName,
            operator: item.operator,
            value: _.join(item.value, ',')
          };
        }
        if (item.propertyName !== 'operationTime' && item.operator === 'DATE_BETWEEN') {
          return {
            connector: 'AND',
            propertyName: item.propertyName,
            operator: item.operator,
            value: _.isArray(item.value)
              ? `${dayjs(item.value[0]).valueOf().toString()},${dayjs(item.value[1]).valueOf().toString()}`
              : item.value
            // value: item.value
          };
        }
        return {
          connector: 'AND',
          propertyName: item.propertyName,
          operator: item.operator,
          value: item.value
        };
      });

      _tableParams = {
        ..._tableParams,
        search: _filters
      };
    } else {
      _tableParams = tableParams;
    }
    log.debug('componentDidMount', this.state);

    _tableParams = {
      ..._tableParams,
      sorts: [{ direction: 'desc', propertyName: 'updateTime' }]
    };

    const { id } = await this.userService.getCurrentUser();
    this.setState({ userId: id });
    const processAuth = await userGroupService.getProcessByType({
      type: 'SEGMENT',
      projectId: localStorage.getItem('projectId'),
      status: 'ENABLE'
    });

    this.setState({
      proessStatus: processAuth.status
    });
    // await this.userGroupListQuery(tableParams);
    await this.userGroupListQuery(_tableParams);
    // 处理下elements的options
    const scenarioList = await scenarioService.scenarioList([]);
    const redata = await campaignV2Service.getBusiness({
      businessType: 'SegmentBusinessConfig'
    });
    const _elements = _.cloneDeep(processAuth.status === 'ENABLE' ? queryElements : elementsWithoutApprove);
    _elements.createUserId.componentOptions.onSearch = (val) => this.onSearch(val);
    _elements.createUserId.componentOptions.onClear = (val) => this.onSearch(val);
    _elements.createUserId.componentOptions.options = _.map(this.state.scenarioOptions, (item) => ({
      key: item.id,
      value: item.id,
      text: item.name
    }));

    _elements.createUserId.componentOptions.options = _.map(this.props.userList, (item) => ({
      key: item.value,
      value: item.value,
      text: item.name
    }));
    _elements['scenario.id'].componentOptions.options = _.map(
      scenarioList.sort((a, b) => a.orderNum - b.orderNum),
      (item) => ({
        key: item.id,
        value: item.id,
        text: `${item.name}[${item.code}]`
      })
    );

    this.setState({
      elements: _elements,
      downloadSegmentMaxCount: redata.config.downloadSegmentMaxCount || 0
    });
  }

  componentWillUnmount() {
    window.removeEventListener('beforeunload', this.beforeunload);
    localStorage.removeItem('shareUserGroupCache');
    this.timer && clearTimeout(this.timer);
  }

  onSearch = _.debounce(async (value) => {
    const res = await this.userService.findAllName({ name: value });
    const _elements = _.cloneDeep(this.state.elements);
    _elements.createUserId.componentOptions.options = _.map(res, (item) => ({
      key: item.id,
      value: item.id,
      text: item.name
    }));
    this.setState({
      scenarioOptions: res,
      elements: _elements
    });
  }, 500);

  beforeunload() {
    localStorage.removeItem('shareUserGroupPage');
    localStorage.removeItem('shareUserGroupFilter');
    localStorage.removeItem('shareUserGroupCache');
  }

  // 子组件查询数组
  queryData(data) {
    try {
      log.debug('子组件传过来的数据', data);
      if (!_.isEmpty(data)) {
        _.forEach(data, (value) => {
          if (value.value === undefined) {
            value.value = '';
          }
          if (value.propertyName === 'id') {
            _.forEach(value.value, (item) => {
              if (_.isNaN(Number(item)) || item.indexOf(' ') > -1) {
                throw new Error(t('portraitCenter-jGezJv0SByo1'));
              } else if (item.length > 19) {
                throw new Error(t('portraitCenter-80CQpsJXzGEd'));
              }
            });
            value.value = _.join(value.value, ',');
          }
        });
      }

      const { tableParams } = this.state;
      this.setState(
        {
          tableParams: {
            ...tableParams,
            search: data,
            page: 1
          }
        },
        () => {
          this.userGroupListQuery(this.state.tableParams);
        }
      );
    } catch (err) {
      message.error(err.message);
    }
  }

  /** 获取列表数据 */
  userGroupListQuery = async (tableParams, isLoading = true) => {
    const _tableParams = _.cloneDeep(tableParams);

    let params = null;
    _tableParams.search = [...tableParams.search, ...searchAdd];
    // _tableParams.search = [...tableParams.search, ...searchAdd];
    const index = _.findIndex(_tableParams.search, (v) => v.propertyName === 'operationTime');
    if (index !== -1) {
      _tableParams.search.splice(index, 1);
    }
    params = _tableParams;
    // tableParams.search = [...tableParams.search, ...searchAdd];
    const _this = this;
    isLoading && this.setState({ loading: true });
    let data;

    try {
      // data = await userGroupService.query(_tableParams);
      data = await userGroupService.segmentShareQuery(params);
    } catch (error) {
      log.debug('userGroupService.query', error);
      data = {
        content: [],
        totalElements: null
      };
    }
    // dataSource = key === 'ALL' || key === 'MINE' ? data.content : sortsData;
    // const { content, totalElements } = data;
    _this.setState({
      data: data ? data.content : [],
      totalCount: data?.totalElements,
      loading: false,
      tableParams
    });
  };

  // 操作
  handel(type, val) {
    const _this = this;
    const { tableParams, totalCount, userId } = this.state;
    switch (type) {
      case 'delete':
        confirm({
          content: t('portraitCenter-yi37lSU0d7tc'),
          okText: t('portraitCenter-jq8h7i8PHF6v'),
          okType: 'danger',
          cancelText: t('portraitCenter-Dn10Lbhvrxuv'),
          async onOk() {
            try {
              await userGroupService.delById(val.id);
              const _tableParams = { ...tableParams };
              if (
                tableParams.page > 1 &&
                tableParams.page === Math.ceil(totalCount / tableParams.size) &&
                totalCount % tableParams.size === 1
              ) {
                _tableParams.page -= 1;
              }
              _this.userGroupListQuery(_tableParams);
              message.success(t('portraitCenter-userGroup-delete-success'), 1);
            } catch (error) {
              log.debug('userGroupService.delById', error);
            }
          }
        });
        break;
      case 'detail':
        this.props.history.push(`/aimarketer/home/<USER>/userGroup/shareDetail?id=${val.id}`);
        break;
      case 'download':
        this.setState({
          currentDownloadSegment: val,
          showDownloadModal: true
        });
        break;
      case 'status':
        if (val.status === 'DISABLE' && val.validDateType === 'TEMPORARY' && dayjs().valueOf() > val.validEndTime)
          return message.error(
            t('portraitCenter-dRD0rq2ZC6J9', { time: dayjs(val.validEndTime).format('YYYY-MM-DD HH:mm') })
          );
        confirm({
          content: t('portraitCenter-userGroup-enable-confirm', {
            action:
              val.status === 'DISABLE'
                ? t('portraitCenter-userGroup-enable-enable')
                : t('portraitCenter-userGroup-enable-disable')
          }),
          okText: t('portraitCenter-userGroup-status-confirm-onText'),
          okType: 'danger',
          cancelText: t('portraitCenter-userGroup-status-confirm-cancelText'),
          async onOk() {
            await userGroupService.switchStatus({
              enable: val.status === 'DISABLE',
              id: val.id
            });
            await userGroupService.saveUserOperationRecord({
              targetId: val.id,
              id: val?.recentUserOperationRecord?.id,
              targetType: 'SEGMENT',
              type: 'RECENT',
              createUserId: userId,
              updateUserId: userId,
              createTime: dayjs().valueOf(),
              updateTime: dayjs().valueOf()
            });

            const _tableParams = { ...tableParams };
            _this.userGroupListQuery(_tableParams);
            message.success(
              t('portraitCenter-userGroup-enable-success-message', {
                action:
                  val.status === 'DISABLE'
                    ? t('portraitCenter-userGroup-enable-enable')
                    : t('portraitCenter-userGroup-enable-disable')
              }),
              1
            );
          }
        });

        break;
      case 'reCalc':
        confirm({
          title: t('portraitCenter-userGroup-reCalc-confirm-title'),
          content: t('portraitCenter-userGroup-reCalc-confirm-content'),
          okText: t('portraitCenter-userGroup-reCalc-confirm-onText'),
          okType: 'danger',
          cancelText: t('portraitCenter-userGroup-reCalc-confirm-cancelText'),
          async onOk() {
            _this.doRequest({ force: true, id: val.id });
          }
        });
        break;

      case 'copyUrl':
        const url = `${window.location.origin}/aimarketer/home/<USER>/userGroup/shareDetail?id=${val.id}`;
        copyToClipboard(url);
        break;
      default:
        break;
    }
  }

  doRequest = async (data) => {
    try {
      const res = await userGroupService.recountSegment({
        id: data.id,
        force: data.force
      });
      const _tableParams = { ...this.state.tableParams };
      this.userGroupListQuery(_tableParams, false);
      if (res.header.code === 210) {
        this.timer = setTimeout(async () => {
          data.force = false;
          await this.doRequest(data);
        }, 3000);
      }
    } catch (error) {
      console.error(error);
    }
  };

  /** 表格操作包含排序/翻页/每页显示条数 */
  handleTableChange = (page, filter, sorter) => {
    log.debug('goPage', page, filter, sorter);
    localStorage.setItem('shareUserGroupPage', page.current || 1);
    const { tableParams } = this.state;
    const { current, pageSize } = page;
    const direction = sorter.order ? sorter.order.substr(0, sorter.order.length - 3) : tableParams.sorts[0].direction;
    const propertyName =
      sorter.field && sorter.order
        ? Array.isArray(sorter.field)
          ? sorter.field.join('.')
          : sorter.field
        : tableParams.sorts[0].propertyName;

    if (sorter.order) {
      this.setState({
        sorterInfo: { columnKey: sorter.field, order: sorter.order }
      });
    } else {
      this.setState({ sorterInfo: { columnKey: null, order: null } });
    }

    this.setState(
      {
        tableParams: {
          ...tableParams,
          sorts: [{ direction, propertyName }],
          page: current,
          size: pageSize
        }
      },
      () => {
        this.userGroupListQuery(this.state.tableParams);
      }
    );
  };

  // 隐藏modal
  hideModal() {
    this.setState({ visible: false });
  }

  // 选择分群方式
  selectSegmentType(value) {
    if (value.type !== 'AI') {
      // `/aimarketer/home/<USER>/userGroup/create?type=${value.type}`
      this.props.history.push({
        pathname: '/aimarketer/home/<USER>/userGroup/create',
        state: { type: value.type }
      });
    } else {
      this.props.history.push('/aimarketer/home/<USER>/model/aimarketer/iframe/aicenter');
    }
  }

  getRecentRange = (key) => {
    let startTime;
    const endTime = dayjs().endOf('day').valueOf();
    if (key === 'WEEK') {
      startTime = dayjs().subtract(1, 'week').startOf('day').valueOf();
    } else if (key === 'MON') {
      startTime = dayjs().subtract(1, 'months').startOf('day').valueOf();
    } else if (key === 'SIXMON') {
      startTime = dayjs().subtract(6, 'months').startOf('day').valueOf();
    } else {
      startTime = dayjs().subtract(3, 'months').startOf('day').valueOf();
    }
    return `${startTime},${endTime}`;
  };

  queryData1 = (data) => {
    let _filters = _.map(data, (item) => {
      if (item.propertyName === 'customerCount') {
        const valueArr = item.value.split(',');
        if (valueArr[0] === 'DATE_BETWEEN') {
          return [
            {
              connector: 'AND',
              propertyName: item.propertyName,
              operator: 'GTE',
              value: valueArr[1]
            },
            {
              connector: 'AND',
              propertyName: item.propertyName,
              operator: 'LTE',
              value: valueArr[2]
            }
          ];
        }
        return {
          connector: 'AND',
          propertyName: item.propertyName,
          operator: valueArr[0] === '' ? 'EQ' : valueArr[0],
          value: valueArr.splice(1).toString()
        };
      } else if (item.propertyName === 'validDateType') {
        const valueArr = item.value.split(',').filter((item) => item && item !== '');

        if (valueArr[0] === 'DATE') {
          if (valueArr[2]) {
            valueArr[2] = Number(valueArr[2]) + 1;
          }

          return [
            {
              connector: 'AND',
              propertyName: item.propertyName,
              operator: 'EQ',
              value: 'TEMPORARY'
            },
            {
              connector: 'AND',
              propertyName: 'validEndTime',
              operator: 'DATE_BETWEEN',
              value: valueArr.length === 3 ? valueArr.slice(1, 3).join(',') : ''
            }
          ];
        }
      }
      return {
        connector: 'AND',
        propertyName: item.propertyName,
        operator: item.operator,
        value:
          item.operator === 'DATE_BETWEEN' && _.isArray(item.value)
            ? item.value.map((item) => dayjs(item).valueOf()).join(',')
            : item.value
      };
    });

    // _.forEach(_filters, item => {
    //   if (_.isArray(item)) {
    //     item.forEach(item2 => {
    //       _filters.push(item2);
    //     });
    //     item.splice()
    //   }
    // });

    _filters.forEach((item) => {
      if (_.isArray(item)) {
        item.forEach((item2) => {
          _filters.push(item2);
        });
      }
    });

    _filters = _filters.filter((item) => !_.isArray(item));

    _.forEach(_filters, (item) => {
      if (item.propertyName === 'customerCount') {
        const valueArr = item.value.split(',');
        if (item.operator === 'DATE_BETWEEN') {
          if (!valueArr[0] || !valueArr[1] || valueArr[0] === '' || valueArr[1] === '') {
            throw new Error(t('portraitCenter-userGroup-customerCount-error'));
          }
        }
      }
    });

    localStorage.setItem('shareUserGroupFilter', JSON.stringify(_filters));
    this.queryData(_filters);
  };

  handleExportSegmentCancel = () => {
    this.setState({
      currentDownloadSegment: null,
      showDownloadModal: false
    });
  };

  handleExportSegmentOk = async (data, labelList, dateType) => {
    const hide = message.loading(t('portraitCenter-userGroup-export-loading'), 0);
    const doRequest = async () => {
      const res = await userGroupService.download({
        id: this.state.currentDownloadSegment.id,
        schemaList: data,
        labelList,
        dateType
      });
      await userGroupService.saveUserOperationRecord({
        targetId: this.state.currentDownloadSegment.id,
        id: this.state.currentDownloadSegment?.recentUserOperationRecord?.id,
        targetType: 'SEGMENT',
        type: 'RECENT',
        createUserId: this.state.userId,
        updateUserId: this.state.userId,
        createTime: dayjs().valueOf(),
        updateTime: dayjs().valueOf()
      });

      this.userGroupListQuery(this.state.tableParams);

      if (res.header.code === 0) {
        if (!_.isEmpty(res.body.path)) {
          window.location.href = transformUrl(res.body.path);
          this.setState({
            currentDownloadSegment: null,
            showDownloadModal: false
          });
        } else {
          message.error(res.body.errorMsg, 2);
        }
      } else if (res.header.code === 210) {
        this.timer = setTimeout(async () => {
          await doRequest();
        }, 3000);
        if (this.state.showDownloadModal) {
          this.setState({
            showDownloadModal: false
          });
        }
      }
    };

    doRequest();
    hide();
  };

  render() {
    const { loading, totalCount, data, tableParams, visible, elements, sorterInfo } = this.state;
    // const { idSort, countSort, recentTimeSort, statusSort, createTimeSort, updateTimeSort } = sorterState;
    const { page, size } = tableParams;
    const { toggle, isFold } = this.props;

    /** 表头数据 */
    const columns = [
      {
        title: 'ID',
        dataIndex: 'id',
        sorter: true,
        sortOrder: sorterInfo.columnKey === 'id' ? sorterInfo.order : null,
        width: 80,
        fixed: 'left'
      },
      {
        title: t('portraitCenter-userGroup-elements-name'),
        dataIndex: 'name',
        className: 'name',
        render: (text, val) => {
          return (
            <div>
              <img src={val.src} style={{ width: 28, height: 28, marginRight: 5 }} alt="png" />
              {val.status === 'DRAFT' || val.type === 'EX_SUBSCRIBE' ? (
                <span>{text}</span>
              ) : (
                <Link to={`/aimarketer/home/<USER>/userGroup/shareDetail?id=${val.id}`}>
                  <span>{text}</span>
                </Link>
              )}
            </div>
          );
        },
        width: 180,
        fixed: 'left'
      },
      {
        title: t('portraitCenter-tuDeaBiSWJtk'),
        width: 150,
        dataIndex: 'shareName',
        render: (text, render) => (
          <Tooltip
            title={
              <div>
                <div>
                  {t('portraitCenter-tuDeaBiSWJtk')}：{text}
                </div>
                <div>
                  {t('portraitCenter-Ld8pB09MdR02')}：{getDeptPath(render.deptId)}
                </div>
              </div>
            }
          >
            <span>{text}</span>
          </Tooltip>
        )
      },
      {
        title: t('portraitCenter-userGroup-elements-customerCount'),
        dataIndex: 'customerCount',
        render: (text, record) => (record.calcStatus === 'CALCING' ? '--' : text),
        sorter: true,
        sortOrder: sorterInfo.columnKey === 'customerCount' ? sorterInfo.order : null,
        width: 100
      },
      {
        title: t('portraitCenter-userGroup-elements-lastCalcTime'),
        dataIndex: 'lastCalcTime',
        render: (text, record) =>
          record.calcStatus === 'CALCING' ? '--' : text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '--',
        sorter: true,
        sortOrder: sorterInfo.columnKey === 'lastCalcTime' ? sorterInfo.order : null,
        width: 180
      },
      {
        title: t('portraitCenter-5SVPqbDgDmWS'),
        dataIndex: 'validBeginTime',
        width: 360,
        render: (text, record) => {
          const timeText =
            record.validDateType === 'TEMPORARY'
              ? `${dayjs(record.validBeginTime).format(
                  'YYYY-MM-DD HH:mm'
                )} ~ ${dayjs(record.validEndTime).format('YYYY-MM-DD HH:mm')}`
              : t('portraitCenter-6r5qA1IVkzpj');
          return <div>{timeText}</div>;
        }
      },
      {
        title: t('portraitCenter-userGroup-elements-status'),
        dataIndex: 'status',
        render: (text) => {
          return (
            <div className="status">
              <span className={`circle ${text === 'NORMAL' ? 'green' : text === 'DISABLE' ? 'red' : null}`} />
              {_.filter(statusList, (v) => v.value === text)[0]?.name}
            </div>
          );
        },
        sortOrder: sorterInfo.columnKey === 'status' ? sorterInfo.order : null,
        sorter: true,
        width: 150
      },
      {
        title: t('portraitCenter-aioVwHX9LFNt'),
        dataIndex: ['scenario', 'name'],
        sorter: false,
        width: 120
      },
      {
        title: t('portraitCenter-userGroup-elements-calcStatus'),
        dataIndex: 'calcStatus',
        render: (text) => {
          return (
            <Badge
              status={text === 'CALCING' ? null : text === 'FAIL' ? 'error' : 'success'}
              color={text === 'CALCING' && '#1677FF'}
              text={_.filter(calcStatusList, (v) => v.value === text)[0]?.name}
            />
          );
        },
        width: 120
      },
      {
        title: t('portraitCenter-bUfiTbxW8xtV'),
        dataIndex: 'processStatus',
        render: (text, record) => (record.approvalStatus ? proessStatusList[record.approvalStatus] : '-'),
        width: 150
      },
      {
        title: t('portraitCenter-0r4DEwNZj5rr'),
        dataIndex: 'type',
        width: 120,
        render: (text) => {
          return _.filter(typeList, (v) => v.value === text)[0]?.name;
        }
      },
      {
        title: t('portraitCenter-MQskFIsGfy8L'),
        dataIndex: 'whetherTest',
        width: 100,
        render: (text) => {
          return text ? t('portraitCenter-68Q8PSrKjoVt') : t('portraitCenter-Q5lNBs6t6sqA');
        }
      },
      {
        title: t('portraitCenter-kYlkekoGHCip'),
        dataIndex: 'calcRule',
        className: 'maxWidth',
        width: 150,
        render: (text) => (text ? _.filter(ruleList, (v) => v.value === text)[0].name : '-')
      },
      {
        title: t('portraitCenter-xo1Bf97Jl1oL'),
        width: 150,
        dataIndex: 'createUserName'
      },
      {
        title: t('portraitCenter-1H7oDz8Gakpt'),
        dataIndex: 'createTime',
        width: 180,
        render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
        sortOrder: sorterInfo.columnKey === 'createTime' ? sorterInfo.order : null,
        sorter: true
      },
      {
        title: t('portraitCenter-TScEoMrVPTzh'),
        width: 150,
        dataIndex: 'updateUserName'
      },
      {
        title: t('portraitCenter-w9OOXQ6C19yr'),
        dataIndex: 'updateTime',
        width: 180,
        render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
        sorter: true,
        sortOrder: sorterInfo.columnKey === 'updateTime' ? sorterInfo.order : null
        // sorter: (a, b) => a.updateTime - b.updateTime,
        // defaultSortOrder: 'descend'
      },
      {
        title: t('portraitCenter-tt5pDiXHbhnn'),
        dataIndex: 'set',
        className: 'td-set',
        fixed: 'right',
        width: 180,
        render: (text, val) => <span>{domSet(text, val)}</span>
      }
    ];

    /** 操作按钮 */
    const domSet = (text, val) => {
      const disabled =
        val.type === 'EX_SUBSCRIBE' ||
        (this.state.proessStatus === 'ENABLE' && val.approvalStatus && val.approvalStatus === 'RUNNING') ||
        (val.status === 'DISABLE' && val.validEndTime > dayjs().valueOf());

      return (
        <>
          {val.shareRoleAuthVos.find((item) => item.code === 'aim_segment_edit') && (
            <CheckAuth code="aim_segment_edit">
              <a disabled={disabled} onClick={disabled ? null : () => editGroup(val)}>
                {t('portraitCenter-vgJQmWI4z9pe')}
              </a>
            </CheckAuth>
          )}
          {val.shareRoleAuthVos.find((item) => item.code === 'aim_segment_edit') && (
            <Divider
              type="vertical"
              style={{
                display: !isCheckAuth('aim_segment_edit') && 'none'
              }}
            />
          )}

          {val.type === 'EX_SUBSCRIBE' ? (
            <a disabled>{t('portraitCenter-7dAvuFuX7ewX')}</a>
          ) : (
            <Dropdown
              disabled={val.status === 'DRAFT'}
              menu={{
                items: _.map(text, (v, i) => {
                  if (
                    (v.type === 'detail' && val.status === 'DRAFT') ||
                    (v.type === 'download' && val.calcStatus !== 'SUC') ||
                    (v.type === 'download' && val.status === 'NOTRUN') ||
                    (v.type === 'status' && val.status === 'DRAFT')
                  ) {
                    return null;
                  }

                  return {
                    label: (
                      <a onClick={() => this.handel(v.type, val)}>
                        {v.type === 'status'
                          ? val.status === 'DISABLE'
                            ? t('portraitCenter-uW91wRsZlrGd')
                            : t('portraitCenter-QhWA3hrz1eu8')
                          : v.name}
                      </a>
                    ),
                    key: i,
                    disabled:
                      v.type === 'status' &&
                      val.status !== 'DISABLE' &&
                      this.state.proessStatus === 'ENABLE' &&
                      val.approvalStatus &&
                      (val.approvalStatus === 'RUNNING' || val.approvalStatus === 'DRAFT')
                  };
                })
              }}
            >
              <a disabled>{t('portraitCenter-7dAvuFuX7ewX')}</a>
            </Dropdown>
          )}
        </>
      );
    };

    const editGroup = async (val) => {
      if (
        val.type === 'AI' ||
        val.type === 'SHORT_LINK' ||
        val.type === 'FUNNEL_CHART' ||
        val.type === 'RETENTION_CHART' ||
        val.type === 'CAMPAIGN_LOG' ||
        val.type === 'EX_SUBSCRIBE'
      ) {
        this.setState({
          editVisible: true,
          editValue: val
        });

        await userGroupService.saveUserOperationRecord({
          targetId: val.id,
          id: val?.recentUserOperationRecord?.id,
          targetType: 'SEGMENT',
          type: 'RECENT',
          createUserId: this.state.userId,
          updateUserId: this.state.userId,
          createTime: dayjs().valueOf(),
          updateTime: dayjs().valueOf()
        });
      } else {
        // 路由跳转
        this.props.history.push({
          pathname: '/aimarketer/home/<USER>/userGroup/create',
          state: { id: val.id, type: val.type }
        });
      }
    };

    /** 处理列表数据 */
    const tableData = _.forEach(data, (v) => {
      v.key = v.id;
      v.src = usergroupicon;
      v.set = [];
      if (isCheckAuth('aim_segment_view') && !(v.status === 'DRAFT' || v.type === 'EX_SUBSCRIBE')) {
        v.set.push({
          name: t('portraitCenter-KC4NBKy7Q71r'),
          type: 'detail'
        });
      }

      if (v.shareRoleAuthVos.find((item) => item.code === 'aim_segment_delete') && isCheckAuth('aim_segment_delete')) {
        v.set.push({
          name: t('portraitCenter-jq8h7i8PHF6v'),
          type: 'delete'
        });
      }
      if (
        v.shareRoleAuthVos.find((item) => item.code === 'aim_segment_enable') &&
        isCheckAuth('aim_segment_enable') &&
        !(v.status === 'DRAFT' || v.type === 'EX_SUBSCRIBE')
      ) {
        v.set.push({
          name: t('portraitCenter-QhWA3hrz1eu8'),
          type: 'status'
        });
      }
      if (
        v.shareRoleAuthVos.find((item) => item.code === 'aim_segment_download') &&
        localStorage.getItem('env') !== 'SW' &&
        isCheckAuth('aim_segment_download') &&
        (v.status === 'NORMAL' || v.status === 'DISABLE')
      ) {
        v.set.push({
          name: t('portraitCenter-U40FAnOgTj1F'),
          type: 'download'
        });
      }
      if (
        v.shareRoleAuthVos.find((item) => item.code === 'aim_segment_recalc') &&
        isCheckAuth('aim_segment_recalc') &&
        v.calcStatus !== 'CALCING' &&
        v.status !== 'DISABLE' &&
        v.status !== 'DRAFT'
      ) {
        v.set.push({
          name: t('portraitCenter-Hl3iv5yzCGjo'),
          type: 'reCalc'
        });
      }

      if (v.status !== 'DRAFT') {
        v.set.push({
          name: t('portraitCenter-dc01FBnxJZBZ'),
          type: 'copyUrl'
        });
      }
    });

    const editAiGroupOnOk = async (formValue) => {
      try {
        const value = await userGroupService.get(this.state.editValue.id);
        const data = {
          ...value,
          ...formValue
        };
        await userGroupService.save(data);
        await userGroupService.saveUserOperationRecord({
          targetId: data.id,
          id: this.state.editValue?.recentUserOperationRecord?.id,
          targetType: 'SEGMENT',
          type: 'RECENT',
          createUserId: this.state.userId,
          updateUserId: this.state.userId,
          createTime: dayjs().valueOf(),
          updateTime: dayjs().valueOf()
        });
        message.success(t('portraitCenter-jJ00Det1IicL'));
        this.setState({
          editVisible: false,
          editValue: null,
          editLoading: false
        });
        this.userGroupListQuery(this.state.tableParams);
      } catch (err) {
        this.setState({
          editLoading: false
        });
      }
    };

    return (
      <div className="userGroup">
        <div className="mt-[16px]">
          <Breadcrumb>
            <Breadcrumb.Item>
              <Link to="/aimarketer/home/<USER>/userGroup">{t('portraitCenter-FUuxdmBylNxo')}</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>{t('portraitCenter-ytWkgmsHnFgS')}</Breadcrumb.Item>
          </Breadcrumb>
        </div>

        <header className="!pt-[8px] !mb-[16px]">
          <h1>{t('portraitCenter-ytWkgmsHnFgS')}</h1>
          <div className="btnGroup">
            <Button onClick={() => toggle(isFold)}>
              <FilterOutlined />
              {t('portraitCenter-yHRQQUrfatFO')}
            </Button>
          </div>
        </header>
        <QueryForList
          show={!!isFold}
          elements={elements}
          onQuery={this.queryData1}
          defaultFormData={this.state.defaultFormData}
        />

        <div className="table1 rounded-[6px]">
          <Table
            // columns={this.state.proessStatus && this.state.proessStatus === 'ENABLE' ? processColumns : columns}
            columns={columns.filter((item) => {
              if (
                item.dataIndex === 'processStatus' &&
                this.state.proessStatus &&
                this.state.proessStatus === 'DISABLE'
              )
                return false;
              return true;
            })}
            dataSource={tableData}
            bordered={false}
            loading={loading}
            scroll={{ x: 1280 }}
            onChange={this.handleTableChange}
            pagination={{
              current: page,
              total: totalCount,
              defaultPageSize: size,
              showSizeChanger: true,
              showLessItems: true,
              pageSizeOptions: ['10', '20', '50'],
              showTotal: (e) => t('portraitCenter-NOevIpVzi7Ut', { count: e }),
              showQuickJumper: true
            }}
          />
        </div>
        {this.state.showDownloadModal && (
          <DownloadSegment
            handleExportSegmentOk={this.handleExportSegmentOk}
            handleExportSegmentCancel={this.handleExportSegmentCancel}
            segmentInfo={this.state.currentDownloadSegment}
            downloadSegmentMaxCount={this.state.downloadSegmentMaxCount}
          />
        )}
        <Modal
          open={visible}
          onCancel={this.hideModal}
          footer={null}
          width={800}
          title={t('portraitCenter-nPe74KJz11BX')}
          getContainer={document.getElementsByClassName('userGroup')[0]}
        >
          <div className="userGroupCreateModal">
            {/* <p className="title">选择创建分群方式</p> */}
            <div className="container">
              {_.map(modalData, (item, index) => {
                if (localStorage.getItem('env') === 'NS') {
                  if (item.type !== 'AI') {
                    return (
                      <div key={index} className="item" onClick={() => this.selectSegmentType(item)}>
                        {/* <img src={item.src} alt="png" /> */}
                        <div className="leftIcon">{item.src}</div>
                        <div className="right">
                          <h2>{item.title}</h2>
                          <div className="desc">{item.des}</div>
                        </div>
                      </div>
                    );
                  }
                } else {
                  return (
                    <div key={index} className="item" onClick={() => this.selectSegmentType(item)}>
                      {/* <img src={item.src} alt="png" /> */}
                      <div className="leftIcon">{item.src}</div>
                      <div className="right">
                        <h2>{item.title}</h2>
                        <div className="desc">{item.des}</div>
                      </div>
                    </div>
                  );
                }
              })}
            </div>
          </div>
        </Modal>
        {this.state.editVisible && (
          <EditAiGroup
            state={this.state}
            setLoading={() => this.setState({ editLoading: true })}
            onCancel={() => this.setState({ editVisible: false, editValue: null })}
            editAiGroupOnOk={editAiGroupOnOk}
          />
        )}
      </div>
    );
  }
}

export default withRouter(withStore(connect(stateToProps, mapDispatchToProps)(ShareGroupList)));
