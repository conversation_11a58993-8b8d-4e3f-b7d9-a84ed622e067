.filterComponents {
  display: flex;

  &>:first-child {
    &>.ant-select-single {

      .ant-select-selector {
        background-color: transparent !important;
        border-color: transparent !important;
        box-shadow: none !important;
      }
    }

    .ant-select-selection-placeholder {
      color: rgba(0, 0, 0, 0.65);
    }
  }

  .ant-select-multiple {
    margin-left: 20px;

    .ant-select-selector {
      border-radius: 6px !important;
    }
  }

  .campaignsFilterPop {
    left: 778px;

    .ant-select-multiple {
      margin-left: 0px;
    }

    .ant-popover-arrow {
      display: none;
    }

    .ant-popover-inner,
    .ant-input-affix-wrapper,
    .ant-select-selector,
    .ant-picker {
      border-radius: 6px !important;
    }

    .ant-popover-inner-content {
      width: 352px;
      padding: 24px 24px 0 24px;
    }

    .moreContent {
      .query-for-list {
        margin-bottom: 0;
        border: none;
        padding: 24px 12px 0 12px;

        .ant-btn {
          border-radius: 6px;
        }

        .right {
          padding-top: 12px;
          border-top: 1px solid rgba(0, 0, 0, 0.06);
        }

        .ant-row {
          margin-left: 4px;
          margin-right: -12px;

          .ant-legacy-form-item-control {
            width: 320px;
          }

          .ant-legacy-form-item {
            flex-flow: column;
          }
        }
      }
    }

  }

  .myFormFooter {
    height: 56px;
    position: relative;

    .ant-divider {
      width: 352px;
      margin: 0;
      margin-left: -24px;
      position: absolute;
    }

    .buttons {
      height: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;


      &>:first-child {
        margin-right: 8px;
      }

      button {
        border-radius: 6px;
      }
    }
  }
}

.moreFilter {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.65);
  margin-left: 20px;

  .anticon-down {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.25);
    margin-left: 8px;
  }
}