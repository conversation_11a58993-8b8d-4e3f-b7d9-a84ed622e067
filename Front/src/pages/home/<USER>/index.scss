.approvalDetail {
  .ant-timeline {
    .ant-tag {
      border-radius: 4px;
    }
  }

  .flowEditor {
    height: 100%;

    #containers {
      height: 100%;
      display: flex;
    }

    .x6-widget-stencil {
      background-color: #fff;

      .x6-widget-stencil-content {
        top: 16px !important
      }
    }

    .x6-widget-stencil::after {
      display: none;
    }

    .x6-widget-stencil-group {
      border-bottom: 1px solid #F0F0F0;
    }

    .x6-widget-stencil-group:nth-last-of-type(1) {
      border-bottom: none;
    }

    .x6-widget-stencil-title {
      display: none;
      background-color: #fff;
    }

    .x6-widget-stencil-group-title {
      padding: 0 5px 0 24px !important;
      margin-top: 16px !important;
      background-color: #fff !important;
      font-size: 14px !important;
      font-weight: 600 !important;
      color: rgba(0, 0, 0, 0.85) !important;
    }

    .x6-widget-stencil-group:nth-of-type(1) {
      .x6-widget-stencil-group-title {
        margin-top: 0 !important;
      }
    }

    .x6-widget-transform {
      margin: -1px 0 0 -1px;
      padding: 0px;
      border: 1px solid #239edd;
    }

    .x6-widget-transform>div {
      border: 1px solid #239edd;
    }

    .x6-widget-transform>div:hover {
      background-color: #3dafe4;
    }

    .x6-widget-transform-active-handle {
      background-color: #3dafe4;
    }

    .x6-widget-transform-resize {
      border-radius: 0;
    }

    .x6-widget-selection-inner {
      border: 1px solid #239edd;
    }

    .x6-widget-selection-box {
      opacity: 0;
    }

    .x6-edge:hover path:nth-child(2) {
      stroke: #1890ff;
      stroke-width: 1.5px;
    }

    .x6-edge-selected path:nth-child(2) {
      stroke: #1890ff;
      stroke-width: 1.5px !important;
    }

    #graph-container {
      flex: 1;
      height: 100%;

      .parentTitleWrap {
        flex-direction: row !important;
        align-items: center;
        gap: 4px;
        padding: 8px 0 8px 12px;
      }

      .titleWrap {
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .contentNode {
        box-shadow: 0px 5px 12px 4px #00000017;
        gap: 8px;
        justify-content: center;
        align-items: center;

        .editIcon {
          display: none;
        }
      }

      .contentNode:hover {
        .editIcon {
          cursor: pointer;
          display: block;
          position: absolute;
          top: 8px;
          right: 12px;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.45);
        }
      }

      .contentParentNode {
        box-shadow: 0px 5px 12px 4px #00000017;
        justify-content: flex-start !important;
        align-items: flex-start !important;
      }
    }

    .scaleToolsWrap {
      position: absolute;
      right: 150px;
      bottom: 24px;
      display: flex;
      padding: 5px 8px;
      border-radius: 8px;
      background: #fff;
      box-shadow: 0px 12px 48px 16px rgba(0, 0, 0, 0.03), 0px 9px 28px 0px rgba(0, 0, 0, 0.05), 0px 6px 16px -8px rgba(0, 0, 0, 0.08);

      .ant-select-selector {
        padding: 0 8px;
        border-radius: 6px;

        .ant-select-selection-item {
          padding-right: 0;
        }
      }

      .ant-select-selector:hover {
        background: #F0F0F0 !important;
      }

      .scaleSm,
      .scaleBg {
        cursor: pointer;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: .5s all;
      }

      .scaleSm:hover {
        background: #F5F5F5;
      }

      .scaleBg:hover {
        background: #F5F5F5;
      }

      .scaleSm:active {
        background: #F0F0F0;
      }

      .scaleBg:active {
        background: #F0F0F0;
      }

      .scaleDisable {
        color: rgba(0, 0, 0, .25);
        cursor: not-allowed;
        pointer-events: none;
      }

      .gapLine {
        width: 1px;
        margin: 4px 2px;
        border-right: 1px solid #F0F0F0;
      }

      .resizeWrap {
        width: 32px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: .5s all;
        border-radius: 6px;
      }

      .resizeWrap:hover {
        background: #F5F5F5;
      }
    }

    .help {
      position: absolute;
      right: 24px;
      bottom: 24px;
      padding: 2px;

      .ant-btn {
        width: 110px;
        height: 40px;
        box-shadow: 0px 12px 48px 16px rgba(0, 0, 0, 0.03), 0px 9px 28px 0px rgba(0, 0, 0, 0.05), 0px 6px 16px -8px rgba(0, 0, 0, 0.08) !important;
        border-radius: 8px;
      }
    }
  }
}

.approveDetailWrap {
  .ant-descriptions-item-label {
    color: rgba(0, 0, 0, .65);
  }

  .ant-btn {
    border-radius: 6px;
  }

  .ant-tabs {
    .ant-tabs-nav {
      margin: 0 !important;
    }
  }
}

.approveModalWrap {
  .ant-modal-content {
    border-radius: 6px !important;
    width: 560px;

    .ant-modal-header {
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
    }

    .ant-select-selector,
    .ant-input,
    .ant-btn {
      border-radius: 6px;
    }
  }
}

.approveDrawer {
  .content {
    .ant-alert-info {
      border-radius: 6px;
      margin-bottom: 24px;
      background: #E6F4FF !important;
      border-color: #91CAFF !important;

      .ant-alert-icon {
        color: #1677ff !important;
      }
    }

    .approverContent {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: flex-start;

      .ConnectorPanel {
        margin-right: 24px;
        // border: 1px solid #ccc;
        width: 40px;
        min-width: 40px;
        position: relative;

        .VLine {
          position: absolute;
          top: 16px;
          bottom: 15px;
          left: 65%;
          width: 50%;
          border-left: 1px solid #ccc;
          z-index: 100;
        }

        .TopLine {
          position: absolute;
          top: 16px;
          left: 65%;
          height: 0;
          width: 32px;
          border-top: 1px solid #ccc;
          z-index: 100;
        }

        .BottomLine {
          position: absolute;
          bottom: 15px;
          left: 68%;
          height: 0;
          width: 32px;
          border-top: 1px solid #ccc;
          z-index: 100;
        }

        .Connector {
          position: absolute;
          left: 11px;
          top: 50%;
          height: 30px;
          line-height: 30px;
          transform: translateY(-15px);
          vertical-align: middle;
          z-index: 200;

          .ant-switch {
            background-color: #ccc;
            opacity: 1;
          }

          // .ant-switch-checked {
          //   background-color: $primary_color;
          //   opacity: 1;
          // }

          .ant-switch-disabled {
            opacity: 1;
          }
        }
      }

      .FilterList {
        display: flex;
        flex-direction: column;
        flex: auto;

        .ant-form-item {
          margin-bottom: 6px;
        }

        .ant-form-item:last-of-type {
          margin-bottom: 0;
        }

        .ant-space-item:nth-of-type(1) {
          width: 100%;
        }

        .ant-space-item:nth-of-type(2) {
          position: relative;
          cursor: pointer;
          top: 14px;
        }

        .ant-select-selector {
          border-radius: 6px !important;
        }
      }

      .ruleForm {
        padding: 16px;
        margin-top: 16px;
        background: #FAFAFA;
      }
    }
  }

  footer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    padding: 12px 24px;
    border-top: 1px solid #F0F0F0;
    background-color: #fff;
    z-index: 1000;

    .btnGroup {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .ant-btn {
        border-radius: 6px !important;
      }
    }
  }
}

.approvalRulesDrawer {

  .ant-alert-info {
    border-radius: 6px;
    margin-bottom: 24px;
    background: #E6F4FF !important;
    border-color: #91CAFF !important;

    .ant-alert-icon {
      color: #1677ff !important;
    }
  }

  .ant-select-selector,
  .ant-input,
  .ant-btn,
  .ant-input-number,
  .ant-input-affix-wrapper {
    border-radius: 6px !important;
  }

  .ant-modal-body {
    padding: 0;
    max-height: calc(100vh - 300px);
    overflow: auto;
  }

  .ant-modal-content {
    border-radius: 6px !important;
  }

  .ant-modal-header {
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;
  }

  .ant-form {
    .ant-form-item {
      margin-bottom: 16px;
    }

    .ant-form-item:nth-last-of-type() {
      margin-bottom: 0;
    }
  }

  .rulesContent {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    width: 100%;

    .switchTitle {
      display: flex;
      align-items: center;
      margin-right: 8px;
    }

    .ConnectorPanel {
      margin-right: 24px;
      left: -18px;
      // border: 1px solid #ccc;
      position: relative;

      .VLine {
        position: absolute;
        top: 16px;
        bottom: 15px;
        left: 78%;
        width: 30%;
        border-left: 1px solid #ccc;
        z-index: 100;
      }

      .TopLine {
        position: absolute;
        top: 16px;
        left: 77%;
        height: 0;
        width: 32px;
        border-top: 1px solid #ccc;
        z-index: 100;
      }

      .BottomLine {
        position: absolute;
        bottom: 15px;
        left: 79%;
        height: 0;
        width: 32px;
        border-top: 1px solid #ccc;
        z-index: 100;
      }

      .Connector {
        position: absolute;
        left: -10px;
        top: 50%;
        line-height: 30px;
        transform: translateY(-15px);
        vertical-align: middle;
        z-index: 200;

        .ant-switch {
          background: #FF6800 !important;
        }

        .ant-switch-disabled {
          opacity: 1;
        }
      }
    }
  }

  .ant-radio-group,
  .ant-space,
  .ant-radio-wrapper {
    width: 100% !important;
  }

  .ant-form-item-explain-error {
    display: flex;
    gap: 56px;
  }

  footer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    padding: 12px 24px;
    border-top: 1px solid #F0F0F0;
    background-color: #fff;
    z-index: 1000;

    .btnGroup {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .ant-btn {
        border-radius: 6px !important;
      }
    }
  }
}

.helpDrawer {
  .ant-drawer-body {
    padding: 16px 24px;
  }
}