.todoList {
  p.rule {
    margin: 0 -24px 24px -24px;
    padding: 0 24px 0 24px;
    background-color: #fff;

    span {
      display: inline-block;
      padding: 0 10px;
      height: 36px;
      line-height: 36px;
      cursor: pointer;
      // border-bottom: 2px solid $primary_color;
    }

    .selectSpan {
      color: $primary_color;
      display: inline-block;
      cursor: pointer;
      padding: 0 10px;
      height: 36px;
      line-height: 36px;
      border-bottom: 2px solid $primary_color;
    }
  }

  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    margin: 0 -24px 0;
    padding: 14px 24px;
    box-shadow: 0 1px 2px 0 rgba(220, 226, 230, 0.8), 0 1px 8px 0 rgba(220, 226, 230, 0.59);

    h1 {
      font-size: 24px;
      margin: 0;
    }

    .btnGroup {
      button {
        &:first-child {
          margin-right: 16px;
        }
      }
    }
  }

  .ant-tabs {
    margin: 0 -24px 0 -24px;

    .ant-tabs-nav {
      background-color: #fff;
      padding: 0 24px 0 24px;
    }

    .ant-tabs-content-holder {
      margin: 0 24px 0 24px;
    }
  }

  .rightSide {
    .showTypeWrap {
      margin-right: 6px;
      width: 160px;

      .ant-select-selector {
        border-radius: 6px
      }
    }
  }

  .search {
    margin-bottom: 24px;

    .ant-input-affix-wrapper,
    .ant-picker-range,
    .ant-select-selector {
      border-radius: 6px;
    }

    .ant-advanced-search-form,
    .ant-btn {
      border-radius: 6px;
    }
  }

  .content {
    padding: 24px;
    background-color: #fff;
    border-radius: 6px;

    .optionWrap {
      display: block;
      width: 200px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .cardWrap {
      margin-top: -24px;

      .cardItem {
        .header {
          height: 96px;
          display: flex;

          .titleWrap {
            width: 40%;
            display: flex;
            flex-direction: column;
            align-self: center;

            .title {
              font-weight: 600;
            }

            .count {
              color: rgba(0, 0, 0, 0.45);
            }
          }

          .statusWrap {
            width: 453px;
            display: flex;
            justify-content: space-between;
            align-self: center;

            .statusItem {
              .title {
                margin-bottom: 4px;
              }

              .count {
                color: rgba(0, 0, 0, 0.45)
              }
            }
          }

          .option {
            flex: 1;
            display: flex;
            justify-content: flex-end;
            align-items: center;
          }
        }
      }

      .pagination {
        margin-top: 24px;
        display: flex;
        justify-content: flex-end;
      }

      .ant-tree-treenode {
        border-bottom: 1px solid #F0F0F0;
        flex-direction: row-reverse;
        width: 100%;

        .ant-tree-switcher-noop,
        .ant-tree-indent {
          display: none;
        }

        .ant-tree-switcher {
          align-self: center;
          width: 100px;
        }

        .ant-tree-node-content-wrapper {
          cursor: initial;
          width: 100%;
        }

        .ant-tree-node-content-wrapper:hover {
          background-color: transparent;
        }

        .ant-tree-node-content-wrapper.ant-tree-node-selected {
          background-color: transparent;
        }

      }

      // .ant-tree-treenode-leaf-last {
      //   flex-direction: column;
      // }
    }
  }
}

.processModalWrap .ant-modal-content {
  border-radius: 6px;
  width: 560px;

  .ant-select-selector {
    border-radius: 6px;
  }
}

.processHistoryModalWrap {
  width: 1050px !important;

  .optionWrap {
    display: block;
    width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .ant-modal-content {
    border-radius: 6px;

    .ant-modal-body {
      max-height: 560px;
      overflow-y: auto;
    }

    .ant-select-selector {
      border-radius: 6px;
    }
  }
}

.ant-modal-header {
  border-radius: 6px;

  .ant-modal-title {
    font-weight: 600;
    font-size: 16px;
  }
}

.ant-modal-footer {
  .ant-btn {
    border-radius: 6px;
  }
}

.backWrap {
  .backDesc {
    width: 252px;
  }

  .ant-modal-content {
    border-radius: 6px;

    .ant-modal-confirm-btns {
      .ant-btn {
        border-radius: 6px;
      }
    }
  }
}