.budgetManageWrap {
    header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #fff;
        margin: 0 -24px 24px;
        padding: 14px 24px;
        box-shadow: 0 1px 2px 0 rgba(220, 226, 230, 0.8), 0 1px 8px 0 rgba(220, 226, 230, 0.59);
        h1 {
          font-size: 24px;
          margin: 0;
        }
        .btnGroup {
          button {
            &:first-child {
              margin-right: 16px;
            }
          }
        }
      }

      .tableWrap {
        border-radius: 6px;
        padding: 24px;
        background-color: #fff;
        margin-top: 24px;
      }
      .cardWrap {
        border-radius: 6px;
        height: 98px;
        background: #fff;
        display: flex;
        align-items: center;
        padding: 0 24px;

        .item {
            margin-right: 82px;
        
            .title {
                font-size: 14px;
            }
            .count {
                font-weight: 600;
                font-size: 18px;
            }
        }
      }
}

.drawerWrap {
    .titleWrap {
        display: flex;
        justify-content: space-between
    }

    .flexWrap {
        display: flex;
        .label {
            position: relative;
            left: 372px;
            color: rgba(0, 0, 0, 0.650980392156863);
        }

        .label2 {
            position: relative;
            left: 400px;
            color: rgba(0, 0, 0, 0.650980392156863);
        }
    }
}