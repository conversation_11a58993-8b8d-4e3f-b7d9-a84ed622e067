// #region 初始化图形
const ports = {
  groups: {
    top: {
      position: 'top',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: '#fff',
          style: {
            visibility: 'hidden'
          }
        }
      }
    },
    right: {
      position: 'right',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: '#fff',
          style: {
            visibility: 'hidden'
          }
        }
      }
    },
    bottom: {
      position: 'bottom',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: '#fff',
          style: {
            visibility: 'hidden'
          }
        }
      }
    },
    left: {
      position: 'left',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: '#fff',
          style: {
            visibility: 'hidden'
          }
        }
      }
    }
  },
  items: [
    {
      group: 'top'
    },
    {
      group: 'right'
    },
    {
      group: 'bottom'
    },
    {
      group: 'left'
    }
  ]
};

const operatorList = [
  {
    name: '等于',
    operator: 'EQ'
  },
  {
    name: '不等于',
    operator: 'NE'
  },
  {
    name: '大于',
    operator: 'GT'
  },
  {
    name: '大于等于',
    operator: 'GTE'
  },
  {
    name: '小于',
    operator: 'LT'
  },
  {
    name: '小于等于',
    operator: 'LTE'
  },
  {
    name: '范围',
    operator: 'BETWEEN'
  },
  {
    name: '包含',
    operator: 'IN'
  },
  {
    name: '不包含',
    operator: 'NOT_IN'
  },
  {
    name: '匹配',
    operator: 'LIKE'
  },
  {
    name: '不匹配',
    operator: 'NOT_LIKE'
  },
  {
    name: '开头匹配',
    operator: 'START_WITH'
  },
  {
    name: '开头不匹配',
    operator: 'NOT_START_WITH'
  },
  {
    name: '结尾匹配',
    operator: 'END_WITH'
  },
  {
    name: '结尾不匹配',
    operator: 'NOT_END_WITH'
  }
];

const typeOperator = {
  INT: ['EQ', 'NE', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
  LONG: ['EQ', 'NE', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
  DOUBLE: ['EQ', 'NE', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
  STRING: ['EQ', 'NE', 'IN', 'NOT_IN'],
  NAME_STRING: ['IN', 'NOT_IN'],
  DEFAULT: ['IN', 'NOT_IN']
};

export { operatorList, ports, typeOperator };
