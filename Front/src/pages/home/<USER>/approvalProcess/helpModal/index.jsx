import {
  ArrowRightOutlined,
  AuditOutlined,
  FormOutlined,
  GroupOutlined,
  LogoutOutlined,
  PlayCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import HelpImg1 from 'assets/images/helpImg1.png';
import HelpImg2 from 'assets/images/helpImg2.png';
import HelpImg3 from 'assets/images/helpImg3.png';
import HelpImg4 from 'assets/images/helpImg4.png';
import HelpImg5 from 'assets/images/helpImg5.png';

import { Drawer, Image } from 'antd';
import React from 'react';

const HelpModal = (props) => {
  const { helpOpen, onClose } = props;
  return (
    <Drawer open={helpOpen} onClose={onClose} title="使用帮助" width={880} className="helpDrawer">
      <div className="flex h-full flex-col">
        <div style={{ marginTop: 8, marginBottom: 40 }}>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            一、流程配置组件
          </div>
          <div className="flex flex-col gap-[16px]">
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#1EC78B', borderRadius: '50%', width: 24, height: 24 }}
              >
                <PlayCircleOutlined />
              </div>
              <div>开始：审批流程开始（不能编辑）；</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#1EC78B', borderRadius: '50%', width: 24, height: 24 }}
              >
                <LogoutOutlined />
              </div>
              <div>结束：审批流程结束（不能编辑）；</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#1EC78B', borderRadius: 6, width: 24, height: 24 }}
              >
                <UserOutlined />
              </div>
              <div>提交人：审批任务的提交人（不能编辑），此节点读取具体的审批人及其部门信息等；</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#1890FF', borderRadius: 6, width: 24, height: 24 }}
              >
                <AuditOutlined />
              </div>
              <div>审批人：审批任务的审批人，支持按部门、按角色、按指定审批人审批，支持多选，可设置或签；</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#FAAD14', borderRadius: 6, width: 24, height: 24 }}
              >
                <FormOutlined />
              </div>
              <div>会签：将审批流变为会签形式，从会签节点输出的分支为且的关系，即分支需要全部走完；</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#FAAD14', borderRadius: 6, width: 24, height: 24 }}
              >
                <GroupOutlined />
              </div>
              <div>组合：将【审批人节点】拖入组合框内，形成组合条件，组合框内不需要连线；</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#000000A6', borderRadius: '50%', width: 24, height: 24 }}
              >
                <ArrowRightOutlined />
              </div>
              <div>
                连线：代表审批的流转方向，【双击连线】设置业务规则可控制审批的流转（开始、结束、提交人的输入输出连线不能设置）；
              </div>
            </div>
          </div>
        </div>
        <div style={{ marginTop: 8, marginBottom: 40 }}>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            二、配置一个正确的审批流程
          </div>
          <div style={{ width: '100%' }}>
            <Image src={HelpImg1} alt="" style={{ width: '100%', height: '100%' }} />
          </div>
          <div style={{ marginTop: 16 }}>
            <div>
              审批对象创建后，第一次做流程配置时，会初始化一个简单的审批流（如上图），在简单审批流的基础上进行调整，或删除重新编排。一个正确的审批流程必须符合：
            </div>
            <div>1. 节点不能少：必须包含开始、提交人、审批人、结束节点； </div>
            <div>2. 节点不能多：开始和提交人节点只能是一个，审批人和结束节点可以是多个； </div>
            <div>3. 顺序不能错：开始-提交人-审批人-结束；</div>
            <div>4. 连线要正确：除开始和结束节点只有一条连线，其余节点至少有“进入“和”输出“两条线；</div>
          </div>
        </div>
        <div style={{ marginTop: 8, marginBottom: 40 }}>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            三、配置或签流程
          </div>
          <div style={{ width: '100%' }}>
            <Image src={HelpImg2} alt="" style={{ width: '100%', height: '100%' }} />
          </div>
          <div style={{ marginTop: 16 }}>
            <div>1. 点击审批人节点，在弹出的侧边栏设置审批人；</div>
            <div>2. 支持按部门、按角色、按指定审批人三种类型设置，每种类型支持多选；</div>
            <div>3. 每种类型内部，各类型之间都是”或“的关系，即为“或签”；</div>
          </div>
        </div>
        <div style={{ marginTop: 8, marginBottom: 40 }}>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            四、配置会签流程
          </div>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            方法一：通过会签组件
          </div>
          <div style={{ width: '100%' }}>
            <Image src={HelpImg3} alt="" style={{ width: '100%', height: '100%' }} />
          </div>
          <div style={{ marginTop: 16 }}>
            <div>1. 将会签节点拖入画布中，会签节点后至少跟着两个审批人节点；</div>
            <div>2. 会签节点以组的形式存在，即一前一后代表会签的开始与结束；</div>
            <div>3. 会签的所有分支走完，才会走到下一个审批节点；</div>
          </div>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8, marginTop: 24 }}>
            方法二：通过组合组件
          </div>
          <div style={{ width: '100%' }}>
            <Image src={HelpImg5} alt="" style={{ width: '100%', height: '100%' }} />
          </div>
          <div style={{ marginTop: 16 }}>
            <div>1. 将组合组件拖入画布中，组合节点只有输入、输出两条连线；</div>
            <div>2. 将审批人节点拖入组合框中，并设置审批人；</div>
            <div>3. 组合框中的审批人节点为“且”的关系，即为会签；</div>
          </div>
        </div>
        <div style={{ marginTop: 8 }}>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            五、设置流转的业务规则
          </div>
          <div style={{ width: '100%' }}>
            <Image src={HelpImg4} alt="" style={{ width: '100%', height: '100%' }} />
          </div>
          <div style={{ marginTop: 16, marginBottom: 24 }}>
            <div>业务规则有两种：通用业务规则以及配置的业务规则；</div>
            <div>1. 通用业务规则主要针对提交人，即按提交人的不同走不同的分支；</div>
            <div>2. 通用规则不绑定审批对象，所有审批对象通用；</div>
            <div>3. 通用业务规则有三个参数：提交人-按部门、提交人-按角色、提交人-指定提交人；</div>
            <div>
              4.
              配置的业务规则绑定审批对象，每个审批对象子类都可以设置自己的业务规则，当对审批对象进行流程配置时，仅展示自己的业务规则；
            </div>
            <div>5. 双击连线即可设置业务规则；</div>
          </div>
        </div>
      </div>
    </Drawer>
  );
};

export default HelpModal;
