import { Drawer, Form, Radio, Space, Spin } from 'antd';
import _ from 'lodash';

import Tooltip from 'antd/es/tooltip';
import React, { useEffect, useState } from 'react';
import MyToDoListService from 'service/myToDoListService';

const EdgeDrawer = (props) => {
  const { edgeOpen, onClose, edgeInfo } = props;

  const { parameterListVo, approvalObjData } = edgeInfo;

  const [ruleTypeValue, setRuleTypeValue] = useState('other');
  const [connectorChecked, setConnectorChecked] = useState(false);
  const [paramList, setParamList] = useState([]);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [fieldLength, setFieldLength] = useState(2);
  const [processRuleData, setProcessRuleData] = useState([]);

  const [form] = Form.useForm();

  useEffect(() => {
    const init = async () => {
      if (edgeOpen) {
        setConfirmLoading(true);
        const { businessRule, connector, parameterList } = parameterListVo;

        const { projectId, approvalTypePcode, approvalTypeCode } = approvalObjData;

        const res = await MyToDoListService.getApprovalRuleQuery({
          page: 1,
          size: 9999,
          search: [
            {
              propertyName: 'projectId',
              operator: 'IN',
              value: `${projectId},0`
            },
            {
              propertyName: 'approvalTypePcode',
              operator: 'IN',
              value: `${approvalTypePcode},approval_universal`
            },
            {
              propertyName: 'approvalTypeCode',
              operator: 'IN',
              value: `${approvalTypeCode},approval_universal_department,approval_universal_role,approval_universal_submitter`
            }
          ],
          sorts: [
            {
              direction: 'desc',
              propertyName: 'updateTime'
            }
          ]
        });

        setParamList(res.content.filter((item) => item.state === 'ENABLING_IN_PROGRESS'));

        setConnectorChecked(connector === 'AND');
        setRuleTypeValue(businessRule ? 'rules' : 'other');

        if (businessRule) {
          setFieldLength(parameterList.length);
          const formList = parameterList.map((item) => {
            const approvalTypeCode = res.content.find((item3) => item3.id === item.parameterId)?.approvalTypeCode;
            if (item.parameterNameSymbol === 'EQ' || item.parameterNameSymbol === 'NE') {
              return {
                ...item
              };
            } else {
              return {
                ...item,
                parameterNameValue:
                  approvalTypeCode === 'approval_universal_department' ||
                  approvalTypeCode === 'approval_universal_submitter' ||
                  approvalTypeCode === 'approval_universal_role'
                    ? item.parameterNameValue.split(',').map((paramItem) => Number(paramItem))
                    : item.parameterNameValue.split(',')
              };
            }
          });
          form.setFieldsValue({ parameterList: formList });

          const paramRes = res.content;
          let departList = [];
          const _parameterList = _.cloneDeep(parameterList);

          paramRes.forEach((item) => {
            const paramItem = _parameterList.find((paramItem) => paramItem.parameterId === item.id);
            if (paramItem) {
              if (item.approvalTypeCode === 'approval_universal_department') {
                departList = [...departList, ...paramItem.parameterDeptInfo];
              }
            }
          });

          setProcessRuleData(formList);
        } else {
          form.setFieldsValue({
            parameterList: [
              {
                parameterId: undefined,
                parameterNameSymbol: undefined,
                parameterNameValue: undefined
              },
              {
                parameterId: undefined,
                parameterNameSymbol: undefined,
                parameterNameValue: undefined
              }
            ]
          });

          setProcessRuleData([]);
        }

        setConfirmLoading(false);
      }
    };
    !_.isEmpty(edgeInfo) && !_.isEmpty(edgeInfo.parameterListVo) && init();
  }, [edgeOpen, edgeInfo]);

  const onRuleTypeChange = (e) => {
    form.setFieldsValue({
      parameterList: [
        {
          parameterId: undefined,
          parameterNameSymbol: undefined,
          parameterNameValue: undefined
        },
        {
          parameterId: undefined,
          parameterNameSymbol: undefined,
          parameterNameValue: undefined
        }
      ]
    });

    setConnectorChecked(false);

    setRuleTypeValue(e.target.value);
  };

  const approvalRuleValueRender = (valueItem) => {
    const parameterCode = paramList.find((item) => item.id === valueItem.parameterId)?.approvalTypeCode;

    switch (parameterCode) {
      case 'approval_universal_department':
        return valueItem.parameterDeptInfo.map((item) => item.name).join('、');
      case 'approval_universal_role':
        return valueItem.rolesInfo.map((item) => item.name).join('、');
      case 'approval_universal_submitter':
        return valueItem.userListInfo.map((item) => item.name).join('、');
      default:
        return _.isArray(valueItem.parameterNameValue)
          ? valueItem.parameterNameValue.join('、')
          : valueItem.parameterNameValue;
    }
  };

  const opreatorEnum = {
    IN: '包含',
    NOT_IN: '不包含',
    EQ: '等于',
    NE: '不等于',
    GT: '大于',
    GTE: '大于等于',
    LT: '小于',
    LTE: '小于等于',
    BETWEEN: '范围'
  };

  return (
    <Drawer title="业务规则" open={edgeOpen} onClose={onClose} width={720} className="approvalRulesDrawer">
      <>
        <Spin spinning={_.isEmpty(edgeInfo) || confirmLoading}>
          <div>
            <Radio.Group onChange={onRuleTypeChange} value={ruleTypeValue}>
              <Space direction="vertical" className="!w-full">
                {ruleTypeValue === 'rules' && (
                  <Radio value="rules" disabled>
                    业务规则流程
                    {ruleTypeValue === 'rules' && (
                      <div
                        className="flex flex-col rulesContent"
                        style={{ marginTop: 16 }}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                        }}
                      >
                        {fieldLength > 1 ? (
                          <div className="ConnectorPanel">
                            <div className="TopLine" />
                            <div className="VLine" />
                            <div className="BottomLine" />
                            <div className="w-[22px] h-[22px] flex justify-center items-center text-[12px] bg-[#FF6800] rounded-[6px] text-[#fff] Connector">
                              {connectorChecked ? '且' : '或'}
                            </div>
                          </div>
                        ) : null}

                        <div className="flex flex-col gap-16">
                          {processRuleData.map((item, index) => (
                            <div className="flex gap-8" key={index}>
                              <Tooltip
                                title={
                                  paramList.find((paramItem) => paramItem.id === item.parameterId) &&
                                  paramList.find((paramItem) => paramItem.id === item.parameterId).parameterName
                                }
                                placement="topLeft"
                              >
                                {paramList.find((paramItem) => paramItem.id === item.parameterId) ? (
                                  <div
                                    className="bg-[#F5F5F5] rounded-[6px] max-w-[160px] whitespace-nowrap text-ellipsis overflow-hidden text-[rgba(0,0,0,.85)]"
                                    style={{ padding: '4px 8px' }}
                                  >
                                    {paramList.find((paramItem) => paramItem.id === item.parameterId).parameterName}
                                  </div>
                                ) : (
                                  <div>规则已被删除或停用</div>
                                )}
                              </Tooltip>
                              {paramList.find((paramItem) => paramItem.id === item.parameterId) && (
                                <div
                                  className="bg-[#F5F5F5] rounded-[6px] text-[rgba(0,0,0,.85)]"
                                  style={{ padding: '4px 8px' }}
                                >
                                  {opreatorEnum[item.parameterNameSymbol]}
                                </div>
                              )}

                              {paramList.find((paramItem) => paramItem.id === item.parameterId) && (
                                <Tooltip title={approvalRuleValueRender(item)} placement="topLeft">
                                  <div
                                    className="whitespace-nowrap text-ellipsis overflow-hidden w-[364px] bg-[#F5F5F5] rounded-[6px] text-[rgba(0,0,0,.85)]"
                                    style={{ padding: '4px 8px' }}
                                  >
                                    {approvalRuleValueRender(item)}
                                  </div>
                                </Tooltip>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </Radio>
                )}

                {ruleTypeValue === 'other' && (
                  <Radio value="other" disabled>
                    其他：不符合业务规则的流转
                  </Radio>
                )}
              </Space>
            </Radio.Group>
          </div>
        </Spin>
      </>
    </Drawer>
  );
};

export default EdgeDrawer;
