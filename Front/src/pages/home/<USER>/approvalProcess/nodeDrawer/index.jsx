import React from 'react';

import { Alert, Drawer, Form } from 'antd';
import Tooltip from 'antd/es/tooltip';
import _ from 'lodash';
import '../../index.scss';

const NodeDrawer = (props) => {
  const { nodeOpen, onClose, nodeInfo } = props;

  const [form] = Form.useForm();

  const onNodeCancel = () => {
    onClose();
  };

  return (
    <Drawer title="审批人" open={nodeOpen} onClose={onNodeCancel} width={600} className="approveDrawer">
      <>
        <div className="content">
          <Alert message="支持同时按部门、角色、指定审批人配置。符合任一条件的用户均可审批。" type="info" showIcon />

          <div className="approverContent">
            <div className="ConnectorPanel">
              <div className="TopLine" />
              <div className="VLine" />
              <div className="BottomLine" />
              <div className="w-[32px] h-[32px] flex justify-center items-center text-[12px] bg-[#FF6800] rounded-[6px] text-[#fff] Connector">
                或签
              </div>
            </div>
            <div className="FilterList">
              <Form form={form} name="approveForm">
                <Form.Item name="candidateGroupList" rules={[{ required: false }]} label="部门">
                  {nodeInfo && !_.isEmpty(nodeInfo.candidateGroupList) ? (
                    <Tooltip
                      placement="topLeft"
                      title={nodeInfo.candidateGroupList.map((item) => item.name).join('、')}
                    >
                      <div className="text-ellipsis whitespace-nowrap overflow-hidden w-[438px]">
                        {nodeInfo.candidateGroupList.map((item) => item.name).join('、')}
                      </div>
                    </Tooltip>
                  ) : (
                    '-'
                  )}
                </Form.Item>
                <Form.Item name="candidateRoleGroupList" rules={[{ required: false }]} label="角色">
                  {nodeInfo && !_.isEmpty(nodeInfo.candidateRoleGroupList) ? (
                    <Tooltip
                      placement="topLeft"
                      title={nodeInfo.candidateRoleGroupList.map((item) => item.name).join('、')}
                    >
                      <div className="text-ellipsis whitespace-nowrap overflow-hidden w-[438px]">
                        {nodeInfo.candidateRoleGroupList.map((item) => item.name).join('、')}
                      </div>
                    </Tooltip>
                  ) : (
                    '-'
                  )}
                </Form.Item>
                <Form.Item name="candidateUserList" rules={[{ required: false }]} label="指定审批人">
                  {nodeInfo && !_.isEmpty(nodeInfo.candidateUserList) ? (
                    <Tooltip placement="topLeft" title={nodeInfo.candidateUserList.map((item) => item.name).join('、')}>
                      <div className="text-ellipsis whitespace-nowrap overflow-hidden w-[410px]">
                        {nodeInfo.candidateUserList.map((item) => item.name).join('、')}
                      </div>
                    </Tooltip>
                  ) : (
                    '-'
                  )}
                </Form.Item>
              </Form>
            </div>
          </div>
        </div>
      </>
    </Drawer>
  );
};

export default NodeDrawer;
