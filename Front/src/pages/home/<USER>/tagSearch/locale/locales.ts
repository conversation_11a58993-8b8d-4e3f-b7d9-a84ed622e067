export default {
  cn: {
  "dataCenter-aPsZer3QTWso": "ID类型",
  "dataCenter-04rWJ0qbdFMU": "请选择ID类型",
  "dataCenter-GCk4mZS0yMug": "请选择",
  "dataCenter-vt6U15mfCoCG": "用户",
  "dataCenter-5K5dqXey3Lth": "分群",
  "dataCenter-f9FlgfQFrboO": "最多只能选5个分群",
  "dataCenter-SN5ZKT7pouXz": "标签",
  "dataCenter-qVeHRTrNNXvB": "请选择标签",
  "dataCenter-UjF7l3zAzWsF": "查询条件为空",
  "dataCenter-bgD94stvpBRd": "ID类型和标签值不能为空",
  "dataCenter-uykqIOcxA8df": "用户和分群至少有一个有值",
  "dataCenter-ESsKY07XydTP": "人",
  "dataCenter-Aegru8AHuqIK": "无可用批次",
  "dataCenter-uGa1B7NmSvZQ": "初始化失败",
  "dataCenter-Eh1t6obC3rNh": "标签列表",
  "dataCenter-C3zqgKoI5WS8": "标签详情",
  "dataCenter-Vjxn9zlXeTkV": "标签值查询",
  "dataCenter-6FRB9MJJnhby": "标签值查询列表",
   "dataCenter-UqCreNCvyHQp": "最多支持100条数据预览",
  "dataCenter-U3uO0lLh86Dd": "下载",
  "dataCenter-O9yvkVswKjPY": "ID类型",
  "dataCenter-zDAEIZKueq0j": "用户ID",
  },
  en: {
  "dataCenter-aPsZer3QTWso": "ID Type",
  "dataCenter-04rWJ0qbdFMU": "Please select ID type",
  "dataCenter-GCk4mZS0yMug": "Please select",
  "dataCenter-vt6U15mfCoCG": "User",
  "dataCenter-5K5dqXey3Lth": "Segment",
  "dataCenter-f9FlgfQFrboO": "You can only select up to 5 segments",
  "dataCenter-SN5ZKT7pouXz": "Tag",
  "dataCenter-qVeHRTrNNXvB": "Please select tag",
  "dataCenter-UjF7l3zAzWsF": "Query condition is empty",
  "dataCenter-bgD94stvpBRd": "ID type and tag value cannot be empty",
  "dataCenter-uykqIOcxA8df": "User and segment must have at least one value",
  "dataCenter-ESsKY07XydTP": "Person",
  "dataCenter-Aegru8AHuqIK": "No available batch",
  "dataCenter-uGa1B7NmSvZQ": "Initialization failed",
  "dataCenter-Eh1t6obC3rNh": "Tag list",
  "dataCenter-C3zqgKoI5WS8": "Tag details",
  "dataCenter-Vjxn9zlXeTkV": "Tag value query",
  "dataCenter-6FRB9MJJnhby": "Tag value query list",
   "dataCenter-UqCreNCvyHQp": "Up to 100 data preview is supported",
  "dataCenter-U3uO0lLh86Dd": "Download",
  "dataCenter-O9yvkVswKjPY": "ID type",
  "dataCenter-zDAEIZKueq0j": "User ID",
  }
};
