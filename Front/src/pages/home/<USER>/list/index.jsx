import CheckAuth from '@/utils/checkAuth';
import { ExclamationCircleOutlined, FilterOutlined } from '@ant-design/icons';
import { useAntdTable } from 'ahooks';
import { Badge, Button, Drawer, message, Modal, Table } from 'antd';
import QueryForList from 'components/bussinesscoms/queryforlist/index';
import ColumnActionCom from 'components/featurecoms/tableactioncom/index';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import CalendarService from 'service/calendarService';
import UserService from 'service/UserService';
import getMenuTitle from 'utils/menuTitle';
import { t } from 'utils/translation';
import Create from '../create/index';
import Detail from '../detail';
import config from './config';

import './index.scss';

const { confirm } = Modal;
const userService = new UserService();

const stateToProps = (state) => {
  return {
    meunData: state.meunData.menus.main
  };
};

const Calendar = (props) => {
  const [isFold, setIsFold] = useState(false);
  const [searchQueryList, setSearchQueryList] = useState([]);
  const [elements, setElements] = useState(config.elements);
  const queryData = useCallback((data) => setSearchQueryList([...data]), []);
  const [editInfo, setEditInfo] = useState({
    open: false,
    record: null
  });
  const [detailInfo, setDetailInfo] = useState({
    open: false,
    record: null
  });

  useEffect(() => {
    const init = async () => {
      const redata = await userService.listBy([]);
      const _elements = _.cloneDeep(elements);
      _elements.updateUserId.componentOptions.options = _.map(redata, (item) => ({
        key: item.id,
        value: item.id,
        text: item.name
      }));
      setElements(_elements);
    };
    init();
  }, []);

  const getTableList = async ({ current, pageSize, sorter: s }) => {
    const p = { page: current || 1, size: pageSize };
    if (s?.field && s?.order) {
      p.sorts = [
        {
          direction: _.includes(s?.order, 'desc') ? 'desc' : 'asc',
          propertyName: _.isArray(s?.field) ? s?.field.join('.') : s?.field
        }
      ];
    } else if (!s) {
      p.sorts = [
        {
          direction: 'desc',
          propertyName: 'updateTime'
        }
      ];
    } else {
      p.sorts = [];
    }

    const search = _.map(searchQueryList, (item) => {
      if (item.propertyName === 'passedCount' || item.propertyName === 'joinCount') {
        const valueArr = item.value.split(',');
        return {
          connector: 'AND',
          propertyName: item.propertyName,
          operator: valueArr[0] === '' ? 'EQ' : valueArr[0],
          value: valueArr.splice(1).toString()
        };
      }
      if (item.propertyName === 'id') {
        return {
          connector: 'AND',
          propertyName: item.propertyName,
          operator: item.operator,
          value: _.join(item.value, ',')
        };
      }
      if (item.operator === 'IN') {
        return {
          connector: 'AND',
          propertyName: item.propertyName,
          operator: item.operator,
          value: _.join(item.value, ',')
        };
      }
      if (item.propertyName !== 'operationTime' && item.operator === 'DATE_BETWEEN') {
        return {
          connector: 'AND',
          propertyName: item.propertyName,
          operator: item.operator,
          value: _.isArray(item.value)
            ? `${dayjs(item.value[0]).valueOf().toString()},${dayjs(item.value[1]).valueOf().toString()}`
            : item.value
          // value: item.value
        };
      }
      return {
        connector: 'AND',
        propertyName: item.propertyName,
        operator: item.operator,
        value: item.value
      };
    });
    const data = await CalendarService.query({ ...p, search });
    return {
      total: data.totalElements,
      list: data.content
    };
  };

  // eslint-disable-next-line no-unused-vars
  const { tableProps, params, search } = useAntdTable(getTableList, {
    defaultPageSize: 10,
    refreshDeps: [searchQueryList]
  });

  const onColumnActionClick = (type, _, record) => {
    const { id, status } = record;
    if (type === 'edit') {
      setEditInfo({ open: true, record });
    } else if (type === 'view') {
      setDetailInfo({ open: true, record });
    } else if (type === 'status') {
      confirm({
        title: <span style={{ fontWeight: '600' }}>{status === 'NORMAL' ? t('dataCenter-JOhNoLG0b6Ae') : t('dataCenter-qRPRANdn6P3x')}{t('dataCenter-KD56SiTjPdCp')}</span>,
        icon: <ExclamationCircleOutlined />,
        content: (
          <div>
            <div>
              {t('dataCenter-AKpvvwSRp8zk')}
              {status === 'NORMAL' ? t('dataCenter-JOhNoLG0b6Ae') : t('dataCenter-qRPRANdn6P3x')}
              {t('dataCenter-WunN0Q7UxBDo')}
              {record.name}
            </div>
            <div>{t('dataCenter-KPfjP9hBIA10')}</div>
          </div>
        ),
        async onOk() {
          await CalendarService.saveCalendar({
            ...record,
            id,
            status: status === 'NORMAL' ? 'CLOSE' : 'NORMAL'
          });
          refreshList();
          message.success(status === 'NORMAL' ? t('dataCenter-hcjHBf33BLT7') : t('dataCenter-sCqM7tlvveCy'));
        },
        onCancel() {}
      });
    } else if (type === 'delete') {
      confirm({
        title: <span style={{ fontWeight: '600' }}>{t('dataCenter-AS1AdkDXiJnf')}</span>,
        icon: <ExclamationCircleOutlined />,
        okText: t('dataCenter-AjuI39STcfIJ'),
        content: (
          <div>
            <div>{t('dataCenter-uxxkhK8Spvga')}{record.name}</div>
            <div>{t('dataCenter-zl5debf5cP0O')}</div>
          </div>
        ),
        async onOk() {
          try {
            await CalendarService.delById(record.id);
            refreshList();
            message.success(t('dataCenter-IItDr461UNH5'));
          } catch (err) {
            // message.error(err.message);
            console.error(err.message);
          }
        },
        onCancel() {}
      });
    }
  };

  const refreshList = () => {
    const timeer = setTimeout(() => {
      clearTimeout(timeer);
      const _searchQueryList = _.cloneDeep(searchQueryList);
      setSearchQueryList(_searchQueryList);
    }, 1000);
  };

  const columns = [
    {
      title: t('dataCenter-vMVZL2hfjlqD'),
      dataIndex: 'name',
      width: 310,
      ellipsis: {
        showTitle: true
      }
    },
    {
      title: t('dataCenter-PBJUaEW9gKui'),
      dataIndex: 'status',
      width: 310,
      ellipsis: {
        showTitle: false
      },
      render: (text) => (
        <div>
          <Badge status={text === 'NORMAL' ? 'success' : 'error'} style={{ marginRight: 8 }} />
          {text === 'NORMAL' ? t('dataCenter-GokzAd2NSEeq') : t('dataCenter-JOhNoLG0b6Ae')}
        </div>
      )
    },
    {
      title: t('dataCenter-dVIPAgFIM8oD'),
      dataIndex: 'updateUserName',
      width: 310
    },
    {
      title: t('dataCenter-OdfjivJ2FIKs'),
      dataIndex: 'updateTime',
      width: 310,
      sorter: (a, b) => a.updateTime - b.updateTime,
      defaultSortOrder: 'descend',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: t('dataCenter-13wvTndFOZQP'),
      className: 'td-set',
      width: 122,
      fixed: 'right',
      render: (text, record) => {
        const featureData = {
          // 特征
          view: {
            text: t('dataCenter-woUReXUiErs5')
          },
          dropdownData: {
            edit: {
              text: t('dataCenter-gj21grMfTNrI'),
              code: 'aim_campaign_calendar_edit'
            },
            status: {
              text: `${record.status === 'NORMAL' ? t('dataCenter-JOhNoLG0b6Ae') : t('dataCenter-qRPRANdn6P3x')}`,
              code: 'aim_campaign_calendar_edit'
            },
            delete: {
              text: t('dataCenter-MzjJ9cybZbgY'),
              code: 'aim_campaign_calendar_edit'
            }
          }
        };
        return (
          <ColumnActionCom closeDropdown featureData={featureData} record={record} onClick={onColumnActionClick} />
        );
      }
    }
  ];

  const calendarCreate = () => {
    setEditInfo({
      open: true,
      record: null
    });
  };

  const saveCalendar = async (value, id) => {
    try {
      const url = value.fileValue[0].response.body.result;
      const icsFileName = value.fileValue[0].response.body.sourceName;
      const icsFilePath = url.startsWith('tmp/') ? url.substring(4) : url;
      const params = {
        name: value.name,
        icsFilePath,
        icsFileName,
        memo: value.memo,
        status: 'NORMAL',
        id: id || null
      };
      await CalendarService.saveCalendar(params);
      message.success(editInfo.record ? t('dataCenter-dtz1WcRVRap2') : t('dataCenter-t5JtlGmzu7v2'));
      const timeer = setTimeout(() => {
        clearTimeout(timeer);
        const _searchQueryList = _.cloneDeep(searchQueryList);
        setSearchQueryList(_searchQueryList);
        onClose();
      }, 1000);
    } catch (err) {
      console.error(err.message);
    }
  };

  const onClose = () => {
    setEditInfo({ open: false, record: null });
  };

  const onCloseDetail = () => {
    setDetailInfo({ open: false, record: null });
  };

  return (
    <div className="calendarList">
      <header>
        <div className="title">
          <h1>{getMenuTitle(props.meunData, props.history.location.pathname)}</h1>
        </div>
        <div className="btnGroup">
          <Button onClick={() => setIsFold(!isFold)} icon={<FilterOutlined />}>
            {t('dataCenter-2PzTfjyo3m8k')}
          </Button>
          <CheckAuth code="aim_campaign_calendar_edit">
            <Button onClick={calendarCreate} style={{ marginLeft: '8px' }} type="primary">
              {t('dataCenter-hGmm0Fvxu3TL')}
            </Button>
          </CheckAuth>
        </div>
      </header>
      <QueryForList elements={elements} onQuery={queryData} show={isFold} />
      <Table
        {...tableProps}
        columns={columns}
        rowKey="id"
        scroll={{ x: 1300 }}
        pagination={{
          ...tableProps.pagination,
          showQuickJumper: true,
          showSizeChanger: true,
          showLessItems: true,
          pageSizeOptions: ['10', '20', '50'],
          showTotal: (e) => `${t('dataCenter-qjw9B8RbfV5n')} ${e} ${t('dataCenter-FKm31fik6w1u')}`
        }}
      />
      <Drawer
        title={editInfo.record ? t('dataCenter-q4eplfFILQrm') : t('dataCenter-qyhJ82JRpovn')}
        placement="right"
        // size="large"
        width={600}
        onClose={onClose}
        open={editInfo.open}
        destroyOnClose
      >
        <Create onClose={onClose} onOk={saveCalendar} record={editInfo.record} />
      </Drawer>
      <Drawer
        title={t('dataCenter-diwyuCSiRdIU')}
        placement="right"
        // size="large"
        width={960}
        onClose={onCloseDetail}
        open={detailInfo.open}
        destroyOnClose
      >
        <Detail onClose={onClose} onOk={saveCalendar} record={detailInfo.record} />
      </Drawer>
    </div>
  );
};

export default connect(stateToProps)(Calendar);
