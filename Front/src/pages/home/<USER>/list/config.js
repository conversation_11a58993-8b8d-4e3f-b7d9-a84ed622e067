import { t } from 'utils/translation';

const statusList = [
  {
    name: t('dataCenter-GokzAd2NSEeq'),
    text: t('dataCenter-GokzAd2NSEeq'),
    value: 'NORMAL',
    key: 'NORMAL'
  },
  {
    name: t('dataCenter-JOhNoLG0b6Ae'),
    text: t('dataCenter-JOhNoLG0b6Ae'),
    value: 'CLOSE',
    key: 'CLOSE'
  }
];

export default {
  // query表单配置
  elements: {
    name: {
      type: 'input',
      label: t('dataCenter-vMVZL2hfjlqD'),
      operator: 'LIKE',
      width: 12,
      componentOptions: {
        allowClear: true,
        placeholder: t('dataCenter-fXOqTihvtXJ9')
      }
    },
    status: {
      type: 'select',
      label: t('dataCenter-PBJUaEW9gKui'),
      width: 12,
      operator: 'EQ',
      componentOptions: {
        allowClear: true,
        showSearch: true,
        options: statusList,
        filterOption: (input, option) => {
          return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        placeholder: t('dataCenter-2z2hsFY795hI')
      }
    },
    updateUserId: {
      type: 'select',
      label: t('dataCenter-dVIPAgFIM8oD'),
      operator: 'EQ',
      width: 12,
      componentOptions: {
        allowClear: true,
        showSearch: true,
        placeholder: t('dataCenter-5LFwJyjKaoA1'),
        filterOption: (input, option) => {
          return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        options: []
      }
    },
    updateTime: {
      type: 'dateRange',
      label: t('dataCenter-OdfjivJ2FIKs'),
      width: 12,
      operator: 'DATE_BETWEEN',
      componentOptions: {
        allowClear: true,
        format: 'YYYY-MM-DD'
      }
    }
  }
};
