import { t } from 'utils/translation';

export const elements = {
  name: {
    type: 'input',
    label: t('analysisCenter-lTSgRunhkhV9'),
    width: 12,
    operator: 'LIKE',
    componentOptions: {
      allowClear: true,
      placeholder: t('analysisCenter-miV3qUOSqU3p')
    }
  },
  'scenario.id': {
    type: 'select',
    label: t('analysisCenter-K8puj492Oa5b'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true,
      showSearch: true,
      filterOption: (input, option) => {
        return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      placeholder: t('analysisCenter-g84Uk8PG2ZAY')
    }
  },
  createTime: {
    type: 'dateRange',
    label: t('analysisCenter-YSPO5KN1v4M5'),
    width: 12,
    operator: 'DATE_BETWEEN',
    componentOptions: {
      allowClear: true
    }
  },
  user: {
    type: 'select',
    label: t('analysisCenter-gA8Izjj6u4Sb'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true,
      showSearch: true,
      filterOption: (input, option) => {
        return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      placeholder: t('analysisCenter-W7hdws5i2T2u')
    }
  }
};
