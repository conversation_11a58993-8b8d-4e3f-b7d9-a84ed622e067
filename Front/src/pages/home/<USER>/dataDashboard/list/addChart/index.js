import { Checkbox, Col, Input, Modal, Pagination, Row, Spin } from 'antd';
import _ from 'lodash';
import React, { useState } from 'react';
import AnalysisCenterService from 'service/analysisCenterService';
import { useDebounce, useDeepCompareEffect } from 'utils/customhooks';
import { t } from 'utils/translation';
import ComChart from '../../../database/comChart/index';

import './index.scss';

const { Search } = Input;

export default ({ state, dispatch }) => {
  const { selectValue, refresh, loading: refreshLoading } = state;
  // const { chartList, selectValue, refresh, loading: refreshLoading } = state;
  const { widgets = [] } = selectValue;
  const [loading, setLoading] = useState(false);
  const [chartList, setChartList] = useState([]);
  const [checkedValues, setCheckedValues] = useState([]);
  const [pagination, setPagination] = useState({
    total: 0,
    current: 1,
    pageSize: 12,
    name: ''
  });
  const [currentChart, setCurrentChart] = useState(null);

  const okHandle = () => {
    try {
      setLoading(true);
      const _widgets = widgets || [];
      checkedValues.forEach((n) => {
        _widgets.push({
          x: (_widgets.length * 4) % 12,
          y: widgets[widgets?.length - 1]?.y + 100 || 0,
          w: 4,
          h: 3,
          isResizable: true,
          i: `${n}`
        });
      });
      // widgets = _.concat(widgets, _widgets);
      dispatch({
        selectValue: {
          ...selectValue,
          widgets: _widgets
        },
        chartModalVisible: false
      });
      setLoading(false);
    } catch {
      setLoading(false);
    }
    // const addItem = {
    //   x: (widgets.length * 3) % (12),
    //   y: Infinity, // puts it at the bottom
    //   w: 3,
    //   h: 2,
    //   i: new Date().getTime().toString()
    // };
  };

  useDeepCompareEffect(() => {
    (async () => {
      try {
        setLoading(true);
        const result = await AnalysisCenterService.getChartConfigList({
          size: pagination.pageSize,
          page: pagination.current,
          search: [
            {
              operator: 'NOT_IN',
              propertyName: 'id',
              value: _.map(widgets, (item) => item.i).join(',')
            },
            { operator: 'LIKE', propertyName: 'name', value: pagination.name },
            {
              operator: 'EQ',
              propertyName: 'deptId',
              value: window.getDeptId()
            }
          ]
        });
        pagination.total = result.totalElements;
        setPagination({ ...pagination });
        setChartList(result.content);
        setLoading(false);
      } catch {
        setLoading(false);
      }
    })();
  }, [pagination]);

  const handleTableChange = (page, pageSize) => {
    const newParam = { ...pagination };
    newParam.current = page;
    newParam.pageSize = pageSize;
    setPagination(newParam);
  };

  const onClickCheckbox = (chart) => {
    if (checkedValues.length < 50 - widgets.length && !_.includes(checkedValues, chart.id)) {
      setCheckedValues([...checkedValues, chart.id]);
    } else {
      setCheckedValues(_.filter(checkedValues, (item) => item !== chart.id));
    }
    setCurrentChart(null);
    setTimeout(() => {
      setCurrentChart(chart);
    }, 4);
  };

  const onChangeSearch = useDebounce((e) => {
    setPagination({
      total: 0,
      current: 1,
      pageSize: 12,
      name: e
    });
  }, 400);

  return (
    <Modal
      title={t('analysisCenter-yALATAMieLE9')}
      open
      width={900}
      height={480}
      onOk={okHandle}
      maskClosable={false}
      onCancel={() => dispatch({ chartModalVisible: false })}
      confirmLoading={loading}
      className="dashBoardAddModal"
    >
      <div className="mainContent">
        <div className="contentLeft">
          <Search
            placeholder={t('analysisCenter-DAMxE7JpZrPQ')}
            onChange={(e) => onChangeSearch(e.target.value)}
            style={{ width: '100%' }}
          />
          <div className="contentLeftHandle">
            <a
              onClick={() => {
                dispatch({ refresh: !refresh });
              }}
            >
              {t('analysisCenter-u2MfMcHJoIUe')}
            </a>
            <span>{`${t('analysisCenter-O43mSSNERIsC')}   [${checkedValues?.length || 0}/${50 - widgets.length}]`}</span>
          </div>
          <Spin spinning={refreshLoading || loading}>
            <Checkbox.Group value={checkedValues} style={{ width: '100%' }}>
              {chartList.map((n) => (
                <Row key={n.id}>
                  <Col
                    style={{ marginBottom: 5 }}
                    className={`${currentChart && currentChart.id === n.id ? 'active' : ''}`}
                    span={24}
                  >
                    <Checkbox
                      onClick={() => {
                        onClickCheckbox(n);
                      }}
                      value={n.id}
                    >
                      {n.name}
                    </Checkbox>
                  </Col>
                </Row>
              ))}
            </Checkbox.Group>
            <div style={{ margin: '30px 10px', textAlign: 'right' }} hidden={chartList.length === 0}>
              <Pagination
                onChange={handleTableChange}
                current={pagination.current}
                pageSize={pagination.pageSize}
                total={pagination.total}
                size="small"
              />
            </div>
          </Spin>
        </div>
        <div className="contentRight smWrap">
          <div className="rightWrapper">{currentChart && <ComChart info={currentChart} />}</div>
        </div>
      </div>
    </Modal>
  );
};
