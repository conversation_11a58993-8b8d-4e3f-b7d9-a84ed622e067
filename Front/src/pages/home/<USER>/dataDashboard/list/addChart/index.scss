.dashBoardAddModal {
  .mainContent {
    display: flex;

    .contentLeft {
      width: 49%;
      margin-right: 1%;

      .ant-checkbox-group {
        margin-top: 10px;
        height: 400px;
        overflow-y: auto;
      }

      .ant-row {
        .ant-col.active {
          background-color: #F2F2F2;
        }
      }

      .contentLeftHandle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 3px;
      }
    }

    .smWrap {
      .pieWrap {
        justify-content: initial !important;

        .pieItem {
          min-width: 100% !important;
        }
      }
    }
    
    .contentRight {
      width: 50%;
      height: 480px;

      .rightWrapper {
        width: 100%;
        height: 100%;
      }
    }
  }
}