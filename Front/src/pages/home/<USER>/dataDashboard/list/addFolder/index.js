import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Modal, TreeSelect } from 'antd';
import { getDeptPath } from 'pages/home/<USER>/dataPermissions/config';
import React, { useState } from 'react';
import DashboardService from 'service/dashboardService';
import { t } from 'utils/translation';

const { TextArea } = Input;

const getLevelData = (data, parentId) => {
  const itemArr = [];
  for (let i = 0; i < data.length; i++) {
    const node = data[i];
    if (node.parentId === parentId) {
      const newNode = {};
      newNode.key = `${node.id}`;
      newNode.value = `${node.id}`;
      newNode.title = node.name;
      itemArr.push(newNode);
    }
  }
  return itemArr;
};

const EditChart = (props) => {
  const {
    form: { getFieldDecorator, validateFields },
    state,
    dispatch
  } = props;
  const { allCategoryList, refresh, folderValue } = state;
  const [loading, setLoading] = useState(false);
  const [treeData] = useState(() => {
    const data = [
      {
        key: '0',
        value: '0',
        title: t('analysisCenter-Mn2pQ4rS6tU'),
        children: getLevelData(allCategoryList, 0)
      }
    ];
    return data;
  });
  const [deptPath] = useState(getDeptPath(folderValue?.deptId));

  const getType = (parentId) => {
    const obj = { type: 'FIRST_LEVEL', code: '0,' };
    const info = allCategoryList.find((n) => n.id === parseInt(parentId));
    if (!info) {
      return obj;
    }
    if (info.type === '0') {
      obj.type = 'FIRST_LEVEL';
      obj.code = '0,';
    } else if (info.type === 'FIRST_LEVEL') {
      obj.type = 'SECOND_LEVEL';
      obj.code = `${obj.code}${info.id}.`;
    }
    return obj;
  };

  const okHandle = () => {
    validateFields(async (err, fields) => {
      if (err) return;
      try {
        setLoading(true);
        const data = { ...folderValue, ...fields, ...getType(fields.parentId) };
        await DashboardService.saveChartBoardCategory(data);
        setLoading(false);
        dispatch({ folderModalVisible: false, refresh: !refresh });
      } catch {
        setLoading(false);
      }
    });
  };

  return (
    <Modal
      title={`${folderValue.id ? t('analysisCenter-Xk9mN2pQr5vT') : t('analysisCenter-Vw4xY6zA8bC')}${t('analysisCenter-Df2gH4jK6lM')}`}
      onOk={okHandle}
      open
      maskClosable={false}
      onCancel={() => dispatch({ folderModalVisible: false })}
      confirmLoading={loading}
    >
      <Form labelCol={{ span: 5 }} wrapperCol={{ span: 17 }}>
        <Form.Item label={t('analysisCenter-Np2qR4sT6vW')}>
          <div className="my-text-overflow" title={deptPath}>
            {deptPath}
          </div>
        </Form.Item>
        <Form.Item label={t('analysisCenter-Xy4zA6bC8dE')}>
          {getFieldDecorator('parentId', {
            rules: [{ required: true, message: t('analysisCenter-Fg2hJ4kL6mN') }],
            initialValue: folderValue.parentId && folderValue.parentId.toString()
          })(
            <TreeSelect
              showSearch
              filterTreeNode={(value, TreeNode) => TreeNode.props.title.indexOf(value) > -1}
              style={{ width: '100%' }}
              dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
              treeData={treeData}
              disabled={folderValue.id !== undefined}
              placeholder={t('analysisCenter-Pq2rS4tU6vW2')}
              treeDefaultExpandAll
            />
          )}
        </Form.Item>
        <Form.Item label={t('analysisCenter-Wx2yZ4aB6cD')}>
          {getFieldDecorator('name', {
            rules: [
              { required: true, message: t('analysisCenter-Ef2gH4jK6lM') },
              { max: 20, message: t('analysisCenter-Np2qR4sT6vW2') }
            ],
            initialValue: folderValue.name
          })(<Input />)}
        </Form.Item>
        <Form.Item label={t('analysisCenter-Xy2zA4bC6dE')}>
          {getFieldDecorator('remark', {
            rules: [{ max: 100, message: t('analysisCenter-Fg2hJ4kL6mN2') }],
            initialValue: folderValue.remark
          })(<TextArea />)}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create()(EditChart);
