import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Checkbox, Input, Modal, TreeSelect } from 'antd';
import { getDeptPath } from 'pages/home/<USER>/dataPermissions/config';
import React, { useState } from 'react';
// import AnalysisCenterService from 'service/analysisCenterService';
import DashboardService from 'service/dashboardService';
import { t } from 'utils/translation';

const { TextArea } = Input;

const getLevelData = (data, parentId) => {
  const itemArr = [];
  for (let i = 0; i < data.length; i++) {
    const node = data[i];
    if (node.parentId === parentId) {
      const newNode = {};
      newNode.key = `${node.id}`;
      newNode.value = `${node.id}`;
      newNode.title = node.name;
      const res = getLevelData(data, node.id);
      if (res.length > 0) {
        newNode.children = res;
      }
      itemArr.push(newNode);
    }
  }
  return itemArr;
};

const EditChart = (props) => {
  const {
    form: { getFieldDecorator, validateFields },
    state,
    dispatch
  } = props;
  const { allCategoryList, refresh, dashboardValue } = state;
  const [loading, setLoading] = useState(false);
  const [treeData] = useState(getLevelData(allCategoryList, 0) || []);
  const [deptPath] = useState(getDeptPath(dashboardValue?.deptId));

  // useEffect(() => {
  //   (async () => {
  //     getLevelData(allCategoryList, 0)
  //   })();
  // }, []);

  const getType = (parentId) => {
    const obj = { type: 'FIRST_LEVEL', code: '0,' };
    const info = allCategoryList.find((n) => n.id === parseInt(parentId));
    if (!info) {
      return obj;
    }
    if (info.type === '0') {
      obj.type = 'FIRST_LEVEL';
      obj.code = '0,';
    } else if (info.type === 'FIRST_LEVEL') {
      obj.type = 'SECOND_LEVEL';
      obj.code = `${obj.code}${info.id}.`;
    }
    return obj;
  };

  const okHandle = () => {
    validateFields(async (err, fields) => {
      if (err) return;
      try {
        setLoading(true);
        const data = {
          ...dashboardValue,
          ...fields,
          ...getType(fields.parentId)
        };
        await DashboardService.saveChartBoardData(data);
        setLoading(false);
        dispatch({ dashboardModalVisible: false, refresh: !refresh });
      } catch {
        setLoading(false);
      }
    });
  };

  return (
    <Modal
      title={`${dashboardValue.id ? t('analysisCenter-Iu7fuQKoyxoE') : t('analysisCenter-GA2GtdUoNwhw')} ${t('analysisCenter-GHhHVboUuoQ0')}`}
      onOk={okHandle}
      open
      maskClosable={false}
      onCancel={() => dispatch({ dashboardModalVisible: false })}
      confirmLoading={loading}
    >
      <Form labelCol={{ span: 5 }} wrapperCol={{ span: 17 }}>
        <Form.Item label={t('analysisCenter-lzOAPEQBLelm')}>
          <div className="my-text-overflow" title={deptPath}>
            {deptPath}
          </div>
        </Form.Item>
        <Form.Item label={t('analysisCenter-8YQ6q5eBdTze')}>
          {getFieldDecorator('category.id', {
            rules: [{ required: true, message: t('analysisCenter-UeXMBGTDYlkN') }],
            initialValue: dashboardValue.category && dashboardValue.category.id.toString()
          })(
            <TreeSelect
              showSearch
              filterTreeNode={(value, TreeNode) => TreeNode.props.title.indexOf(value) > -1}
              style={{ width: '100%' }}
              dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
              treeData={treeData}
              placeholder={t('analysisCenter-DAMxE7JpZrPQ')}
              treeDefaultExpandAll
            />
          )}
        </Form.Item>
        <Form.Item label={t('analysisCenter-Tp5fsM3axsfU')}>
          {getFieldDecorator('boardName', {
            rules: [
              { required: true, message: t('analysisCenter-GE9ZCmBOuJkS') },
              { max: 20, message: t('analysisCenter-Np2qR4sT6vW2') }
            ],
            initialValue: dashboardValue.boardName
          })(<Input />)}
        </Form.Item>
        <Form.Item label={t('analysisCenter-Xy2zA4bC6dE')}>
          {getFieldDecorator('remark', {
            rules: [
              // { required: true, message: '请输入' }
              { max: 100, message: t('analysisCenter-Fg2hJ4kL6mN2') }
            ],
            initialValue: dashboardValue.remark
          })(<TextArea />)}
        </Form.Item>
        <Form.Item colon={false} label=" ">
          {getFieldDecorator('default', {
            valuePropName: 'checked',
            initialValue: dashboardValue.default
          })(<Checkbox>{t('analysisCenter-Yh7jK4lM9nB')}</Checkbox>)}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create()(EditChart);
