// @import 'assets/css/variable.scss';
@import "~react-grid-layout/css/styles.css";
@import "~react-resizable/css/styles.css";

// import '';
// import '/node_modules/react-resizable/css/styles.css';

// .react-grid-item {
//       & .react-grid-placeholder {
//         background-color: #ccc !important;
//       }
// }

.react-grid-placeholder {
  background: #ccc !important;
}


.dataDashboardList {
  display: flex;
  background-color: #f0f2f5;
  // padding-bottom: 10px;
  margin: 0 -24px;
  height: calc(100vh - 60px);

  // ::-webkit-scrollbar{
  //   width:4px;
  //   height:4px;
  //   /**/
  // }
  .sider {
    background-color: #fff;
    overflow-y: auto;
    height: 100%;
    width: 250px;

    .siderToolbar {
      height: 56px;
      margin-top: 3px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;

      .title {
        font-size: 20px;
        font-weight: 600;
        color: black;
      }

      .iconStyle {
        font-size: 16px;
        margin-left: 10px;
      }
    }

    .ant-tree-treenode {
      padding: 0 0 4px 16px;

      &:hover {
        background: $active_color;
      }

      .ant-tree-node-content-wrapper.ant-tree-node-selected {
        color: $primary_color;
      }

      .parentLeft {
        height: 40px;
        line-height: 40px;
      }

      .ant-tree-switcher {
        width: 16px !important;
        height: 40px;
        line-height: 40px;
      }

      .ant-tree-iconEle {
        display: none;
      }
    }

    // .ant-tree-treenode-leaf-last {
    //   .ant-tree-switcher {
    //     display: none;
    //   }
    // }

    .ant-tree-treenode-switcher-close,
    .ant-tree-treenode-switcher-open {
      .ant-tree-switcher {
        height: 40px;
        line-height: 40px;
        // padding: 0 20px 0 20px;
      }
    }

    .ant-tree-treenode-selected::before {
      // opacity: .08;
      background: $active_color !important;
    }

    .ant-tree-node-content-wrapper {
      & .parentRight {
        display: inline-block;
        margin-top: 10px;
      }

      &:hover {
        .parentRight {
          display: inline-block;
          margin-left: 42px;
        }
      }

    }

    .hoverMore {
      &:hover {
        &>.ant-tree-node-content-wrapper {
          .parentRight {
            display: inline-block;
          }
        }
      }
    }

    .parentNode {
      display: inline-flex;
      align-items: center;

      .parentLeft {
        flex: 1;
        display: inline-block;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
      }

      .parentRight {
        display: none;
        font-size: 16px;
      }
    }
  }

  .content {
    flex: 1;
    padding: 0 5px;
    height: 100%;
    background-color: #f0f2f5;
    overflow-y: auto;

    .contentTitle {
      height: 40px;
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 8px;

      .title {
        font-family: 'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC';
        font-weight: 500;
        font-style: normal;
        font-size: 20px;
        margin-right: 16px;
      }

    }

    .emptyStyle {
      height: calc(100% - 80px);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .hasInfo {
    z-index: 3;
    border: 1px solid white;
    width: 100%;
    height: 100%;
    background-color: #fff;

    .lgWrap {
      .pieWrap {
        // justify-content: center !important;

        .pieItem {
          min-width: 50% !important;
        }
      }
    }

    .smWrap {
      .pieWrap {
        justify-content: initial !important;

        .pieItem {
          min-width: 100% !important;
        }
      }
    }

    .toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 60px;

      // background-color: white;
      .left {
        width: calc(100% - 80px);

        .title {
          font-size: 16px;
          padding-left: 10px;
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .time {
          font-size: 12px;
          padding-left: 10px;
          color: rgba(0, 0, 0, .45);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .right {
        padding-right: 10px;
        display: none;
        align-items: center;
        width: 60px;
      }
    }
  }

  .hasInfo {
    & .react-resizable-handle {
      visibility: hidden;
    }
  }

  .hasInfo:hover {
    .right {
      display: flex;
    }

    & .react-resizable-handle {
      visibility: visible;
    }
  }

  .noInfo {
    border: 1px solid white;
    width: 100%;
    height: 100%;
    background-color: #fff;

    .toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 50px;

      // background-color: white;
      .left {
        font-size: 16px;
        padding-left: 10px;
        font-weight: 500;
      }

      .right {
        display: none;
        padding-right: 10px;
      }
    }

    .noInfoContent {
      height: calc(100% - 50px);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      color: red;
    }
  }

  .noInfo:hover {
    .right {
      display: flex;
    }
  }

  // .layout {
  //   // background-color: white;
  //   height: 1000px;
  // }

}