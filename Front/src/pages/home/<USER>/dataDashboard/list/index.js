import { t } from '@/utils/translation';
import { DownOutlined, FullscreenOutlined, MoreOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, Dropdown, Empty, Input, Modal, Spin, Tree, message } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useReducer, useRef, useState } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import VisibilitySensor from 'react-visibility-sensor';
import AnalysisCenterService from 'service/analysisCenterService';
import DashboardService from 'service/dashboardService';
import CheckAuth from 'utils/checkAuth';
import getMenuTitle from 'utils/menuTitle';
import ComChart from '../../database/comChart/index';
import AddChart from './addChart/index';
import AddChartBoard from './addChartBoard/index';
import AddFolder from './addFolder/index';
import './index.scss';

const stateToProps = (state) => {
  return {
    meunData: state.meunData.menus.main
  };
};

const ResponsiveReactGridLayout = WidthProvider(Responsive);
const defaultProps = {
  cols: { lg: 12, md: 12, sm: 12, xs: 12, xxs: 12 },
  rowHeight: 100,
  margin: [4, 4],
  breakpoints: { lg: 0, md: 0, sm: 0, xs: 0, xxs: 0 }
};

const { DirectoryTree } = Tree;
const { Search } = Input;
const reducer = (state, action) => ({ ...state, ...action });

const getWidth = (type, parent) => {
  let width = 117;
  if (type === 'FIRST_LEVEL') {
    if (parent) {
      width = 117;
    } else {
      width = 141;
    }
  } else if (type === 'SECOND_LEVEL') {
    if (parent) {
      width = 100;
    } else {
      width = 117;
    }
  }
  return width;
};

const getLevelData = (chartBoardList, data, parentId) => {
  const itemArr = [];
  for (let i = 0; i < data.length; i++) {
    const node = data[i];
    if (node.parentId === parentId) {
      const newNode = {};
      newNode.key = `folder.${node.id}`;
      newNode.selectable = false;
      newNode.width = getWidth(node.type);
      newNode.title = node.name;
      newNode.isLeaf = false;
      const res = getLevelData(chartBoardList, data, node.id);
      if (res.length > 0) {
        newNode.children = res;
      }
      const list = chartBoardList.filter((w) => parseInt(node.id) === w.category?.id);
      if (list.length > 0) {
        if (newNode.children) {
          list.forEach((n) => {
            newNode.children.push({
              key: `${n.id}`,
              width: getWidth(node.type, true),
              title: n.boardName,
              isLeaf: true
            });
          });
        } else {
          newNode.children = list.map((n) => ({
            key: `${n.id}`,
            width: getWidth(node.type, true),
            title: n.boardName,
            isLeaf: true
          }));
        }
      }
      itemArr.push(newNode);
    }
  }
  return itemArr;
};

const DataDashboard = (props) => {
  const { id } = useParams();
  const [state, dispatch] = useReducer(reducer, {
    folderModalVisible: false,
    folderValue: {},
    chartModalVisible: false,
    selectValue: {},
    dashboardModalVisible: false,
    dashboardValue: {},
    chartList: [],
    allCategoryList: [],
    menuList: [],
    refresh: true,
    expandedKeys: [],
    selectedKeys: id ? [id] : props.location.state?.selectedKeys || [],
    refreshSelected: Math.random(),
    chartBoardList: [],
    loading: false,
    getLoading: false
  });

  const [searchValue, setSearchValue] = useState('');

  const [loadedComChartIndexArr, setLoadedComChartIndexArr] = useState([]);

  const [domLoading, setDomLoading] = useState(false);

  const [itemWidth, setItemWidth] = useState(0);

  const {
    selectedKeys,
    refreshSelected,
    chartList,
    chartBoardList,
    chartModalVisible,
    folderModalVisible,
    menuList,
    refresh,
    allCategoryList,
    expandedKeys,
    selectValue,
    dashboardModalVisible,
    getLoading,
    loading
  } = state;

  const { layouts = {}, widgets = [] } = selectValue;

  useEffect(() => {
    const timer = setInterval(() => {
      dispatch({ refreshSelected: Math.random() });
    }, 300000);
    return () => {
      clearInterval(timer);
    };
  }, []);

  useEffect(() => {
    (async () => {
      try {
        dispatch({ loading: true });
        const allCategoryList = await DashboardService.getAllchartBoardCategory([
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: window.getDeptId()
          }
        ]);
        const chartList = await AnalysisCenterService.getChartConfigAllList([
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: window.getDeptId()
          }
        ]);
        const chartBoardList = await DashboardService.getAllchartBoard([
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: window.getDeptId()
          }
        ]);
        const menuList = getLevelData(chartBoardList, allCategoryList, 0);
        const defaultSelectedKeys = chartBoardList.find((n) => n.default === true) || chartBoardList[0];
        const _selectedKeys =
          selectedKeys.length === 0 && defaultSelectedKeys ? [`${defaultSelectedKeys.id}`] : selectedKeys;
        dispatch({
          chartList,
          allCategoryList,
          menuList,
          chartBoardList,
          selectedKeys: _selectedKeys,
          expandedKeys: allCategoryList.map((n) => `folder.${n.id}`),
          loading: false
        });
      } catch (error) {
        dispatch({ loading: false });
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refresh]);

  const isGetLayouts = useRef(false);

  useEffect(() => {
    (async () => {
      dispatch({ getLoading: true });
      if (selectedKeys.length > 0) {
        const selectValue = await DashboardService.getChartBoardById(selectedKeys[0]);
        isGetLayouts.current = true;
        if (selectValue) {
          props.history.replace({ state: { selectedKeys } });
          dispatch({ selectValue, getLoading: false });
        } else {
          dispatch({
            selectValue: {
              layouts: {},
              widgets: []
            },
            selectedKeys: [],
            getLoading: false,
            refresh: !refresh
          });
        }
      } else {
        dispatch({ getLoading: false, selectValue: {} });
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedKeys, refreshSelected]);

  const editData = (key, isLeaf) => {
    if (!isLeaf) {
      const info = allCategoryList.find((n) => n.id === parseInt(key.split('.')[1]));
      if (info) {
        dispatch({ folderValue: { ...info }, folderModalVisible: true });
      }
    } else {
      const info = chartBoardList.find((n) => n.id === parseInt(key));
      if (info) {
        dispatch({ dashboardValue: { ...info }, dashboardModalVisible: true });
      }
    }
  };

  const deleteData = (key, isLeaf, title) => {
    if (!isLeaf) {
      Modal.confirm({
        title: t('analysisCenter-7AoIZNCFO742'),
        content: (
          <p className="confirmDelete">
            {t('analysisCenter-W9Ei6Q6FJzhy', { title })}
            <br />
            {t('analysisCenter-bjrWyxtLxZNb')}
          </p>
        ),
        okText: t('analysisCenter-1W0TQyXLcgym'),
        okType: 'danger',
        cancelText: t('analysisCenter-srIbM3voQpZg'),
        async onOk() {
          try {
            await DashboardService.delChartBoardCategory(key.split('.')[1]);
            message.success(t('analysisCenter-Qfqt1neEnibG'));
            dispatch({ refresh: !refresh });
          } catch {
            console.error(11);
          }
        },
        onCancel() {}
      });
    } else {
      Modal.confirm({
        title: t('analysisCenter-rxnguX6xXMzN'),
        content: (
          <p className="confirmDelete">
            {t('analysisCenter-A8Y6k0LMt1jg', { title })}
            <br />
            {t('analysisCenter-ehiRmLdwpmkE')}
          </p>
        ),
        okText: t('analysisCenter-1W0TQyXLcgym'),
        okType: 'danger',
        cancelText: t('analysisCenter-srIbM3voQpZg'),
        async onOk() {
          try {
            await DashboardService.delChartBoardById(key);
            message.success('刪除成功!');
            if (selectedKeys[0] === key) {
              dispatch({ selectedKeys: [] });
            }
            dispatch({ refresh: !refresh });
          } catch {
            console.error(11);
          }
        },
        onCancel() {}
      });
    }
  };

  const setDefault = async (id) => {
    await DashboardService.updateDefaultBoard({ id });
    message.success(t('analysisCenter-Pq2rS4tU6vW'));
    dispatch({ refresh: !refresh });
  };

  const treeTitle = (item) => {
    const moreMenu = [
      CheckAuth.checkAuth('aim_chart_board_edit') && {
        key: 'edit',
        label: t('analysisCenter-Xk9mN2pQr5vT'),
        onClick: () => editData(item.key, item.isLeaf)
      },
      CheckAuth.checkAuth('aim_chart_board_edit') &&
        item.isLeaf && {
          key: 'setDefault',
          label: t('analysisCenter-Yh7jK4lM9nB'),
          onClick: () => setDefault(item.key)
        },
      !item.isLeaf &&
        CheckAuth.checkAuth('aim_chart_board_category_delete') && {
          key: 'delete',
          label: t('analysisCenter-Zw3xV5bN8mK'),
          onClick: () => deleteData(item.key, item.isLeaf, item.title)
        },
      item.isLeaf &&
        CheckAuth.checkAuth('aim_chart_board_delete') && {
          key: 'deleteBoard',
          label: t('analysisCenter-Zw3xV5bN8mK'),
          onClick: () => deleteData(item.key, item.isLeaf, item.title)
        }
    ];

    const showMoreMenu =
      CheckAuth.checkAuth('aim_chart_board_edit') ||
      CheckAuth.checkAuth('aim_chart_board_category_delete') ||
      CheckAuth.checkAuth('aim_chart_board_delete');

    return (
      <span className="parentNode">
        <span title={item.title} className="parentLeft" style={{ width: item.width }}>
          {item.title}
        </span>
        <span style={{ display: 'inline-block' }} onClick={(e) => e.stopPropagation()} hidden={!showMoreMenu}>
          <Dropdown
            trigger={['click']}
            style={{ zIndex: 1000 }}
            placement="bottomLeft"
            // getPopupContainer={triggerNode => triggerNode.parentNode}
            menu={{
              items: moreMenu,
              onClick: (e) => {
                e.domEvent.stopPropagation();
              }
            }}
          >
            <MoreOutlined className="parentRight" />
          </Dropdown>
        </span>
      </span>
    );
  };

  const delData = (id) => {
    const findIndex = _.findIndex(widgets, (item) => item.i === id);
    if (findIndex !== -1) {
      widgets.splice(findIndex, 1);
      dispatch({});
    }
  };

  const onEdit = (e, id, info) => {
    e.domEvent.preventDefault();
    e.domEvent.stopPropagation();
    if (info.chartType === 'NEW_FUNNEL') {
      props.history.push(`/aimarketer/home/<USER>/analysisCenter/database/funnelAnalysis/${id}`);
    } else if (info.chartType === 'RETENTION_ANALYSIS') {
      props.history.push(`/aimarketer/home/<USER>/analysisCenter/database/retentionAnalysis/${id}`);
    } else if (info.chartType === 'EVENT_ANALYSIS') {
      props.history.push(`/aimarketer/home/<USER>/analysisCenter/database/eventAnalysis/${id}`);
    } else {
      props.history.push(`/aimarketer/home/<USER>/database/edit/${id}?fullScreen=${true}`);
    }
  };

  // 当布局变更调用保存接口
  const menu = (id, showEdit, info) => {
    return [
      CheckAuth.checkAuth('aim_chart_anaysis_edit') &&
        showEdit && {
          label: <span>{t('analysisCenter-Xk9mN2pQr5vT')}</span>,
          key: 'edit',
          onClick: (e) => onEdit(e, id, info)
        },
      CheckAuth.checkAuth('aim_chart_board_edit') && {
        label: <span>{t('analysisCenter-Dvn12kOHYd6j')}</span>,
        key: 'del',
        onClick: () => delData(id)
      }
    ];
  };

  const onClickDetail = (id) => {
    props.history.push(`/aimarketer/home/<USER>/database/detail/${id}?fullScreen=${true}`);
  };

  const onLayoutChange = async (layout, layouts) => {
    run(layout, layouts);
  };

  const fn = (layout, layouts) => {
    dispatch({
      selectValue: {
        ...selectValue,
        layouts,
        widgets: layout
      }
    });
    isGetLayouts.current = false;
  };

  /**
   * @description: 通过设置 options.debounceWait，进入防抖模式，此时如果频繁触发 run 或者 runAsync，则会以防抖策略进行请求。
   * @param {*} run 运行函数
   * @return {*}
   */
  const { run } = useRequest(fn, {
    debounceWait: 2000,
    manual: true
  });
  // const onResizeStop = (layout) => {
  //   dispatch({
  //     selectValue: {
  //       ...selectValue,
  //       layouts: {
  //         ...selectValue.layouts,
  //         lg: [...layout]
  //       },
  //       widgets: layout
  //     }
  //   });
  // };

  useEffect(() => {
    const save = async () => {
      if (selectValue.id) {
        // 调用保存接口
        await DashboardService.saveChartBoardData({
          ...selectValue
        });
      }
    };
    CheckAuth.checkAuth('aim_chart_board_edit') && save();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(selectValue)]);

  const onVisibilityChange = (isVisible, n) => {
    if (isVisible && !_.isEqual(_.uniq([...loadedComChartIndexArr, parseInt(n.i)]), loadedComChartIndexArr)) {
      setLoadedComChartIndexArr((prevState) => {
        return _.uniq([...prevState, parseInt(n.i)]);
      });
    }
  };

  const onResizeStart = () => {
    setDomLoading(true);
  };

  const onResizeEnd = (layouts, preEvent, nextEvent) => {
    setTimeout(() => setDomLoading(false), 2000);
    setItemWidth(nextEvent.w);
  };

  return (
    <div className="dataDashboardList">
      <div className="sider">
        <div hidden={searchValue === ''} className="siderToolbar">
          <Search value={searchValue} onChange={(e) => setSearchValue(e.target.value)} />
        </div>
        <div hidden={searchValue !== ''} className="siderToolbar">
          <span className="title">{getMenuTitle(props.meunData, props.history.location.pathname)}</span>
          <div>
            {/* <Icon style={{ cursor: 'pointer' }} onClick={() => setSearchValue(' ')} className="iconStyle" type="search" /> */}
            <CheckAuth code="aim_chart_board_edit">
              <Dropdown
                menu={{
                  items: [
                    {
                      label: (
                        <span
                          onClick={() =>
                            dispatch({
                              folderModalVisible: true,
                              folderValue: {}
                            })
                          }
                        >
                          {t('analysisCenter-41IeZITMzeqf')}
                        </span>
                      ),
                      key: t('analysisCenter-41IeZITMzeqf')
                    },
                    {
                      label: (
                        <span
                          onClick={() =>
                            dispatch({
                              dashboardModalVisible: true,
                              dashboardValue: {}
                            })
                          }
                        >
                          {t('analysisCenter-Rs4tU6vW8xY')}
                        </span>
                      ),
                      key: t('analysisCenter-Rs4tU6vW8xY')
                    }
                  ]
                }}
              >
                <PlusCircleOutlined style={{ cursor: 'pointer' }} className="iconStyle" />
              </Dropdown>
            </CheckAuth>
            <Dropdown
              menu={{
                items: [
                  {
                    label: (
                      <span onClick={() => dispatch({ expandedKeys: [] })}>{t('analysisCenter-Ab2cD4eF6gH')}</span>
                    ),
                    key: t('analysisCenter-Ab2cD4eF6gH')
                  },
                  {
                    label: (
                      <span
                        onClick={() =>
                          dispatch({
                            expandedKeys: allCategoryList.map((n) => `folder.${n.id}`)
                          })
                        }
                      >
                        {t('analysisCenter-Ij8kL0mN2pQ')}
                      </span>
                    ),
                    key: t('analysisCenter-Ij8kL0mN2pQ')
                  }
                ]
              }}
            >
              <MoreOutlined style={{ cursor: 'pointer' }} className="iconStyle" />
            </Dropdown>
          </div>
        </div>
        <Spin spinning={loading}>
          <DirectoryTree
            // showIcon
            width={240}
            onSelect={(selectedKeys) => dispatch({ selectedKeys })}
            expandedKeys={expandedKeys}
            onExpand={(expandedKeys) => dispatch({ expandedKeys })}
            selectedKeys={selectedKeys}
            titleRender={(item) => treeTitle(item)}
            switcherIcon={<DownOutlined />}
            treeData={menuList}
          >
            {/* {renderTreeNodes(menuList)} */}
          </DirectoryTree>
        </Spin>
      </div>
      <div className="content">
        <div className="contentTitle">
          <div hidden={selectValue.id === undefined}>
            <span className="title">{selectValue.boardName}</span>
            <CheckAuth code="aim_chart_board_edit">
              <Button onClick={() => dispatch({ chartModalVisible: true })} type="link" style={{ marginRight: 16 }}>
                <PlusCircleOutlined style={{ fontSize: 16 }} />
                {t('analysisCenter-yALATAMieLE9')}
              </Button>
            </CheckAuth>
            <Button
              onClick={() =>
                props.history.push(
                  `/aimarketer/home/<USER>/dataDashboard/fullscreen/${selectedKeys[0]}?fullScreen=true`
                )
              }
              type="link"
              style={{ marginRight: 16 }}
            >
              <FullscreenOutlined style={{ fontSize: 16 }} />
              {t('analysisCenter-LT9vshSss27V')}
            </Button>
          </div>
          {/* <div><Button type="primary">{t('analysisCenter-Xy3zA5bC7dE')}</Button></div> */}
        </div>
        {!selectValue.id && !getLoading && (
          <div className="emptyStyle">
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={t('analysisCenter-Za1bC3dE5fG')} />
          </div>
        )}
        <Spin spinning={getLoading}>
          <ResponsiveReactGridLayout
            {...defaultProps}
            layouts={layouts}
            className={!CheckAuth.checkAuth('aim_chart_board_edit') ? 'disableResize' : ''}
            isDraggable={!!CheckAuth.checkAuth('aim_chart_board_edit')}
            isResizable
            style={{ width: 'calc(100% - 10px)' }}
            onLayoutChange={onLayoutChange}
            onResizeStart={onResizeStart}
            onResizeStop={onResizeEnd}
          >
            {widgets.map((l) => {
              if (_.isEmpty(chartList)) return null;
              const info = chartList.find((n) => n.id === parseInt(l.i));
              if (!info) {
                return (
                  <div className="noInfo" key={l.i} data-grid={l}>
                    <div className="toolbar">
                      <div className="left" />
                      <div className="right">
                        <Dropdown
                          placement="bottomRight"
                          // getPopupContainer={(triggerNode) => triggerNode.parentNode}
                          menu={{ items: menu(l.i, false, info) }}
                        >
                          <Button type="default" icon={<MoreOutlined />} size="small" shape="circle" />
                        </Dropdown>
                      </div>
                    </div>
                    <div className="noInfoContent">{t('analysisCenter-Hi7jK9lM1nO')}</div>
                  </div>
                );
              }
              let time = '';
              if (info.dateTimeStamp) {
                if (info.dateTimeStamp.beginDate) {
                  time += `${dayjs(info.dateTimeStamp.beginDate).format('YYYY-MM-DD HH:mm:ss')} ~ `;
                }
                if (info.dateTimeStamp.endDate) {
                  time += dayjs(info.dateTimeStamp.endDate).format('YYYY-MM-DD HH:mm:ss');
                }
              }

              return (
                <div className="hasInfo" key={l.i} data-grid={l}>
                  <div className="toolbar">
                    <div className="left">
                      <div className="title">{info.name}</div>
                      <div className="time">{time}</div>
                    </div>
                    <div className="right">
                      <FullscreenOutlined
                        onClick={() => {
                          onClickDetail(l.i);
                        }}
                        style={{ fontSize: 16, marginRight: 10 }}
                      />
                      <Dropdown placement="bottomRight" menu={{ items: menu(l.i, true, info) }}>
                        <Button type="default" icon={<MoreOutlined />} size="small" shape="circle" />
                      </Dropdown>
                    </div>
                  </div>
                  <div style={{ height: 'calc(100% - 60px)' }} className={l.w >= 8 ? 'lgWrap' : 'smWrap'}>
                    <VisibilitySensor
                      partialVisibility
                      key={l.i}
                      onChange={(isVisible) => onVisibilityChange(isVisible, l)}
                    >
                      {_.includes(loadedComChartIndexArr, parseInt(l.i)) ? (
                        <ComChart info={info} domLoading={domLoading} itemWidth={itemWidth} />
                      ) : (
                        <div style={{ width: '100%', height: '100%' }} />
                      )}
                    </VisibilitySensor>
                  </div>
                </div>
              );
            })}
          </ResponsiveReactGridLayout>
        </Spin>
      </div>
      {chartModalVisible && <AddChart state={state} dispatch={dispatch} />}
      {folderModalVisible && <AddFolder state={state} dispatch={dispatch} />}
      {dashboardModalVisible && <AddChartBoard state={state} dispatch={dispatch} />}
    </div>
  );
};

export default connect(stateToProps)(DataDashboard);
