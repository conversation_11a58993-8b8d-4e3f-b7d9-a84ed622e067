import { Icon as LegacyIcon } from '@ant-design/compatible';
import { LeftOutlined, RightOutlined, ShrinkOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import Slider from 'react-slick';
import AnalysisCenterService from 'service/analysisCenterService';
import DashboardService from 'service/dashboardService';
import 'slick-carousel/slick/slick-theme.css';
import 'slick-carousel/slick/slick.css';
import { t } from 'utils/translation';
import ComChart from '../../database/comChart/index';
import './index.scss';

const rowHeight = 100;
const ResponsiveReactGridLayout = WidthProvider(Responsive);

export default (props) => {
  const [chartList, setChartList] = useState([]);
  const [data, setData] = useState([]);
  const [info, setInfo] = useState({});
  const [play, setPlay] = useState(true);
  const ref = useRef(null);
  useEffect(() => {
    (async () => {
      const info = await DashboardService.getChartBoardById(props.match.params.id);
      const maxHeight = Math.floor((document.body.clientHeight - 120) / rowHeight);
      if (info.widgets && info.widgets.length > 0) {
        let dataList = info.widgets.map((w) => ({
          ...w,
          static: true,
          isResizable: false,
          h: w.h > maxHeight ? maxHeight : w.h
        }));
        const _data = [];
        while (dataList.length > 0) {
          const filter = [];
          const other = [];
          dataList.forEach((n) => {
            if (n.y + n.h <= maxHeight) {
              filter.push(n);
            } else {
              other.push(n);
            }
          });
          if (filter.length > 0) {
            _data.push(filter);
          }
          if (other.length > 0) {
            const sortList = _.cloneDeep(other);
            sortList.sort((a, b) => a.y - b.y);
            dataList = other.map((w) => ({ ...w, y: w.y - sortList[0].y }));
          } else {
            dataList = [];
          }
        }
        setData(_data);
      }
      const chartList = await AnalysisCenterService.getChartConfigAllList([
        {
          operator: 'EQ',
          propertyName: 'deptId',
          value: window.getDeptId()
        }
      ]);
      setChartList(chartList);
      setInfo(info);
    })();
  }, []);

  const setPlayMethod = () => {
    setPlay(!play);
    if (!play) {
      ref.current.slickPlay();
    } else {
      ref.current.slickPause();
    }
  };

  const settings = {
    dots: true,
    // infinite: true,
    autoplay: true,
    autoplaySpeed: 5000
  };

  return (
    <div className="fullscreenStyle">
      <div className="title">
        <div className="left">{info.boardName}</div>
        <div className="right">
          <LeftOutlined className="iconStyle" onClick={() => ref.current.slickPrev()} />
          <LegacyIcon
            className="iconStyle"
            type={play ? 'pause' : 'caret-right'}
            onClick={setPlayMethod}
            style={{ margin: '0 10px' }}
          />
          <RightOutlined className="iconStyle" onClick={() => ref.current.slickNext()} />
          <Button onClick={() => props.history.go(-1)} type="link" style={{ marginLeft: 20 }}>
            <ShrinkOutlined style={{ fontSize: 16 }} />
            {t('analysisCenter-srIbM3voQpZg')}
          </Button>
        </div>
      </div>
      <Slider {...settings} className="carouselStyle" ref={ref}>
        {data.map((item, i) => (
          <div key={i}>
            <ResponsiveReactGridLayout
              cols={{ lg: 12, md: 12, sm: 12, xs: 12, xxs: 12 }}
              rowHeight={rowHeight}
              margin={[5, 5]}
            >
              {item.map((l) => {
                if (_.isEmpty(chartList)) return null;
                const info = chartList.find((n) => n.id === parseInt(l.i));
                if (!info) {
                  return (
                    <div className="noInfo" key={l.i} data-grid={l}>
                      <div className="toolbar">
                        <div className="left" />
                        <div className="right" />
                      </div>
                      <div className="noInfoContent">{t('analysisCenter-Hi7jK9lM1nO')}</div>
                    </div>
                  );
                }
                return (
                  <div className="hasInfo" key={l.i} data-grid={l}>
                    <div className="toolbar">
                      <div className="left">{info.name}</div>
                      <div className="right" />
                    </div>
                    <div style={{ height: 'calc(100% - 50px)' }}>
                      <ComChart info={info} />
                    </div>
                  </div>
                );
              })}
            </ResponsiveReactGridLayout>
          </div>
        ))}
      </Slider>
    </div>
  );
};
