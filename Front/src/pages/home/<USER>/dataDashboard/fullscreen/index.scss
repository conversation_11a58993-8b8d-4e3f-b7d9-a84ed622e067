.fullscreenStyle{
    height: 100vh;
    .title{
        display: flex;
        justify-content: space-between;
        // align-items: center;
        height: 46px;
        .left{
            margin-top: 18px;
            margin-left: 10px;
            font-family: PingFangSC-Medium;
            font-size: 20px;
            color: rgba(0,0,0,0.85);
            line-height: 20px;
        }
        .right{
            margin-top: 12px;
            .iconStyle{
                color: #000000;
                font-size: 16px;
                cursor: pointer;
            }
        }
    }
    .carouselStyle{
        height: calc(100% - 86px);
        width: 100%;
    }
    .hasInfo{
        border: 1px solid white;
        width: 100%;
        height: 100%;
        background-color: #fff;
        .toolbar{
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 50px;
          // background-color: white;
          .left{
            font-size: 16px;
            padding-left: 10px;
            font-weight: 500;
          }
          .right{
            padding-right: 10px;
            display: flex;
            align-items: center;
          }
        }
    }
    .noInfo{
        border: 1px solid white;
        width: 100%;
        height: 100%;
        background-color: #fff;
    .toolbar{
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 50px;
        // background-color: white;
        .left{
            font-size: 16px;
            padding-left: 10px;
            font-weight: 500;
        }
        .right{
            padding-right: 10px;
        }
    }
    .noInfoContent{
        height: calc(100% - 40px);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: red;
    }
    }
}