import { CloseOutlined } from '@ant-design/icons';
import { Checkbox, Divider, Modal } from 'antd';
import _ from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import ProjectMangerService from 'service/projectMangerService';
import { t } from 'utils/translation';
import './index.scss';

const _projectMangerService = new ProjectMangerService();

const ExportSegment = (props) => {
  const [segmentList, setSegmentList] = useState([]);
  const [sourceData, setSourceData] = useState({});
  // const [selectedList, setSelectedList] = useState([]);
  useEffect(() => {
    let ignore = false;
    const init = async () => {
      const reData = await _projectMangerService.getDownloadSegment({});
      !ignore && setSegmentList(reData?.schemaList);
      setSourceData(reData);
      // !ignore && setSelectedList(_.filter(reData?.schemaList, item => item.selected) || []);
    };
    init();
    return () => {
      ignore = true;
    };
  }, []);

  const handleOk = () => {
    props.handleExportSegmentOk &&
      props.handleExportSegmentOk({
        ...sourceData,
        schemaList: _.filter(segmentList, (item) => item.selected)
      });
  };

  const handleCancel = () => {
    props.handleExportSegmentCancel && props.handleExportSegmentCancel();
  };

  const onListItemChange = (data) => {
    const findIndex = _.findIndex(segmentList, (item) => item.schemaId === data.schemaId);
    const _segmentList = _.cloneDeep(segmentList);
    _segmentList[findIndex] = {
      ..._segmentList[findIndex],
      selected: !_segmentList[findIndex].selected
    };
    setSegmentList(_segmentList);
  };

  const onChangeSelectAll = () => {
    if (_.find(segmentList, (item) => item.selected === false)) {
      // 全选
      setSegmentList(_.map(segmentList, (item) => ({ ...item, selected: true })));
    } else {
      // 全不选
      setSegmentList(_.map(segmentList, (item) => ({ ...item, selected: false })));
    }
  };

  const handleClose = (data) => {
    const _segmentList = _.cloneDeep(segmentList);
    const findIndex = _.findIndex(_segmentList, (item) => item.schemaId === data.schemaId);
    _segmentList[findIndex] = {
      ..._segmentList[findIndex],
      selected: false
    };
    setSegmentList(_segmentList);
  };

  const selectedSegment = useMemo(() => {
    return _.filter(segmentList, (item) => item.selected);
  }, [segmentList]);

  return (
    <Modal
      className="exportSegmentModal"
      title={t('setting-f1YWJb96j0HQ')}
      width={1000}
      open
      onOk={handleOk}
      onCancel={handleCancel}
    >
      <div className="wrapper">
        <div className="selectArea">
          <div className="title">{t('setting-oDzDhbAUP3dZ')}</div>
          <div className="header">
            <Checkbox checked={!_.find(segmentList, (item) => item.selected === false)} onChange={onChangeSelectAll}>
              <span>{t('setting-OczztbOJE5WH')}</span>
              <span>{t('setting-yKcilm2Dl7vi')}</span>
            </Checkbox>
          </div>
          <div className="listWrapper">
            {_.map(segmentList, (item) => {
              return (
                <div className="listItem" key={item.name}>
                  <Checkbox checked={item.selected} onChange={() => onListItemChange(item)}>
                    <div title={item.name}>{item.name}</div>
                    <div title={item.displayName}>{item.displayName}</div>
                  </Checkbox>
                </div>
              );
            })}
          </div>
        </div>
        <Divider type="vertical" />
        <div className="selectedArea">
          <div className="title">
            {t('setting-qQFRmOqhO0E5', { count: selectedSegment?.length || 0, total: segmentList.length })}
          </div>
          <div className="header">
            <span>{t('setting-OczztbOJE5WH')}</span>
            <span>{t('setting-yKcilm2Dl7vi')}</span>
            <CloseOutlined
              onClick={() => {
                handleClose();
              }}
            />
          </div>
          <div className="listWrapper">
            {_.map(selectedSegment, (item) => {
              return (
                <div className="listItem" key={item.name}>
                  <div title={item.name}>{item.name}</div>
                  <div title={item.displayName}>{item.displayName}</div>
                  <CloseOutlined
                    onClick={() => {
                      handleClose(item);
                    }}
                  />
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ExportSegment;
