import { InputNumber, Modal, Radio, Space, message } from 'antd';
import React, { useEffect, useState } from 'react';

import ProjectMangerService from 'service/projectMangerService';
import { t } from 'utils/translation';

import './index.scss';

const projectMangerService = new ProjectMangerService();

const SegmentCountLimit = (props) => {
  const [limitType, setLimitType] = useState('none');
  const [limitCount, setLimitCount] = useState(1);

  const [segmentCount, setSegmentCount] = useState(0);

  useEffect(() => {
    const init = async () => {
      const segmentRes = await projectMangerService.getBusiness({
        businessType: 'SegmentBusinessConfig'
      });

      setLimitType(props.testSegmentCustLimit === -1 ? 'none' : 'limit');
      setLimitCount(props.testSegmentCustLimit || -1);
      setSegmentCount(segmentRes.config.saveSegmentMaxCount);
    };
    init();
  }, [props.testSegmentCustLimit]);

  const handleCancel = () => {
    props.handleSegmentLimitCancel && props.handleSegmentLimitCancel();
  };

  const handleOk = () => {
    if (!limitCount) {
      message.error(t('setting-njvBg03WoTji'));
      return;
    }
    if (limitCount > segmentCount) {
      message.error('超过设置的最大客群数，请重新编辑规则');
      return;
    }
    props.handleSegmentLimitOk &&
      props.handleSegmentLimitOk({
        count: limitCount,
        limitType
      });
  };

  const onLimitTypeChange = (e) => {
    setLimitType(e.target.value);
    if (e.target.value === 'limit') {
      setLimitCount(1);
    }
  };

  const onLimitCountChange = (val) => {
    setLimitCount(val);
  };

  return (
    <Modal className="segmentCountLimitModal" title="设置测试客群人数限制" open onOk={handleOk} onCancel={handleCancel}>
      <div>
        <div className="mb-[16px]">
          <Radio.Group value={limitType} onChange={onLimitTypeChange}>
            <Space direction="vertical" size="large">
              <Radio value="none">无限制</Radio>
              <Radio value="limit">限制人数 最大不可超过 {segmentCount}</Radio>
            </Space>
          </Radio.Group>
        </div>
        {limitType === 'limit' && (
          <InputNumber
            value={limitCount}
            min={1}
            max={segmentCount}
            className=" w-[40%]"
            onChange={onLimitCountChange}
          />
        )}
      </div>
    </Modal>
  );
};

export default SegmentCountLimit;
