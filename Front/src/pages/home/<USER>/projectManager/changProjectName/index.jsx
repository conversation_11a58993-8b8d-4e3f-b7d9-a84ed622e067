import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Input } from 'antd';
import { t } from 'utils/translation';
import './index.scss';

class ChangeProjectName extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  handleOk = () => {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        this.props.changModalOk(values.newProjectName);
      }
    });
  };

  handleCancel = () => {
    this.props.changModalCancel();
    this.props.form.setFieldsValue({
      newProjectName: this.props.projectName
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    return (
      <div>
        <Modal
          title={t('setting-q3UCY94NwOJM')}
          open={this.props.changModalVisible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={600}
          style={{ height: 238 }}
          className="changeProjectNameModal"
        >
          <Form>
            <Form.Item label={t('setting-Fqr6TIb6T9WR')}>
              {getFieldDecorator('newProjectName', {
                initialValue: this.props.projectName,
                validateTrigger: 'onBlur',
                rules: [
                  {
                    required: true,
                    message: t('setting-U9Rwgj410SvT')
                  },
                  {
                    max: 32,
                    message: t('setting-OtHzohKzRqYh')
                  },
                  {
                    pattern: /^[A-Za-z0-9_\-\u4e00-\u9fa5]+$/g,
                    message: t('setting-cFn4FiD0q3f1')
                  }
                ]
              })(<Input placeholder={t('setting-JAZ8BO4IF5W2')} />)}
            </Form.Item>
          </Form>
        </Modal>
      </div>
    );
  }
}
const ChangeProjectNameComponet = Form.create({
  name: 'changeProjectName_login'
})(ChangeProjectName);
export default ChangeProjectNameComponet;
