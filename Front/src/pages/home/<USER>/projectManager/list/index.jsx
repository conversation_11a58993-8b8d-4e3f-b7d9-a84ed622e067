import { Button, Divider, List, message, Popover } from 'antd';
import SystemInfo from 'components/bussinesscoms/systeminfocom/index';
import copy from 'copy-to-clipboard';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { Component } from 'react';
import ProjectMangerService from 'service/projectMangerService';
import { t } from 'utils/translation';
import CheckAuth from 'utils/checkAuth';
import ChangeProjectName from '../changProjectName';
import ExportSegment from '../exportSegment/index';
import SegmentCountLimit from '../segmentsCountLimit';
import './index.scss';

const projectMangerService = new ProjectMangerService();

class projectManagerList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      projectId: '',
      projectName: '',
      superAdmin: '',
      projectTimeZone: t('setting-RwjxY2UonPGP'),
      createTime: '',
      updateTime: '',
      resBody: '',
      changModalVisible: false,
      createUserName: '',
      updateUserName: '',
      showExportSegment: false,
      showSegmentLimit: false,
      selectedSegmentList: [],
      segmentDownLoadConf: {}
    };
  }

  componentDidMount() {
    const { state } = this.props.location;
    const projectId = state?.projectId || localStorage.getItem('projectId');
    this.getProjectDetails(projectId);
    this.getSuperAdmin();
    this.init();
  }

  init = async () => {
    const reData = await projectMangerService.getDownloadSegment({});
    this.setState({
      selectedSegmentList: _.filter(reData.schemaList, (item) => item.selected),
      segmentDownLoadConf: reData
    });
  };

  getProjectDetails = async (projectId) => {
    const res = await projectMangerService.reqGetProjectDetailsById(projectId);
    this.setState({
      projectId: res.id,
      projectName: res.name,
      createTime: dayjs(res.createTime).format('YYYY-MM-DD HH:mm:ss'),
      updateTime: dayjs(res.updateTime).format('YYYY-MM-DD HH:mm:ss'),
      createUserName: res.createUserName,
      updateUserName: res.updateUserName,
      resBody: res
    });
  };

  saveNewProjectName = async (projectId) => {
    await projectMangerService.saveNewProjectName(projectId);
  };

  getSuperAdmin = async () => {
    const res = await projectMangerService.getSuperAdmin();
    this.setState({
      superAdmin: res.user.email
    });
  };

  openModel = () => {
    this.setState({
      changModalVisible: true
    });
  };

  openExportSegmentModel = () => {
    this.setState({
      showExportSegment: true
    });
  };

  openSegmentLimitModel = () => {
    this.setState({
      showSegmentLimit: true
    });
  };

  changModalOk = (newName) => {
    this.setState(
      {
        changModalVisible: false,
        projectName: newName
      },
      () => {
        const theResBody = this.state.resBody;
        theResBody.name = this.state.projectName;
        this.saveNewProjectName(theResBody).then(() => {
          message.success(t('setting-4h5MSesddDix'));
        });
      }
    );
  };

  changModalCancel = () => {
    this.setState({
      changModalVisible: false
    });
  };

  cobyBtn = () => {
    copy(this.state.projectId);
    message.success(t('setting-udAuV3OAoXIw'));
  };

  handleExportSegmentOk = async (data) => {
    const res = await projectMangerService.saveDownloadSegment(data);
    res && message.success(t('setting-xmoKNd86q7wO'), 1);
    res && this.setState({ showExportSegment: false });
    this.init();
  };

  handleSegmentLimitOk = async (data) => {
    const reData = await projectMangerService.getDownloadSegment({});

    const params = _.cloneDeep(reData);

    if (data.limitType === 'none') {
      params.testSegmentCustLimit = -1;
    } else {
      params.testSegmentCustLimit = data.count;
    }

    const res = await projectMangerService.saveDownloadSegment(params);
    res && message.success(t('setting-xmoKNd86q7wO'), 1);
    res && this.setState({ showSegmentLimit: false });
    this.init();
  };

  render() {
    return (
      <div className="projectManagerList">
        <header>
          <h1>{t('setting-M5tmFa7RtNaH')}</h1>
        </header>
        <div className="projectList">
          <div className="projectListHeader">{t('setting-rfMaFFys3dhZ')}</div>
          <Divider style={{ marginBottom: 0 }} />
          <List>
            <List.Item
              actions={[
                <CheckAuth code="aim_project_mgr_edit">
                  <Button type="primary" onClick={this.openModel}>
                    {t('setting-8uZOpmy06K7v')}
                  </Button>
                </CheckAuth>
              ]}
            >
              <List.Item.Meta title={t('setting-7csEG6nO1PZm')} description={this.state.projectName} />
            </List.Item>
            <List.Item
              actions={[
                <Button
                  type="primary"
                  onClick={() => {
                    this.cobyBtn();
                  }}
                >
                  {t('setting-j6jJNiGyVTpt')}
                </Button>
              ]}
            >
              <List.Item.Meta title={t('setting-D2ly8bprBBBP')} description={this.state.projectId} />
            </List.Item>
            <List.Item>
              <List.Item.Meta title={t('setting-ja04jKYYooZG')} description={this.state.projectTimeZone} />
            </List.Item>
            <List.Item>
              <List.Item.Meta title={t('setting-5lSMeYEu1M4z')} description={this.state.superAdmin} />
            </List.Item>
            <List.Item>
              <List.Item.Meta title={t('setting-2L5frDaY8o8D')} description={this.state.createUserName} />
            </List.Item>
            <List.Item>
              <List.Item.Meta title={t('setting-ecxTVHixY7pT')} description={this.state.createTime} />
            </List.Item>
            <List.Item>
              <List.Item.Meta title={t('setting-JBa0TgYrtcN9')} description={this.state.updateUserName} />
            </List.Item>
            <List.Item>
              <List.Item.Meta title={t('setting-vBXGEeXwuYwM')} description={this.state.updateTime} />
            </List.Item>
          </List>
          {/* <div
            className="projectListFooter"
          >
            <span className="projectAuthor">更新者:&nbsp;{this.state.updater}</span>
            <span className="projectUpdateTime">更新时间:&nbsp;{this.state.updateTime}</span>
          </div> */}
        </div>
        <div className="downloadSegmentList">
          <div className="projectListHeader">{t('setting-RFVByCtx735O')}</div>
          <Divider style={{ marginBottom: 0 }} />
          <List>
            <List.Item
              actions={[
                <CheckAuth code="aim_segment_download_setting">
                  <Button type="primary" onClick={this.openExportSegmentModel}>
                    {t('setting-e6aiLIyFUWtK')}
                  </Button>
                </CheckAuth>
              ]}
            >
              <List.Item.Meta
                title={t('setting-sjVfUMXEHQXz')}
                description={
                  <Popover
                    trigger="hover"
                    overlayClassName="downloadSegmentPop"
                    placement="top"
                    title={t('setting-sjVfUMXEHQXz')}
                    content={_.join(
                      _.map(this.state.selectedSegmentList, (item) => item.displayName),
                      ','
                    )}
                  >
                    {_.join(
                      _.map(this.state.selectedSegmentList, (item) => item.displayName),
                      ','
                    )}
                  </Popover>
                }
              />
            </List.Item>
            <List.Item
              actions={[
                <CheckAuth code="aim_segment_download_setting">
                  <Button type="primary" onClick={this.openSegmentLimitModel}>
                    {t('setting-e6aiLIyFUWtK')}
                  </Button>
                </CheckAuth>
              ]}
            >
              <List.Item.Meta
                title={t('setting-gfz1VJKY4f7J')}
                description={
                  this.state.segmentDownLoadConf.testSegmentCustLimit === -1
                    ? t('setting-evqe0fDoJZWh')
                    : this.state.segmentDownLoadConf.testSegmentCustLimit
                }
              />
            </List.Item>
          </List>
        </div>
        <ChangeProjectName
          changModalVisible={this.state.changModalVisible}
          changModalOk={this.changModalOk}
          changModalCancel={this.changModalCancel}
          projectName={this.state.projectName}
        />
        {this.state.showExportSegment && (
          <ExportSegment
            handleExportSegmentOk={this.handleExportSegmentOk}
            handleExportSegmentCancel={() => this.setState({ showExportSegment: false })}
          />
        )}
        {this.state.showSegmentLimit && (
          <SegmentCountLimit
            testSegmentCustLimit={this.state.segmentDownLoadConf.testSegmentCustLimit}
            handleSegmentLimitOk={this.handleSegmentLimitOk}
            handleSegmentLimitCancel={() => this.setState({ showSegmentLimit: false })}
          />
        )}
        <SystemInfo />
      </div>
    );
  }
}
export default projectManagerList;
