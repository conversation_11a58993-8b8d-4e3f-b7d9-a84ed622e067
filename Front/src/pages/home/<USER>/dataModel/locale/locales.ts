export default {
  cn: {
 'dataCenter-dataModel-addBtnText': '新建',
  'dataCenter-dataModel-search-placeholder': '搜索',
  'dataCenter-dataModel-dropdown-view': '查看',
  'dataCenter-dataModel-dropdown-edit': '编辑',
  'dataCenter-dataModel-modelName': '模型名称',
  'dataCenter-dataModel-modelDisplayName': '模型显示名',
  'dataCenter-dataModel-memo': '备注',
  'dataCenter-dataModel-factTable': '事实表',
  'dataCenter-dataModel-idType': 'ID 类型',
  'dataCenter-dataModel-updater': '更新者',
  'dataCenter-dataModel-updateTime': '更新时间',
  'dataCenter-dataModel-actions': '操作',
  'dataCenter-dataModel-edit': '编辑',
  'dataCenter-dataModel-view': '查看',
  'dataCenter-dataModel-delete': '删除',
  'dataCenter-dataModel-more': '更多',
  'dataCenter-dataModel-deleteConfirmTitle': '删除',
  'dataCenter-dataModel-deleteConfirmContent': '您将删除数据模型：{{name}}',
  'dataCenter-dataModel-deleteConfirmWarning': '该操作无法撤销，请谨慎操作。',
  'dataCenter-dataModel-deleteConfirmOk': '确定删除',
  'dataCenter-dataModel-deleteConfirmCancel': '取消',
  'dataCenter-dataModel-listTitle': '数据模型列表',
  'dataCenter-dataModel-idTypeFilter': 'ID类型',
  'dataCenter-dataModel-factTableFilter': '事实表',
   "dataCenter-SRfaifiS7iTv": "模型名称",
  "dataCenter-rEDBW2He43e7": "模型显示名",
  "dataCenter-JxDeYfLYmvho": "一对一",
  "dataCenter-Oji8mLt6tGF8": "一对多",
  "dataCenter-JJnydVS7vIMI": "多对一",
  "dataCenter-BshqnByW4HyV": "多对多",
  "dataCenter-Dv66AcnbQcvi": "事件表信息未填写完整",
  "dataCenter-U04XDZaurBHw": "ID类型不能相同",
  "dataCenter-uUdv0GeFytCW": "保存成功",
  "dataCenter-8yIRIuAbI0Hp": "编辑数据模型",
  "dataCenter-Kgv1CH41fPln": "新建数据模型",
  "dataCenter-9wwTsXjpT5Mb": "基础信息",
  "dataCenter-4PuGu0jbH3vm": "所属实体",
  "dataCenter-1e3kjMoJUpUp": "请选择所属实体",
  "dataCenter-i78kxl8ZX0OJ": "请选择",
  "dataCenter-2Js9plkSZ28w": "模型名称",
  "dataCenter-2ooXmIOQpxSr": "请输入模型名称",
  "dataCenter-A001FeUvPJji": "仅允许输入英文+下划线",
  "dataCenter-hAZxoZZTqrOR": "最大长度限制为64位字符",
    "dataCenter-JJ0klJMCloKX": "请输入",
  "dataCenter-zS99sTtdvLl1": "模型显示名",
  "dataCenter-pBDpvcII81G6": "请输入模型显示名",
  "dataCenter-D8FYPIp1DvXC": "仅支持中文、英文、数字、下划线",
  "dataCenter-GKoHZyMHW04l": "备注",
  "dataCenter-3k1VH26KBwIx": "最大长度限制为100位字符",
  "dataCenter-ElC9EqUQYCph": "事实表信息",
  "dataCenter-VYCAYd6diR0E": "表名称",
  "dataCenter-2JkNgJ6fmkWD": "表id：",
  "dataCenter-36sIEHhOwUoL": "请选择表名称",
  "dataCenter-zLJr2b6xQb3k": "ID类型",
  "dataCenter-aD2ND6htY22Q": "请选择",
  "dataCenter-UNHv7IqjURiN": "对应字段",
  "dataCenter-KtyDa0AAH7Nb": "添加ID类型",
  "dataCenter-W3w9wrQrk4hA": "取消",
  "dataCenter-s0hDIjvSJlLv": "确认",
  "dataCenter-lOFsOXXgCkHR": "关系ID",
  "dataCenter-6zywFTOpo3CC": "父逻辑实体",
  "dataCenter-gLOOj02VYSVg": "父实体属性",
    "dataCenter-vdVruqnssony": "数据类型",
  "dataCenter-5VtR9akpd8na": "父对子",
  "dataCenter-p3nr6Ub1H2IU": "子实体",
  "dataCenter-S4XkK2elZmb8": "子实体属性",
  "dataCenter-o93pSof9TgOn": "数据类型",
  "dataCenter-Uot1spDSOR2G": "子对父",
  "dataCenter-jBFI3gjM944A": "更新者",
  "dataCenter-CRLV1jQsylCv": "更新时间",
  "dataCenter-QxfQuCAlkPZ3": "操作",
  "dataCenter-nw2oaO5ttclF": "编辑",
  "dataCenter-6d6VkwJi8m6G": "删除",
  "dataCenter-eyNUJ5h7T6dE": "更多",
  "dataCenter-IlpVPwy3DF99": "数据类型",
  "dataCenter-4unSIzgsSYRN": "字段名称",
  "dataCenter-SVxorfvVDM11": "字段显示名",
  "dataCenter-b5U91KHvz3dZ": "ID类型：",
  "dataCenter-rkQB0hJYPBxf": "删除",
  "dataCenter-DrdAz39PAZMl": "确定删除",
  "dataCenter-ltOC5RI2yk8q": "取消",
  "dataCenter-i8eNZokvaCzL": "删除成功",
  "dataCenter-mmeGC4s1QC2Y": "数据模型",
  "dataCenter-IpdzdHG3ssx8": "数据模型关系",
  "dataCenter-2eJW6QzzmTPN": "新建",
  "dataCenter-QcFRakN6ufg3": "关系",
  "dataCenter-kDbKbomuu9e3": "ID类型",
  "dataCenter-T42MDv89wBFh": "重置定位",
  "dataCenter-kSosL68vax1v": "请添加逻辑实体属性",
  "dataCenter-rAKIeYBlrHSR": "数据类型不匹配，无法建立关联关系",
  "dataCenter-mMoHNnRmnn3J": "父子实体属性重复",
  "dataCenter-hOcleo99RbrX": "保存成功",
  "dataCenter-baSHw3d6s2Jn": "编辑数据关系",
  "dataCenter-iobmnUhyLHKV": "新建数据关系",
  "dataCenter-FbEeETZAhC2k": "父逻辑实体",
  "dataCenter-PXXcNUjuZDr9": "请选择父逻辑实体",
  "dataCenter-kSlYlPgNIy9j": "子逻辑实体",
  "dataCenter-ndYodtwRZndq": "请选择子逻辑实体",
  "dataCenter-ra4c42c7goLC": "父对子",
  "dataCenter-ZuCqqDNBqgN0": "请选择父对子",
  "dataCenter-adyxUqxWAKkx": "子对父",
  "dataCenter-JcqnJ7p4FtM5": "请选择子对父",
  "dataCenter-6y18uSGnqwfq": "父逻辑实体属性",
  "dataCenter-MAW2Wsk8dV81": "请选择",
  "dataCenter-t1k2unDi4WzH": "子逻辑实体属性",
  "dataCenter-c4IArYPytJ3r": "添加逻辑实体属性",
  "dataCenter-y51iLPMdM4B9": "取消",
  "dataCenter-AvDRc8GbIHX9": "确认",
   "dataCenter-JBXSB0j6hQt1": "您将删除关系ID：",
  "dataCenter-n1uRU7cOPb0c": "该操作无法撤销，请谨慎操作。",
  },
  en: {
  'dataCenter-dataModel-addBtnText': 'Create New',
  'dataCenter-dataModel-search-placeholder': 'Search',
  'dataCenter-dataModel-dropdown-view': 'View',
  'dataCenter-dataModel-dropdown-edit': 'Edit',
  'dataCenter-dataModel-modelName': 'Model Name',
  'dataCenter-dataModel-modelDisplayName': 'Display Name',
  'dataCenter-dataModel-memo': 'Notes',
  'dataCenter-dataModel-factTable': 'Fact Table',
  'dataCenter-dataModel-idType': 'ID Type',
  'dataCenter-dataModel-updater': 'Updated By',
  'dataCenter-dataModel-updateTime': 'Update Time',
  'dataCenter-dataModel-actions': 'Actions',
  'dataCenter-dataModel-edit': 'Edit',
  'dataCenter-dataModel-view': 'View',
  'dataCenter-dataModel-delete': 'Delete',
  'dataCenter-dataModel-more': 'More',
  'dataCenter-dataModel-deleteConfirmTitle': 'Delete',
  'dataCenter-dataModel-deleteConfirmContent': 'You are about to delete the data model: {{name}}',
  'dataCenter-dataModel-deleteConfirmWarning': 'This action cannot be undone. Please proceed with caution.',
  'dataCenter-dataModel-deleteConfirmOk': 'Confirm Delete',
  'dataCenter-dataModel-deleteConfirmCancel': 'Cancel',
  'dataCenter-dataModel-listTitle': 'Data Model List',
  'dataCenter-dataModel-idTypeFilter': 'ID Type',
  'dataCenter-dataModel-factTableFilter': 'Fact Table',
  "dataCenter-SRfaifiS7iTv": "Model Name",
  "dataCenter-rEDBW2He43e7": "Display Name",
  "dataCenter-JxDeYfLYmvho": "One to One",
  "dataCenter-Oji8mLt6tGF8": "One to Many",
  "dataCenter-JJnydVS7vIMI": "Many to One",
  "dataCenter-BshqnByW4HyV": "Many to Many",
  "dataCenter-Dv66AcnbQcvi": "Event table information is not complete",
  "dataCenter-U04XDZaurBHw": "ID type cannot be the same",
  "dataCenter-uUdv0GeFytCW": "Save successfully",
  "dataCenter-8yIRIuAbI0Hp": "Edit Data Model",
  "dataCenter-Kgv1CH41fPln": "New Data Model",
  "dataCenter-9wwTsXjpT5Mb": "Basic Information",
  "dataCenter-4PuGu0jbH3vm": "Belonging Entity",
  "dataCenter-1e3kjMoJUpUp": "Please select the belonging entity",
  "dataCenter-i78kxl8ZX0OJ": "Please select",
  "dataCenter-2Js9plkSZ28w": "Model Name",
  "dataCenter-2ooXmIOQpxSr": "Please enter the model name",
  "dataCenter-A001FeUvPJji": "Only allow English + underscore",
  "dataCenter-hAZxoZZTqrOR": "Maximum length limit of 64 characters",
    "dataCenter-JJ0klJMCloKX": "Please enter",
  "dataCenter-zS99sTtdvLl1": "Display Name",
  "dataCenter-pBDpvcII81G6": "Please enter the display name",
  "dataCenter-D8FYPIp1DvXC": "Only support Chinese, English, numbers, and underscores",
  "dataCenter-GKoHZyMHW04l": "Notes",
  "dataCenter-3k1VH26KBwIx": "Maximum length limit of 100 characters",
  "dataCenter-ElC9EqUQYCph": "Fact Table Information",
  "dataCenter-VYCAYd6diR0E": "Table Name",
  "dataCenter-2JkNgJ6fmkWD": "Table id：",
  "dataCenter-36sIEHhOwUoL": "Please select the table name",
  "dataCenter-zLJr2b6xQb3k": "ID Type",
  "dataCenter-aD2ND6htY22Q": "Please select",
  "dataCenter-UNHv7IqjURiN": "Corresponding Field",
  "dataCenter-KtyDa0AAH7Nb": "Add ID Type",
  "dataCenter-W3w9wrQrk4hA": "Cancel",
  "dataCenter-s0hDIjvSJlLv": "Confirm",
  "dataCenter-lOFsOXXgCkHR": "Relation ID",
  "dataCenter-6zywFTOpo3CC": "Parent Logical Entity",
  "dataCenter-gLOOj02VYSVg": "Parent Entity Attribute",
    "dataCenter-vdVruqnssony": "Data Type",
  "dataCenter-5VtR9akpd8na": "Parent to Child",
  "dataCenter-p3nr6Ub1H2IU": "Child Entity",
  "dataCenter-S4XkK2elZmb8": "Child Entity Attribute",
  "dataCenter-o93pSof9TgOn": "Data Type",
  "dataCenter-Uot1spDSOR2G": "Child to Parent",
  "dataCenter-jBFI3gjM944A": "Updated By",
  "dataCenter-CRLV1jQsylCv": "Updated Time",
  "dataCenter-QxfQuCAlkPZ3": "Operation",
  "dataCenter-nw2oaO5ttclF": "Edit",
  "dataCenter-6d6VkwJi8m6G": "Delete",
  "dataCenter-eyNUJ5h7T6dE": "More",
  "dataCenter-IlpVPwy3DF99": "Data Type",
  "dataCenter-4unSIzgsSYRN": "Field Name",
  "dataCenter-SVxorfvVDM11": "Field Display Name",
  "dataCenter-b5U91KHvz3dZ": "ID Type：",
  "dataCenter-rkQB0hJYPBxf": "Delete",
  "dataCenter-DrdAz39PAZMl": "Confirm Delete",
  "dataCenter-ltOC5RI2yk8q": "Cancel",
  "dataCenter-i8eNZokvaCzL": "Delete Success",
  "dataCenter-mmeGC4s1QC2Y": "Data Model",
  "dataCenter-IpdzdHG3ssx8": "Data Model Relation",
  "dataCenter-2eJW6QzzmTPN": "New",
  "dataCenter-QcFRakN6ufg3": "Relation",
  "dataCenter-kDbKbomuu9e3": "ID Type",
  "dataCenter-T42MDv89wBFh": "Reset Position",
  "dataCenter-kSosL68vax1v": "Please add logical entity attributes",
  "dataCenter-rAKIeYBlrHSR": "Data type mismatch, cannot establish association relationship",
  "dataCenter-mMoHNnRmnn3J": "Parent and child entity attributes are repeated",
  "dataCenter-hOcleo99RbrX": "Save successfully",
  "dataCenter-baSHw3d6s2Jn": "Edit Data Relation",
  "dataCenter-iobmnUhyLHKV": "New Data Relation",
  "dataCenter-FbEeETZAhC2k": "Parent Logical Entity",
  "dataCenter-PXXcNUjuZDr9": "Please select the parent logical entity",
  "dataCenter-kSlYlPgNIy9j": "Child Logical Entity",
  "dataCenter-ndYodtwRZndq": "Please select the child logical entity",
  "dataCenter-ra4c42c7goLC": "Parent to Child",
  "dataCenter-ZuCqqDNBqgN0": "Please select the parent to child",
  "dataCenter-adyxUqxWAKkx": "Child to Parent",
  "dataCenter-JcqnJ7p4FtM5": "Please select the child to parent",
  "dataCenter-6y18uSGnqwfq": "Parent Logical Entity Attribute",
  "dataCenter-MAW2Wsk8dV81": "Please select",
  "dataCenter-t1k2unDi4WzH": "Child Logical Entity Attribute",
  "dataCenter-c4IArYPytJ3r": "Add Logical Entity Attribute",
  "dataCenter-y51iLPMdM4B9": "Cancel",
  "dataCenter-AvDRc8GbIHX9": "Confirm",
  "dataCenter-JBXSB0j6hQt1": "You will delete the relation ID：",
  "dataCenter-n1uRU7cOPb0c": "This action cannot be undone. Please proceed with caution.",
  }
};
