// @import 'assets/css/variable.scss';

.chartDetail {
  width: 100%;
  height: 100vh;

  header {
    display: flex;
    height: 65px;
    margin: 0 -24px;
    padding: 0 24px;
    justify-content: space-between;
    align-items: center;

    h1 {
      font-size: 24px;
      margin: 0;
    }

    .exit {
      display: flex;
      align-items: center;
      cursor: pointer;

      .anticon-fullscreen-exit {
        margin-right: 5px;
      }
    }
  }

  .mainContent {
    display: flex;
    align-items: center;
    background-color: #fff;
    min-height: 460px;
    height: calc(100% - 78px);

    .column {
      width: 100%;
    }
  }

  .time {
    font-size: 12px;
    color: rgba(0, 0, 0, .45);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}