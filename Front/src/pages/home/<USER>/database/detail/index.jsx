import { FullscreenExitOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { getString, getTime } from 'pages/home/<USER>/userGroup/detail/config';
import React, { useEffect, useState } from 'react';
import AnalysisCenterService from 'service/analysisCenterService';
import { t } from 'utils/translation';
import ComChart from '../comChart/index';
import './index.scss';

export default (props) => {
  const [resultData, setResultData] = useState({});
  const isActivityAnalysis = localStorage.getItem('isActivityAnalysis');
  // 初始化获取
  useEffect(() => {
    const compute = async () => {
      try {
        let info = await AnalysisCenterService.getChartConfig(props.match.params.id);
        const timeFilter = GetQueryString('timeFilter');
        if (timeFilter) {
          localStorage.setItem('timeFilter', JSON.stringify(timeFilter));
          const timeFilterArr = timeFilter.split(',');
          info = {
            ...info,
            dateRange2: [
              {
                type: 'ABSOLUTE',
                timestamp: timeFilterArr[0],
                times: 30,
                timeTerm: 'DAY',
                isPast: true
              },
              {
                type: 'ABSOLUTE',
                timestamp: timeFilterArr[1],
                times: 0,
                timeTerm: 'DAY',
                isPast: true
              }
            ]
          };
        }
        setResultData(info);
        // eslint-disable-next-line no-empty
      } catch (error) {}
    };
    compute();
    // 获取参数
  }, []);

  function GetQueryString(name) {
    const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
    const r = window.location.search.substr(1).match(reg); // search,查询？后面的参数，并匹配正则
    if (r != null) return unescape(r[2]);
    return null;
  }

  const exitFullScreen = async () => {
    if (isActivityAnalysis) {
      // 设置loaclstorage
      localStorage.setItem('isActivityAnalysis', 'true');
    }
    props.history.goBack();
  };

  const renderTime = (data) => {
    if (_.isEmpty(data)) return;
    const { chartType, chartConfig, dateRange2, updateTime } = data;

    const analysisTypes = ['NEW_FUNNEL', 'RETENTION_ANALYSIS', 'EVENT_ANALYSIS'];
    let timeTerm = null;

    if (analysisTypes.includes(chartType)) {
      timeTerm =
        chartConfig?.retentionAnalysisDataQuery?.analysisDate?.timeTerm ||
        chartConfig?.funnelDataQuery?.timeConfig?.timeTerm ||
        chartConfig?.eventAnalysis?.timeTerm;
    }

    const dateString1 = getString(dateRange2[0], !timeTerm);
    const dateString2 = getString(dateRange2[1], !timeTerm);
    const timestamp1 = getTime(dateRange2[0]);
    const timestamp2 = getTime(dateRange2[1]);
    const formattedHoverTime1 = dayjs(timestamp1).format('YYYY-MM-DD HH:mm:ss');
    const formattedHoverTime2 = dayjs(timestamp2).format('YYYY-MM-DD HH:mm:ss');
    const formattedUpdateTime = dayjs(updateTime).format('YYYY-MM-DD HH:mm:ss');

    return (
      <div className="time flex gap-24">
        <Tooltip
          overlayStyle={{ maxWidth: 'fit-content' }}
          title={
            <>
              <div>{`${dateString1} ~ ${dateString2} | ${formattedHoverTime1} ~ ${formattedHoverTime2}`}</div>
              <div>{`${t('analysisCenter-K8puj492Oa5b')} ${formattedUpdateTime}`}</div>
            </>
          }
        >
          <span>{`${dateString1} ~ ${dateString2}`}</span>{' '}
          <span>{`${t('analysisCenter-K8puj492Oa5b')} ${formattedUpdateTime}`}</span>
        </Tooltip>
      </div>
    );
  };

  return (
    <div className="chartDetail">
      <header>
        <div>
          <h1>{resultData.name || t('analysisCenter-g84Uk8PG2ZAY')}</h1>
          {renderTime(resultData)}
        </div>
        <div className="exit" onClick={exitFullScreen}>
          <FullscreenExitOutlined />
          {t('analysisCenter-YSPO5KN1v4M5')}
        </div>
      </header>
      <div className="mainContent">
        {resultData.id && <ComChart info={{ ...resultData, chartType: resultData.chartType }} />}
      </div>
    </div>
  );
};
