/* eslint-disable vars-on-top */

/* eslint-disable no-var */

import { t } from 'utils/translation';

export const configElements = {
  // id: {
  //   type: 'select',
  //   label: '图表ID',
  //   width: 12,
  //   operator: 'IN',
  //   componentOptions: {
  //     mode: 'tags',
  //     // autoClearSearchValue: 'tags',
  //     allowClear: true,
  //     placeholder: '请输入'
  //   }
  // },
  name: {
    type: 'input',
    label: t('analysisCenter-3EP0QT660Ua5'),
    width: 12,
    operator: 'LIKE',
    componentOptions: {
      allowClear: true,
      placeholder: t('analysisCenter-K9v8nXwIR5Ee')
    }
  },
  chartType: {
    type: 'select',
    label: t('analysisCenter-SnT11drRuupA'),
    width: 12,
    operator: 'IN',
    componentOptions: {
      allowClear: true,
      placeholder: t('analysisCenter-6yJN0EXOjCEw'),
      showSearch: true,
      mode: 'multiple',
      options: [
        {
          name: t('analysisCenter-GY9B23S6glKm'),
          text: t('analysisCenter-GY9B23S6glKm'),
          value: 'TABLE,PIE,BAR,LINE,AREA,COLUMN,RADAR,DONUT,CARD,SCATTER,BUBBLE,FUNNEL'
        },
        { name: t('analysisCenter-gjdHxUlYPQkD'), text: t('analysisCenter-gjdHxUlYPQkD'), value: 'NEW_FUNNEL' },
        { name: t('analysisCenter-Z8DeicMvwDpE'), text: t('analysisCenter-Z8DeicMvwDpE'), value: 'RETENTION_ANALYSIS' },
        { name: t('analysisCenter-G17b9AJY32x3'), text: t('analysisCenter-G17b9AJY32x3'), value: 'EVENT_ANALYSIS' }
      ]
    }
  },
  createTime: {
    type: 'dateRange',
    label: t('analysisCenter-ypwLuS5jSoov'),
    width: 12,
    operator: 'DATE_BETWEEN',
    componentOptions: {
      allowClear: true
    }
  }
  // customCampaign: {
  //   type: 'select',
  //   label: '营销活动',
  //   width: 12,
  //   operator: 'IN',
  //   componentOptions: {
  //     allowClear: true,
  //     showSearch: true,
  //     filterOption: false,
  //     placeholder: '请选择营销活动'
  //   }
  // },
  // customChartBoard: {
  //   type: 'treeSelect',
  //   label: '数据看板',
  //   width: 12,
  //   operator: 'IN',
  //   componentOptions: {
  //     allowClear: true,
  //     showSearch: true,
  //     filterOption: false,
  //     placeholder: '请选择数据看板'
  //   }
  // }
  // user: {
  //   type: 'select',
  //   label: '创建账号',
  //   width: 12,
  //   operator: 'EQ',
  //   componentOptions: {
  //     allowClear: true,
  //     showSearch: true,
  //     filterOption: (input, option) => {
  //       return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  //     },
  //     placeholder: '请选择创建账号'
  //   }
  // }
};

export const initParam = {
  page: 1,
  search: [],
  size: 12,
  sorts: [{ propertyName: 'updateTime', direction: 'desc' }]
};

export const initPagination = {
  total: 0,
  current: 1,
  pageSize: 12,
  pageSizeOptions: [12, 20, 50, 100]
};

export const userContrastTypeList = ['SEGMENT', 'CAMPAIGN', 'USER_TAG', 'CAMPAIGN_NODE'];

export const userContrastTypeMap = [
  {
    name: t('analysisCenter-EH2TitljoIvb'),
    dataType: 'SEGMENT'
  },
  {
    name: t('analysisCenter-gPuDSkzSyL0e'),
    dataType: 'CAMPAIGN'
  },
  {
    name: t('analysisCenter-fo49EZyPcsrQ'),
    dataType: 'CAMPAIGN_NODE'
  },
  {
    name: t('analysisCenter-OJKSP6thchzT'),
    dataType: 'USER_TAG'
  }
];

export const dragType = {
  rowList: 'ROW',
  columnList: 'COLUMN',
  valueList: 'VALUE',
  legendList: 'LEGEND',
  axisList: 'AXIS',
  xvalueList: 'X_VALUE',
  yvalueList: 'Y_VALUE'
};

export const chartType = {
  table: 'TABLE',
  column: 'COLUMN',
  donut: 'DONUT',
  card: 'CARD',
  line: 'LINE',
  bubble: 'BUBBLE',
  funnel: 'FUNNEL',
  group_table: 'GROUP_TABLE'
};

export const tableSchemaStatisticsType = {
  LONG: ['UNIQUE_COUNT', 'COUNT', 'AVG', 'MAX', 'MIN', 'SUM'],
  INTEGER: ['UNIQUE_COUNT', 'COUNT', 'AVG', 'MAX', 'MIN', 'SUM'],
  INT: ['UNIQUE_COUNT', 'COUNT', 'AVG', 'MAX', 'MIN', 'SUM'],
  DOUBLE: ['UNIQUE_COUNT', 'COUNT', 'AVG', 'MAX', 'MIN', 'SUM'],
  STRING: ['UNIQUE_COUNT', 'COUNT'],
  DATE: ['UNIQUE_COUNT', 'COUNT', 'MAX', 'MIN'],
  HIVE_DATE: ['UNIQUE_COUNT', 'COUNT', 'MAX', 'MIN'],
  DATETIME: ['UNIQUE_COUNT', 'COUNT', 'MAX', 'MIN'],
  TIMESTAMP: ['UNIQUE_COUNT', 'COUNT', 'MAX', 'MIN'],
  HIVE_TIMESTAMP: ['UNIQUE_COUNT', 'COUNT', 'MAX', 'MIN']
};

export const pieSchemaStatisticsType = {
  LONG: ['UNIQUE_COUNT', 'COUNT', 'SUM'],
  INTEGER: ['UNIQUE_COUNT', 'COUNT', 'SUM'],
  INT: ['UNIQUE_COUNT', 'COUNT', 'SUM'],
  DOUBLE: ['UNIQUE_COUNT', 'COUNT', 'SUM'],
  STRING: ['UNIQUE_COUNT', 'COUNT'],
  DATE: ['UNIQUE_COUNT', 'COUNT'],
  HIVE_DATE: ['UNIQUE_COUNT', 'COUNT'],
  DATETIME: ['UNIQUE_COUNT', 'COUNT'],
  TIMESTAMP: ['UNIQUE_COUNT', 'COUNT'],
  HIVE_TIMESTAMP: ['UNIQUE_COUNT', 'COUNT']
};

export const lineStatisticsType = {
  TOTAL: t('analysisCenter-3qMO8CzEzchS'),
  AVG: t('analysisCenter-NQ60XPdNkn0E'),
  MAX: t('analysisCenter-CuSMTek7xsOE'),
  MIN: t('analysisCenter-xaVaBffXwzqx')
};

export const columnStatisticsType = {
  TOTAL: t('analysisCenter-TlUStL3lHotj'),
  AVG: t('analysisCenter-iBnWuwhL5Nw0'),
  MAX: t('analysisCenter-l0mS6zbCroqk'),
  MIN: t('analysisCenter-DWtv5jS911lp')
};

export const tableDataTypeConfig = {
  NUMBER: ['LONG', 'INTEGER', 'INT', 'DOUBLE'],
  DATE: ['DATE', 'DATETIME', 'TIMESTAMP', 'HIVE_DATE', 'HIVE_TIMESTAMP'],
  STRING: ['STRING']
};

export const dateStepTerm = [
  {
    value: 'MONTH',
    label: t('analysisCenter-5Bm6ZZ1XFpnb')
  },
  {
    value: 'WEEK',
    label: t('analysisCenter-HSZuxFTHkELx')
  },
  {
    value: 'DAY',
    label: t('analysisCenter-lHoMOLJNNRWY')
  },
  {
    value: 'HOUR',
    label: t('analysisCenter-oDhw7Oej6v6R')
  },
  {
    value: 'MINUTE',
    label: t('analysisCenter-52GLPVMjdYTq')
  }
];

export const timeType = ['DATE', 'DATETIME', 'TIMESTAMP', 'HIVE_DATE', 'HIVE_TIMESTAMP'];
export const indexType = ['DATE', 'DATETIME', 'TIMESTAMP', 'STRING', 'BOOL', 'HIVE_DATE', 'HIVE_TIMESTAMP'];
export const timeFilter = ['DATE', 'DATETIME', 'TIMESTAMP', 'HIVE_DATE', 'HIVE_TIMESTAMP'];
export const formatNumber = (numbers, numberConfig) => {
  let number = !isNaN(Number(numbers)) ? Number(numbers) : 0;
  const format = numberConfig?.format || 'NUMBER';
  const places = numberConfig?.decimalSeparator ?? 0;
  const thousand = numberConfig?.thousandsSeparator ? ',' : '';
  if (format === 'PERCENT') {
    number *= 100;
  }

  const symbol = '';

  const decimal = '.';
  const negative = number < 0 ? '-' : '';
  const i = `${parseInt((number = Math.abs(+number || 0).toFixed(places)), 10)}`;
  var j = (j = i.length) > 3 ? j % 3 : 0;
  return (
    symbol +
    negative +
    (j ? i.substr(0, j) + thousand : '') +
    i.substr(j).replace(/(\d{3})(?=\d)/g, `$1${thousand}`) +
    (places
      ? decimal +
        Math.abs(number - i)
          .toFixed(places)
          .slice(2)
      : '')
  );
};
