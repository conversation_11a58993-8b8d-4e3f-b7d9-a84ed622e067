/* eslint-disable react-hooks/exhaustive-deps */
import axios from 'axios';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import AnalysisCenterService from 'service/analysisCenterService';
import EventAnalysis from 'service/eventAnalysis.js';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { t } from 'utils/translation';
import EventArea from '../../eventAnalysis/charts/areaChart';
import EventColumn from '../../eventAnalysis/charts/column';
import EventLine from '../../eventAnalysis/charts/lineChart';
import EventPie from '../../eventAnalysis/charts/pieChart';
import FunnelColumn from '../../funnelAnalysis/charts/column/index';
import FunnelPCT from '../../funnelAnalysis/charts/columnPCT/index';
import FunnelLine from '../../funnelAnalysis/charts/line/index';
import RetentionTable from '../../retentionAnalysis/charts/readOnTable';
import Retention<PERSON><PERSON> from '../../retentionAnalysis/charts/renderCharts';
import Bubble<PERSON>hart from '../create/components/bubble/chart/chart';
import CardChart from '../create/components/card/chart/chart';
import ColumnChart from '../create/components/column/chart/chart';
import DonutChart from '../create/components/donut/chart/chart';
import FunnelChart from '../create/components/funnel/chart/chart';
import GroupingTableChart from '../create/components/group_table/chart/chart';
import LineChart from '../create/components/line/chart/chart';
import TableChart from '../create/components/table/chart/chart';
import './index.scss';

const CancelToken = axios.CancelToken;

export default ({ info, forceCalc, domLoading, itemWidth, chartConfigLogs = [], batchId = '' }) => {
  const { chartType } = info;
  const [dataSource, setDataSource] = useState({});
  const [loading, setLoading] = useState(false);
  const [tableColumn, setTableColumn] = useState(null);
  const [lineChartList, setLineChartList] = useState(null);
  const [bussinessData, setBussinessData] = useState({});
  useEffect(() => {
    init({ forceCalc: false });
  }, []);

  const init = (obj) => {
    let ignore = false;
    let timer = null;
    let cancel = () => {};
    const cancelToken = new CancelToken((c) => {
      // executor 函数接收一个 cancel 函数作为参数
      cancel = c;
    });

    const doRequest = async (data, queryType) => {
      try {
        if (
          chartConfigLogs.length &&
          data?.chartConfig?.campaignFilters &&
          batchId !== t('analysisCenter-esjKieWfQTQn')
        ) {
          data.chartConfig.campaignFilters = data.chartConfig?.campaignFilters.map((item, index) => {
            if (index === 0) {
              item.logList = chartConfigLogs;
            }
            return item;
          });
        }
        const timeFilter = JSON.parse(localStorage.getItem('timeFilter'));
        if (timeFilter) {
          const timeFilterArr = timeFilter.split(',');
          data = {
            ...data,
            dateRange2: [
              {
                type: 'ABSOLUTE',
                timestamp: timeFilterArr[0],
                times: 30,
                timeTerm: 'DAY',
                isPast: true
              },
              {
                type: 'ABSOLUTE',
                timestamp: timeFilterArr[1],
                times: 0,
                timeTerm: 'DAY',
                isPast: true
              }
            ]
          };
        }

        let result = null;
        if (
          data.chartType !== 'NEW_FUNNEL' &&
          data.chartType !== 'RETENTION_ANALYSIS' &&
          data.chartType !== 'EVENT_ANALYSIS'
        ) {
          result = await AnalysisCenterService.calcChartResult(data, cancelToken);
        } else {
          if (data.chartType === 'NEW_FUNNEL') data.chartConfig.funnelDataQuery.queryType = queryType;
          if (data.chartType === 'EVENT_ANALYSIS') {
            result = await EventAnalysis.calcEventChartResult(data, cancelToken);
          } else {
            result = await FunnelAnalysis.calcFunnelChartResult(data, cancelToken);
          }
        }
        const { body, header } = result;

        if (header.code === 0) {
          // if (body.errorMsg) {
          //   message.error(res.errorMsg);
          // }
          if (!_.isEmpty(body.chartResult) && _.isEmpty(body.chartResult.dataMap)) {
            const _dataSource = { ...body.chartResult };
            if (!ignore) {
              if (
                (body.chartType === 'LINE' || body.chartType === 'COLUMN') &&
                _dataSource.rowList &&
                _dataSource.rowList.length
              ) {
                _dataSource.rowList.forEach((item) => {
                  item.displayName = _.isEmpty(_dataSource.axis.stackField)
                    ? item.METRIC_NAME
                    : `${item[_dataSource.axis.stackField.dataIndex]}-${item.METRIC_NAME}`;
                });
              }
              if (body.chartType === 'GROUP_TABLE') {
                _dataSource.columnList = body.chartResult?.columnNameList || [];
                _dataSource.valueList = body.chartResult?.rowsValueList || [];
              }
              setDataSource(_dataSource);

              setLineChartList(body.chartResult.lineChartList);
              setLoading(false);
            }
          } else if (!_.isEmpty(body)) {
            const _dataSource = { ...body };
            if (!ignore) {
              const res = await EventAnalysis.getBusiness({
                businessType: 'SegmentBusinessConfig'
              });
              setBussinessData(res);
              setDataSource(_dataSource);
              setLoading(false);
            }
          } else {
            if (!ignore) {
              setDataSource({ rowList: [] });
              setLoading(false);
            }
          }

          // let chartType = body?.chartResult?.
        } else if (header.code === 210) {
          timer = setTimeout(async () => {
            data.forceCalc = false;
            await doRequest(data, obj && 'TABLE');
          }, 3000);
          // setLoading(false);
        } else if (header.code === 1) {
          setDataSource({ rowList: null });
          setLoading(false);
        }
      } catch (error) {
        setLoading(false);
      }
    };

    (async () => {
      if (!info.chartType) {
        setDataSource({ rowList: [] });
      } else {
        setLoading(true);

        await doRequest({ ...info, ...obj }, 'TABLE');

        if (info?.chartConfig?.funnelDataQuery?.chartDisplayType === 'LINE_CHART') {
          const tableColumn = await FunnelAnalysis.queryFunnelTableColumn(info.chartConfig.funnelDataQuery);
          setTableColumn(tableColumn.columns);
        }
      }
    })();
    return () => {
      ignore = true;
      timer && clearTimeout(timer);
      cancel();
    };
  };

  useEffect(() => {
    // console.log(info, 'info');
    if (forceCalc && forceCalc > 0) {
      init({ forceCalc: true });
    }
  }, [forceCalc]);

  const renderChart = () => {
    switch (chartType) {
      case 'TABLE':
        return <TableChart loading={loading} dataSource={dataSource} />;
      case 'COLUMN':
        return <ColumnChart loading={loading} dataSource={dataSource} />;
      case 'CARD':
        return <CardChart loading={loading} dataSource={dataSource} />;
      case 'DONUT':
        return <DonutChart loading={loading} dataSource={dataSource} filterCount={info.chartConfig?.limit || 10} />;
      case 'LINE':
        return <LineChart loading={loading} dataSource={dataSource} />;
      case 'FUNNEL':
        return <FunnelChart loading={loading} dataSource={dataSource} />;
      case 'BUBBLE':
        return <BubbleChart loading={loading} dataSource={dataSource} />;
      case 'GROUP_TABLE':
        return (
          <GroupingTableChart
            grandchildData={info.chartConfig?.isMerged || 'flated'}
            loading={loading}
            dataSource={dataSource}
          />
        );
      case 'RETENTION_ANALYSIS':
        // type类型为 TREND_CHART, CHANGE_CHART, RETAINED_TABLE;
        const { dimensionGroup, displayType: oldDisplayType } = info.chartConfig.retentionAnalysisDataQuery;
        const props = {
          loading,
          chartResult: dataSource,
          chartSelect: t('analysisCenter-esjKieWfQTQn'),
          chartDisplayType:
            oldDisplayType === 'TREND_CHART' || info?.displayType === 'TREND_CHART' ? 'TREND_CHART' : 'CHANGE_CHART',
          dimensionGroup
        };
        if (oldDisplayType === 'TREND_CHART' || info?.displayType === 'TREND_CHART') {
          return <RetentionChart showChartList={dataSource?.trendChartDataList} readOn {...props} />;
        } else if (oldDisplayType === 'CHANGE_CHART' || info?.displayType === 'CHANGE_CHART') {
          return <RetentionChart showChartList={dataSource?.changeChartDataList} readOn {...props} />;
        } else if (oldDisplayType === 'RETAINED_TABLE' || info?.displayType === 'RETAINED_TABLE') {
          const stepList = info.chartConfig.retentionAnalysisDataQuery.stepList;
          const columnList = dataSource.columnList;
          const dataList = dataSource.dataList;
          return <RetentionTable stepList={stepList} columnList={columnList} dataList={dataList} showTotal={false} />;
        }
        return null;
      case 'NEW_FUNNEL':
        // type类型为 COLUMN_CHART,PERCENT_COLUMN_CHART,LINE_CHART
        const oldChartDisplayType = info.chartConfig.funnelDataQuery.chartDisplayType;
        const { displayGroupNames } = info.chartConfig.funnelDataQuery;
        if (oldChartDisplayType === 'COLUMN_CHART' || info?.displayType === 'COLUMN_CHART') {
          return (
            <FunnelColumn
              loading={loading}
              chartList={dataSource.chartList}
              readOn
              selectedRowKeys={displayGroupNames}
            />
          );
        } else if (oldChartDisplayType === 'PERCENT_COLUMN_CHART' || info?.displayType === 'PERCENT_COLUMN_CHART') {
          return (
            <FunnelPCT loading={loading} chartList={dataSource.chartList} readOn selectedRowKeys={displayGroupNames} />
          );
        } else if (oldChartDisplayType === 'LINE_CHART' || info?.displayType === 'LINE_CHART') {
          return (
            <FunnelLine
              tableColumn={tableColumn}
              loading={loading}
              chartList={dataSource.chartList}
              readOn
              selectedRowKeys={displayGroupNames}
              lineChartList={lineChartList}
            />
          );
        }
        return null;
      case 'EVENT_ANALYSIS':
        const chartData = _.cloneDeep(dataSource?.chartResult?.dataMap);

        const { config } = bussinessData;
        const eventData = config?.eventBusiTable?.config;

        if (!_.isEmpty(chartData)) {
          const groupStatus = chartData.every((item) => Object.keys(item).length === 6);

          chartData.forEach((item) => {
            const index = _.findIndex(
              Object.keys(item),
              (o) => o !== eventData.times && o !== eventData.event_name && o !== 'res'
            );
            item.eventValue = `${item[eventData.event_name]}·${item.metricName}`;
            item[eventData.event_name] = `${item.step}·${item[eventData.event_name]}·${item.metricName}`;
            item.nameType = eventData.event_name;
            item.timeType = eventData.times;
            item.res = Number(item.res.toFixed(2));
            item.displayName = groupStatus
              ? `${item[eventData.event_name]}-${item[Object.keys(item)[index]]}`
              : `${item[eventData.event_name]}`;
            item.nameValue = groupStatus ? item[Object.keys(item)[index]] : undefined;
          });
        }

        // type类型为 LINE,COLUMN,PIE,AREA
        const eventChartType = info.chartConfig.eventAnalysis.showStyle;
        const eventInfo = info.chartConfig.eventAnalysis;
        const _metric = info.chartConfig.eventAnalysis.metric;

        eventInfo.axisConf.forEach((item, index) => {
          item.step = _metric[index]?.step;
          item.metricName = _metric[index]?.metricName;
        });

        if (eventChartType === 'LINE') {
          return <EventLine loading={loading} chartList={chartData} readOn eventInfo={eventInfo} />;
        } else if (eventChartType === 'COLUMN') {
          return <EventColumn loading={loading} chartList={chartData} readOn eventInfo={eventInfo} />;
        } else if (eventChartType === 'PIE') {
          return (
            <EventPie
              tableColumn={tableColumn}
              loading={domLoading || loading}
              chartList={chartData}
              readOn
              eventInfo={eventInfo}
              itemWidth={itemWidth}
            />
          );
        } else if (eventChartType === 'AREA') {
          return (
            <EventArea tableColumn={tableColumn} loading={loading} chartList={chartData} readOn eventInfo={eventInfo} />
          );
        }
        return null;
      default:
        return null;
    }
  };

  return <>{renderChart()}</>;
};
