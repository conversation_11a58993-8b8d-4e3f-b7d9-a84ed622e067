import { CloseOutlined } from '@ant-design/icons';
import { useDebounce } from '@umijs/hooks';
import { Button, Drawer, Input, message, Popover, Table } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import CampaignV2Service from 'service/CampaignV2Service';
import FunnelAnalysis from 'service/funnelAnalysis';
import { useDeepCompareEffect } from 'utils/customhooks';
import { t } from 'utils/translation';
import { FlowCanvas } from 'wolf-static-cpnt';
import './index.scss';

const campaignV2Service = new CampaignV2Service();

const ActivityNodeDrawer = (props) => {
  const [name, setName] = useState('');
  const [type] = useState('');
  const [tableParams, setTableParams] = useState({
    size: 5,
    page: 1,
    search: [
      {
        connector: 'AND',
        propertyName: 'phase',
        operator: 'IN',
        value: 'ENABLE,STOPPED'
      },
      {
        connector: 'AND',
        propertyName: 'scenario.code',
        operator: 'EQ',
        value: props.scenario.code
      },
      { connector: 'AND', propertyName: 'name', operator: 'LIKE', value: '' },
      { connector: 'AND', propertyName: 'type', operator: 'EQ', value: '' },
      {
        operator: 'EQ',
        propertyName: 'deptId',
        value: window.getDeptId()
      }
    ],
    sorts: [
      {
        direction: 'desc',
        propertyName: 'id'
      }
    ]
  });

  const dataSourceCache = useRef([]);
  const debouncedName = useDebounce(name, 300);

  const [loading, setLoading] = useState(false);

  const [dataSource, setDataSource] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState(() => {
    dataSourceCache.current = props.dataSource.campaignNodeList;
    return _.map(props.dataSource.campaignNodeList, (item) => item.id) ?? [];
  });

  const [selectedBatch, setSelectedBatch] = useState(() => {
    let logList = [];
    _.map(props.dataSource.campaignNodeList, (item) => {
      if (!_.isEmpty(item.logList)) {
        logList = _.concat(logList, item.logList);
      }
    });
    return logList;
  });

  const [selectedNodes, setSelectedNodes] = useState(() => {
    return props.dataSource.campaignNodeList[0]?.logList[0]?.selectedFlows ?? [];
  });

  const [currentPopover, setCurrentPopover] = useState('');

  // 搜索内容变更触发搜索
  const onNameChange = (e) => {
    setName(e.target.value);
  };

  useEffect(() => {
    // 当type和name变化的时候请求列表数据
    setTableParams((prevState) => {
      return {
        ...prevState,
        search: [
          {
            connector: 'AND',
            propertyName: 'phase',
            operator: 'IN',
            value: 'ENABLE,STOPPED'
          },
          {
            connector: 'AND',
            propertyName: 'scenario.code',
            operator: 'EQ',
            value: props.scenario.code
          },
          {
            connector: 'AND',
            propertyName: 'name',
            operator: 'LIKE',
            value: debouncedName ?? ''
          },
          {
            connector: 'AND',
            propertyName: 'type',
            operator: 'EQ',
            value: type ?? ''
          },
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: window.getDeptId()
          }
        ]
      };
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, debouncedName]);

  useDeepCompareEffect(() => {
    let ignore = false;
    const queryData = async () => {
      setLoading(true);
      const data = await campaignV2Service.query(tableParams);
      dataSourceCache.current = _.concat(dataSourceCache.current, data.content);
      setDataSource(data.content);
      setTotalCount(data.totalElements);
      setLoading(false);
    };

    !ignore && queryData();
    return () => {
      ignore = true;
    };
  }, [tableParams]);

  const columns = [
    {
      title: t('analysisCenter-UJ1DAEPjIIa5'),
      dataIndex: 'name',
      width: 200
    },
    {
      title: t('analysisCenter-zPiGauRMgb5H'),
      dataIndex: ['scenario', 'name'],
      width: 110
    },
    {
      title: t('analysisCenter-55A87DVRPwrr'),
      dataIndex: 'campaignType',
      render: (text) => (text ? _.filter(typeList, (v) => v.value === text)[0].text : '-'),
      width: 110
    },
    {
      title: t('analysisCenter-So7Sk6BPzfC1'),
      dataIndex: 'phase',
      render: (text) => {
        return (
          <div className="status">
            {/* <span className="circle" style={{ backgroundColor: _.filter(phaseList, (v) => v.value === text)[0]?.color }} /> */}
            {text ? _.filter(phaseList, (v) => v.value === text)[0]?.name : '-'}
          </div>
        );
      },
      width: 110
    },
    {
      title: t('analysisCenter-wi4sZqpNnYbc'),
      dataIndex: 'calcRule',
      render: (text) => (text ? _.filter(ruleList, (v) => v.value === text)[0].text : '-'),
      width: 110
    },
    {
      title: t('analysisCenter-2qRabFncRp8F'),
      dataIndex: 'beginTime',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 150,
      sorter: true
    },
    {
      title: t('analysisCenter-SqAvRXjE7o1g'),
      dataIndex: 'endTime',
      render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
      width: 150,
      sorter: true
    }
  ];

  const handleTableChange = (page, filter, sorter) => {
    const { current, pageSize } = page;
    const direction = sorter.order ? sorter.order.substr(0, sorter.order.length - 3) : tableParams.sorts[0].direction;
    const propertyName = sorter.field ? (Array.isArray(sorter.field) ? sorter.field.join('.') : sorter.field) : 'id';
    setTableParams({
      ...tableParams,
      sorts: [{ direction, propertyName }],
      page: current,
      size: pageSize
    });
  };

  const onSelectChange = (selectedRowKeys) => {
    if (selectedRowKeys.length >= 10) {
      message.warn(t('analysisCenter-4glyZJlomrZn'));
      return;
    }
    setSelectedRowKeys(selectedRowKeys);
    // 清空选择的节点，清空流程数据
    setSelectedBatch([]);
    setSelectedNodes([]);
  };

  const deleteSelectedKey = (key) => {
    const findIndex = _.findIndex(selectedRowKeys, (item) => item === key);
    const _selectedRowKeys = _.clone(selectedRowKeys);
    _selectedRowKeys.splice(findIndex, 1);
    setSelectedRowKeys(_selectedRowKeys);
    // 删除活动对应的批次
    const _selectedBatch = _.filter(selectedBatch, (item) => item.campaignId !== key);
    setSelectedBatch(_selectedBatch);
    setSelectedNodes([]);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    type: 'radio',
    preserveSelectedRowKeys: true
  };

  const onClose = () => {
    props.onClose && props.onClose();
  };

  // 提交
  const onSubmit = () => {
    if (_.isEmpty(selectedNodes)) {
      message.warn(t('analysisCenter-xOqVE9D6oeRx'));
      return;
    }
    const submitData = _.map(selectedRowKeys, (item) => {
      const targetData = _.find(dataSourceCache.current, (data) => data.id === item);
      // 给目标数据增加批次字段
      const logList = _.filter(selectedBatch, (batch) => batch.campaignId === item);
      targetData.logList = logList;
      logList[0].selectedFlows = selectedNodes;
      return targetData;
    });
    props.onSubmit && props.onSubmit(submitData, props.dataSource.filterId);
  };

  // 选择活动批次
  const selectActivityBatch = (data) => {
    setCurrentPopover(data);
  };

  // 关闭
  const clearCurrentPopover = () => {
    setCurrentPopover('');
  };

  // 选中的批次变化
  const onSelectedBatchChange = (data) => {
    // 先找出选中的批次中是否有当前活动的，如果有替换，如果没有则添加
    const findIndex = _.findIndex(selectedBatch, (item) => item.campaignId === data[0].campaignId);
    if (findIndex === -1) {
      setSelectedBatch(_.concat(selectedBatch, data));
    } else {
      const _selectedBatch = _.cloneDeep(selectedBatch);
      _selectedBatch.splice(findIndex, 1, data[0]);
      setSelectedBatch(_selectedBatch);
    }
    // 清空选中节点
    setSelectedNodes([]);
  };

  const onFlowCanvasClick = (data) => {
    if (selectedNodes.length >= 10) {
      message.warn(t('analysisCenter-yUMs1e5oDePe'), 1);
      return;
    }
    if (!_.find(selectedNodes, (item) => item.nodeId === data.nodeId)) {
      const _selectedNodes = _.cloneDeep(selectedNodes);
      _selectedNodes.push(data);
      setSelectedNodes(_selectedNodes);
    }
  };

  const onClickDelete = (data) => {
    const _findIndex = _.findIndex(selectedNodes, (item) => item.nodeId === data.nodeId);
    if (_findIndex !== -1) {
      const _selectedNodes = _.cloneDeep(selectedNodes);
      _selectedNodes.splice(_findIndex, 1);
      setSelectedNodes(_selectedNodes);
    }
  };

  const dataProvider = {
    getAtTimeNodesData: (flows) => {
      return campaignV2Service.getNextTimeInAtTimeNodes(flows);
    },
    getEventCountLogsByProjectId: async () =>
      FunnelAnalysis.getEventCountLogsByProjectId({
        projectId: localStorage.getItem('projectId')
      })
  };

  return (
    <Drawer
      className="activityNodeDrawer"
      title={t('analysisCenter-8MjmZOcDHmWl')}
      destroyOnClose
      placement="bottom"
      height={500}
      closable={false}
      // onClose={() => props.onClose(false)}
      open
      // bodyStyle={{ height: '100%' }}
    >
      <div className="filterWrapper">
        <Input className="nameSearch" placeholder={t('analysisCenter-iidrnXDH2khh')} onChange={onNameChange} />
        {/* <Select
          showTime
          onChange={onTypeChange}
          allowClear
          maxTagCount={10}
          value={type}
          style={{ minWidth: 150 }}
        >
          {typeList.map((item) => (
            <Option
              value={item.value}
              key={item.key}
            >
              {item.text}
            </Option>
          ))}
        </Select> */}
      </div>
      <div className="mainContent">
        <div className="table1">
          <Table
            columns={columns}
            dataSource={dataSource}
            bordered={false}
            loading={loading}
            onChange={handleTableChange}
            scroll={{ x: 500, y: 280 }}
            rowSelection={rowSelection}
            rowKey="id"
            size="middle"
            pagination={{
              current: tableParams.page,
              total: totalCount,
              defaultPageSize: tableParams.size,
              showQuickJumper: true,
              showSizeChanger: true,
              showLessItems: true,
              pageSizeOptions: ['5', '10', '20']
            }}
          />
        </div>
        <section className="FlowCanvas-wrap">
          {_.map(selectedRowKeys, (item) => {
            const selectedData = _.findLast(dataSourceCache.current, (data) => data.id === item);
            const selectedBatchData = _.find(selectedBatch, (batch) => batch.campaignId === item);
            return (
              <div className="selectedItem" key={item}>
                <div className="selectedItemLeft">
                  <div className="selectedItemName">{selectedData?.name}</div>
                  <div className="selectedBatchInfo">{`${t('analysisCenter-7jQoTbhwKn1b')}：${
                    selectedBatchData?.id ?? ''
                  }  ${t('analysisCenter-z0ix5QcmuxYS')}：${selectedBatchData?.passedCount ?? ''}`}</div>
                </div>
                <div className="selectedItemRight">
                  <Popover
                    content={
                      <PopvoerActivetyBatch
                        selectedBatch={selectedBatchData}
                        id={selectedData.id}
                        onSubmit={onSelectedBatchChange}
                        onClose={clearCurrentPopover}
                      />
                    }
                    // title="Title"
                    trigger="click"
                    open={`${currentPopover}` === `${selectedData.id}`}
                  >
                    <a
                      className="selectBatchButton"
                      onClick={() => {
                        selectActivityBatch(item);
                      }}
                    >
                      {t('analysisCenter-XztOxd12pvEe')}
                    </a>
                  </Popover>

                  <a
                    className="delete"
                    onClick={() => {
                      deleteSelectedKey(item);
                    }}
                  >
                    删除
                  </a>
                </div>
              </div>
            );
          })}
          <FlowCanvas
            dataProvider={dataProvider}
            value={selectedBatch[0]?.flows ?? []}
            onClickNode={onFlowCanvasClick}
            mode="detail"
          />
        </section>
        <div className="selectedSegment">
          <div className="selectedSegmentTitle">{`${t('analysisCenter-lccNkPz8uyzS')} [${selectedNodes.length}/10] ${t('analysisCenter-B9SZLFnNBccx')}`}</div>
          <div className="selectedSegmentContent">
            {_.map(selectedNodes, (item) => {
              return (
                <div className="bubbleItem" key={`${item.nodeId}`}>
                  <div
                    className="itemName"
                    title={`[${item.nodeId}] ${item.name}`}
                  >{`[${item.nodeId}] ${item.name}`}</div>
                  <CloseOutlined
                    onClick={() => {
                      onClickDelete(item);
                    }}
                    title={t('analysisCenter-pQOI8Qh5Jexj')}
                  />
                </div>
              );
            })}
          </div>
        </div>
      </div>
      <div className="handleButton">
        <Button onClick={onClose}>{t('analysisCenter-QkmoBSLl2cZB')}</Button>
        <Button onClick={onSubmit} type="primary">
          {t('analysisCenter-kUo7ZfJbZ7Ll')}
        </Button>
      </div>
    </Drawer>
  );
};

const typeList = [
  {
    text: t('analysisCenter-zu1c5cpMNzqS'),
    value: 'CUSTOM',
    key: 'CUSTOM'
  },
  {
    text: t('analysisCenter-AViUbw8WOXTN'),
    value: 'TEMPLATE',
    key: 'TEMPLATE'
  }
];

const ruleList = [
  {
    text: t('analysisCenter-eunUKdUo40CL'),
    value: 'ONCE',
    key: 'ONCE'
  },
  {
    text: '定期运行',
    value: 'SCHEDULE',
    key: 'SCHEDULE'
  }
];

const PopvoerActivetyBatch = (props) => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [totalElements, setTotalElements] = useState(0);

  const [selectedRowKeys, setSelectedRowKeys] = useState(() => {
    return props.selectedBatch?.id ? [props.selectedBatch?.id] : [];
  });

  useEffect(() => {
    setSelectedRowKeys(props.selectedBatch?.id ? [props.selectedBatch?.id] : []);
  }, [props.selectedBatch]);

  const [tableParams, setTableParams] = useState({
    size: 5,
    page: 1,
    search: [{ propertyName: 'campaignId', operator: 'EQ', value: props.id }],
    sorts: [
      {
        direction: 'desc',
        propertyName: 'updateTime'
      }
    ]
  });

  useEffect(() => {
    const mask = document.getElementsByClassName('ant-drawer-mask')[0];
    const body = document.getElementsByClassName('ant-drawer-wrapper-body')[0];
    mask.addEventListener('click', props.onClose);
    body.addEventListener('click', props.onClose);
    return () => {
      mask.removeEventListener('click', props.onClose);
      body.removeEventListener('click', props.onClose);
    };
  }, []);

  useEffect(() => {
    let ignore = false;
    const init = async () => {
      const logData = await campaignV2Service.listCalcLogs(tableParams);
      if (!ignore) {
        setLoading(true);
        setDataSource(logData.content);
        setTotalElements(logData.totalElements);
        setLoading(false);
      }
    };

    init();
    return () => {
      ignore = true;
    };
  }, []);

  useEffect(() => {
    let ignore = false;
    const queryData = async () => {
      setLoading(true);
      const data = await campaignV2Service.listCalcLogs(tableParams);
      setDataSource(data.content);
      setTotalElements(data.totalElements);
      setLoading(false);
    };

    !ignore && queryData();
    return () => {
      ignore = true;
    };
  }, [tableParams]);

  const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      props.onSubmit && props.onSubmit(selectedRows);
      props.onClose && props.onClose();
    },
    type: 'radio',
    selectedRowKeys
  };

  const columns = [
    {
      title: t('analysisCenter-ndw7qlnAWRLm'),
      dataIndex: 'id',
      width: 200
    },
    {
      title: t('analysisCenter-C2bolAWQMgyT'),
      dataIndex: 'faked',
      render: (faked) => (faked ? '是' : '否')
    },
    {
      title: t('analysisCenter-z0ix5QcmuxYS'),
      dataIndex: 'passedCount'
    },
    {
      title: t('analysisCenter-kWn6468MLfSz'),
      dataIndex: 'calcBeginTime',
      render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-')
    },
    {
      title: t('analysisCenter-HUDVi6H3yBMC'),
      dataIndex: 'calcEndTime',
      render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-')
    }
  ];

  const handleTableChange = (page, filter, sorter) => {
    const { current, pageSize } = page;
    const direction = sorter.order ? sorter.order.substr(0, sorter.order.length - 3) : tableParams.sorts[0].direction;
    const propertyName = sorter.field ? (Array.isArray(sorter.field) ? sorter.field.join('.') : sorter.field) : 'id';
    setTableParams({
      ...tableParams,
      sorts: [{ direction, propertyName }],
      page: current,
      size: pageSize
    });
  };

  return (
    <Table
      columns={columns}
      dataSource={dataSource}
      bordered={false}
      loading={loading}
      rowSelection={rowSelection}
      onChange={handleTableChange}
      rowKey="id"
      size="middle"
      pagination={{
        current: tableParams.page,
        total: totalElements,
        defaultPageSize: 5,
        showSizeChanger: true,
        showLessItems: true,
        pageSizeOptions: ['5', '10', '20']
      }}
    />
  );
};

const phaseList = [
  {
    name: t('analysisCenter-KlmLHS1gebUW'),
    text: t('analysisCenter-KlmLHS1gebUW'),
    value: 'DRAFT',
    key: 'DRAFT',
    color: 'rgba(0, 0, 0, 0)'
  },
  {
    name: t('analysisCenter-frnqbp2gXiTz'),
    text: t('analysisCenter-frnqbp2gXiTz'),
    value: 'TESTING',
    key: 'TESTING',
    color: '#52c41a'
  },
  {
    name: t('analysisCenter-78QmnplSNj8N'),
    text: t('analysisCenter-78QmnplSNj8N'),
    value: 'TEST_SUC',
    key: 'TEST_SUC',
    color: '#1890ff'
  },
  {
    name: t('analysisCenter-xUKHiBs7qxV6'),
    text: t('analysisCenter-xUKHiBs7qxV6'),
    value: 'ENABLE',
    key: 'ENABLE',
    color: '#f5222d'
  },
  {
    name: t('analysisCenter-0SG6SaPejQB5'),
    text: t('analysisCenter-0SG6SaPejQB5'),
    value: 'STOPPED',
    key: 'STOPPED',
    color: '#faad14'
  }
];

export default React.memo(ActivityNodeDrawer);
