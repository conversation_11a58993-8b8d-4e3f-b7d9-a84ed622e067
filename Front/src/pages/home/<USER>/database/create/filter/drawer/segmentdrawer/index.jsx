import { useDebounce } from '@umijs/hooks';
import { Button, Drawer, Input, Select, Table, Tooltip, message } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import UserGroupService from 'service/UserGroupService';
import { useDeepCompareEffect } from 'utils/customhooks';
import { t } from 'utils/translation';
import './index.scss';

const { Option } = Select;
const userGroupService = new UserGroupService();

const SegmentDrawer = (props) => {
  const [name, setName] = useState('');
  const [type, setType] = useState('');
  const [tableParams, setTableParams] = useState({
    size: 5,
    page: 1,
    search: [
      {
        connector: 'AND',
        propertyName: 'scenario.id',
        operator: 'EQ',
        value: props.scenario.id
      },
      {
        connector: 'AND',
        propertyName: 'status',
        operator: 'EQ',
        value: 'NORMAL'
      },
      { connector: 'AND', propertyName: 'name', operator: 'LIKE', value: '' },
      { connector: 'AND', propertyName: 'type', operator: 'EQ', value: '' },
      {
        operator: 'IN',
        propertyName: 'approvalStatus',
        value: 'NONE,PASS,CANCEL'
      },
      {
        operator: 'EQ',
        propertyName: 'deptId',
        value: window.getDeptId()
      }
    ],
    sorts: [
      {
        direction: 'desc',
        propertyName: 'updateTime'
      }
    ]
  });

  const dataSourceCache = useRef([]);
  const debouncedName = useDebounce(name, 300);

  const [loading, setLoading] = useState(false);

  const [dataSource, setDataSource] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState(() => {
    dataSourceCache.current = props.dataSource.comparisonList;
    return _.map(props.dataSource.comparisonList, (item) => item.id) ?? [];
  });

  // 搜索内容变更触发搜索
  const onNameChange = (e) => {
    setName(e.target.value);
  };

  const onTypeChange = (value) => {
    setType(value);
  };

  useEffect(() => {
    // 当type和name变化的时候请求列表数据
    setTableParams((prevState) => {
      return {
        ...prevState,
        search: [
          {
            connector: 'AND',
            propertyName: 'scenario.id',
            operator: 'EQ',
            value: props.scenario.id
          },
          {
            connector: 'AND',
            propertyName: 'status',
            operator: 'EQ',
            value: 'NORMAL'
          },
          {
            connector: 'AND',
            propertyName: 'calcStatus',
            operator: 'EQ',
            value: 'SUC'
          },
          {
            connector: 'AND',
            propertyName: 'name',
            operator: 'LIKE',
            value: debouncedName ?? ''
          },
          {
            connector: 'AND',
            propertyName: 'type',
            operator: 'EQ',
            value: type ?? ''
          },
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: window.getDeptId()
          }
        ]
      };
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, debouncedName]);

  useDeepCompareEffect(() => {
    let ignore = false;
    const queryData = async () => {
      setLoading(true);
      const data = await userGroupService.query(tableParams);
      dataSourceCache.current = _.concat(dataSourceCache.current, data.content);
      setDataSource(data.content);
      setTotalCount(data.totalElements);
      setLoading(false);
    };

    !ignore && queryData();
    return () => {
      ignore = true;
    };
  }, [tableParams]);

  const columns = [
    {
      title: t('analysisCenter-eb7YBFlovWTk'),
      dataIndex: 'name',
      className: 'name',
      width: 200,
      render: (text) => {
        return (
          <div>
            {/* <img src={val.src} style={{ width: 28, height: 28, marginRight: 5 }} alt="png" /> */}
            <span>{text}</span>
          </div>
        );
      }
    },
    {
      title: t('analysisCenter-4jsrvlLaRMMG'),
      width: 100,
      dataIndex: 'customerCount',
      sorter: true
    },
    {
      title: t('analysisCenter-gICskpveZF18'),
      width: 200,
      dataIndex: 'lastCalcTime',
      render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
      sorter: true
    },
    {
      title: t('analysisCenter-RaeYXODFinrQ'),
      dataIndex: 'status',
      width: 100,
      render: (text) => (
        <div className="status">
          <span className={`circle ${text === 'NORMAL' ? 'green' : null}`} />
          {_.filter(statusList, (v) => v.value === text)[0]?.name}
        </div>
      ),
      sorter: true
    },
    {
      title: t('analysisCenter-zPiGauRMgb5H'),
      width: 100,
      dataIndex: ['scenario', 'name'],
      sorter: false
    },
    {
      title: t('analysisCenter-JNHxCCbPpXFm'),
      width: 100,
      dataIndex: 'type',
      render: (text) => {
        return _.filter(typeList, (v) => v.value === text)[0]?.text;
      }
    },
    {
      title: t('analysisCenter-kBasVCEHGVPp'),
      width: 100,
      dataIndex: 'calcRule',
      className: 'maxWidth',
      render: (text) => (text ? _.filter(ruleList, (v) => v.value === text)[0]?.text : '-')
    },
    {
      title: t('analysisCenter-V4PREWd9Ycrc'),
      width: 100,
      dataIndex: 'createUserName'
    },
    {
      title: t('analysisCenter-HSFD5onzqzH0'),
      width: 200,
      dataIndex: 'createTime',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      sorter: true
    }
  ];

  const handleTableChange = (page, filter, sorter) => {
    const { current, pageSize } = page;
    const direction = sorter.order ? sorter.order.substr(0, sorter.order.length - 3) : tableParams.sorts[0].direction;
    const propertyName = sorter.field
      ? Array.isArray(sorter.field)
        ? sorter.field.join('.')
        : sorter.field
      : 'updateTime';
    setTableParams({
      ...tableParams,
      sorts: [{ direction, propertyName }],
      page: current,
      size: pageSize
    });
  };

  const onSelectChange = (selectedRowKeys) => {
    if (selectedRowKeys.length > 10) {
      message.warn(t('analysisCenter-sc7CHNEiVRzh'));
      return;
    }
    setSelectedRowKeys(selectedRowKeys);
  };

  const deleteSelectedKey = (key) => {
    const findIndex = _.findIndex(selectedRowKeys, (item) => item === key);
    const _selectedRowKeys = _.clone(selectedRowKeys);
    _selectedRowKeys.splice(findIndex, 1);
    setSelectedRowKeys(_selectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    preserveSelectedRowKeys: true
  };

  const onClose = () => {
    props.onClose && props.onClose();
  };

  // 提交
  const onSubmit = () => {
    if (_.isEmpty(selectedRowKeys)) {
      message.warn(t('analysisCenter-I75Io1jWi7tJ'));
      return;
    }
    const submitData = _.map(selectedRowKeys, (item) => {
      return _.findLast(dataSourceCache.current, (data) => data.id === item);
    });
    props.onSubmit && props.onSubmit(submitData, props.dataSource.filterId);
  };

  return (
    <Drawer
      className="segmentDrawer"
      title={t('analysisCenter-eDj00oTzbJYS')}
      destroyOnClose
      placement="bottom"
      height={500}
      closable={false}
      // onClose={() => props.onClose(false)}
      open
    >
      <div className="filterWrapper">
        <Input className="segmentNameSearch" placeholder={t('analysisCenter-iidrnXDH2khh')} onChange={onNameChange} />
        <Select showTime onChange={onTypeChange} maxTagCount={10} value={type} style={{ minWidth: 150 }}>
          {typeList.map((item) => (
            <Option value={item.value} key={item.key}>
              {item.text}
            </Option>
          ))}
        </Select>
        <div className="selectedSegmentTitle">{`${t('analysisCenter-dUmR1iLJT4ef')}   [${selectedRowKeys.length}/10] ${t('analysisCenter-ZJcW3zIdFmJU')}`}</div>
      </div>
      <div className="mainContent">
        <div className="table1">
          <Table
            columns={columns}
            dataSource={dataSource}
            bordered={false}
            loading={loading}
            onChange={handleTableChange}
            scroll={{ x: 800, y: 280 }}
            rowSelection={rowSelection}
            rowKey="id"
            size="middle"
            pagination={{
              current: tableParams.page,
              total: totalCount,
              defaultPageSize: tableParams.size,
              showQuickJumper: true,
              showSizeChanger: true,
              showLessItems: true,
              pageSizeOptions: ['5', '10', '20']
            }}
          />
        </div>
        <div className="selectedSegment">
          <div className="selectedSegmentContent">
            {_.map(selectedRowKeys, (item) => {
              const selectedData = _.findLast(dataSourceCache.current, (data) => data.id === item);
              return (
                <div className="selectedItem" key={item}>
                  <div className="selectedItemName">
                    <Tooltip title={selectedData?.name}>{selectedData?.name}</Tooltip>
                  </div>
                  <a
                    className="delete"
                    onClick={() => {
                      deleteSelectedKey(item);
                    }}
                  >
                    {t('analysisCenter-806bYCdiTYiy')}
                  </a>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      <div className="handleButton">
        <Button onClick={onClose}>{t('analysisCenter-QkmoBSLl2cZB')}</Button>
        <Button onClick={onSubmit} type="primary">
          {t('analysisCenter-kUo7ZfJbZ7Ll')}
        </Button>
      </div>
    </Drawer>
  );
};

const typeList = [
  {
    text: t('analysisCenter-QVxPjRhBmH1j'),
    value: '',
    key: ''
  },
  {
    text: t('analysisCenter-XrqmdJT40bEI'),
    value: 'CONDITIONAL',
    key: 'CONDITIONAL'
  },
  {
    text: t('analysisCenter-GFAdT8Efhwo4'),
    value: 'AI',
    key: 'AI'
  },
  {
    text: t('analysisCenter-LIIobuc99Sqr'),
    value: 'COMPLEX',
    key: 'COMPLEX'
  },
  {
    text: t('analysisCenter-VcEUNOUl9RzZ'),
    value: 'UPLOAD',
    key: 'UPLOAD'
  },
  {
    text: t('analysisCenter-0iKlvCJnKSjH'),
    value: 'CAMPAIGN',
    key: 'CAMPAIGN'
  },
  {
    text: t('analysisCenter-vJuIN9OnOIH9'),
    value: 'CONDITION_AGGREGATE',
    key: 'CONDITION_AGGREGATE'
  },
  {
    text: t('analysisCenter-YtV4JZFB6ZvW'),
    value: 'SHORT_LINK',
    key: 'SHORT_LINK'
  },
  {
    name: t('analysisCenter-OMdGneBD9SbX'),
    text: t('analysisCenter-OMdGneBD9SbX'),
    value: 'FUNNEL_CHART',
    key: 'FUNNEL_CHART'
  },
  {
    name: t('analysisCenter-XO5myx9D6LiI'),
    text: t('analysisCenter-XO5myx9D6LiI'),
    value: 'RETENTION_CHART',
    key: 'RETENTION_CHART'
  }
];

const ruleList = [
  {
    text: t('analysisCenter-qMzKUWOMj4Dx'),
    key: 'ONCE',
    value: 'ONCE'
  },
  {
    text: t('analysisCenter-7us28heRMDye'),
    value: 'SCHEDULE',
    key: 'SCHEDULE'
  }
];

// 状态
export const statusList = [
  {
    name: t('analysisCenter-KlmLHS1gebUW'),
    text: t('analysisCenter-KlmLHS1gebUW'),
    value: 'DRAFT',
    key: 'DRAFT'
  },
  {
    name: t('analysisCenter-reLxx76re7WZ'),
    text: t('analysisCenter-reLxx76re7WZ'),
    value: 'NORMAL',
    key: 'NORMAL'
  }
];

export default React.memo(SegmentDrawer);
