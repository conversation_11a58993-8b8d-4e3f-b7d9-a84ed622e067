import { DeleteOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Drawer, Input, message } from 'antd';
import _ from 'lodash';
import React, { useRef, useState } from 'react';
import { t } from 'utils/translation';
import LabelFilter from './LabelFilter/index';
import './index.scss';

const SegmentDrawer = (props) => {
  const [data, setData] = useState(() => {
    return _.isEmpty(props.dataSource.userLabelList)
      ? [{ name: t('analysisCenter-t5cQOZzCoalc'), label: {} }]
      : _.cloneDeep(props.dataSource.userLabelList);
  });

  const refArr = useRef([]);

  const add = () => {
    if (data.length > 10) {
      message.error(t('analysisCenter-ZLx0OQUk4t1a'));
      return;
    }
    data.push({ name: `${t('analysisCenter-Lhsi3MX9K8Bf')}${data.length + 1}`, label: {} });
    setData([...data]);
  };

  const deleteTag = (index) => {
    data.splice(index, 1);
    setData([...data]);
  };

  const onChange = (value, index) => {
    data[index].label = value;
    setData([...data]);
  };

  const save = () => {
    if (data.length === 0) {
      message.error(t('analysisCenter-wel4jIsAooBz'));
      return;
    }
    let flags = true;
    refArr.current.forEach((item) => {
      if (item) {
        const res = item.isValid();
        flags = flags && res;
      }
    });
    if (!flags) {
      message.error(t('analysisCenter-WIsoNErf8GWu'));
      return;
    }
    props.onSubmit && props.onSubmit(data, props.dataSource.filterId);
  };

  return (
    <Drawer className="tagdrawer" closable={false} destroyOnClose placement="bottom" height={500} open>
      <header>
        <div className="left">{t('analysisCenter-drPo06sO6aTT')}</div>
        <div className="right">
          <Button
            style={{ marginRight: 20 }}
            onClick={() => {
              props.onClose && props.onClose();
            }}
          >
            {t('analysisCenter-xOIkpPQrtgiy')}
          </Button>
          <Button onClick={save} type="primary">
            {t('analysisCenter-g0JR9fQ2duUT')}
          </Button>
        </div>
      </header>
      <div className="content">
        {data.map((n, i) => (
          <Card style={{ marginBottom: 10 }} key={i}>
            <div style={{ display: 'flex' }}>
              <div style={{ flex: 1, marginBottom: 10 }}>
                <div>
                  <span>{t('analysisCenter-tZ1wEE2Ve2r7')}</span>
                  <Input
                    placeholder={t('analysisCenter-MBPmOanX2RZh')}
                    value={n.name}
                    onChange={(e) => {
                      data[i].name = e.target.value;
                      setData([...data]);
                    }}
                    style={{ width: 200 }}
                  />
                </div>
              </div>
              <div style={{ width: 100, textAlign: 'center' }}>
                <DeleteOutlined onClick={() => deleteTag(i)} style={{ marginTop: 10, fontSize: 20, color: 'red' }} />
              </div>
            </div>
            <LabelFilter
              ref={(el) => (refArr.current[i] = el)}
              value={n.label}
              onChange={(value) => onChange(value, i)}
            />
            {/* <Divider /> */}
          </Card>
        ))}
        <Button disabled={data.length >= 10} onClick={add} type="primary">
          {t('analysisCenter-JRvsYLY6sSGc')}
        </Button>
      </div>
    </Drawer>
  );
};

export default React.memo(SegmentDrawer);
