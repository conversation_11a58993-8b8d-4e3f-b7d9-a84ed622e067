// @import 'assets/css/variable.scss';

// @import '~antd/dist/antd.css';
// ant-tree ant-tree-directory
.labelFilter {
  .ant-select-tree-checkbox {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    position: relative;
    top: -0.09em;
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    vertical-align: middle;
    outline: none;
    cursor: pointer;
  }

  .ant-select-tree-checkbox-wrapper:hover .ant-select-tree-checkbox-inner,
  .ant-select-tree-checkbox:hover .ant-select-tree-checkbox-inner,
  .ant-select-tree-checkbox-input:focus+.ant-select-tree-checkbox-inner {
    border-color: #1890ff;
  }

  .ant-select-tree-checkbox-checked::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #1890ff;
    border-radius: 2px;
    visibility: hidden;
    -webkit-animation: antCheckboxEffect 0.36s ease-in-out;
    animation: antCheckboxEffect 0.36s ease-in-out;
    -webkit-animation-fill-mode: backwards;
    animation-fill-mode: backwards;
    content: '';
  }

  .ant-select-tree-checkbox:hover::after,
  .ant-select-tree-checkbox-wrapper:hover .ant-select-tree-checkbox::after {
    visibility: visible;
  }

  .ant-select-tree-checkbox-inner {
    position: relative;
    top: 0;
    left: 0;
    display: block;
    width: 16px;
    height: 16px;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    border-collapse: separate;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }

  .ant-select-tree-checkbox-inner::after {
    position: absolute;
    top: 50%;
    left: 22%;
    display: table;
    width: 5.71428571px;
    height: 9.14285714px;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    -webkit-transform: rotate(45deg) scale(0) translate(-50%, -50%);
    -ms-transform: rotate(45deg) scale(0) translate(-50%, -50%);
    transform: rotate(45deg) scale(0) translate(-50%, -50%);
    opacity: 0;
    -webkit-transition: all 0.1s cubic-bezier(0.71, -0.46, 0.88, 0.6), opacity 0.1s;
    transition: all 0.1s cubic-bezier(0.71, -0.46, 0.88, 0.6), opacity 0.1s;
    content: ' ';
  }

  .ant-select-tree-checkbox-input {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    cursor: pointer;
    opacity: 0;
  }

  .ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner::after {
    position: absolute;
    display: table;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    -webkit-transform: rotate(45deg) scale(1) translate(-50%, -50%);
    -ms-transform: rotate(45deg) scale(1) translate(-50%, -50%);
    transform: rotate(45deg) scale(1) translate(-50%, -50%);
    opacity: 1;
    -webkit-transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
    transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
    content: ' ';
  }

  .ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner {
    background-color: #1890ff;
    border-color: #1890ff;
  }

  .ant-select-tree-checkbox-disabled {
    cursor: not-allowed;
  }

  .ant-select-tree-checkbox-disabled.ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner::after {
    border-color: rgba(0, 0, 0, 0.25);
    -webkit-animation-name: none;
    animation-name: none;
  }

  .ant-select-tree-checkbox-disabled .ant-select-tree-checkbox-input {
    cursor: not-allowed;
  }

  .ant-select-tree-checkbox-disabled .ant-select-tree-checkbox-inner {
    background-color: #f5f5f5;
    border-color: #d9d9d9 !important;
  }

  .ant-select-tree-checkbox-disabled .ant-select-tree-checkbox-inner::after {
    border-color: #f5f5f5;
    border-collapse: separate;
    -webkit-animation-name: none;
    animation-name: none;
  }

  .ant-select-tree-checkbox-disabled+span {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }

  .ant-select-tree-checkbox-disabled:hover::after,
  .ant-select-tree-checkbox-wrapper:hover .ant-select-tree-checkbox-disabled::after {
    visibility: hidden;
  }

  .ant-select-tree-checkbox-wrapper {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    display: inline-block;
    line-height: unset;
    cursor: pointer;
  }

  .ant-select-tree-checkbox-wrapper.ant-select-tree-checkbox-wrapper-disabled {
    cursor: not-allowed;
  }

  .ant-select-tree-checkbox-wrapper+.ant-select-tree-checkbox-wrapper {
    margin-left: 8px;
  }

  .ant-select-tree-checkbox+span {
    padding-right: 8px;
    padding-left: 8px;
  }

  .ant-select-tree-checkbox-group {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    display: inline-block;
  }

  .ant-select-tree-checkbox-group-item {
    display: inline-block;
    margin-right: 8px;
  }

  .ant-select-tree-checkbox-group-item:last-child {
    margin-right: 0;
  }

  .ant-select-tree-checkbox-group-item+.ant-select-tree-checkbox-group-item {
    margin-left: 0;
  }

  .ant-select-tree-checkbox-indeterminate .ant-select-tree-checkbox-inner {
    background-color: #fff;
    border-color: #d9d9d9;
  }

  .ant-select-tree-checkbox-indeterminate .ant-select-tree-checkbox-inner::after {
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background-color: #1890ff;
    border: 0;
    -webkit-transform: translate(-50%, -50%) scale(1);
    -ms-transform: translate(-50%, -50%) scale(1);
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
    content: ' ';
  }

  .ant-select-tree-checkbox-indeterminate.ant-select-tree-checkbox-disabled .ant-select-tree-checkbox-inner::after {
    background-color: rgba(0, 0, 0, 0.25);
    border-color: rgba(0, 0, 0, 0.25);
  }

  .ant-select-tree {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    margin: 0;
    margin-top: -4px;
    padding: 0 4px;
  }

  .ant-select-tree li {
    margin: 8px 0;
    padding: 0;
    white-space: nowrap;
    list-style: none;
    outline: 0;
  }

  .ant-select-tree li.filter-node>span {
    font-weight: 500;
  }

  .ant-select-tree li ul {
    margin: 0;
    padding: 0 0 0 18px;
  }

  .ant-select-tree li .ant-select-tree-node-content-wrapper {
    display: inline-block;
    width: calc(100% - 24px);
    margin: 0;
    padding: 3px 5px;
    color: rgba(0, 0, 0, 0.65);
    text-decoration: none;
    border-radius: 2px;
    cursor: pointer;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }

  .ant-select-tree li .ant-select-tree-node-content-wrapper:hover {
    background-color: #e6f7ff;
  }

  .ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
    background-color: #bae7ff;
  }

  .ant-select-tree li span.ant-select-tree-checkbox {
    margin: 0 4px 0 0;
  }

  .ant-select-tree li span.ant-select-tree-checkbox+.ant-select-tree-node-content-wrapper {
    width: calc(100% - 46px);
  }

  .ant-select-tree li span.ant-select-tree-switcher,
  .ant-select-tree li span.ant-select-tree-iconEle {
    display: inline-block;
    width: 24px;
    height: 24px;
    margin: 0;
    line-height: 22px;
    text-align: center;
    vertical-align: middle;
    border: 0 none;
    outline: none;
    cursor: pointer;
  }

  .ant-select-tree li span.ant-select-icon_loading .ant-select-switcher-loading-icon {
    position: absolute;
    left: 0;
    display: inline-block;
    color: #1890ff;
    font-size: 14px;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }

  .ant-select-tree li span.ant-select-icon_loading .ant-select-switcher-loading-icon svg {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
  }

  .ant-select-tree li span.ant-select-tree-switcher {
    position: relative;
  }

  .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher-noop {
    cursor: auto;
  }

  .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_open .ant-tree-switcher-icon,
  .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_open .ant-select-switcher-icon {
    font-size: 12px;
    font-size: 10px \9;
    -webkit-transform: scale(0.83333333) rotate(0deg);
    -ms-transform: scale(0.83333333) rotate(0deg);
    transform: scale(0.83333333) rotate(0deg);
    display: inline-block;
    font-weight: bold;
  }

  :root .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_open .ant-tree-switcher-icon,
  :root .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_open .ant-select-switcher-icon {
    font-size: 12px;
  }

  .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_open .ant-tree-switcher-icon svg,
  .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_open .ant-select-switcher-icon svg {
    -webkit-transition: -webkit-transform 0.3s;
    transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
    transition: transform 0.3s, -webkit-transform 0.3s;
  }

  .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_close .ant-tree-switcher-icon,
  .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_close .ant-select-switcher-icon {
    font-size: 12px;
    font-size: 10px \9;
    -webkit-transform: scale(0.83333333) rotate(0deg);
    -ms-transform: scale(0.83333333) rotate(0deg);
    transform: scale(0.83333333) rotate(0deg);
    display: inline-block;
    font-weight: bold;
  }

  :root .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_close .ant-tree-switcher-icon,
  :root .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_close .ant-select-switcher-icon {
    font-size: 12px;
  }

  .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_close .ant-tree-switcher-icon svg,
  .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_close .ant-select-switcher-icon svg {
    -webkit-transition: -webkit-transform 0.3s;
    transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
    transition: transform 0.3s, -webkit-transform 0.3s;
  }

  .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_close .ant-select-switcher-icon svg {
    -webkit-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    transform: rotate(-90deg);
  }

  .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_open .ant-select-switcher-loading-icon,
  .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_close .ant-select-switcher-loading-icon {
    position: absolute;
    left: 0;
    display: inline-block;
    width: 24px;
    height: 24px;
    color: #1890ff;
    font-size: 14px;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }

  .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_open .ant-select-switcher-loading-icon svg,
  .ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_close .ant-select-switcher-loading-icon svg {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
  }

  .ant-select-tree .ant-select-tree-treenode-loading .ant-select-tree-iconEle {
    display: none;
  }

  .ant-select-tree-child-tree {
    display: none;
  }

  .ant-select-tree-child-tree-open {
    display: block;
  }

  li.ant-select-tree-treenode-disabled>span:not(.ant-select-tree-switcher),
  li.ant-select-tree-treenode-disabled>.ant-select-tree-node-content-wrapper,
  li.ant-select-tree-treenode-disabled>.ant-select-tree-node-content-wrapper span {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }

  li.ant-select-tree-treenode-disabled>.ant-select-tree-node-content-wrapper:hover {
    background: transparent;
  }

  .ant-select-tree-icon__open {
    margin-right: 2px;
    vertical-align: top;
  }

  .ant-select-tree-icon__close {
    margin-right: 2px;
    vertical-align: top;
  }

  .ant-select-tree-dropdown {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
  }

  .ant-select-tree-dropdown .ant-select-dropdown-search {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1;
    display: block;
    padding: 4px;
    background: #fff;
  }

  .ant-select-tree-dropdown .ant-select-dropdown-search .ant-select-search__field__wrap {
    width: 100%;
  }

  .ant-select-tree-dropdown .ant-select-dropdown-search .ant-select-search__field {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
    padding: 4px 7px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    outline: none;
  }

  .ant-select-tree-dropdown .ant-select-dropdown-search.ant-select-search--hide {
    display: none;
  }

  .ant-select-tree-dropdown .ant-select-not-found {
    display: block;
    padding: 7px 16px;
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }

  /* stylelint-disable at-rule-empty-line-before,at-rule-name-space-after,at-rule-no-unknown */
  /* stylelint-disable no-duplicate-selectors */
  /* stylelint-disable */
  /* stylelint-disable declaration-bang-space-before,no-duplicate-selectors,string-no-newline */
  @-webkit-keyframes antCheckboxEffect {
    0% {
      -webkit-transform: scale(1);
      transform: scale(1);
      opacity: 0.5;
    }

    100% {
      -webkit-transform: scale(1.6);
      transform: scale(1.6);
      opacity: 0;
    }
  }

  @keyframes antCheckboxEffect {
    0% {
      -webkit-transform: scale(1);
      transform: scale(1);
      opacity: 0.5;
    }

    100% {
      -webkit-transform: scale(1.6);
      transform: scale(1.6);
      opacity: 0;
    }
  }

  .ant-tree.ant-tree-directory {
    position: relative;
  }

  .ant-tree.ant-tree-directory>li span.ant-tree-switcher,
  .ant-tree.ant-tree-directory .ant-tree-child-tree>li span.ant-tree-switcher {
    position: relative;
    z-index: 1;
  }

  .ant-tree.ant-tree-directory>li span.ant-tree-switcher.ant-tree-switcher-noop,
  .ant-tree.ant-tree-directory .ant-tree-child-tree>li span.ant-tree-switcher.ant-tree-switcher-noop {
    pointer-events: none;
  }

  .ant-tree.ant-tree-directory>li span.ant-tree-checkbox,
  .ant-tree.ant-tree-directory .ant-tree-child-tree>li span.ant-tree-checkbox {
    position: relative;
    z-index: 1;
  }

  .ant-tree.ant-tree-directory>li span.ant-tree-node-content-wrapper,
  .ant-tree.ant-tree-directory .ant-tree-child-tree>li span.ant-tree-node-content-wrapper {
    border-radius: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .ant-tree.ant-tree-directory>li span.ant-tree-node-content-wrapper:hover,
  .ant-tree.ant-tree-directory .ant-tree-child-tree>li span.ant-tree-node-content-wrapper:hover {
    background: transparent;
  }

  .ant-tree.ant-tree-directory>li span.ant-tree-node-content-wrapper:hover::before,
  .ant-tree.ant-tree-directory .ant-tree-child-tree>li span.ant-tree-node-content-wrapper:hover::before {
    background: #e6f7ff;
  }

  .ant-tree.ant-tree-directory>li span.ant-tree-node-content-wrapper.ant-tree-node-selected,
  .ant-tree.ant-tree-directory .ant-tree-child-tree>li span.ant-tree-node-content-wrapper.ant-tree-node-selected {
    color: #fff;
    background: transparent;
  }

  .ant-tree.ant-tree-directory>li span.ant-tree-node-content-wrapper::before,
  .ant-tree.ant-tree-directory .ant-tree-child-tree>li span.ant-tree-node-content-wrapper::before {
    position: absolute;
    right: 0;
    left: 0;
    height: 24px;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
    content: '';
  }

  .ant-tree.ant-tree-directory>li span.ant-tree-node-content-wrapper>span,
  .ant-tree.ant-tree-directory .ant-tree-child-tree>li span.ant-tree-node-content-wrapper>span {
    position: relative;
    z-index: 1;
  }

  .ant-tree.ant-tree-directory>li.ant-tree-treenode-selected>span.ant-tree-switcher,
  .ant-tree.ant-tree-directory .ant-tree-child-tree>li.ant-tree-treenode-selected>span.ant-tree-switcher {
    color: #fff;
  }

  .ant-tree.ant-tree-directory>li.ant-tree-treenode-selected>span.ant-tree-checkbox .ant-tree-checkbox-inner,
  .ant-tree.ant-tree-directory .ant-tree-child-tree>li.ant-tree-treenode-selected>span.ant-tree-checkbox .ant-tree-checkbox-inner {
    border-color: #1890ff;
  }

  .ant-tree.ant-tree-directory>li.ant-tree-treenode-selected>span.ant-tree-checkbox.ant-tree-checkbox-checked::after,
  .ant-tree.ant-tree-directory .ant-tree-child-tree>li.ant-tree-treenode-selected>span.ant-tree-checkbox.ant-tree-checkbox-checked::after {
    border-color: #fff;
  }

  .ant-tree.ant-tree-directory>li.ant-tree-treenode-selected>span.ant-tree-checkbox.ant-tree-checkbox-checked .ant-tree-checkbox-inner,
  .ant-tree.ant-tree-directory .ant-tree-child-tree>li.ant-tree-treenode-selected>span.ant-tree-checkbox.ant-tree-checkbox-checked .ant-tree-checkbox-inner {
    background: #fff;
  }

  .ant-tree.ant-tree-directory>li.ant-tree-treenode-selected>span.ant-tree-checkbox.ant-tree-checkbox-checked .ant-tree-checkbox-inner::after,
  .ant-tree.ant-tree-directory .ant-tree-child-tree>li.ant-tree-treenode-selected>span.ant-tree-checkbox.ant-tree-checkbox-checked .ant-tree-checkbox-inner::after {
    border-color: #1890ff;
  }

  .ant-tree.ant-tree-directory>li.ant-tree-treenode-selected>span.ant-tree-node-content-wrapper::before,
  .ant-tree.ant-tree-directory .ant-tree-child-tree>li.ant-tree-treenode-selected>span.ant-tree-node-content-wrapper::before {
    background: #1890ff;
  }

  .ant-tree-checkbox {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    position: relative;
    top: -0.09em;
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    vertical-align: middle;
    outline: none;
    cursor: pointer;
  }

  .ant-tree-checkbox-wrapper:hover .ant-tree-checkbox-inner,
  .ant-tree-checkbox:hover .ant-tree-checkbox-inner,
  .ant-tree-checkbox-input:focus+.ant-tree-checkbox-inner {
    border-color: #1890ff;
  }

  .ant-tree-checkbox-checked::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #1890ff;
    border-radius: 2px;
    visibility: hidden;
    -webkit-animation: antCheckboxEffect 0.36s ease-in-out;
    animation: antCheckboxEffect 0.36s ease-in-out;
    -webkit-animation-fill-mode: backwards;
    animation-fill-mode: backwards;
    content: '';
  }

  .ant-tree-checkbox:hover::after,
  .ant-tree-checkbox-wrapper:hover .ant-tree-checkbox::after {
    visibility: visible;
  }

  .ant-tree-checkbox-inner {
    position: relative;
    top: 0;
    left: 0;
    display: block;
    width: 16px;
    height: 16px;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    border-collapse: separate;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }

  .ant-tree-checkbox-inner::after {
    position: absolute;
    top: 50%;
    left: 22%;
    display: table;
    width: 5.71428571px;
    height: 9.14285714px;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    -webkit-transform: rotate(45deg) scale(0) translate(-50%, -50%);
    -ms-transform: rotate(45deg) scale(0) translate(-50%, -50%);
    transform: rotate(45deg) scale(0) translate(-50%, -50%);
    opacity: 0;
    -webkit-transition: all 0.1s cubic-bezier(0.71, -0.46, 0.88, 0.6), opacity 0.1s;
    transition: all 0.1s cubic-bezier(0.71, -0.46, 0.88, 0.6), opacity 0.1s;
    content: ' ';
  }

  .ant-tree-checkbox-input {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    cursor: pointer;
    opacity: 0;
  }

  .ant-tree-checkbox-checked .ant-tree-checkbox-inner::after {
    position: absolute;
    display: table;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    -webkit-transform: rotate(45deg) scale(1) translate(-50%, -50%);
    -ms-transform: rotate(45deg) scale(1) translate(-50%, -50%);
    transform: rotate(45deg) scale(1) translate(-50%, -50%);
    opacity: 1;
    -webkit-transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
    transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
    content: ' ';
  }

  .ant-tree-checkbox-checked .ant-tree-checkbox-inner {
    background-color: #1890ff;
    border-color: #1890ff;
  }

  .ant-tree-checkbox-disabled {
    cursor: not-allowed;
  }

  .ant-tree-checkbox-disabled.ant-tree-checkbox-checked .ant-tree-checkbox-inner::after {
    border-color: rgba(0, 0, 0, 0.25);
    -webkit-animation-name: none;
    animation-name: none;
  }

  .ant-tree-checkbox-disabled .ant-tree-checkbox-input {
    cursor: not-allowed;
  }

  .ant-tree-checkbox-disabled .ant-tree-checkbox-inner {
    background-color: #f5f5f5;
    border-color: #d9d9d9 !important;
  }

  .ant-tree-checkbox-disabled .ant-tree-checkbox-inner::after {
    border-color: #f5f5f5;
    border-collapse: separate;
    -webkit-animation-name: none;
    animation-name: none;
  }

  .ant-tree-checkbox-disabled+span {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }

  .ant-tree-checkbox-disabled:hover::after,
  .ant-tree-checkbox-wrapper:hover .ant-tree-checkbox-disabled::after {
    visibility: hidden;
  }

  .ant-tree-checkbox-wrapper {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    display: inline-block;
    line-height: unset;
    cursor: pointer;
  }

  .ant-tree-checkbox-wrapper.ant-tree-checkbox-wrapper-disabled {
    cursor: not-allowed;
  }

  .ant-tree-checkbox-wrapper+.ant-tree-checkbox-wrapper {
    margin-left: 8px;
  }

  .ant-tree-checkbox+span {
    padding-right: 8px;
    padding-left: 8px;
  }

  .ant-tree-checkbox-group {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    display: inline-block;
  }

  .ant-tree-checkbox-group-item {
    display: inline-block;
    margin-right: 8px;
  }

  .ant-tree-checkbox-group-item:last-child {
    margin-right: 0;
  }

  .ant-tree-checkbox-group-item+.ant-tree-checkbox-group-item {
    margin-left: 0;
  }

  .ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner {
    background-color: #fff;
    border-color: #d9d9d9;
  }

  .ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner::after {
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background-color: #1890ff;
    border: 0;
    -webkit-transform: translate(-50%, -50%) scale(1);
    -ms-transform: translate(-50%, -50%) scale(1);
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
    content: ' ';
  }

  .ant-tree-checkbox-indeterminate.ant-tree-checkbox-disabled .ant-tree-checkbox-inner::after {
    background-color: rgba(0, 0, 0, 0.25);
    border-color: rgba(0, 0, 0, 0.25);
  }

  .ant-tree {
    /* see https://github.com/ant-design/ant-design/issues/16259 */
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    margin: 0;
    padding: 0;
  }

  .ant-tree-checkbox-checked::after {
    position: absolute;
    top: 16.67%;
    left: 0;
    width: 100%;
    height: 66.67%;
  }

  .ant-tree ol,
  .ant-tree ul {
    margin: 0;
    padding: 0;
    list-style: none;
  }

  .ant-tree li {
    margin: 0;
    padding: 4px 0;
    white-space: nowrap;
    list-style: none;
    outline: 0;
  }

  .ant-tree li span[draggable],
  .ant-tree li span[draggable='true'] {
    line-height: 20px;
    border-top: 2px transparent solid;
    border-bottom: 2px transparent solid;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    /* Required to make elements draggable in old WebKit */
    -khtml-user-drag: element;
    -webkit-user-drag: element;
  }

  .ant-tree li.drag-over>span[draggable] {
    color: white;
    background-color: #1890ff;
    opacity: 0.8;
  }

  .ant-tree li.drag-over-gap-top>span[draggable] {
    border-top-color: #1890ff;
  }

  .ant-tree li.drag-over-gap-bottom>span[draggable] {
    border-bottom-color: #1890ff;
  }

  .ant-tree li.filter-node>span {
    color: #f5222d !important;
    font-weight: 500 !important;
  }

  .ant-tree li.ant-tree-treenode-loading span.ant-tree-switcher.ant-tree-switcher_open .ant-tree-switcher-loading-icon,
  .ant-tree li.ant-tree-treenode-loading span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-loading-icon {
    position: absolute;
    left: 0;
    display: inline-block;
    width: 24px;
    height: 24px;
    color: #1890ff;
    font-size: 14px;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }

  .ant-tree li.ant-tree-treenode-loading span.ant-tree-switcher.ant-tree-switcher_open .ant-tree-switcher-loading-icon svg,
  .ant-tree li.ant-tree-treenode-loading span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-loading-icon svg {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
  }

  :root .ant-tree li.ant-tree-treenode-loading span.ant-tree-switcher.ant-tree-switcher_open::after,
  :root .ant-tree li.ant-tree-treenode-loading span.ant-tree-switcher.ant-tree-switcher_close::after {
    opacity: 0;
  }

  .ant-tree li ul {
    margin: 0;
    padding: 0 0 0 18px;
  }

  .ant-tree li .ant-tree-node-content-wrapper {
    display: inline-block;
    height: 24px;
    margin: 0;
    padding: 0 5px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 24px;
    text-decoration: none;
    vertical-align: top;
    border-radius: 2px;
    cursor: pointer;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }

  .ant-tree li .ant-tree-node-content-wrapper:hover {
    background-color: #e6f7ff;
  }

  .ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background-color: #bae7ff;
  }

  .ant-tree li span.ant-tree-checkbox {
    top: initial;
    height: 24px;
    margin: 0 4px 0 2px;
    padding: 4px 0;
  }

  .ant-tree li span.ant-tree-switcher,
  .ant-tree li span.ant-tree-iconEle {
    display: inline-block;
    width: 24px;
    height: 24px;
    margin: 0;
    line-height: 24px;
    text-align: center;
    vertical-align: top;
    border: 0 none;
    outline: none;
    cursor: pointer;
  }

  .ant-tree li span.ant-tree-iconEle:empty {
    display: none;
  }

  .ant-tree li span.ant-tree-switcher {
    position: relative;
  }

  .ant-tree li span.ant-tree-switcher.ant-tree-switcher-noop {
    cursor: default;
  }

  .ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-tree-switcher-icon,
  .ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-select-switcher-icon {
    font-size: 12px;
    font-size: 10px \9;
    -webkit-transform: scale(0.83333333) rotate(0deg);
    -ms-transform: scale(0.83333333) rotate(0deg);
    transform: scale(0.83333333) rotate(0deg);
    display: inline-block;
    font-weight: bold;
  }

  :root .ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-tree-switcher-icon,
  :root .ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-select-switcher-icon {
    font-size: 12px;
  }

  .ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-tree-switcher-icon svg,
  .ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-select-switcher-icon svg {
    -webkit-transition: -webkit-transform 0.3s;
    transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
    transition: transform 0.3s, -webkit-transform 0.3s;
  }

  .ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-icon,
  .ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-select-switcher-icon {
    font-size: 12px;
    font-size: 10px \9;
    -webkit-transform: scale(0.83333333) rotate(0deg);
    -ms-transform: scale(0.83333333) rotate(0deg);
    transform: scale(0.83333333) rotate(0deg);
    display: inline-block;
    font-weight: bold;
  }

  :root .ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-icon,
  :root .ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-select-switcher-icon {
    font-size: 12px;
  }

  .ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-icon svg,
  .ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-select-switcher-icon svg {
    -webkit-transition: -webkit-transform 0.3s;
    transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
    transition: transform 0.3s, -webkit-transform 0.3s;
  }

  .ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-icon svg {
    -webkit-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    transform: rotate(-90deg);
  }

  .ant-tree li:last-child>span.ant-tree-switcher::before,
  .ant-tree li:last-child>span.ant-tree-iconEle::before {
    display: none;
  }

  .ant-tree>li:first-child {
    padding-top: 7px;
  }

  .ant-tree>li:last-child {
    padding-bottom: 7px;
  }

  .ant-tree-child-tree>li:first-child {
    padding-top: 8px;
  }

  .ant-tree-child-tree>li:last-child {
    padding-bottom: 0;
  }

  li.ant-tree-treenode-disabled>span:not(.ant-tree-switcher),
  li.ant-tree-treenode-disabled>.ant-tree-node-content-wrapper,
  li.ant-tree-treenode-disabled>.ant-tree-node-content-wrapper span {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
  }

  li.ant-tree-treenode-disabled>.ant-tree-node-content-wrapper:hover {
    background: transparent;
  }

  .ant-tree-icon__open {
    margin-right: 2px;
    vertical-align: top;
  }

  .ant-tree-icon__close {
    margin-right: 2px;
    vertical-align: top;
  }

  .ant-tree.ant-tree-show-line li {
    position: relative;
  }

  .ant-tree.ant-tree-show-line li span.ant-tree-switcher {
    color: rgba(0, 0, 0, 0.45);
    background: #fff;
  }

  .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher-noop .ant-tree-switcher-icon,
  .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher-noop .ant-select-switcher-icon {
    display: inline-block;
    font-weight: normal;
    font-size: 12px;
  }

  .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher-noop .ant-tree-switcher-icon svg,
  .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher-noop .ant-select-switcher-icon svg {
    -webkit-transition: -webkit-transform 0.3s;
    transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
    transition: transform 0.3s, -webkit-transform 0.3s;
  }

  .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher_open .ant-tree-switcher-icon,
  .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher_open .ant-select-switcher-icon {
    display: inline-block;
    font-weight: normal;
    font-size: 12px;
  }

  .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher_open .ant-tree-switcher-icon svg,
  .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher_open .ant-select-switcher-icon svg {
    -webkit-transition: -webkit-transform 0.3s;
    transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
    transition: transform 0.3s, -webkit-transform 0.3s;
  }

  .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-icon,
  .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher_close .ant-select-switcher-icon {
    display: inline-block;
    font-weight: normal;
    font-size: 12px;
  }

  .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-icon svg,
  .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher_close .ant-select-switcher-icon svg {
    -webkit-transition: -webkit-transform 0.3s;
    transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
    transition: transform 0.3s, -webkit-transform 0.3s;
  }

  .ant-tree.ant-tree-show-line li:not(:last-child)::before {
    position: absolute;
    left: 12px;
    width: 1px;
    height: 100%;
    height: calc(100% - 22px);
    margin: 22px 0 0;
    border-left: 1px solid #d9d9d9;
    content: ' ';
  }

  .ant-tree.ant-tree-icon-hide .ant-tree-treenode-loading .ant-tree-iconEle {
    display: none;
  }

  .ant-tree.ant-tree-block-node li .ant-tree-node-content-wrapper {
    width: calc(100% - 24px);
  }

  .ant-tree.ant-tree-block-node li span.ant-tree-checkbox+.ant-tree-node-content-wrapper {
    width: calc(100% - 46px);
  }
}