.activityNodeDrawer{
  .ant-drawer-wrapper-body{
    overflow: hidden;
  }
  .filterWrapper{
    display: flex;
    .nameSearch{
      width: 254px;
      margin-right: 10px;
    }
  }
  .mainContent{
    margin-top: 10px;
    display: flex;
    .table{
      width: 400px;
    }
    .FlowCanvas-wrap{
      width: 600px;
      height: 378px;
      margin-left: 20px;
      .selectedItem{
        display: flex;
        justify-content: space-between;
        .selectedItemName{
          width: 180px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .selectedItemLeft{
          display: flex;
          .selectedBatchInfo{
            padding-left: 10px;
          }
        }
        .selectedItemRight{
          display: flex;
          .delete{
            padding-left: 10px;
          }
        }
      }
    }
    .selectedSegment{
      margin-left: 20px;
      .selectedSegmentContent{
        .bubbleItem{
          cursor: pointer;
          background: #F8F8F8;
          max-width: 100%;
          display: inline-flex;
          align-items: center;
          padding-left: 5px;
          padding-right: 5px;
          height: 30px;
          line-height: 30px;
          border: 1px solid #C1C1C1;
          margin-bottom: 5px;
          border-radius: 5px;
          .itemName{
              display: inline-block;
              max-width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
          }
        }
      }
    }
  }
  .handleButton{
    position: absolute;
    right: 0px;
    top: 10px;
    button{
      margin-right: 20px;
    }
  }
}