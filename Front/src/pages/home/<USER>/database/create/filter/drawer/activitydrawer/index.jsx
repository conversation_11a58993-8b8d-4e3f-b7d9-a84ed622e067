import { useDebounce } from '@umijs/hooks';
import { Button, Drawer, Input, message, Popover, Table, Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import CampaignV2Service from 'service/CampaignV2Service';
import { useDeepCompareEffect } from 'utils/customhooks';
import { t } from 'utils/translation';
import './index.scss';

const campaignV2Service = new CampaignV2Service();

const ActivityDrawer = (props) => {
  const [name, setName] = useState('');
  const [type] = useState('');
  const [tableParams, setTableParams] = useState({
    size: 5,
    page: 1,
    search: [
      {
        connector: 'AND',
        propertyName: 'phase',
        operator: 'IN',
        value: 'ENABLE,STOPPED'
      },
      {
        connector: 'AND',
        propertyName: 'scenario.code',
        operator: 'EQ',
        value: props.scenario.code
      },
      { connector: 'AND', propertyName: 'name', operator: 'LIKE', value: '' },
      { connector: 'AND', propertyName: 'type', operator: 'EQ', value: '' },
      {
        operator: 'EQ',
        propertyName: 'deptId',
        value: window.getDeptId()
      }
    ],
    sorts: [
      {
        direction: 'desc',
        propertyName: 'id'
      }
    ]
  });

  const dataSourceCache = useRef([]);
  const debouncedName = useDebounce(name, 300);

  const [loading, setLoading] = useState(false);

  const [dataSource, setDataSource] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState(() => {
    dataSourceCache.current = props.dataSource.campaignList;
    return _.map(props.dataSource.campaignList, (item) => item.id) ?? [];
  });

  const [selectedBatch, setSelectedBatch] = useState(() => {
    let logList = [];
    _.map(props.dataSource.campaignList, (item) => {
      if (!_.isEmpty(item.logList)) {
        logList = _.concat(logList, item.logList);
      }
    });
    return logList;
  });

  const [currentPopover, setCurrentPopover] = useState('');

  // 搜索内容变更触发搜索
  const onNameChange = (e) => {
    setName(e.target.value);
  };

  useEffect(() => {
    // 当type和name变化的时候请求列表数据
    setTableParams((prevState) => {
      return {
        ...prevState,
        search: [
          {
            connector: 'AND',
            propertyName: 'phase',
            operator: 'IN',
            value: 'ENABLE,STOPPED'
          },
          {
            connector: 'AND',
            propertyName: 'scenario.code',
            operator: 'EQ',
            value: props.scenario.code
          },
          {
            connector: 'AND',
            propertyName: 'name',
            operator: 'LIKE',
            value: debouncedName ?? ''
          },
          {
            connector: 'AND',
            propertyName: 'type',
            operator: 'EQ',
            value: type ?? ''
          },
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: window.getDeptId()
          }
        ]
      };
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, debouncedName]);

  useDeepCompareEffect(() => {
    let ignore = false;
    const queryData = async () => {
      setLoading(true);
      const data = await campaignV2Service.query(tableParams);
      dataSourceCache.current = _.concat(dataSourceCache.current, data.content);
      setDataSource(data.content);
      setTotalCount(data.totalElements);
      setLoading(false);
    };

    !ignore && queryData();
    return () => {
      ignore = true;
    };
  }, [tableParams]);

  const columns = [
    {
      title: t('analysisCenter-a9YVdD9c1R5P'),
      dataIndex: 'name',
      width: 200
    },
    {
      title: t('analysisCenter-nQ0AgR9kLpwT'),
      dataIndex: ['scenario', 'name'],
      width: 110
    },
    {
      title: t('analysisCenter-ZH9ndeUwwAFP'),
      dataIndex: 'campaignType',
      render: (text) => (text ? _.filter(typeList, (v) => v.value === text)[0].text : '-'),
      width: 110
    },
    {
      title: t('analysisCenter-HG0lxFngNpj7'),
      dataIndex: 'phase',
      render: (text) => {
        return (
          <div className="status">
            {/* <span className="circle" style={{ backgroundColor: _.filter(phaseList, (v) => v.value === text)[0]?.color }} /> */}
            {text ? _.filter(phaseList, (v) => v.value === text)[0]?.name : '-'}
          </div>
        );
      },
      width: 110
    },
    {
      title: t('analysisCenter-7jQoTbhwKn1b'),
      dataIndex: 'calcRule',
      render: (text) => (text ? _.filter(ruleList, (v) => v.value === text)[0].text : '-'),
      width: 110
    },
    {
      title: t('analysisCenter-d89Z6BTSWH4F'),
      dataIndex: 'beginTime',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 150,
      sorter: true
    },
    {
      title: t('analysisCenter-6wzJCjOCu1lj'),
      dataIndex: 'endTime',
      render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
      width: 150,
      sorter: true
    }
  ];

  const handleTableChange = (page, filter, sorter) => {
    const { current, pageSize } = page;
    const direction = sorter.order ? sorter.order.substr(0, sorter.order.length - 3) : tableParams.sorts[0].direction;
    const propertyName = sorter.field ? (Array.isArray(sorter.field) ? sorter.field.join('.') : sorter.field) : 'id';
    setTableParams({
      ...tableParams,
      sorts: [{ direction, propertyName }],
      page: current,
      size: pageSize
    });
  };

  const onSelectChange = (selectedRowKeys) => {
    if (selectedRowKeys.length > 10) {
      message.warn(t('analysisCenter-KlbYxF2lLqVO'));
      return;
    }
    setSelectedRowKeys(selectedRowKeys);
  };

  const deleteSelectedKey = (key) => {
    const findIndex = _.findIndex(selectedRowKeys, (item) => item === key);
    const _selectedRowKeys = _.clone(selectedRowKeys);
    _selectedRowKeys.splice(findIndex, 1);
    setSelectedRowKeys(_selectedRowKeys);
    // 删除活动对应的批次
    const _selectedBatch = _.filter(selectedBatch, (item) => item.campaignId !== key);
    setSelectedBatch(_selectedBatch);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    preserveSelectedRowKeys: true
  };

  const onClose = () => {
    props.onClose && props.onClose();
  };

  // 提交
  const onSubmit = () => {
    if (_.isEmpty(selectedBatch)) {
      message.warn(t('analysisCenter-XPK7YPD7o4Vm'));
      return;
    }
    const submitData = _.map(selectedRowKeys, (item) => {
      const targetData = _.find(dataSourceCache.current, (data) => data.id === item);
      // 给目标数据增加批次字段
      const logList = _.filter(selectedBatch, (batch) => batch.campaignId === item);
      targetData.logList = logList;
      return targetData;
    });
    props.onSubmit && props.onSubmit(submitData, props.dataSource.filterId);
  };

  // 选择活动批次
  const selectActivityBatch = (data) => {
    setCurrentPopover(data);
  };

  // 关闭
  const clearCurrentPopover = () => {
    setCurrentPopover('');
  };

  // 选中的批次变化
  const onSelectedBatchChange = (data) => {
    // 先找出选中的批次中是否有当前活动的，如果有替换，如果没有则添加
    const findIndex = _.findIndex(selectedBatch, (item) => item.campaignId === data[0].campaignId);
    if (findIndex === -1) {
      setSelectedBatch(_.concat(selectedBatch, data));
    } else {
      const _selectedBatch = _.cloneDeep(selectedBatch);
      _selectedBatch.splice(findIndex, 1, data[0]);
      setSelectedBatch(_selectedBatch);
    }
  };

  return (
    <Drawer
      className="activityDrawer"
      title={t('analysisCenter-YJXhl0vX1eEs')}
      destroyOnClose
      placement="bottom"
      height={500}
      closable={false}
      // onClose={() => props.onClose(false)}
      open
      // bodyStyle={{ height: '100%' }}
    >
      <div className="filterWrapper">
        <Input className="nameSearch" placeholder="输入名称搜索" onChange={onNameChange} />
        {/* <Select
          showTime
          onChange={onTypeChange}
          allowClear
          maxTagCount={10}
          value={type}
          style={{ minWidth: 150 }}
        >
          {typeList.map((item) => (
            <Option
              value={item.value}
              key={item.key}
            >
              {item.text}
            </Option>
          ))}
        </Select> */}
      </div>
      <div className="mainContent">
        <div className="table1">
          <Table
            columns={columns}
            dataSource={dataSource}
            bordered={false}
            loading={loading}
            onChange={handleTableChange}
            scroll={{ x: 500, y: 280 }}
            rowSelection={rowSelection}
            rowKey="id"
            size="middle"
            pagination={{
              current: tableParams.page,
              total: totalCount,
              defaultPageSize: tableParams.size,
              showQuickJumper: true,
              showSizeChanger: true,
              showLessItems: true,
              pageSizeOptions: ['5', '10', '20']
            }}
          />
        </div>
        <div className="selectedSegment">
          <div className="selectedSegmentTitle">{`${t('analysisCenter-inA1cz79lYZX')}   [${selectedRowKeys.length}/10] ${t('analysisCenter-K64yhzyN18Pe')}`}</div>
          <div className="selectedSegmentContent">
            {_.map(selectedRowKeys, (item) => {
              const selectedData = _.findLast(dataSourceCache.current, (data) => data.id === item);
              const selectedBatchData = _.find(selectedBatch, (batch) => batch.campaignId === item);
              return (
                <div className="selectedItem" key={item}>
                  <div className="selectedItemName">
                    <Tooltip title={selectedData?.name}>{selectedData?.name}</Tooltip>
                  </div>
                  <div className="selectedBatchInfo">{`${t('analysisCenter-VvRiebgm4MnF')}${
                    selectedBatchData?.id ?? ''
                  }  ${t('analysisCenter-z0ix5QcmuxYS')}${selectedBatchData?.passedCount ?? ''}`}</div>
                  <Popover
                    content={
                      <PopvoerActivetyBatch
                        selectedBatch={selectedBatchData}
                        id={selectedData.id}
                        onSubmit={onSelectedBatchChange}
                        onClose={clearCurrentPopover}
                      />
                    }
                    // title="Title"
                    trigger="click"
                    open={`${currentPopover}` === `${selectedData.id}`}
                  >
                    <a
                      className="selectBatchButton"
                      onClick={() => {
                        selectActivityBatch(item);
                      }}
                    >
                      {t('analysisCenter-KxgQ0NugwWCx')}
                    </a>
                  </Popover>

                  <a
                    className="delete"
                    onClick={() => {
                      deleteSelectedKey(item);
                    }}
                  >
                    {t('analysisCenter-dKHgkLrycTqM')}
                  </a>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      <div className="handleButton">
        <Button onClick={onClose}>{t('analysisCenter-YZxBmeFX1eKP')}</Button>
        <Button onClick={onSubmit} type="primary">
          {t('analysisCenter-ZJlpt9SHsaer')}
        </Button>
      </div>
    </Drawer>
  );
};

const typeList = [
  {
    text: t('analysisCenter-Jkwg3zxlt3ZO'),
    value: 'CUSTOM',
    key: 'CUSTOM'
  },
  {
    text: t('analysisCenter-JVDebUcZnF7u'),
    value: 'TEMPLATE',
    key: 'TEMPLATE'
  }
];

const ruleList = [
  {
    text: t('analysisCenter-56diMKNLCXAm'),
    value: 'ONCE',
    key: 'ONCE'
  },
  {
    text: t('analysisCenter-ocFaW9tVQ7tg'),
    value: 'SCHEDULE',
    key: 'SCHEDULE'
  }
];

const PopvoerActivetyBatch = (props) => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [totalElements, setTotalElements] = useState(0);

  const [selectedRowKeys, setSelectedRowKeys] = useState(() => {
    return props.selectedBatch?.id ? [props.selectedBatch?.id] : [];
  });

  useEffect(() => {
    setSelectedRowKeys(props.selectedBatch?.id ? [props.selectedBatch?.id] : []);
  }, [props.selectedBatch]);

  const [tableParams, setTableParams] = useState({
    size: 5,
    page: 1,
    search: [{ propertyName: 'campaignId', operator: 'EQ', value: props.id }],
    sorts: [
      {
        direction: 'desc',
        propertyName: 'updateTime'
      }
    ]
  });

  useEffect(() => {
    const mask = document.getElementsByClassName('ant-drawer-mask')[0];
    const body = document.getElementsByClassName('ant-drawer-wrapper-body')[0];
    mask.addEventListener('click', props.onClose);
    body.addEventListener('click', props.onClose);
    return () => {
      mask.removeEventListener('click', props.onClose);
      body.removeEventListener('click', props.onClose);
    };
  }, []);

  useEffect(() => {
    let ignore = false;
    const init = async () => {
      const logData = await campaignV2Service.listCalcLogs(tableParams);
      if (!ignore) {
        setLoading(true);
        setDataSource(logData.content);
        setTotalElements(logData.totalElements);
        setLoading(false);
      }
    };

    init();
    return () => {
      ignore = true;
    };
  }, []);

  useEffect(() => {
    let ignore = false;
    const queryData = async () => {
      setLoading(true);
      const data = await campaignV2Service.listCalcLogs(tableParams);
      setDataSource(data.content);
      setTotalElements(data.totalElements);
      setLoading(false);
    };

    !ignore && queryData();
    return () => {
      ignore = true;
    };
  }, [tableParams]);

  const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      props.onSubmit && props.onSubmit(selectedRows);
      props.onClose && props.onClose();
    },
    type: 'radio',
    selectedRowKeys
  };

  const columns = [
    {
      title: t('analysisCenter-ndw7qlnAWRLm'),
      dataIndex: 'id',
      width: 200
    },
    {
      title: t('analysisCenter-C2bolAWQMgyT'),
      dataIndex: 'faked',
      render: (faked) => (faked ? t('analysisCenter-ZqXEUekwHkrR') : t('analysisCenter-JuxF6nFBvQjm'))
    },
    {
      title: t('analysisCenter-z0ix5QcmuxYS'),
      dataIndex: 'passedCount'
    },
    {
      title: t('analysisCenter-kWn6468MLfSz'),
      dataIndex: 'calcBeginTime',
      render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-')
    },
    {
      title: t('analysisCenter-HUDVi6H3yBMC'),
      dataIndex: 'calcEndTime',
      render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-')
    }
  ];

  const handleTableChange = (page, filter, sorter) => {
    const { current, pageSize } = page;
    const direction = sorter.order ? sorter.order.substr(0, sorter.order.length - 3) : tableParams.sorts[0].direction;
    const propertyName = sorter.field ? (Array.isArray(sorter.field) ? sorter.field.join('.') : sorter.field) : 'id';
    setTableParams({
      ...tableParams,
      sorts: [{ direction, propertyName }],
      page: current,
      size: pageSize
    });
  };

  return (
    <Table
      columns={columns}
      dataSource={dataSource}
      bordered={false}
      loading={loading}
      rowSelection={rowSelection}
      onChange={handleTableChange}
      rowKey="id"
      size="middle"
      pagination={{
        current: tableParams.page,
        total: totalElements,
        defaultPageSize: 5,
        showSizeChanger: true,
        showLessItems: true,
        pageSizeOptions: ['5', '10', '20']
      }}
    />
  );
};

const phaseList = [
  {
    name: t('analysisCenter-KlmLHS1gebUW'),
    text: t('analysisCenter-KlmLHS1gebUW'),
    value: 'DRAFT',
    key: 'DRAFT',
    color: 'rgba(0, 0, 0, 0)'
  },
  {
    name: t('analysisCenter-frnqbp2gXiTz'),
    text: t('analysisCenter-frnqbp2gXiTz'),
    value: 'TESTING',
    key: 'TESTING',
    color: '#52c41a'
  },
  {
    name: t('analysisCenter-78QmnplSNj8N'),
    text: t('analysisCenter-78QmnplSNj8N'),
    value: 'TEST_SUC',
    key: 'TEST_SUC',
    color: '#1890ff'
  },
  {
    name: t('analysisCenter-xUKHiBs7qxV6'),
    text: t('analysisCenter-xUKHiBs7qxV6'),
    value: 'ENABLE',
    key: 'ENABLE',
    color: '#f5222d'
  },
  {
    name: t('analysisCenter-0SG6SaPejQB5'),
    text: t('analysisCenter-0SG6SaPejQB5'),
    value: 'STOPPED',
    key: 'STOPPED',
    color: '#faad14'
  }
];

export default React.memo(ActivityDrawer);
