.activityDrawer{
  .ant-drawer-wrapper-body{
    overflow: hidden;
  }
  .filterWrapper{
    display: flex;
    .nameSearch{
      width: 254px;
      margin-right: 10px;
    }
  }
  .mainContent{
    margin-top: 10px;
    display: flex;
    .table1 {
      width: 500px;
    }
    .selectedSegment{
      margin-left: 20px;
      .selectedItem{
        height: 30px;
        display: flex;
        align-items: center;
        .selectedItemName{
          width: 180px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-right: 8px;
        }
        .selectBatchButton{
          margin-left: 20px;
        }
        .delete{
          margin-left: 20px;
        }
      }
    }
  }
  .handleButton{
    position: absolute;
    right: 0px;
    top: 10px;
    button{
      margin-right: 20px;
    }
  }
}