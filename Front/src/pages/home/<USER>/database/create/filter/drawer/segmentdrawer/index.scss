.segmentDrawer{
  .ant-drawer-wrapper-body{
    overflow: hidden;
  }
  .filterWrapper{
    display: flex;
    .segmentNameSearch{
      width: 254px;
      margin-right: 10px;
    }
  }
  .mainContent{
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    .table{
      width: 800px;
    }
    .selectedSegment{
      margin-left: 20px;
      .selectedItem{
        height: 30px;
        display: flex;
        align-items: center;
        .selectedItemName{
          width: 254px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
  }
  .handleButton{
    position: absolute;
    right: 0px;
    top: 10px;
    button{
      margin-right: 20px;
    }
  }
  .status{
    display: flex;
    align-items: center;
    .circle.green{
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 5px;
      background-color: #52c41a;
      border-radius: 3px;
    }
  }
  
  .selectedSegmentTitle {
    position: absolute;
    right: 24px;
  }
}