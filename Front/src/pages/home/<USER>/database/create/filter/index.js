import {
  CloseCircleFilled,
  CloseOutlined,
  DoubleRightOutlined,
  DownOutlined,
  FilterOutlined,
  SearchOutlined,
  UpOutlined
} from '@ant-design/icons';
import { AutoComplete, Checkbox, DatePicker, Input, Radio, Select } from 'antd';
import React, { useContext, useMemo, useRef, useState } from 'react';
import { useDrop } from 'react-dnd';

import dayjs from 'dayjs';
import _ from 'lodash';
import analysisCenterService from 'service/analysisCenterService';
import { t } from 'utils/translation';
import { v4 as getUuid } from 'uuid';
import { DataContext } from '../context';
import filterConfig from '../filterConfig';
import ActivityDrawer from './drawer/activitydrawer/index';
import ActivityNodeDrawer from './drawer/activitynodedrawer/index';
import SegmentDrawer from './drawer/segmentdrawer/index';
import TagDrawer from './drawer/tagdrawer/index';
import './index.scss';

const { Option } = Select;
const { RangePicker } = DatePicker;

const nullConditionValueArray = ['IS_NOT_NULL', 'IS_NULL', 'IS_TRUE', 'IS_FALSE'];

const getStyle = (canDrop, isOver) => {
  let color = '';
  if (canDrop && isOver) {
    color = 'var(--ant-primary-color)';
  } else if (canDrop) {
    color = '#fc8e44';
  } else {
    color = '#D7D7D7';
  }
  return color;
};

/**
 * 单一输入的输入框，要根据fieldType决定显示那种输入框，可能是TextInput, 也可能是DateInput
 * @param {FilterModel} value
 * @param {function} onChange
 */
function SingleInput(props) {
  const { fieldType } = props;
  if (
    fieldType === 'DATE' ||
    fieldType === 'DATETIME' ||
    fieldType === 'TIMESTAMP' ||
    fieldType === 'HIVE_DATE' ||
    fieldType === 'HIVE_TIMESTAMP'
  ) {
    return DateInput(props);
  }

  return TextInput(props);
}

const stringCanDropArr = ['EQ', 'NE'];
const numberCanDropArr = ['EQ', 'NE', 'GT', 'GTE', 'LT', 'LTE'];
const dateCanDropArr = ['EQ', 'NE', 'GT', 'GTE', 'LT', 'LTE'];

/**
 * 输入一个值的情况
 */
function TextInput(props) {
  let { fieldValue, schemaValue, items, onChange, onDropdownVisibleChange, dataSource, index } = props;
  const inputEl = useRef(null);

  const [{ canDrop, isOver }, drop] = useDrop({
    accept: 'box',
    drop: (item) => {
      // 判断item的类型，根据不同类型设置不同数据结构的数据
      if (item.dataType === 'TABLE_SCHEMA') {
        onChange(
          {
            ...item.dataSource
          },
          'TABLE_SCHEMA'
        );
      } else if (item.dataType === 'METRICS') {
        onChange(
          {
            ...item.dataSource
          },
          'TABLE_SCHEMA'
        );
      }
    },
    canDrop: (item) => {
      const { dataType } = item;
      const { tableSchema, filters } = dataSource;
      if (
        dataType === 'SEGMENT' ||
        dataType === 'CAMPAIGN' ||
        dataType === 'USER_TAG' ||
        dataType === 'CAMPAIGN_NODE' ||
        dataType === 'METRICS'
      ) {
        return false;
      } else if (
        item.dataSource.dataType === 'LONG' ||
        item.dataSource.dataType === 'INT' ||
        item.dataSource.dataType === 'DOUBLE'
      ) {
        if (tableSchema.dataType !== 'LONG' && tableSchema.dataType !== 'INT' && tableSchema.dataType !== 'DOUBLE') {
          return false;
        }
        if (!_.includes(numberCanDropArr, filters[index].operator)) {
          return false;
        }
      } else if (item.dataSource.dataType === 'STRING') {
        if (tableSchema.dataType !== 'STRING') {
          return false;
        }
        if (!_.includes(stringCanDropArr, filters[index].operator)) {
          return false;
        }
      } else if (
        item.dataSource.dataType === 'DATE' ||
        item.dataSource.dataType === 'DATETIME' ||
        item.dataSource.dataType === 'TIMESTAMP' ||
        item.dataSource.dataType === 'HIVE_DATE' ||
        item.dataSource.dataType === 'HIVE_TIMESTAMP'
      ) {
        if (
          tableSchema.dataType !== 'DATE' &&
          tableSchema.dataType !== 'DATETIME' &&
          tableSchema.dataType !== 'TIMESTAMP' &&
          tableSchema.dataType !== 'HIVE_DATE' &&
          tableSchema.dataType !== 'HIVE_TIMESTAMP'
        ) {
          return false;
        }
        if (!_.includes(dateCanDropArr, filters[index].operator)) {
          return false;
        }
      }
      if (!_.isEmpty(fieldValue)) {
        return false;
      }

      return true;
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop()
    })
  });

  const Style = useMemo(() => {
    return { borderColor: getStyle(canDrop, isOver) };
  }, [canDrop, isOver]);

  if (_.isArray(items)) {
    items.filter((i) => typeof i === 'number').forEach((v, i) => (items[i] = v.toString()));
  } else {
    items = [];
  }

  const itemsDs = items.map((v) => ({
    lable: v.name || v,
    value: v.value || v
  }));
  const getComponent = () => {
    if (!_.isEmpty(schemaValue)) {
      return (
        <div className="schemaTextInput">
          <span
            className="schemaTextInputText"
            title={`${schemaValue.table.displayName}.${schemaValue.displayName}`}
          >{`${schemaValue.table.displayName}.${schemaValue.displayName}`}</span>
          <CloseCircleFilled onClick={() => onChange(null, 'DEFAULT')} />
        </div>
      );
    } else {
      return (
        <AutoComplete
          options={itemsDs}
          showSearch={items.length > 0}
          onChange={(value) => {
            onChange(value, 'DEFAULT');
          }}
          // getPopupContainer={(triggerNode) => triggerNode}
          value={fieldValue}
          allowClear
          popupClassName="filterDropdown"
          ref={inputEl}
          filterOption={(input, option) => {
            return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          }}
          onDropdownVisibleChange={onDropdownVisibleChange}
        />
      );
    }
  };
  return (
    <div className="eleDropStyle" ref={drop} style={{ ...Style }}>
      {getComponent()}
    </div>
  );
  // return 'OneInput';
}

function DateInput(props) {
  const { fieldType, schemaValue, onChange, fieldValue, dataSource, index } = props;

  const [{ canDrop, isOver }, drop] = useDrop({
    accept: 'box',
    drop: (item) => {
      // 判断item的类型，根据不同类型设置不同数据结构的数据
      if (item.dataType === 'TABLE_SCHEMA') {
        onChange(
          {
            ...item.dataSource
          },
          'TABLE_SCHEMA'
        );
      } else if (item.dataType === 'METRICS') {
        onChange(
          {
            ...item.dataSource
          },
          'TABLE_SCHEMA'
        );
      }
    },
    canDrop: (item) => {
      const { dataType } = item;
      const { tableSchema, filters } = dataSource;
      if (
        dataType === 'SEGMENT' ||
        dataType === 'CAMPAIGN' ||
        dataType === 'USER_TAG' ||
        dataType === 'CAMPAIGN_NODE' ||
        dataType === 'METRICS'
      ) {
        return false;
      } else if (
        item.dataSource.dataType === 'LONG' ||
        item.dataSource.dataType === 'INT' ||
        item.dataSource.dataType === 'DOUBLE'
      ) {
        if (tableSchema.dataType !== 'LONG' && tableSchema.dataType !== 'INT' && tableSchema.dataType !== 'DOUBLE') {
          return false;
        }
        if (!_.includes(numberCanDropArr, filters[index].operator)) {
          return false;
        }
      } else if (item.dataSource.dataType === 'STRING') {
        if (tableSchema.dataType !== 'STRING') {
          return false;
        }
        if (!_.includes(stringCanDropArr, filters[index].operator)) {
          return false;
        }
      } else if (
        item.dataSource.dataType === 'DATE' ||
        item.dataSource.dataType === 'DATETIME' ||
        item.dataSource.dataType === 'TIMESTAMP' ||
        item.dataSource.dataType === 'HIVE_DATE' ||
        item.dataSource.dataType === 'HIVE_TIMESTAMP'
      ) {
        if (
          tableSchema.dataType !== 'DATE' &&
          tableSchema.dataType !== 'DATETIME' &&
          tableSchema.dataType !== 'TIMESTAMP' &&
          tableSchema.dataType !== 'HIVE_DATE' &&
          tableSchema.dataType !== 'HIVE_TIMESTAMP'
        ) {
          return false;
        }
        if (!_.includes(dateCanDropArr, filters[index].operator)) {
          return false;
        }
      }
      if (!_.isEmpty(fieldValue)) {
        return false;
      }

      return true;
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop()
    })
  });

  const Style = useMemo(() => {
    return { borderColor: getStyle(canDrop, isOver) };
  }, [canDrop, isOver]);

  const showTime =
    fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP'
      ? { format: 'HH:mm:ss' }
      : false;
  const format =
    fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP'
      ? 'YYYY-MM-DD HH:mm:ss'
      : 'YYYY-MM-DD';

  const onValueChange = (m) => {
    onChange((m && m?.valueOf()) || null);
  };

  const getComponent = () => {
    if (!_.isEmpty(schemaValue)) {
      return (
        <div className="schemaDateInput">
          <span
            className="schemaDateInputText"
            title={`${schemaValue.table.displayName}.${schemaValue.displayName}`}
          >{`${schemaValue.table.displayName}.${schemaValue.displayName}`}</span>
          <CloseCircleFilled onClick={() => onChange(null, 'DEFAULT')} />
        </div>
      );
    } else {
      return (
        <DatePicker
          placeholder={t('analysisCenter-2euiHrNY8b47')}
          showTime={showTime}
          format={format}
          // allowClear
          value={(props.fieldValue && dayjs(props.fieldValue)) || null}
          onChange={onValueChange}
        />
      );
    }
  };

  return (
    <div className="eleDropStyle" ref={drop} style={{ ...Style }}>
      {getComponent()}
    </div>
  );
}

const longTodayjs = (fv) => {
  return _.isArray(fv) ? fv.map((v) => dayjs(v)) : null;
};

function DateBetweenInput(props) {
  const { fieldValue, fieldType, onChange } = props;
  const [value, setValue] = useState(longTodayjs(fieldValue));
  const showTime =
    fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_DATE' || fieldType === 'HIVE_TIMESTAMP'
      ? { format: 'HH:mm:ss' }
      : false;
  const format =
    fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP'
      ? 'YYYY-MM-DD HH:mm:ss'
      : 'YYYY-MM-DD';

  const onValueChange = (m) => {
    setValue(m);
    if (m) {
      onChange([(m && m[0]?.valueOf()) || null, (m && m[1]?.valueOf()) || null]);
    } else {
      onChange(null);
    }
  };

  const onValueOk = (m) => {
    onChange([(m && m[0]?.valueOf()) || null, (m && m[1]?.valueOf()) || null]);
  };

  return (
    <RangePicker
      showTime={showTime}
      format={format}
      placeholder={[t('analysisCenter-kMGTEobozNaR'), t('analysisCenter-PNBw090kiha3')]}
      onOk={onValueOk}
      onChange={onValueChange}
      value={value}
    />
  );
}

function TwoInput(props) {
  const { fieldType, operator, onChange, fieldValue, schemaValue, dataSource, items } = props;
  if (
    fieldType === 'DATE' ||
    fieldType === 'DATETIME' ||
    fieldType === 'TIMESTAMP' ||
    fieldType === 'HIVE_DATE' ||
    fieldType === 'HIVE_TIMESTAMP'
  ) {
    return (
      <DateBetweenInput
        fieldType={fieldType}
        operator={operator}
        fieldValue={fieldValue}
        items={items}
        onChange={onChange}
        schemaValue={schemaValue}
        dataSource={dataSource}
      />
    );
  }
  return (
    <NumberBetweenInput
      fieldType={fieldType}
      operator={operator}
      fieldValue={fieldValue}
      items={items}
      onChange={onChange}
      schemaValue={schemaValue}
      dataSource={dataSource}
    />
  );
}

function NumberBetweenInput(props) {
  return (
    <>
      <TwoInputDrop {...props} index={0} />
      至
      <TwoInputDrop {...props} index={1} />
    </>
  );
}

function TwoInputDrop(props) {
  // let { index, onChange, schemaValue, fieldValue, dataSource } = props;
  let { index, onChange, fieldValue } = props;

  if (!_.isArray(fieldValue)) {
    fieldValue = ['', ''];
  }
  // const [{ canDrop, isOver }, drop] = useDrop({
  //   accept: 'box',
  //   drop: (item) => {
  //     // 判断item的类型，根据不同类型设置不同数据结构的数据
  //     if (item.dataType === 'TABLE_SCHEMA' || item.dataType === 'METRICS') {
  //       let lastValue = schemaValue || [];
  //       if (index === 0) {
  //         lastValue[0] = {
  //           ...item.dataSource
  //         };
  //       } else {
  //         lastValue[1] = {
  //           ...item.dataSource
  //         };
  //       }
  //       onChange(lastValue, 'TABLE_SCHEMA');
  //     }
  //   },
  //   canDrop: (item) => {
  //     const { dataType } = item;
  //     const { tableSchema } = dataSource;
  //     if (dataType === 'SEGMENT' || dataType === 'CAMPAIGN' || dataType === 'USER_TAG' || dataType === 'CAMPAIGN_NODE') {
  //       return false;
  //     } else if (dataType === 'METRICS' || item.dataSource.dataType === 'LONG' || item.dataSource.dataType === 'INT' || item.dataSource.dataType === 'DOUBLE') {
  //       if (tableSchema.dataType !== 'LONG' && tableSchema.dataType !== 'INT' && tableSchema.dataType !== 'DOUBLE') {
  //         return false;
  //       }
  //     } else if (item.dataSource.dataType === 'STRING') {
  //       if (tableSchema.dataType !== 'STRING') {
  //         return false;
  //       }
  //     } else if (item.dataSource.dataType === 'DATE' || item.dataSource.dataType === 'DATETIME' || item.dataSource.dataType === 'TIMESTAMP') {
  //       if (tableSchema.dataType !== 'DATE' && tableSchema.dataType !== 'DATETIME' && tableSchema.dataType !== 'TIMESTAMP') {
  //         return false;
  //       }
  //     }
  //     if (!_.isEmpty(fieldValue[index])) {
  //       return false;
  //     }

  //     return true;
  //   },
  //   collect: (monitor) => ({
  //     isOver: monitor.isOver(),
  //     canDrop: monitor.canDrop()
  //   })
  // });

  // const Style = useMemo(() => {
  //   return { borderColor: getStyle(canDrop, isOver) };
  // }, [canDrop, isOver]);

  const getComponent = () => {
    // if (!_.isEmpty(schemaValue) && !_.isEmpty(schemaValue[index])) {
    //   return <div className="schemaBetweenInput">
    //     <span className="schemaBetweenInputText" title={`${schemaValue[index].table.displayName}.${schemaValue[index].displayName}`}>{`${schemaValue[index].table.displayName}.${schemaValue[index].displayName}`}</span>
    //     <Icon type="close-circle" theme="filled" onClick={() => onChange(null, 'DEFAULT')} />
    //   </div>;
    // } else {
    return (
      <>
        <Input
          style={{ width: 85 }}
          placeholder={index === 0 ? t('analysisCenter-a9YVdD9c1R5P') : t('analysisCenter-nQ0AgR9kLpwT')}
          value={fieldValue[index] || undefined}
          size="small"
          className="filterInput"
          onChange={(e) => {
            let value = [];
            if (index === 0) {
              value = [e.target.value, fieldValue[1]];
            } else {
              value = [fieldValue[0], e.target.value];
            }
            onChange(value, 'DEFAULT');
          }}
        />
      </>
    );
    // }
  };
  // return <div className="eleDropStyle" ref={drop} style={{ ...Style }}>
  return getComponent();
  /* </div>; */
}

/**
 * 枚举类型的输入框
 * @param {object} props
 */
function EnumInput(props) {
  let { onChange, fieldValue, schemaValue, items, onDropdownVisibleChange, dataSource, index } = props;
  const inputEl = useRef(null);
  const [{ canDrop, isOver }, drop] = useDrop({
    accept: 'box',
    drop: (item) => {
      // 判断item的类型，根据不同类型设置不同数据结构的数据
      if (item.dataType === 'TABLE_SCHEMA') {
        onChange(
          {
            ...item.dataSource
          },
          'TABLE_SCHEMA'
        );
      } else if (item.dataType === 'METRICS') {
        onChange(
          {
            ...item.dataSource
          },
          'TABLE_SCHEMA'
        );
      }
    },
    canDrop: (item) => {
      const { dataType } = item;
      const { tableSchema, filters } = dataSource;
      if (
        dataType === 'SEGMENT' ||
        dataType === 'CAMPAIGN' ||
        dataType === 'USER_TAG' ||
        dataType === 'CAMPAIGN_NODE' ||
        dataType === 'METRICS'
      ) {
        return false;
      } else if (
        item.dataSource.dataType === 'LONG' ||
        item.dataSource.dataType === 'INT' ||
        item.dataSource.dataType === 'DOUBLE'
      ) {
        if (tableSchema.dataType !== 'LONG' && tableSchema.dataType !== 'INT' && tableSchema.dataType !== 'DOUBLE') {
          return false;
        }
        if (!_.includes(numberCanDropArr, filters[index].operator)) {
          return false;
        }
      } else if (item.dataSource.dataType === 'STRING') {
        if (tableSchema.dataType !== 'STRING') {
          return false;
        }
        if (!_.includes(stringCanDropArr, filters[index].operator)) {
          return false;
        }
      } else if (
        item.dataSource.dataType === 'DATE' ||
        item.dataSource.dataType === 'DATETIME' ||
        item.dataSource.dataType === 'TIMESTAMP' ||
        item.dataSource.dataType === 'HIVE_DATE' ||
        item.dataSource.dataType === 'HIVE_TIMESTAMP'
      ) {
        if (
          tableSchema.dataType !== 'DATE' &&
          tableSchema.dataType !== 'DATETIME' &&
          tableSchema.dataType !== 'TIMESTAMP' &&
          tableSchema.dataType !== 'HIVE_DATE' &&
          tableSchema.dataType !== 'HIVE_TIMESTAMP'
        ) {
          return false;
        }
        if (!_.includes(dateCanDropArr, filters[index].operator)) {
          return false;
        }
      }
      if (!_.isEmpty(fieldValue)) {
        return false;
      }

      return true;
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop()
    })
  });

  const Style = useMemo(() => {
    return { borderColor: getStyle(canDrop, isOver) };
  }, [canDrop, isOver]);

  if (!_.isArray(fieldValue)) {
    fieldValue = [];
  }

  const getComponent = () => {
    if (!_.isEmpty(schemaValue)) {
      return (
        <div className="schemaSelectInput">
          <span
            className="schemaSelectInputText"
            title={`${schemaValue.table.displayName}.${schemaValue.displayName}`}
          >{`${schemaValue.table.displayName}.${schemaValue.displayName}`}</span>
          <CloseCircleFilled onClick={() => onChange(null, 'DEFAULT')} />
        </div>
      );
    } else {
      return (
        <Select
          showTime
          mode="tags"
          onChange={(value) => onChange(value, 'DEFAULT')}
          allowClear
          maxTagCount={10}
          value={fieldValue}
          popupClassName="filterSelect"
          ref={inputEl}
          style={{ minWidth: 150 }}
          filterOption={(input, option) => {
            return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          }}
          optionLabelProp="label"
          onDropdownVisibleChange={onDropdownVisibleChange}
        >
          {items.map((item, i) => (
            <Option value={item.value ? item.value : item} label={item.name ? item.name : item} key={i}>
              {item.name ? item.name : item}
            </Option>
          ))}
        </Select>
      );
    }
  };
  return (
    <div className="eleDropStyle" ref={drop} style={{ ...Style }}>
      {getComponent()}
    </div>
  );
}

const Filter = () => {
  const { state, dispatch } = useContext(DataContext);
  const [filterSearch, setFilterSearch] = useState('');

  const { filterData: filterDataOriginalData, selectedChart, openedDrawer, info } = state;
  // console.log(state, 'state');

  const filterData = filterDataOriginalData[selectedChart];
  // console.log(filterData, 'filterData');

  // const [openedDrawer, setOpenedDrawer] = useState(null);

  const checkOptionsCache = useRef({});

  const [openedFilter, setOpenedFilter] = useState([]);

  // const [inUseFilter, setInUseFilter] = useState([]);

  const [{ canDrop, isOver }, drop] = useDrop({
    accept: 'box',
    drop: (item) => {
      // 判断item的类型，根据不同类型设置不同数据结构的数据
      let data = {};
      if (item.dataType === 'SEGMENT') {
        data = {
          type: item.dataType,
          name: item.name,
          filterId: getUuid(),
          key: item.key,
          dragType: 'FILTER',
          // 对比数据
          comparisonList: []
        };
      } else if (item.dataType === 'TABLE_SCHEMA') {
        data = {
          connector: 'AND',
          tableSchema: item.dataSource,
          // groupBy: item.groupBy,
          filters: [
            {
              operator:
                item.dataType === 'TABLE_SCHEMA' &&
                (item.dataSource.dataType === 'DATETIME' ||
                  item.dataSource.dataType === 'DATE' ||
                  item.dataSource.dataType === 'TIMESTAMP' ||
                  item.dataSource.dataType === 'HIVE_DATE' ||
                  item.dataSource.dataType === 'HIVE_TIMESTAMP')
                  ? 'BETWEEN'
                  : filterConfig.typeOperator[item.dataSource.dataType][0],
              fieldValue: ''
            },
            { operator: null, fieldValue: '' }
          ],
          filterType: 'ADVANCED',
          // item.dataType === 'TABLE_SCHEMA' &&
          // (item.dataSource.dataType === 'DATETIME' ||
          //   item.dataSource.dataType === 'DATE' ||
          //   item.dataSource.dataType === 'TIMESTAMP' ||
          //   item.dataSource.dataType === 'HIVE_DATE' ||
          //   item.dataSource.dataType === 'HIVE_TIMESTAMP')
          //   ? 'ADVANCED'
          //   : 'BASIC',
          dragType: 'FILTER',
          type: item.dataType,
          items: [], // 枚举值选项
          filterId: getUuid(),
          key: item.key,
          checkedBasicData: [],
          basicFilter: '',
          requestedItems: false // 请求标识，是否获取过当前字段枚举值
        };
      } else if (item.dataType === 'CAMPAIGN') {
        data = {
          type: item.dataType,
          name: item.name,
          filterId: getUuid(),
          key: item.key,
          dragType: 'FILTER',
          // 对比数据
          campaignList: []
        };
      } else if (item.dataType === 'USER_TAG') {
        data = {
          type: item.dataType,
          name: item.name,
          filterId: getUuid(),
          key: item.key,
          dragType: 'FILTER',
          // 对比数据
          userLabelList: []
        };
      } else if (item.dataType === 'CAMPAIGN_NODE') {
        data = {
          type: item.dataType,
          name: item.name,
          filterId: getUuid(),
          key: item.key,
          dragType: 'FILTER',
          // 对比数据
          campaignNodeList: []
        };
      }
      filterData.push(data);
      dispatch({
        openedDrawer: {
          name: item.dataType,
          data
        }
      });
    },
    canDrop: (item) => {
      const { dataType } = item;
      return dataType !== 'METRICS';
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop()
    })
  });
  const Style = useMemo(() => {
    return { borderColor: getStyle(canDrop, isOver) };
  }, [canDrop, isOver]);

  // 渲染第二行操作图标
  // const renderCenterHandle = (data, index) => {
  //   let coms = [];

  //   if (_.find(inUseFilter, inUseItem => inUseItem === index)) {
  //     coms.push(<Icon type="highlight" title="清除筛选器" key={index} />);
  //   }

  //   return coms;
  // };

  const onCloseDrawer = () => {
    dispatch({
      openedDrawer: null
    });
  };

  const onSegmentDrawerSubmit = (submitData, filterId) => {
    const findIndex = _.findIndex(filterData, (item) => item.filterId === filterId);
    if (findIndex !== -1) {
      filterData[findIndex].comparisonList = submitData;
      dispatch({});
    }
    // 关闭抽屉
    onCloseDrawer();
  };

  const onTagDrawerSubmit = (submitData, filterId) => {
    const findIndex = _.findIndex(filterData, (item) => item.filterId === filterId);
    if (findIndex !== -1) {
      filterData[findIndex].userLabelList = submitData;
      dispatch({});
    }
    // 关闭抽屉
    onCloseDrawer();
  };

  const onActivityDrawerSubmit = (submitData, filterId) => {
    const findIndex = _.findIndex(filterData, (item) => item.filterId === filterId);
    if (findIndex !== -1) {
      filterData[findIndex].campaignList = submitData;
      dispatch({});
    }
    // 关闭抽屉
    onCloseDrawer();
  };

  const onActivityNodeDrawerSubmit = (submitData, filterId) => {
    const findIndex = _.findIndex(filterData, (item) => item.filterId === filterId);
    if (findIndex !== -1) {
      filterData[findIndex].campaignNodeList = submitData;
      dispatch({});
    }
    // 关闭抽屉
    onCloseDrawer();
  };

  // 处理操作符变更
  const handleOperatorChange = async (data, filterIndex, value) => {
    const findIndex = _.findIndex(filterData, (item) => item.filterId === data.filterId);
    if (findIndex !== -1) {
      // 需要把对应的值清空
      filterData[findIndex].filters[filterIndex].fieldValue = null;
      filterData[findIndex].filters[filterIndex].operator = value;
      dispatch({});
    }
  };

  // 值变更
  const onChangeFieldValue = (value, data, filterIndex, valueType) => {
    const findIndex = _.findIndex(filterData, (item) => item.filterId === data.filterId);
    if (findIndex !== -1) {
      if (valueType === 'TABLE_SCHEMA') {
        filterData[findIndex].filters[filterIndex].schemaValue = value;
        filterData[findIndex].filters[filterIndex].fieldValue = null;
      } else {
        if (data.filters[filterIndex].operator === 'BETWEEN') {
          if (data?.tableSchema.dataType !== 'INT') {
            filterData[findIndex].filters[filterIndex].fieldValue = Array.isArray(value)
              ? value.map((item) => (item ? Math.floor(item / 1000) * 1000 : null))
              : value;
          } else {
            filterData[findIndex].filters[filterIndex].fieldValue = value;
          }
        } else {
          filterData[findIndex].filters[filterIndex].fieldValue = value;
        }
        filterData[findIndex].filters[filterIndex].schemaValue = null;
      }
      filterData[findIndex].filters[filterIndex].valueType = valueType || 'DEFAULT';
      dispatch({});
    }
  };

  const onDropdownVisibleChange = async (data, open) => {
    // 首先判断数据上是否有缓存的items，如果有，直接返回。如果没有，则请求，并将结果缓存到数据上
    if (!data.requestedItems && open) {
      const items = await analysisCenterService.findFilterEnum({
        tableId: data.tableSchema.table.id,
        schemaId: data?.tableSchema.id
      });
      const findIndex = _.findIndex(filterData, (item) => item.filterId === data.filterId);
      if (findIndex !== -1) {
        filterData[findIndex].items = items;
        filterData[findIndex].requestedItems = true;
        dispatch({});
      }
    }
  };

  const renderValueContent = (data, index) => {
    const { tableSchema, filters } = data;
    const fieldType = tableSchema.dataType;
    switch (filters[index].operator) {
      case 'EQ':
      case 'NE':
      case 'GT':
      case 'GTE':
      case 'LT':
      case 'LTE':
      case 'LIKE':
      case 'NOT_LIKE':
      case 'START_WITH':
      case 'NOT_START_WITH':
      case 'END_WITH':
      case 'NOT_END_WITH':
        return (
          <div className="valueContent">
            <SingleInput
              fieldType={fieldType}
              operator={filters[index].operator}
              fieldValue={filters[index].fieldValue}
              schemaValue={filters[index].schemaValue}
              items={data.items || []}
              onChange={(value, valueType) => {
                onChangeFieldValue(value, data, index, valueType);
              }}
              onDropdownVisibleChange={(open) => {
                onDropdownVisibleChange(data, open);
              }}
              dataSource={data}
              index={index}
            />
          </div>
        );
      case 'BETWEEN':
        return (
          <div className="valueContent">
            <TwoInput
              fieldType={fieldType}
              operator={filters[index].operator}
              fieldValue={filters[index].fieldValue}
              schemaValue={filters[index].schemaValue}
              items={data.items || []}
              onChange={(value, valueType) => {
                onChangeFieldValue(value, data, index, valueType);
              }}
              dataSource={data}
            />
          </div>
        );
      case 'IN':
      case 'NOT_IN':
        return (
          <div className="valueContent">
            <EnumInput
              fieldType={fieldType}
              operator={filters[index].operator}
              fieldValue={filters[index].fieldValue}
              schemaValue={filters[index].schemaValue}
              items={data.items || []}
              onChange={(value, valueType) => {
                onChangeFieldValue(value, data, index, valueType);
              }}
              onDropdownVisibleChange={(open) => {
                onDropdownVisibleChange(data, open);
              }}
              dataSource={data}
              index={index}
            />
          </div>
        );
      case 'IS_NOT_NULL':
      case 'IS_NULL':
      case 'IS_TRUE':
      case 'IS_FALSE':
      case '':
        return null;
      case null:
        return null;
      default:
        return (
          <div className="valueContent">
            <Input
              size="small"
              placeholder={t('analysisCenter-ZH9ndeUwwAFP')}
              disabled={!data.filters[index]?.operator}
            />
          </div>
        );
    }

    // return <div className="valueContent">
    //   <Input onChange={() => { handleConditionValueChange(data, index); }} />
    // </div>;
  };

  const onConnectorChange = (data, value) => {
    const findIndex = _.findIndex(filterData, (item) => item.filterId === data.filterId);
    if (findIndex !== -1) {
      filterData[findIndex].connector = value;
      dispatch({});
    }
  };

  const onCheckBoxGroupChange = (data, index, checkedValue) => {
    const findIndex = _.findIndex(filterData, (item) => item.filterId === data.filterId);
    if (findIndex !== -1) {
      filterData[findIndex].checkedBasicData = checkedValue;
      dispatch({});
    }
  };

  const onCheckAllChange = (data, index, checked) => {
    const findIndex = _.findIndex(filterData, (item) => item.filterId === data.filterId);
    if (findIndex !== -1) {
      filterData[findIndex].checkedBasicData = checked
        ? _.map(checkOptionsCache.current[data.key], (item) => item.value)
        : [];
      dispatch({});
    }
  };

  const basicFilterChange = (data, index, value) => {
    const findIndex = _.findIndex(filterData, (item) => item.filterId === data.filterId);
    if (findIndex !== -1) {
      filterData[findIndex].basicFilter = value;
      dispatch({});
    }
  };

  // 根据选中的类型生成相应的渲染内容，分基本筛选，高级筛选
  const renderOpenedContentByType = (data, index) => {
    // debugger;
    if (data.filterType === 'BASIC') {
      let options = [];
      if (data.basicFilter) {
        options = _.filter(checkOptionsCache.current[data.key], (item) => {
          return _.indexOf(item.label, data.basicFilter) !== -1 || _.includes(item.label, data.basicFilter);
        });
      } else {
        options = checkOptionsCache.current[data.key];
      }
      return (
        <>
          <Input
            placeholder={t('analysisCenter-HG0lxFngNpj7')}
            onChange={(e) => {
              basicFilterChange(data, index, e.target.value);
            }}
            style={{ marginBottom: 8 }}
            allowClear
            suffix={<SearchOutlined />}
          />
          {checkOptionsCache.current[data.key]?.length ? (
            <>
              <Checkbox
                indeterminate={
                  !!data.checkedBasicData.length &&
                  data.checkedBasicData.length < checkOptionsCache.current[data.key].length
                }
                onChange={(e) => {
                  onCheckAllChange(data, index, e.target.checked);
                }}
                checked={data.checkedBasicData.length === checkOptionsCache.current[data.key].length}
              >
                {t('analysisCenter-T8SpcfnSWrRM')}
              </Checkbox>
              <Checkbox.Group
                options={options}
                value={data.checkedBasicData || []}
                onChange={(checkedValue) => {
                  onCheckBoxGroupChange(data, index, checkedValue);
                }}
              />
            </>
          ) : null}
        </>
      );
    } else {
      return renderAdvancedFilter(data, index);
    }
  };

  const handleFilterTypeChange = (data, value) => {
    // debugger
    const findIndex = _.findIndex(filterData, (item) => item.filterId === data.filterId);
    if (findIndex !== -1) {
      filterData[findIndex].filterType = value;
      // 如果是切换到基本筛选，清空高级筛选的内容
      if (value === 'ADVANCED') {
        // filterData[findIndex] = _.omit(filterData[findIndex], 'connector');
        filterData[findIndex].filters = _.map(filterData[findIndex].filters, (item, index) => {
          if (index === 0) {
            return {
              ...item,
              fieldValue: ''
            };
          } else if (index === 1) {
            return {
              operator: null,
              fieldValue: ''
            };
          }
        });
      }
      dispatch({});
    }
  };

  // 点击删除分群
  const onClickDeleteSegment = (data, item) => {
    const filterId = data.filterId;
    const filterIndex = _.findIndex(filterData, (filter) => filter.filterId === filterId);
    if (filterIndex !== -1) {
      const itemIndex = _.findIndex(
        filterData[filterIndex].comparisonList,
        (comparisonItem) => comparisonItem.id === item.id
      );
      filterData[filterIndex].comparisonList.splice(itemIndex, 1);
      dispatch({});
    }
  };
  // 点击删除活动用户
  const onClickDeleteCampaignList = (data, item) => {
    const filterId = data.filterId;
    const filterIndex = _.findIndex(filterData, (filter) => filter.filterId === filterId);
    if (filterIndex !== -1) {
      const itemIndex = _.findIndex(
        filterData[filterIndex].campaignList,
        (campaignItem) => campaignItem.id === item.id
      );
      filterData[filterIndex].campaignList.splice(itemIndex, 1);
      dispatch({});
    }
  };

  // 点击删除标签
  const onClickDelete = (data, item, index) => {
    const filterId = data.filterId;
    const filterIndex = _.findIndex(filterData, (filter) => filter.filterId === filterId);
    if (filterIndex !== -1) {
      filterData[filterIndex].userLabelList.splice(index, 1);
      dispatch({});
    }
  };

  const onClickDeleteCampaignNode = (data, item, index) => {
    const filterId = data.filterId;
    const filterIndex = _.findIndex(filterData, (filter) => filter.filterId === filterId);
    if (filterIndex !== -1) {
      const selectedFlows = filterData[filterIndex].campaignNodeList[0]?.logList[0]?.selectedFlows;
      selectedFlows.splice(index, 1);
      dispatch({});
    }
  };

  // 渲染标签展开的内容
  const renderOpenedContentForTag = (data) => {
    if (!_.find(openedFilter, (item) => item === data.filterId)) {
      return null;
    } else {
      return (
        <div className="listWrapper" onClick={(e) => e.stopPropagation()}>
          {_.map(data.userLabelList, (item, index) => {
            return (
              <div className="bubbleItem">
                <div className="itemName" title={item.name}>
                  {item.name}
                </div>
                <CloseOutlined
                  onClick={() => {
                    onClickDelete(data, item, index);
                  }}
                  title={t('analysisCenter-XPK7YPD7o4Vm')}
                />
              </div>
            );
          })}
        </div>
      );
    }
  };
  // 关闭某个filter
  const onCloseFilter = (data) => {
    const findIndex = _.findIndex(filterData, (item) => item.filterId === data.filterId);
    if (findIndex !== -1) {
      filterData.splice(findIndex, 1);
      dispatch({});
    }
  };

  // 点击用户分群编辑
  const onClickEdit = (e, data, field) => {
    e.stopPropagation();
    dispatch({
      openedDrawer: { name: field, data }
    });
  };
  const getCheckboxGroup = async (targetData) => {
    const items = await analysisCenterService.findFilterEnum({
      tableId: targetData.tableSchema.table.id,
      schemaId: targetData?.tableSchema.id
    });
    // debugger;
    checkOptionsCache.current[targetData.key] = _.map(items, (item) => {
      return {
        label: item.name,
        value: item.value
      };
    });
    dispatch({});
  };
  // 改变筛选器展开状态
  const changeOpenStatus = (data) => {
    const _openedFilter = _.cloneDeep(openedFilter);
    if (_.find(_openedFilter, (item) => item === data.filterId)) {
      // 如果是展开则折叠
      const findIndex = _.findIndex(_openedFilter, (item) => item === data.filterId);
      _openedFilter.splice(findIndex, 1);
    } else {
      _openedFilter.push(data.filterId);
    }
    setOpenedFilter(_openedFilter);
    // 设置枚举值
    if (data.type === 'TABLE_SCHEMA' && !checkOptionsCache.current[data.key]) {
      // 处理table_schema形态的缓存
      getCheckboxGroup(data);
    }
  };

  const renderTopHandleForUser = (data, field) => {
    const coms = [];
    // 编辑按钮
    coms.push(
      <a
        onClick={(e) => {
          onClickEdit(e, data, field);
        }}
        key="edit"
      >
        {t('analysisCenter-nBy8RiPmU6Me')}
      </a>
    );
    // 展开折叠按钮
    if (_.find(openedFilter, (openedItem) => openedItem === data.filterId)) {
      coms.push(<UpOutlined key="up" title={t('analysisCenter-CwbYeUrBahyi')} />);
    } else {
      coms.push(<DownOutlined key="down" title={t('analysisCenter-fnyUT9DME8PL')} />);
    }
    // 关闭按钮,如果来源是拖拽，则生成关闭按钮
    if (data.dragType === 'FILTER') {
      coms.push(
        <CloseOutlined
          key="close"
          onClick={() => {
            onCloseFilter(data);
          }}
          title={t('analysisCenter-Dcml61uY6NCk')}
        />
      );
    }

    return coms;
  };
  // 渲染用户分群展开的内容
  const renderOpenedContentForSegment = (data) => {
    if (!_.find(openedFilter, (item) => item === data.filterId)) {
      return null;
    } else {
      // 分成是值类型和维度类型
      return (
        <div className="listWrapper" onClick={(e) => e.stopPropagation()}>
          {_.map(data.comparisonList, (item, index) => {
            return (
              <div className="bubbleItem" key={`${item.name}${index}`}>
                <div className="itemName" title={item.name}>
                  {item.name}
                </div>
                <CloseOutlined
                  onClick={() => {
                    onClickDeleteSegment(data, item);
                  }}
                  title={t('analysisCenter-VBeT2DMYv2Ik')}
                />
              </div>
            );
          })}
        </div>
      );
    }
  };
  const renderTopHandle = (data) => {
    const coms = [];
    // 展开折叠按钮
    if (_.find(openedFilter, (openedItem) => openedItem === data.filterId)) {
      coms.push(<UpOutlined key="up" title={t('analysisCenter-CwbYeUrBahyi')} />);
    } else {
      coms.push(<DownOutlined key="down" title={t('analysisCenter-fnyUT9DME8PL')} />);
    }
    // 关闭按钮,如果来源是拖拽，则生成关闭按钮
    if (data.dragType === 'FILTER') {
      coms.push(
        <CloseOutlined
          key="close"
          onClick={() => {
            onCloseFilter(data);
          }}
          title={t('analysisCenter-Dcml61uY6NCk')}
        />
      );
    }

    return coms;
  };
  // 渲染展开的内容
  const renderOpenedContent = (data, index) => {
    if (!_.find(openedFilter, (item) => item === data.filterId)) {
      return null;
    } else {
      // 分成是值类型和维度类型
      return (
        <>
          {data.dragType === 'VALUE' ? (
            renderAdvancedFilter(data, index)
          ) : (
            <>
              {/* <div>筛选类型</div> */}
              <Select
                popupClassName="filterSelect"
                style={{ width: '100%', marginTop: 8, marginBottom: 24 }}
                value={data.filterType}
                onChange={(value) => {
                  handleFilterTypeChange(data, value);
                }}
              >
                {/* {data.type === 'TABLE_SCHEMA' &&
                (data?.tableSchema?.dataType === 'DATETIME' ||
                  data?.tableSchema?.dataType === 'DATE' ||
                  data?.tableSchema?.dataType === 'TIMESTAMP' ||
                  data?.tableSchema?.dataType === 'HIVE_DATE' ||
                  data?.tableSchema?.dataType === 'HIVE_TIMESTAMP') ? null : (
                  <Option key="基本筛选" value="BASIC">
                    基本筛选
                  </Option>
                )} */}
                <Option key={t('analysisCenter-VBeT2DMYv2Ik')} value="ADVANCED">
                  {t('analysisCenter-VBeT2DMYv2Ik')}
                </Option>
              </Select>
              {renderOpenedContentByType(data, index)}
            </>
          )}
        </>
      );
    }
  };
  // 渲染活动展开的内容
  const renderOpenedContentForCampaign = (data) => {
    if (!_.find(openedFilter, (item) => item === data.filterId)) {
      return null;
    } else {
      return (
        <div className="listWrapper" onClick={(e) => e.stopPropagation()}>
          {_.map(data.campaignList, (item, index) => {
            return (
              <div className="bubbleItem" key={item.id}>
                <div
                  className="itemName"
                  title={`${item.name} ${t('analysisCenter-7jQoTbhwKn1b')}${item.logList[0]?.id ?? ''} ${t('analysisCenter-d89Z6BTSWH4F')}${item.logList[0]?.passedCount ?? ''}`}
                >{`${item.name} ${t('analysisCenter-7jQoTbhwKn1b')}${item.logList[0]?.id ?? ''} ${t('analysisCenter-d89Z6BTSWH4F')}${item.logList[0]?.passedCount ?? ''}`}</div>
                <CloseOutlined
                  onClick={() => {
                    onClickDeleteCampaignList(data, item, index);
                  }}
                  title={t('analysisCenter-6wzJCjOCu1lj')}
                />
              </div>
            );
          })}
        </div>
      );
    }
  };
  // 渲染活动用户节点
  const renderOpenedContentForCampaignNode = (data) => {
    if (!_.find(openedFilter, (item) => item === data.filterId)) {
      return null;
    } else {
      return (
        <div className="listWrapper" onClick={(e) => e.stopPropagation()}>
          <div
            className="batchInfo"
            title={`${data.campaignNodeList[0]?.name} ${t('analysisCenter-7jQoTbhwKn1b')}${
              data.campaignNodeList[0]?.logList[0]?.id ?? ''
            } ${t('analysisCenter-z0ix5QcmuxYS')}${data.campaignNodeList[0]?.logList[0]?.passedCount ?? ''}`}
          >{`${data.campaignNodeList[0]?.name || ''} ${t('analysisCenter-7jQoTbhwKn1b')}${
            data.campaignNodeList[0]?.logList[0]?.id ?? ''
          } ${t('analysisCenter-z0ix5QcmuxYS')}${data.campaignNodeList[0]?.logList[0]?.passedCount ?? ''}`}</div>
          {_.map(data.campaignNodeList[0]?.logList[0]?.selectedFlows, (item, index) => {
            return (
              <div className="bubbleItem" key={item.nodeId}>
                <div
                  className="itemName"
                  title={`[${item.nodeId}] ${item.name}`}
                >{`[${item.nodeId}] ${item.name}`}</div>
                <CloseOutlined
                  onClick={() => {
                    onClickDeleteCampaignNode(data, item, index);
                  }}
                  title={t('analysisCenter-ttGYqB7y6m6t')}
                />
              </div>
            );
          })}
        </div>
      );
    }
  };
  const renderAdvancedFilter = (data) => {
    return (
      <>
        <div>{t('analysisCenter-KxgQ0NugwWCx')}</div>
        <Select
          popupClassName="filterSelect"
          style={{ width: '100%', marginTop: 5 }}
          value={data.filters[0]?.operator}
          onChange={(value) => {
            handleOperatorChange(data, 0, value);
          }}
        >
          {/* <Option value="jack">Jack</Option> */}
          {_.map(filterConfig.typeOperator[data.tableSchema.dataType], (item) => {
            return (
              <Option key={item} value={item}>
                {_.find(filterConfig.operatorList, (operatorItem) => operatorItem.operator === item)?.name}
              </Option>
            );
          })}
        </Select>
        {renderValueContent(data, 0)}
        <Radio.Group
          value={data.connector}
          onChange={(e) => {
            onConnectorChange(data, e.target.value);
          }}
        >
          <Radio value="AND">{t('analysisCenter-dKHgkLrycTqM')}</Radio>
          <Radio value="OR">{t('analysisCenter-WTOfQ5NS2Udd')}</Radio>
        </Radio.Group>
        <Select
          popupClassName="filterSelect"
          style={{ width: '100%' }}
          value={data.filters[1]?.operator}
          onChange={(value) => {
            handleOperatorChange(data, 1, value);
          }}
        >
          {/* <Option value="jack">Jack</Option> */}
          {_.map(filterConfig.typeOperator[data.tableSchema.dataType], (item) => {
            return (
              <Option key={item} value={item}>
                {_.find(filterConfig.operatorList, (operatorItem) => operatorItem.operator === item)?.name}
              </Option>
            );
          })}
        </Select>
        {renderValueContent(data, 1)}
      </>
    );
  };

  // 根据filter过滤条件过滤筛选器的显示
  let lastFilter = filterData;

  if (filterSearch) {
    lastFilter = _.filter(
      filterData,
      (item) => _.includes(item?.tableSchema?.displayName, filterSearch) || _.includes(item?.name, filterSearch)
    );
  }

  const getFilterValue = (item) => {
    // 根据当前查询条件组装描述
    if (item.filterType === 'ADVANCED') {
      if (_.includes(nullConditionValueArray, item.filters[0].operator)) {
        const operator1 = _.find(
          filterConfig.operatorList,
          (listItem) => listItem.operator === item.filters[0]?.operator
        )?.name;
        const operator2 = _.find(
          filterConfig.operatorList,
          (listItem) => listItem.operator === item.filters[1]?.operator
        )?.name;
        let str = `${operator1} `;
        // 判断第二个
        if (_.includes(nullConditionValueArray, item.filters[1].operator)) {
          if (item.connector === 'AND') {
            str += t('analysisCenter-dKHgkLrycTqM');
          } else if (item.connector === 'OR') {
            str += t('analysisCenter-WTOfQ5NS2Udd');
          }
          str += ` ${operator2}`;
        } else if (!_.isNil(item.filters[1].fieldValue) && item.filters[1].fieldValue !== '') {
          if (item.connector === 'AND') {
            str += t('analysisCenter-dKHgkLrycTqM');
          } else if (item.connector === 'OR') {
            str += t('analysisCenter-WTOfQ5NS2Udd');
          }
          if (
            item.tableSchema.dataType === 'DATE' ||
            item.tableSchema.dataType === 'DATETIME' ||
            item.tableSchema.dataType === 'TIMESTAMP' ||
            item.tableSchema.dataType === 'HIVE_DATE' ||
            item.tableSchema.dataType === 'HIVE_TIMESTAMP'
          ) {
            if (_.isArray(item.filters[1].fieldValue)) {
              str += ` ${operator2} ${
                (item?.filters[1]?.fieldValue[0] &&
                  dayjs(item.filters[1].fieldValue[0]).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }~${
                (item?.filters[1]?.fieldValue[1] &&
                  dayjs(item.filters[1].fieldValue[1]).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }`;
            } else {
              str += ` ${operator2} ${
                (item.filters[1].fieldValue &&
                  dayjs(item.filters[1].fieldValue).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }`;
            }
          } else {
            str += ` ${operator2} ${item.filters[1].fieldValue}`;
          }
        } else if (item.filters[1].valueType === 'TABLE_SCHEMA' && !_.isEmpty(item.filters[1].schemaValue)) {
          if (item.connector === 'AND') {
            str += t('analysisCenter-dKHgkLrycTqM');
          } else if (item.connector === 'OR') {
            str += t('analysisCenter-WTOfQ5NS2Udd');
          }
          str += ` ${operator2} ${`${item.filters[1]?.schemaValue.table?.displayName}.${item.filters[1]?.schemaValue?.displayName}`}`;
        }
        return str;
      } else if (_.includes(nullConditionValueArray, item.filters[1].operator)) {
        const operator1 = _.find(
          filterConfig.operatorList,
          (listItem) => listItem.operator === item.filters[0]?.operator
        )?.name;
        const operator2 = _.find(
          filterConfig.operatorList,
          (listItem) => listItem.operator === item.filters[1]?.operator
        )?.name;
        // 判断第一个
        let str = '';
        if (_.includes(nullConditionValueArray, item.filters[0].operator)) {
          str += `${operator1} `;
          if (item.connector === 'AND') {
            str += t('analysisCenter-dKHgkLrycTqM');
          } else if (item.connector === 'OR') {
            str += t('analysisCenter-WTOfQ5NS2Udd');
          }
        } else if (!_.isNil(item.filters[0].fieldValue) && item.filters[0].fieldValue !== '') {
          if (
            item.tableSchema.dataType === 'DATE' ||
            item.tableSchema.dataType === 'DATETIME' ||
            item.tableSchema.dataType === 'TIMESTAMP' ||
            item.tableSchema.dataType === 'HIVE_DATE' ||
            item.tableSchema.dataType === 'HIVE_TIMESTAMP'
          ) {
            if (_.isArray(item.filters[0].fieldValue)) {
              str += ` ${operator1} ${
                (item.filters[0].fieldValue[0] &&
                  dayjs(item.filters[0].fieldValue[0]).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }~${
                (item.filters[0].fieldValue[1] &&
                  dayjs(item.filters[0].fieldValue[1]).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }`;
            } else {
              str += `${operator1} ${
                (item.filters[0].fieldValue &&
                  dayjs(item.filters[0].fieldValue).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }`;
            }
          } else {
            str += `${operator1} ${item.filters[0].fieldValue}`;
          }
          if (item.connector === 'AND') {
            str += t('analysisCenter-dKHgkLrycTqM');
          } else if (item.connector === 'OR') {
            str += t('analysisCenter-WTOfQ5NS2Udd');
          }
        } else if (item.filters[0].valueType === 'TABLE_SCHEMA' && !_.isEmpty(item.filters[0].schemaValue)) {
          str += ` ${operator1} ${`${item.filters[0]?.schemaValue.table?.displayName}.${item.filters[0]?.schemaValue?.displayName}`}`;
          if (item.connector === 'AND') {
            str += t('analysisCenter-dKHgkLrycTqM');
          } else if (item.connector === 'OR') {
            str += t('analysisCenter-WTOfQ5NS2Udd');
          }
        }
        str += ` ${operator2}`;
        return str;
      } else if (
        ((!_.isNil(item.filters[0].fieldValue) && item.filters[0].fieldValue !== '') ||
          (item.filters[0].valueType === 'TABLE_SCHEMA' && !_.isEmpty(item.filters[0].schemaValue))) &&
        ((!_.isNil(item.filters[1].fieldValue) && item.filters[1].fieldValue !== '') ||
          (item.filters[1].valueType === 'TABLE_SCHEMA' && !_.isEmpty(item.filters[1].schemaValue)))
      ) {
        const operator1 = _.find(
          filterConfig.operatorList,
          (listItem) => listItem.operator === item.filters[0]?.operator
        )?.name;
        const operator2 = _.find(
          filterConfig.operatorList,
          (listItem) => listItem.operator === item.filters[1]?.operator
        )?.name;
        let str = `${operator1} `;

        if (
          item.tableSchema.dataType === 'DATE' ||
          item.tableSchema.dataType === 'DATETIME' ||
          item.tableSchema.dataType === 'TIMESTAMP' ||
          item.tableSchema.dataType === 'HIVE_DATE' ||
          item.tableSchema.dataType === 'HIVE_TIMESTAMP'
        ) {
          if (item.filters[0].valueType === 'TABLE_SCHEMA' && !_.isEmpty(item.filters[0].schemaValue)) {
            str += `${item.filters[0]?.schemaValue?.table?.displayName}.${item.filters[0]?.schemaValue?.displayName}`;
          } else {
            if (_.isArray(item.filters[0].fieldValue)) {
              str += `${
                (item.filters[0].fieldValue[0] &&
                  dayjs(item.filters[0].fieldValue[0]).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }~${
                (item.filters[0].fieldValue[1] &&
                  dayjs(item.filters[0].fieldValue[1]).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }`;
            } else {
              str += `${
                (item.filters[0].fieldValue &&
                  dayjs(item.filters[0].fieldValue).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }`;
            }
          }
        } else {
          if (item.filters[0].valueType === 'TABLE_SCHEMA' && !_.isEmpty(item.filters[0].schemaValue)) {
            str += `${item.filters[0]?.schemaValue?.table?.displayName}.${item.filters[0]?.schemaValue?.displayName}`;
          } else {
            str += item.filters[0].fieldValue;
          }
        }

        if (item.connector === 'AND') {
          str += t('analysisCenter-dKHgkLrycTqM');
        } else if (item.connector === 'OR') {
          str += t('analysisCenter-WTOfQ5NS2Udd');
        }
        if (
          item.tableSchema.dataType === 'DATE' ||
          item.tableSchema.dataType === 'DATETIME' ||
          item.tableSchema.dataType === 'TIMESTAMP' ||
          item.tableSchema.dataType === 'HIVE_DATE' ||
          item.tableSchema.dataType === 'HIVE_TIMESTAMP'
        ) {
          if (item.filters[1].valueType === 'TABLE_SCHEMA' && !_.isEmpty(item.filters[1].schemaValue)) {
            str += ` ${operator2} ${item.filters[1]?.schemaValue?.table?.displayName}.${item.filters[1]?.schemaValue?.displayName}`;
          } else {
            if (_.isArray(item.filters[1].fieldValue)) {
              str += ` ${operator2} ${
                (item.filters[1].fieldValue[0] &&
                  dayjs(item.filters[1].fieldValue[0]).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }~${
                (item.filters[1].fieldValue[1] &&
                  dayjs(item.filters[1].fieldValue[1]).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }`;
            } else {
              str += ` ${operator2} ${
                (item.filters[1].fieldValue &&
                  dayjs(item.filters[1].fieldValue).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }`;
            }
          }
        } else {
          if (item.filters[1].valueType === 'TABLE_SCHEMA' && !_.isEmpty(item.filters[1].schemaValue)) {
            str += ` ${operator2} ${item.filters[1]?.schemaValue?.table?.displayName}.${item.filters[1]?.schemaValue?.displayName}`;
          } else {
            str += ` ${operator2} ${item.filters[1].fieldValue}`;
          }
        }
        return str;
      } else if (
        ((!_.isNil(item.filters[0].fieldValue) && item.filters[0].fieldValue !== '') ||
          (item.filters[0].valueType === 'TABLE_SCHEMA' && !_.isEmpty(item.filters[0].schemaValue))) &&
        (_.isNil(item.filters[1].fieldValue) || item.filters[1].fieldValue === '') &&
        _.isEmpty(item.filters[1].schemaValue)
      ) {
        const operator1 = _.find(
          filterConfig.operatorList,
          (listItem) => listItem.operator === item.filters[0]?.operator
        )?.name;
        let str = `${operator1} `;
        if (
          item.tableSchema.dataType === 'DATE' ||
          item.tableSchema.dataType === 'DATETIME' ||
          item.tableSchema.dataType === 'TIMESTAMP' ||
          item.tableSchema.dataType === 'HIVE_DATE' ||
          item.tableSchema.dataType === 'HIVE_TIMESTAMP'
        ) {
          if (item.filters[0].valueType === 'TABLE_SCHEMA' && !_.isEmpty(item.filters[0].schemaValue)) {
            str += `${item.filters[0]?.schemaValue?.table?.displayName}.${item.filters[0]?.schemaValue?.displayName}`;
          } else {
            if (_.isArray(item.filters[0].fieldValue)) {
              str += `${
                (item.filters[0].fieldValue[0] &&
                  dayjs(item.filters[0].fieldValue[0]).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }~${
                (item.filters[0].fieldValue[1] &&
                  dayjs(item.filters[0].fieldValue[1]).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }`;
            } else {
              str +=
                (item.filters[0].fieldValue &&
                  dayjs(item.filters[0].fieldValue).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                '';
            }
          }
        } else {
          if (item.filters[0].valueType === 'TABLE_SCHEMA' && !_.isEmpty(item.filters[0].schemaValue)) {
            str += `${item.filters[0]?.schemaValue?.table?.displayName}.${item.filters[0]?.schemaValue?.displayName}`;
          } else {
            str += item.filters[0].fieldValue;
          }
        }
        return str;
      } else if (
        (_.isNil(item.filters[0].fieldValue) || item.filters[0].fieldValue === '') &&
        _.isEmpty(item.filters[0].schemaValue) &&
        ((!_.isNil(item.filters[1].fieldValue) && item.filters[1].fieldValue !== '') ||
          (item.filters[1].valueType === 'TABLE_SCHEMA' && !_.isEmpty(item.filters[1].schemaValue)))
      ) {
        const operator2 = _.find(
          filterConfig.operatorList,
          (listItem) => listItem.operator === item.filters[1]?.operator
        )?.name;
        let str = `${operator2} `;
        if (
          item.tableSchema.dataType === 'DATE' ||
          item.tableSchema.dataType === 'DATETIME' ||
          item.tableSchema.dataType === 'TIMESTAMP' ||
          item.tableSchema.dataType === 'HIVE_DATE' ||
          item.tableSchema.dataType === 'HIVE_TIMESTAMP'
        ) {
          if (item.filters[1].valueType === 'TABLE_SCHEMA' && !_.isEmpty(item.filters[1].schemaValue)) {
            str += `${item.filters[1]?.schemaValue?.table?.displayName}.${item.filters[1]?.schemaValue?.displayName}`;
          } else {
            if (_.isArray(item.filters[1].fieldValue)) {
              str += `${
                (item.filters[1].fieldValue[0] &&
                  dayjs(item.filters[1].fieldValue[0]).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }~${
                (item.filters[1].fieldValue[1] &&
                  dayjs(item.filters[1].fieldValue[1]).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                ''
              }`;
            } else {
              str +=
                (item.filters[1].fieldValue &&
                  dayjs(item.filters[1].fieldValue).format(
                    item.tableSchema.dataType === 'DATETIME' ||
                      item.tableSchema.dataType === 'TIMESTAMP' ||
                      item.tableSchema.dataType === 'HIVE_TIMESTAMP'
                      ? 'YYYY-MM-DD HH:mm:ss'
                      : 'YYYY-MM-DD'
                  )) ||
                '';
            }
          }
        } else {
          if (item.filters[1].valueType === 'TABLE_SCHEMA' && !_.isEmpty(item.filters[1].schemaValue)) {
            str += `${item.filters[1]?.schemaValue?.table?.displayName}.${item.filters[1]?.schemaValue?.displayName}`;
          } else {
            str += item.filters[1].fieldValue;
          }
        }

        return str;
      }

      return t('analysisCenter-rSYQBSCopoZf');
    } else if (item.filterType === 'BASIC') {
      if (!_.isEmpty(item.checkedBasicData)) {
        let str = `${t('analysisCenter-nHEKdPV3duCE')} `;
        _.map(item.checkedBasicData, (basic, index) => {
          if (index !== 0) {
            str += ` ${t('analysisCenter-WTOfQ5NS2Udd')} ${basic}`;
          } else {
            str += basic;
          }
        });
        return str;
      }
      return `${t('analysisCenter-rSYQBSCopoZf')} `;
    }
  };

  const clearFilterCondition = (item, e) => {
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    // 清除当前筛选条件
    if (item.filterType === 'ADVANCED') {
      const findIndex = _.findIndex(filterData, (filterItem) => filterItem.filterId === item.filterId);
      if (findIndex !== -1) {
        filterData[findIndex].filters = _.map(filterData[findIndex].filters, (filterItem) => ({
          ...filterItem,
          operator: !filterItem.operator
            ? filterItem.operator
            : item.type === 'TABLE_SCHEMA' &&
                (item.tableSchema.dataType === 'DATETIME' ||
                  item.tableSchema.dataType === 'DATE' ||
                  item.tableSchema.dataType === 'TIMESTAMP' ||
                  item.tableSchema.dataType === 'HIVE_DATE' ||
                  item.tableSchema.dataType === 'HIVE_TIMESTAMP')
              ? 'BETWEEN'
              : filterConfig.typeOperator[item.tableSchema.dataType][0],
          fieldValue: '',
          valueType: 'DEFAULT',
          schemaValue: null
        }));
        filterData[findIndex].connector = 'AND';
        dispatch({});
      }
    } else if (item.filterType === 'BASIC') {
      const findIndex = _.findIndex(filterData, (filterItem) => filterItem.filterId === item.filterId);
      if (findIndex !== -1) {
        filterData[findIndex].checkedBasicData = [];
        dispatch({});
      }
    }
  };

  const onHide = () => {
    dispatch({ filterFold: true });
    // setFilterWidth(48);
  };

  return (
    <div className="analysisFilter">
      <div className="titileStyle">
        <div className="title">
          <FilterOutlined />
          <span style={{ marginLeft: 8 }}>{t('analysisCenter-YJXhl0vX1eEs')}</span>
        </div>
        <div className="hideIconWrap" onClick={onHide}>
          <DoubleRightOutlined style={{ float: 'right', fontSize: 12 }} />
        </div>
      </div>
      <div className="filterSearch">
        <Input
          placeholder={t('analysisCenter-HG0lxFngNpj7')}
          onChange={(e) => {
            setFilterSearch(e.target.value);
          }}
          allowClear
          suffix={<SearchOutlined />}
        />
      </div>
      <section className="filterContent">
        {_.map(lastFilter, (item, index) => {
          // 首先需要判断filterData是哪种类型，一种用户，另一种是表字段。
          if (item.type === 'SEGMENT') {
            // 用户分群
            return (
              <div className="filterConItem" key={item.filterId}>
                <div
                  className="handOpen"
                  onClick={() => {
                    changeOpenStatus(item, index);
                  }}
                >
                  <div className="filterFirstRow">
                    <div className="filterName" title={`${item.name}`}>
                      {item.name}
                    </div>
                    <div className="filterTopHandle">{renderTopHandleForUser(item, 'SEGMENT')}</div>
                  </div>
                  <div className="filterSecondRow">
                    <div>{t('analysisCenter-YZxBmeFX1eKP')}</div>
                  </div>
                  {renderOpenedContentForSegment(item)}
                </div>
              </div>
            );
          } else if (item.type === 'TABLE_SCHEMA') {
            // 表字段
            return (
              <div className="filterConItem" key={item.filterId}>
                <div
                  className="handOpen"
                  onClick={() => {
                    changeOpenStatus(item, index);
                  }}
                >
                  <div className="filterFirstRow">
                    <div
                      className="filterName"
                      title={`${item.tableSchema.table.displayName}[${item.tableSchema.displayName}]`}
                    >
                      {item.tableSchema.displayName}
                    </div>
                    <div className="filterTopHandle">{renderTopHandle(item, index)}</div>
                  </div>
                  <div className="filterSecondRow">
                    <div className="filterCondition" title={getFilterValue(item)}>
                      {getFilterValue(item)}
                    </div>
                    <div className="filterCenterHandle">
                      {/* {renderCenterHandle(item, index)} */}
                      <span
                        title={t('analysisCenter-K64yhzyN18Pe')}
                        onClick={(e) => {
                          clearFilterCondition(item, e);
                        }}
                      >
                        <svg
                          t="1606721714587"
                          className="icon"
                          viewBox="0 0 1024 1024"
                          version="1.1"
                          xmlns="http://www.w3.org/2000/svg"
                          p-id="3248"
                          xmlnsXlink="http://www.w3.org/1999/xlink"
                          width="18"
                          height="18"
                        >
                          <path
                            d="M533 810.2c-2.5 0-4.9-0.9-6.8-2.8L226.4 507.5c-3.8-3.8-3.8-9.9 0-13.6 3.8-3.8 9.9-3.8 13.6 0l299.9 299.9c3.8 3.8 3.8 9.9 0 13.6-1.9 1.8-4.4 2.8-6.9 2.8zM798.8 544.4c-2.5 0-4.9-0.9-6.8-2.8L492.1 241.7c-3.8-3.8-3.8-9.9 0-13.6s9.9-3.8 13.6 0L805.6 528c3.8 3.8 3.8 9.9 0 13.6-1.8 1.9-4.3 2.8-6.8 2.8z"
                            p-id="3249"
                            fill="#8a8a8a"
                          />
                          <path
                            d="M397.1 907.7c-39.5 0-79-15-109-45.1L117.7 692.2c-26.3-26.3-26.3-69.1 0-95.4l463.4-463.4c26.3-26.3 69.1-26.3 95.4 0l231.7 231.7c26.3 26.3 26.3 69.1 0 95.4l-402 402.1c-30.1 30.1-69.6 45.1-109.1 45.1z m231.8-755.5c-7.4 0-14.8 2.8-20.4 8.5L145 624.1c-5.5 5.5-8.5 12.7-8.5 20.4 0 7.7 3 15 8.5 20.4l170.4 170.4c45.1 45.1 118.5 45.1 163.6 0l402-402c11.3-11.3 11.3-29.6 0-40.9L649.3 160.7c-5.6-5.7-13-8.5-20.4-8.5z"
                            p-id="3250"
                            fill="#8a8a8a"
                          />
                        </svg>
                      </span>
                    </div>
                  </div>
                </div>
                {renderOpenedContent(item, index)}
              </div>
            );
          } else if (item.type === 'CAMPAIGN') {
            // 活动用户
            return (
              <div className="filterConItem" key={item.filterId}>
                <div
                  className="handOpen"
                  onClick={() => {
                    changeOpenStatus(item, index);
                  }}
                >
                  <div className="filterFirstRow">
                    <div className="filterName" title={`${item.name}`}>
                      {item.name}
                    </div>
                    <div className="filterTopHandle">{renderTopHandleForUser(item, 'CAMPAIGN')}</div>
                  </div>
                  <div className="filterSecondRow">
                    <div>{t('analysisCenter-XztOxd12pvEe')}</div>
                  </div>
                  {renderOpenedContentForCampaign(item)}
                </div>
              </div>
            );
          } else if (item.type === 'USER_TAG') {
            // 用户标签
            return (
              <div className="filterConItem" key={item.filterId}>
                <div
                  className="handOpen"
                  onClick={() => {
                    changeOpenStatus(item, index);
                  }}
                >
                  <div className="filterFirstRow">
                    <div className="filterName" title={`${item.name}`}>
                      {item.name}
                    </div>
                    <div className="filterTopHandle">{renderTopHandleForUser(item, 'USER_TAG')}</div>
                  </div>
                  <div className="filterSecondRow">
                    <div>{t('analysisCenter-Og8nI4MG4vru')}</div>
                  </div>
                  {renderOpenedContentForTag(item)}
                </div>
              </div>
            );
          } else if (item.type === 'CAMPAIGN_NODE') {
            // 活动用户节点
            return (
              <div className="filterConItem" key={item.filterId}>
                <div
                  className="handOpen"
                  onClick={() => {
                    changeOpenStatus(item, index);
                  }}
                >
                  <div className="filterFirstRow">
                    <div className="filterName" title={`${item.name}`}>
                      {item.name}
                    </div>
                    <div className="filterTopHandle">{renderTopHandleForUser(item, 'CAMPAIGN_NODE')}</div>
                  </div>
                  <div className="filterSecondRow">
                    <div>{t('analysisCenter-V10beejwfgjc')}</div>
                  </div>
                  {renderOpenedContentForCampaignNode(item)}
                </div>
              </div>
            );
          }
        })}
      </section>
      <div className="addFilter">
        <div className="dropStyle" ref={drop} style={{ ...Style }}>
          {t('analysisCenter-inA1cz79lYZX')}
        </div>
      </div>
      {!_.isEmpty(openedDrawer) && openedDrawer.name === 'SEGMENT' ? (
        <SegmentDrawer
          scenario={info.scenario}
          dataSource={openedDrawer.data}
          onClose={onCloseDrawer}
          onSubmit={onSegmentDrawerSubmit}
        />
      ) : null}
      {!_.isEmpty(openedDrawer) && openedDrawer.name === 'USER_TAG' ? (
        <TagDrawer dataSource={openedDrawer.data} onClose={onCloseDrawer} onSubmit={onTagDrawerSubmit} />
      ) : null}
      {!_.isEmpty(openedDrawer) && openedDrawer.name === 'CAMPAIGN' ? (
        <ActivityDrawer
          scenario={info.scenario}
          dataSource={openedDrawer.data}
          onClose={onCloseDrawer}
          onSubmit={onActivityDrawerSubmit}
        />
      ) : null}
      {!_.isEmpty(openedDrawer) && openedDrawer.name === 'CAMPAIGN_NODE' ? (
        <ActivityNodeDrawer
          scenario={info.scenario}
          dataSource={openedDrawer.data}
          onClose={onCloseDrawer}
          onSubmit={onActivityNodeDrawerSubmit}
        />
      ) : null}
    </div>
  );
};

export default Filter;
