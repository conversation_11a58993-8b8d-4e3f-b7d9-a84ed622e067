// @import 'assets/css/variable.scss';

.analysisFilter {
    .ant-input-affix-wrapper, .ant-select-selector, .ant-picker {
        border-radius: 6px !important;
    }

    .anticon-search {
        color: rgba(0, 0, 0, 0.45);
    }
    min-width: 240px;
    // font-size: 12px;

    ::-webkit-scrollbar {
        width: 4px;
        height: 4px;
        /**/
    }

    .ant-input-sm {
        height: 24px;
    }

    // .ant-checkbox-wrapper {
    //     font-size: 12px;

    //     span {
    //         font-size: 12px;
    //     }
    // }

    // .ant-select-selection-item {
    //     font-size: 12px;
    // }

    padding: 0 8px 0 8px;

    .paneHeader {
        font-size: 14px;
        padding-left: 4px;
        padding-top: 10px;
    }

    .titileStyle {
        font-size: 16px;
        font-weight: 600;
        padding: 8px 0 12px 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .hideIconWrap {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
      }

    .filterSearch {
        margin-bottom: 8px;
    }

    .dropStyle {
        border: 1px dashed $primary_color;
        text-align: center;
        border-radius: 6px;
        margin-bottom: 10px;
        height: 36px;
        line-height: 36px;
        margin-top: 5px;
        background-color:#FFF;
        // font-size: 12px;
    }

    .eleDropStyle {
        // border: 1px dashed $primary_color;
        // font-size: 12px;
        width: 100%;
        border-radius: 4px;
        display: inline-block;

        input {
            border: none;
        }

        .ant-select-selection {
            border: none;
        }

        .ant-select {
            width: 100%;
        }
        .ant-select-selector {
            width: 100%;
        }

        .schemaTextInput {
            position: relative;
            width: 167px;
            height: 24px;
            line-height: 24px;
            padding: 0 11px;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .schemaTextInputText {
                display: inline-block;
                max-width: calc(100% - 20px);
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .anticon-close-circle {
                display: none;
                position: absolute;
                right: 10px;
                top: 6px;
                color: rgba(0, 0, 0, 0.25);
                transition: color 0.3s ease, opacity 0.15s ease;
            }

        }

        .schemaTextInput:hover {
            .anticon-close-circle {
                display: inline-block;
            }
        }

        .schemaDateInput {
            position: relative;
            width: 195px;
            height: 24px;
            line-height: 24px;
            padding: 0 11px;
            background-color: #fff;

            .schemaDateInputText {
                display: inline-block;
                max-width: calc(100% - 10px);
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .anticon-close-circle {
                display: none;
                position: absolute;
                right: 10px;
                top: 6px;
                color: rgba(0, 0, 0, 0.25);
                transition: color 0.3s ease, opacity 0.15s ease;
            }

        }

        .schemaDateInput:hover {
            .anticon-close-circle {
                display: inline-block;
            }
        }

        .schemaSelectInput {
            position: relative;
            width: 167px;
            height: 24px;
            line-height: 24px;
            padding: 0 11px;
            background-color: #fff;

            .schemaSelectInputText {
                display: inline-block;
                max-width: calc(100% - 10px);
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .anticon-close-circle {
                display: none;
                position: absolute;
                right: 10px;
                top: 6px;
                color: rgba(0, 0, 0, 0.25);
                transition: color 0.3s ease, opacity 0.15s ease;
            }

        }

        .schemaSelectInput:hover {
            .anticon-close-circle {
                display: inline-block;
            }
        }

        .schemaBetweenInput {
            position: relative;
            width: 85px;
            height: 24px;
            line-height: 24px;
            padding: 0 11px;
            background-color: #fff;

            .schemaBetweenInputText {
                display: inline-block;
                max-width: calc(100% - 10px);
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .anticon-close-circle {
                display: none;
                position: absolute;
                right: 10px;
                top: 6px;
                color: rgba(0, 0, 0, 0.25);
                transition: color 0.3s ease, opacity 0.15s ease;
            }

        }

        .schemaBetweenInput:hover {
            .anticon-close-circle {
                display: inline-block;
            }
        }


    }

    .filterContent {
        max-height: calc(100vh - 230px);
        overflow-y: auto;

        .filterConItem {
            border: 1px solid #D9D9D9;
            background: #fff;
            border-radius: 6px;
            min-height: 50px;
            padding: 8px;
            margin-bottom: 8px;

            .handOpen {
                cursor: pointer;

                .filterFirstRow {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .filterName {
                        flex: 1;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        max-width: calc(100% - 40px);
                    }

                    .filterTopHandle {
                        display: none;
                    }
                }

                .filterSecondRow {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .filterCondition {
                        flex: 1;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        max-width: calc(100% - 30px);
                    }

                    .filterCenterHandle {
                        display: none;
                    }
                }

                .listWrapper {
                    .bubbleItem {
                        background: #F8F8F8;
                        max-width: 100%;
                        display: inline-flex;
                        align-items: center;
                        padding-left: 5px;
                        padding-right: 5px;
                        height: 30px;
                        line-height: 30px;
                        border: 1px solid #C1C1C1;
                        margin-bottom: 5px;
                        border-radius: 5px;

                        .itemName {
                            display: inline-block;
                            max-width: 100%;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                    }

                    .batchInfo {
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }
            }

            .valueContent {
                margin-top: 8px;
                display: flex;
                align-items: center;
            }

            .ant-radio-group {
                margin: 24px 0 8px;
            }

            .ant-checkbox-group {
                display: flex;
                flex-direction: column;
                max-height: 200px;
                overflow-y: auto;
            }

        }
    }

    .filterConItem:hover {
        .handOpen {
            .filterFirstRow {
                .filterTopHandle {
                    display: flex;
                    gap: 8px;
                    color:rgba(0, 0, 0, 0.45);
                    font-size: 12px;
                }
            }

            .filterSecondRow {
                .filterCenterHandle {
                    display: flex;
                    gap: 8px;
                    color:rgba(0, 0, 0, 0.45);
                    font-size: 12px;
                }
            }
        }
    }

    // .ant-select-selection-selected-value {
    //     font-size: 12px;
    // }

    // .ant-input-search {
    //     input {
    //         font-size: 12px;
    //     }
    // }

    // .ant-select-search {
    //     input {
    //         font-size: 12px;
    //     }
    // }

    // .ant-radio-group {
    //     .ant-radio-wrapper {
    //         >span:last-child {
    //             font-size: 12px;
    //         }
    //     }
    // }

}

// .filterSelect {
//     .ant-select-item-option-content {
//         font-size: 12px;
//     }
// }