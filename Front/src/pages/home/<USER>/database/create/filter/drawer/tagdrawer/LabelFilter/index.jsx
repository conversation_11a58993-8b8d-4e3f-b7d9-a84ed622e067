import React, { forwardRef, useContext, useImperativeHandle, useRef } from 'react';

import DataEngineService from 'service/dataEngineService';
import FunnelAnalysis from 'service/funnelAnalysis';
import TagAndTagValueService from 'service/tagAndTagValueService';
import { Label } from 'wolf-static-cpnt';
import { DataContext } from '../../../../context';
import './index.scss';

const dataEngineService = new DataEngineService();

const Labels = (props, parentRef) => {
  const filterEl = useRef();

  const { state } = useContext(DataContext);

  useImperativeHandle(parentRef, () => ({
    isValid: () => filterEl.current.isValid()
  }));

  const dataProvider = {
    getPropertyList: async (name) => {
      const propertyList = await dataEngineService.propertyList({
        name,
        eventId: 0
      });
      return propertyList;
    },
    getPropertyEnumList: async (tableId, schemaId) => {
      const propertyItem = await dataEngineService.findFilterEnum({
        tableId,
        schemaId
      });
      return propertyItem;
    },
    getTagList: async (data) =>
      TagAndTagValueService.getTagList({
        ...data,
        scenario: state.info.scenario
      }),
    findAllCategory: async (data) =>
      TagAndTagValueService.findAllCategory({
        ...data,
        scenario: state.info.scenario
      }),
    findCategoryByProjectId: async () => TagAndTagValueService.findCategoryByProjectId({}),
    getTagValuesById: async (id) => TagAndTagValueService.getTagValuesById({ labelId: id }),
    getEventCountLogsByProjectId: async () =>
      FunnelAnalysis.getEventCountLogsByProjectId({
        projectId: localStorage.getItem('projectId')
      })
  };

  return (
    <div className="labelFilter">
      <Label dataProvider={dataProvider} value={props.value} onChange={props.onChange} mode="edit" ref={filterEl} />
    </div>
  );
};

export default forwardRef(Labels);
