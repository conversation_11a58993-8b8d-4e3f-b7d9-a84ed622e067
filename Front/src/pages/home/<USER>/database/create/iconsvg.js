import React from 'react';

const iconSvg = [
  {
    keywords: 'GROUP_TABLE',
    normal: (
      <svg width="24" height="20" viewBox="0 0 24 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <title>分组表</title>
        <g clipPath="url(#clip0_8735_97725)">
          <rect width="24" height="5" fill="#FF6800" />
          <rect y="7" width="7" height="13" fill="#4C5CFF" />
          <rect x="9" y="7" width="15" height="3" fill="#4C5CFF" />
          <rect x="9" y="12" width="15" height="3" fill="#4C5CFF" />
          <rect x="9" y="17" width="15" height="3" fill="#4C5CFF" />
        </g>
        <defs>
          <clipPath id="clip0_8735_97725">
            <rect width="24" height="20" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    gray: (
      <svg width="24" height="20" viewBox="0 0 24 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <title>分组表</title>
        <g clipPath="url(#clip0_8735_97808)">
          <rect width="24" height="5" fill="#999999" />
          <rect y="7" width="7" height="13" fill="#CCCCCC" />
          <rect x="9" y="7" width="15" height="3" fill="#CCCCCC" />
          <rect x="9" y="12" width="15" height="3" fill="#CCCCCC" />
          <rect x="9" y="17" width="15" height="3" fill="#CCCCCC" />
        </g>
        <defs>
          <clipPath id="clip0_8735_97808">
            <rect width="24" height="20" fill="white" />
          </clipPath>
        </defs>
      </svg>
    )
  },
  {
    keywords: 'TABLE',
    normal: (
      <svg width="24" height="20" viewBox="0 0 24 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <title>交叉表</title>
        <g clipPath="url(#clip0_8735_97766)">
          <rect width="7" height="5" fill="#FF6800" />
          <rect x="9" width="15" height="5" fill="#4C5CFF" />
          <rect y="7" width="7" height="13" fill="#4C5CFF" />
          <rect x="9" y="7" width="15" height="13" fill="#4C5CFF" />
        </g>
        <defs>
          <clipPath id="clip0_8735_97766">
            <rect width="24" height="20" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    gray: (
      <svg width="24" height="20" viewBox="0 0 24 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <title>交叉表</title>
        <g clipPath="url(#clip0_8735_97803)">
          <rect width="7" height="5" fill="#999999" />
          <rect x="9" width="15" height="5" fill="#CCCCCC" />
          <rect y="7" width="7" height="13" fill="#CCCCCC" />
          <rect x="9" y="7" width="15" height="13" fill="#CCCCCC" />
        </g>
        <defs>
          <clipPath id="clip0_8735_97803">
            <rect width="24" height="20" fill="white" />
          </clipPath>
        </defs>
      </svg>
    )
  },
  {
    keywords: 'COLUMN',
    normal: (
      <svg
        width="24px"
        height="20px"
        viewBox="0 0 24 20"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>簇状柱形图</title>
        <g id="Page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Desktop-HD-Copy" transform="translate(-224.000000, -99.000000)">
            <g id="Group-7" transform="translate(224.000000, 97.000000)">
              <g id="柱状图" transform="translate(0.000000, 2.000000)">
                <polygon id="Path" fill="#FF6800" fillRule="nonzero" points="0 10 6 10 6 13 0 13" />
                <polygon id="Path" fill="#4C5CFF" fillRule="nonzero" points="0 13 6 13 6 18 0 18" />
                <polygon id="Path" fill="#FF6800" fillRule="nonzero" points="9 0 15 0 15 6 9 6" />
                <polygon id="Path-Copy" fill="#FF6800" fillRule="nonzero" points="18 5 24 5 24 9 18 9" />
                <polygon id="Path" fill="#4C5CFF" fillRule="nonzero" points="9 6 15 6 15 18 9 18" />
                <polygon id="Path-Copy-2" fill="#4C5CFF" fillRule="nonzero" points="18 9 24 9 24 18 18 18" />
                <rect id="Rectangle" fill="#4C5CFF" x="0" y="19" width="24" height="1" />
              </g>
            </g>
          </g>
        </g>
      </svg>
    ),
    gray: (
      <svg
        width="24px"
        height="20px"
        viewBox="0 0 24 20"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>柱状图</title>
        <g id="Page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Desktop-HD-Copy" transform="translate(-262.000000, -99.000000)">
            <g id="Group-7" transform="translate(224.000000, 97.000000)">
              <g id="柱状图-gray" transform="translate(38.000000, 2.000000)">
                <polygon id="Path" fill="#999999" fillRule="nonzero" points="0 10 6 10 6 13 0 13" />
                <polygon id="Path" fill="#CCCCCC" fillRule="nonzero" points="0 13 6 13 6 18 0 18" />
                <polygon id="Path" fill="#999999" fillRule="nonzero" points="9 0 15 0 15 6 9 6" />
                <polygon id="Path-Copy" fill="#999999" fillRule="nonzero" points="18 5 24 5 24 9 18 9" />
                <polygon id="Path" fill="#CCCCCC" fillRule="nonzero" points="9 6 15 6 15 18 9 18" />
                <polygon id="Path-Copy-2" fill="#CCCCCC" fillRule="nonzero" points="18 9 24 9 24 18 18 18" />
                <rect id="Rectangle" fill="#CCCCCC" x="0" y="19" width="24" height="1" />
              </g>
            </g>
          </g>
        </g>
      </svg>
    )
  },
  {
    keywords: 'DONUT',
    normal: (
      <svg
        width="24px"
        height="20px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>环图</title>
        <g id="Page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Desktop-HD-Copy" transform="translate(-299.000000, -131.000000)" fillRule="nonzero">
            <g id="Group-7" transform="translate(224.000000, 97.000000)">
              <g id="环图" transform="translate(75.000000, 34.000000)">
                <path
                  d="M13.0099378,4.4088014 C17.3527673,4.95166959 19.5675907,8.12186838 19.5675907,11.9652577 C19.5675907,12.9640996 19.4155737,13.9195136 19.0681504,14.7880718 L22.8463786,16.9811619 C23.5412251,15.4612237 23.8886484,13.745802 23.8886484,11.9652577 C23.8886484,5.7116387 19.5241242,0.565412102 13.0099378,0.0225825239 L13.0099378,4.4088014 L13.0099378,4.4088014 Z"
                  id="Path"
                  fill="#FF6800"
                />
                <path
                  d="M10.8385809,19.521714 C7.58148765,18.9788458 4.34612768,15.8086084 4.34612768,11.9652577 C4.34612768,8.12186838 7.58148765,4.95166959 10.8385809,4.4088014 L10.8385809,0.0225825239 C5.41009216,0.565412072 0.0250313694,5.71163867 0.0250313694,11.9652577 C0.0250313694,18.2188767 5.41009216,23.3651033 10.8385809,23.9079328 L10.8385809,19.521714 Z"
                  id="Path"
                  fill="#4C5CFF"
                />
                <path
                  d="M17.9607581,16.6771666 C16.7664713,18.1971048 15.1813719,19.2177186 13.0099764,19.521714 L13.0099764,23.9079328 C16.2670696,23.5822042 19.7196077,21.6713762 21.6738636,18.8702953 L17.9607581,16.6771666 Z"
                  id="Path"
                  fill="#4C5CFF"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    ),
    gray: (
      <svg
        width="24px"
        height="20px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>环图</title>
        <g id="Page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Desktop-HD-Copy" transform="translate(-337.000000, -131.000000)" fillRule="nonzero">
            <g id="Group-7" transform="translate(224.000000, 97.000000)">
              <g id="环图-gray" transform="translate(113.000000, 34.000000)">
                <path
                  d="M13.0099378,4.4088014 C17.3527673,4.95166959 19.5675907,8.12186838 19.5675907,11.9652577 C19.5675907,12.9640996 19.4155737,13.9195136 19.0681504,14.7880718 L22.8463786,16.9811619 C23.5412251,15.4612237 23.8886484,13.745802 23.8886484,11.9652577 C23.8886484,5.7116387 19.5241242,0.565412102 13.0099378,0.0225825239 L13.0099378,4.4088014 L13.0099378,4.4088014 Z"
                  id="Path"
                  fill="#999999"
                />
                <path
                  d="M10.8385809,19.521714 C7.58148765,18.9788458 4.34612768,15.8086084 4.34612768,11.9652577 C4.34612768,8.12186838 7.58148765,4.95166959 10.8385809,4.4088014 L10.8385809,0.0225825239 C5.41009216,0.565412072 0.0250313694,5.71163867 0.0250313694,11.9652577 C0.0250313694,18.2188767 5.41009216,23.3651033 10.8385809,23.9079328 L10.8385809,19.521714 Z"
                  id="Path"
                  fill="#CCCCCC"
                />
                <path
                  d="M17.9607581,16.6771666 C16.7664713,18.1971048 15.1813719,19.2177186 13.0099764,19.521714 L13.0099764,23.9079328 C16.2670696,23.5822042 19.7196077,21.6713762 21.6738636,18.8702953 L17.9607581,16.6771666 Z"
                  id="Path"
                  fill="#CCCCCC"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    )
  },
  {
    keywords: 'CARD',
    normal: (
      <svg
        width="24px"
        height="20px"
        viewBox="0 0 24 20"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>指标卡</title>
        <g id="Page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Desktop-HD-Copy" transform="translate(-224.000000, -286.000000)" fillRule="nonzero">
            <g id="Group-7" transform="translate(224.000000, 97.000000)">
              <g id="指标卡" transform="translate(0.000000, 189.000000)">
                <path
                  d="M-4.54747351e-13,20 L5,20 L2.50003516,15 L-4.54747351e-13,20 Z M7,17 L24,17 L24,20 L7,20 L7,17 Z"
                  id="Shape"
                  fill="#4C5CFF"
                />
                <path
                  d="M24,3.68422319 C24,1.68424169 22.6285809,0 21,0 C19.3714191,0 18,1.68424169 18,3.68422319 L19.7143115,3.68422319 C19.7143115,2.84210234 20.3142754,2.10526511 21,2.10526511 C21.6857246,2.10526511 22.2857186,2.84210234 22.2857186,3.68422319 C22.2857186,4.31581382 22.0285869,4.84212084 21.599994,5.05265106 C21.3428623,5.26318127 21.0857306,5.57895808 21.0857306,6.0000185 C21.0857306,6.42107893 21.2571618,6.73681873 21.599994,6.94738595 C22.0285869,7.26316276 22.2857186,7.78946979 22.2857186,8.31581382 C22.2857186,9.15789766 21.6857246,9.89473489 21,9.89473489 C20.3142754,9.89473489 19.7143115,9.15793466 19.7143115,8.31581382 L18,8.31581382 C18,10.3157953 19.3714492,12 21,12 C22.6285508,12 24,10.3157953 24,8.31581382 C24,7.47369298 23.7428683,6.63157213 23.3142754,6.0000185 C23.7428683,5.36842787 24,4.52634403 24,3.68422319 Z M4.50661817,0.062613635 C4.22496692,-0.0420841342 3.8494099,-0.0420841342 3.56775864,0.2720828 L0,3.41349445 L1.12667105,4.98418187 L3.09829587,3.2040621 L3.09829587,12 L4.97608095,12 L4.97608095,1.10981221 C5.0699537,0.690947504 4.88217519,0.2720828 4.50661817,0.0626504486 L4.50661817,0.062613635 Z M15,3.65218029 C15,2.71305241 14.6576066,1.77392453 14.1439713,1.04349581 C13.5447452,0.313067084 12.8598982,0 12.0038695,0 C11.2334317,0 10.4629938,0.417398322 9.86376772,1.04349581 C9.26454162,1.66959329 9.00770894,2.60872117 9.00770894,3.65218029 L10.7198265,3.65218029 C10.7198265,3.23478197 10.8910082,2.81738365 11.0622199,2.50435325 C11.5758552,1.87825577 12.346293,1.87825577 12.8598982,2.50435325 C13.1167309,2.81738365 13.2023217,3.23478197 13.2023217,3.65218029 C13.2023217,4.06957862 13.03111,4.48697694 12.7743074,4.80000734 L9.17892069,10.2260755 C9.00770894,10.5391059 8.92211811,10.9565042 9.09332986,11.3739025 C9.26454162,11.7913008 9.52134421,12 9.86376772,12 L15,12 L15,9.91300839 L11.661446,9.91300839 L14.1439713,6.26086478 C14.6576066,5.53043606 15,4.59130818 15,3.65218029 Z"
                  id="Shape"
                  fill="#FF6800"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    ),
    gray: (
      <svg
        width="24px"
        height="20px"
        viewBox="0 0 24 20"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>指标卡</title>
        <g id="Page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Desktop-HD-Copy" transform="translate(-262.000000, -286.000000)" fillRule="nonzero">
            <g id="Group-7" transform="translate(224.000000, 97.000000)">
              <g id="指标卡-gray" transform="translate(38.000000, 189.000000)">
                <path
                  d="M-4.54747351e-13,20 L5,20 L2.50003516,15 L-4.54747351e-13,20 Z M7,17 L24,17 L24,20 L7,20 L7,17 Z"
                  id="Shape"
                  fill="#CCCCCC"
                />
                <path
                  d="M24,3.68422319 C24,1.68424169 22.6285809,0 21,0 C19.3714191,0 18,1.68424169 18,3.68422319 L19.7143115,3.68422319 C19.7143115,2.84210234 20.3142754,2.10526511 21,2.10526511 C21.6857246,2.10526511 22.2857186,2.84210234 22.2857186,3.68422319 C22.2857186,4.31581382 22.0285869,4.84212084 21.599994,5.05265106 C21.3428623,5.26318127 21.0857306,5.57895808 21.0857306,6.0000185 C21.0857306,6.42107893 21.2571618,6.73681873 21.599994,6.94738595 C22.0285869,7.26316276 22.2857186,7.78946979 22.2857186,8.31581382 C22.2857186,9.15789766 21.6857246,9.89473489 21,9.89473489 C20.3142754,9.89473489 19.7143115,9.15793466 19.7143115,8.31581382 L18,8.31581382 C18,10.3157953 19.3714492,12 21,12 C22.6285508,12 24,10.3157953 24,8.31581382 C24,7.47369298 23.7428683,6.63157213 23.3142754,6.0000185 C23.7428683,5.36842787 24,4.52634403 24,3.68422319 Z M4.50661817,0.062613635 C4.22496692,-0.0420841342 3.8494099,-0.0420841342 3.56775864,0.2720828 L0,3.41349445 L1.12667105,4.98418187 L3.09829587,3.2040621 L3.09829587,12 L4.97608095,12 L4.97608095,1.10981221 C5.0699537,0.690947504 4.88217519,0.2720828 4.50661817,0.0626504486 L4.50661817,0.062613635 Z M15,3.65218029 C15,2.71305241 14.6576066,1.77392453 14.1439713,1.04349581 C13.5447452,0.313067084 12.8598982,0 12.0038695,0 C11.2334317,0 10.4629938,0.417398322 9.86376772,1.04349581 C9.26454162,1.66959329 9.00770894,2.60872117 9.00770894,3.65218029 L10.7198265,3.65218029 C10.7198265,3.23478197 10.8910082,2.81738365 11.0622199,2.50435325 C11.5758552,1.87825577 12.346293,1.87825577 12.8598982,2.50435325 C13.1167309,2.81738365 13.2023217,3.23478197 13.2023217,3.65218029 C13.2023217,4.06957862 13.03111,4.48697694 12.7743074,4.80000734 L9.17892069,10.2260755 C9.00770894,10.5391059 8.92211811,10.9565042 9.09332986,11.3739025 C9.26454162,11.7913008 9.52134421,12 9.86376772,12 L15,12 L15,9.91300839 L11.661446,9.91300839 L14.1439713,6.26086478 C14.6576066,5.53043606 15,4.59130818 15,3.65218029 Z"
                  id="Shape"
                  fill="#999999"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    )
  },
  {
    keywords: 'LINE',
    normal: (
      <svg
        width="24px"
        height="20px"
        viewBox="0 0 24 20"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>折线图</title>
        <g id="Page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Desktop-HD-Copy" transform="translate(-224.000000, -165.000000)">
            <g id="Group-7" transform="translate(224.000000, 97.000000)">
              <g id="折线图" transform="translate(0.000000, 68.000000)">
                <polygon
                  id="Path"
                  fill="#4C5CFF"
                  fillRule="nonzero"
                  points="12.5683669 18 5.69786596 14.5855879 1.64027543 18.0090076 0 15.8468394 5.5252006 11.1621682 12.7410323 14.7567636 22.6187378 8 24 10.3423515"
                />
                <rect id="Rectangle" fill="#4C5CFF" x="0" y="19" width="24" height="1" />
                <polygon
                  id="Path"
                  fill="#FF6800"
                  fillRule="nonzero"
                  points="12.5683669 10 5.69786596 6.58558787 1.64027543 11.0090076 0 8.84683943 5.5252006 3.16216815 12.7410323 6.7567636 22.6187378 0 24 2.34235147"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    ),
    gray: (
      <svg
        width="24px"
        height="20px"
        viewBox="0 0 24 20"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>折线图</title>
        <g id="Page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Desktop-HD-Copy" transform="translate(-262.000000, -165.000000)">
            <g id="Group-7" transform="translate(224.000000, 97.000000)">
              <g id="折线图-gray" transform="translate(38.000000, 68.000000)">
                <polygon
                  id="Path"
                  fill="#CCCCCC"
                  fillRule="nonzero"
                  points="12.5683669 18 5.69786596 14.5855879 1.64027543 18.0090076 0 15.8468394 5.5252006 11.1621682 12.7410323 14.7567636 22.6187378 8 24 10.3423515"
                />
                <rect id="Rectangle" fill="#CCCCCC" x="0" y="19" width="24" height="1" />
                <polygon
                  id="Path"
                  fill="#999999"
                  fillRule="nonzero"
                  points="12.5683669 10 5.69786596 6.58558787 1.64027543 11.0090076 0 8.84683943 5.5252006 3.16216815 12.7410323 6.7567636 22.6187378 0 24 2.34235147"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    )
  }
  // {
  //   keywords: 'FUNNEL',
  //   normal: (
  //     <svg
  //       xmlns="http://www.w3.org/2000/svg"
  //       xmlnsXlink="http://www.w3.org/1999/xlink"
  //       width="24px"
  //       height="22px"
  //       viewBox="0 0 24 22"
  //       version="1.1"
  //     >
  //       <title>漏斗图</title>
  //       <g
  //         id="Page-1"
  //         stroke="none"
  //         strokeWidth="1"
  //         fill="none"
  //         fillRule="evenodd"
  //       >
  //         <g
  //           id="创建图表-漏斗图"
  //           transform="translate(-1012.000000, -179.000000)"
  //           fillRule="nonzero"
  //         >
  //           <g id="Group-" transform="translate(998.000000, 56.000000)">
  //             <g id="Group-7" transform="translate(10.000000, 10.000000)">
  //               <g id="漏斗图" transform="translate(4.000000, 113.000000)">
  //                 <polygon
  //                   id="Path"
  //                   fill="#FF6B06"
  //                   points="3 9 4.49994366 14 19.4999437 14 21 9"
  //                 />
  //                 <polygon
  //                   id="Path"
  //                   fill="#22CA6C"
  //                   points="10.4999437 22 13.4999437 22 18 17 6 17 10.4999437 22"
  //                 />
  //                 <polygon
  //                   id="Path"
  //                   fill="#4C5CFF"
  //                   points="24 0 22.4999454 5 1.4999419 5 0 0 24 0"
  //                 />
  //               </g>
  //             </g>
  //           </g>
  //         </g>
  //       </g>
  //     </svg>
  //   ),
  //   gray: (
  //     <svg
  //       width="24px"
  //       height="22px"
  //       viewBox="0 0 24 22"
  //       version="1.1"
  //       xmlns="http://www.w3.org/2000/svg"
  //       xmlnsXlink="http://www.w3.org/1999/xlink"
  //     >
  //       <title>漏斗图</title>
  //       <g
  //         id="Page-1"
  //         stroke="none"
  //         strokeWidth="1"
  //         fill="none"
  //         fillRule="evenodd"
  //       >
  //         <g
  //           id="Desktop-HD-Copy"
  //           transform="translate(-262.000000, -329.000000)"
  //           fillRule="nonzero"
  //         >
  //           <g id="漏斗图-gray" transform="translate(262.000000, 329.000000)">
  //             <polygon
  //               id="Path"
  //               fill="#CCCCCC"
  //               points="3 9 4.49994366 14 19.4999437 14 21 9"
  //             />
  //             <polygon
  //               id="Path"
  //               fill="#999999"
  //               points="10.4999437 22 13.4999437 22 18 17 6 17 10.4999437 22"
  //             />
  //             <polygon
  //               id="Path"
  //               fill="#CCCCCC"
  //               points="24 0 22.4999454 5 1.4999419 5 0 0 24 0"
  //             />
  //           </g>
  //         </g>
  //       </g>
  //     </svg>
  //   )
  // },
  // {
  //   keywords: 'BUBBLE',
  //   normal: (
  //     <svg
  //       width="24px"
  //       height="20px"
  //       viewBox="0 0 24 20"
  //       version="1.1"
  //       xmlns="http://www.w3.org/2000/svg"
  //       xmlnsXlink="http://www.w3.org/1999/xlink"
  //     >
  //       <title>散点图</title>
  //       <g
  //         id="Page-1"
  //         stroke="none"
  //         strokeWidth="1"
  //         fill="none"
  //         fillRule="evenodd"
  //       >
  //         <g
  //           id="Desktop-HD-Copy"
  //           transform="translate(-224.000000, -366.000000)"
  //         >
  //           <g id="散点图" transform="translate(224.000000, 366.000000)">
  //             <rect
  //               id="Rectangle"
  //               fill="#4C5CFF"
  //               x="0"
  //               y="19"
  //               width="24"
  //               height="1"
  //             />
  //             <path
  //               d="M0,16 C0,17.1045695 0.8954305,18 2,18 C3.1045695,18 4,17.1045695 4,16 C4,14.8954305 3.1045695,14 2,14 C0.8954305,14 0,14.8954305 0,16 L0,16 Z"
  //               id="Path"
  //               fill="#4C5CFF"
  //               fillRule="nonzero"
  //             />
  //             <path
  //               d="M2,10.5 C2,12.4329966 3.56700338,14 5.5,14 C7.43299662,14 9,12.4329966 9,10.5 C9,8.56700338 7.43299662,7 5.5,7 C3.56700338,7 2,8.56700338 2,10.5 L2,10.5 Z"
  //               id="Path-Copy-5"
  //               fill="#4C5CFF"
  //               fillRule="nonzero"
  //             />
  //             <path
  //               d="M9,14.5 C9,15.8807119 10.1192881,17 11.5,17 C12.8807119,17 14,15.8807119 14,14.5 C14,13.1192881 12.8807119,12 11.5,12 C10.1192881,12 9,13.1192881 9,14.5 L9,14.5 Z"
  //               id="Path-Copy-8"
  //               fill="#4C5CFF"
  //               fillRule="nonzero"
  //             />
  //             <path
  //               d="M18,14 C18,15.6568542 19.3431458,17 21,17 C22.6568542,17 24,15.6568542 24,14 C24,12.3431458 22.6568542,11 21,11 C19.3431458,11 18,12.3431458 18,14 L18,14 Z"
  //               id="Path-Copy-6"
  //               fill="#4C5CFF"
  //               fillRule="nonzero"
  //             />
  //             <path
  //               d="M14,5 C14,6.78632792 14.9529946,8.43696303 16.5,9.33012701 C18.0470054,10.223291 19.9529946,10.223291 21.5,9.33012701 C23.0470054,8.43696303 24,6.78632792 24,5 C24,3.21367205 23.0470054,1.56303696 21.5,0.669872982 C19.9529946,-0.223290994 18.0470054,-0.223290994 16.5,0.669872982 C14.9529946,1.56303696 14,3.21367205 14,5 L14,5 Z"
  //               id="Path"
  //               fill="#FF6800"
  //               fillRule="nonzero"
  //             />
  //             <path
  //               d="M5,3.5 C5,4.39316396 5.47649728,5.21848151 6.24999998,5.66506351 C7.02350268,6.1116455 7.97649732,6.1116455 8.75000002,5.66506351 C9.52350272,5.21848151 10,4.39316396 10,3.5 C10,2.60683602 9.52350271,1.78151848 8.75000002,1.33493649 C7.97649732,0.888354503 7.02350268,0.888354503 6.24999998,1.33493649 C5.47649729,1.78151848 5,2.60683602 5,3.5 L5,3.5 Z"
  //               id="Path-Copy-4"
  //               fill="#FF6800"
  //               fillRule="nonzero"
  //             />
  //             <path
  //               d="M10,9.5 C10,10.0358984 10.2858984,10.5310889 10.75,10.7990381 C11.2141016,11.0669873 11.7858984,11.0669873 12.25,10.7990381 C12.7141016,10.5310889 13,10.0358984 13,9.5 C13,8.96410161 12.7141016,8.46891109 12.25,8.20096189 C11.7858984,7.9330127 11.2141016,7.9330127 10.75,8.20096189 C10.2858984,8.46891109 10,8.96410161 10,9.5 L10,9.5 Z"
  //               id="Path-Copy-7"
  //               fill="#FF6800"
  //               fillRule="nonzero"
  //             />
  //           </g>
  //         </g>
  //       </g>
  //     </svg>
  //   ),
  //   gray: (
  //     <svg
  //       xmlns="http://www.w3.org/2000/svg"
  //       xmlnsXlink="http://www.w3.org/1999/xlink"
  //       width="24px"
  //       height="20px"
  //       viewBox="0 0 24 20"
  //       version="1.1"
  //     >
  //       <title>散点图</title>
  //       <g
  //         id="Page-1"
  //         stroke="none"
  //         strokeWidth="1"
  //         fill="none"
  //         fillRule="evenodd"
  //       >
  //         <g
  //           id="创建图表-漏斗图-1"
  //           transform="translate(-1064.000000, -180.000000)"
  //         >
  //           <g id="Group-" transform="translate(998.000000, 56.000000)">
  //             <g id="Group-7" transform="translate(10.000000, 10.000000)">
  //               <g
  //                 id="散点图-gray"
  //                 transform="translate(56.000000, 114.000000)"
  //               >
  //                 <rect
  //                   id="Rectangle"
  //                   fill="#CCCCCC"
  //                   x="0"
  //                   y="19"
  //                   width="24"
  //                   height="1"
  //                 />
  //                 <path
  //                   d="M0,16 C0,17.1045695 0.8954305,18 2,18 C3.1045695,18 4,17.1045695 4,16 C4,14.8954305 3.1045695,14 2,14 C0.8954305,14 0,14.8954305 0,16 L0,16 Z"
  //                   id="Path"
  //                   fill="#CCCCCC"
  //                   fillRule="nonzero"
  //                 />
  //                 <path
  //                   d="M2,10.5 C2,12.4329966 3.56700338,14 5.5,14 C7.43299662,14 9,12.4329966 9,10.5 C9,8.56700338 7.43299662,7 5.5,7 C3.56700338,7 2,8.56700338 2,10.5 L2,10.5 Z"
  //                   id="Path-Copy-5"
  //                   fill="#CCCCCC"
  //                   fillRule="nonzero"
  //                 />
  //                 <path
  //                   d="M9,14.5 C9,15.8807119 10.1192881,17 11.5,17 C12.8807119,17 14,15.8807119 14,14.5 C14,13.1192881 12.8807119,12 11.5,12 C10.1192881,12 9,13.1192881 9,14.5 L9,14.5 Z"
  //                   id="Path-Copy-8"
  //                   fill="#CCCCCC"
  //                   fillRule="nonzero"
  //                 />
  //                 <path
  //                   d="M18,14 C18,15.6568542 19.3431458,17 21,17 C22.6568542,17 24,15.6568542 24,14 C24,12.3431458 22.6568542,11 21,11 C19.3431458,11 18,12.3431458 18,14 L18,14 Z"
  //                   id="Path-Copy-6"
  //                   fill="#CCCCCC"
  //                   fillRule="nonzero"
  //                 />
  //                 <path
  //                   d="M14,5 C14,6.78632792 14.9529946,8.43696303 16.5,9.33012701 C18.0470054,10.223291 19.9529946,10.223291 21.5,9.33012701 C23.0470054,8.43696303 24,6.78632792 24,5 C24,3.21367205 23.0470054,1.56303696 21.5,0.669872982 C19.9529946,-0.223290994 18.0470054,-0.223290994 16.5,0.669872982 C14.9529946,1.56303696 14,3.21367205 14,5 L14,5 Z"
  //                   id="Path"
  //                   fill="#999999"
  //                   fillRule="nonzero"
  //                 />
  //                 <path
  //                   d="M5,3.5 C5,4.39316396 5.47649728,5.21848151 6.24999998,5.66506351 C7.02350268,6.1116455 7.97649732,6.1116455 8.75000002,5.66506351 C9.52350272,5.21848151 10,4.39316396 10,3.5 C10,2.60683602 9.52350271,1.78151848 8.75000002,1.33493649 C7.97649732,0.888354503 7.02350268,0.888354503 6.24999998,1.33493649 C5.47649729,1.78151848 5,2.60683602 5,3.5 L5,3.5 Z"
  //                   id="Path-Copy-4"
  //                   fill="#999999"
  //                   fillRule="nonzero"
  //                 />
  //                 <path
  //                   d="M10,9.5 C10,10.0358984 10.2858984,10.5310889 10.75,10.7990381 C11.2141016,11.0669873 11.7858984,11.0669873 12.25,10.7990381 C12.7141016,10.5310889 13,10.0358984 13,9.5 C13,8.96410161 12.7141016,8.46891109 12.25,8.20096189 C11.7858984,7.9330127 11.2141016,7.9330127 10.75,8.20096189 C10.2858984,8.46891109 10,8.96410161 10,9.5 L10,9.5 Z"
  //                   id="Path-Copy-7"
  //                   fill="#999999"
  //                   fillRule="nonzero"
  //                 />
  //               </g>
  //             </g>
  //           </g>
  //         </g>
  //       </g>
  //     </svg>
  //   )
  // }
];

export default iconSvg;
