import {
  Bar<PERSON>hartOutlined,
  CloseOutlined,
  DoubleLeftOutlined,
  DoubleRightOutlined,
  DownOutlined,
  FilterOutlined
} from '@ant-design/icons';
import { Button, Card, Dropdown, Popover, Space, Spin, Tooltip, Tree, Typography, message } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { getDeptPath } from 'pages/home/<USER>/dataPermissions/config';
import React, { Suspense, lazy, useEffect, useMemo, useReducer, useRef, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Resizable } from 'react-resizable';
import CampaignV2Service from 'service/CampaignV2Service';
import CampaignsService from 'service/CampaignsService';
import UserService from 'service/UserService';
import AnalysisCenterService from 'service/analysisCenterService';
import IndexManagementService from 'service/indexManagementService';
import { t } from 'utils/translation';
import { SelectTime } from 'wolf-static-cpnt';
import { indexType, timeType, userContrastTypeList } from '../config';
import AddChart from '../list/addChart';
import { DataContext } from './context';
import DataSource from './dataSource/index';
import Filter from './filter/index';
import FormatModal from './format/format';
import iconSvg from './iconsvg';
import './index.scss';

const reducer = (state, action) => ({ ...state, ...action });
const { Paragraph } = Typography;
const campaignV2Service = new CampaignV2Service();
const campaignsService = new CampaignsService();
const userService = new UserService();

export default (props) => {
  const [state, dispatch] = useReducer(reducer, {
    loading: false,
    initLoading: false,
    selectedChart: 'TABLE',
    functionObj: {},
    tableSchemaList: {},
    metricsList: [],
    filterData: {
      TABLE: [],
      COLUMN: [],
      DONUT: [],
      CARD: [],
      LINE: [],
      BUBBLE: [],
      FUNNEL: [],
      GROUP_TABLE: []
    },
    configData: {
      TABLE: {
        rowList: [],
        columnList: [],
        valueList: [],
        columnLimit: 5,
        lineLimit: 20
      },
      COLUMN: { valueList: [], axisList: [], legendList: [] },
      DONUT: { valueList: [], legendList: [] },
      CARD: { valueList: [] },
      LINE: { valueList: [], axisList: [], legendList: [] },
      BUBBLE: {
        xvalueList: [],
        yvalueList: [],
        valueList: [],
        axisList: [],
        legendList: []
      },
      FUNNEL: { valueList: [] },
      GROUP_TABLE: {
        columnList: [],
        valueList: []
      }
    },
    responseData: {
      TABLE: { rowList: [], columnList: [] },
      COLUMN: { rowList: [] },
      DONUT: { rowList: [] },
      CARD: { rowList: [] },
      LINE: { rowList: [] },
      BUBBLE: { rowList: [] },
      FUNNEL: { rowList: [] },
      GROUP_TABLE: { valueList: [], columnList: [], rowsValueList: [] }
    },
    info: {
      dateRange2: [
        { type: 'RELATIVE', timeTerm: 'DAY', isPast: true, times: 30 },
        { type: 'NOW', timeTerm: 'DAY', isPast: true }
      ]
    },
    openedDrawer: null,
    formatVisible: false,
    formatValue: {},
    timeSeriesSchemas: [],
    filterFold: false,
    visualFold: false,
    dataSourceFold: false,
    filterCount: 10,
    grandchildData: 'flated',
    createVisible: false,
    propsDataModelId: null
  });

  let {
    selectedChart,
    filterData,
    configData,
    info,
    responseData,
    timeSeriesSchemas,
    formatVisible,
    filterFold,
    visualFold,
    dataSourceFold,
    createVisible,
    propsDataModelId
  } = state;
  const [load, setLoad] = useState(false);
  const currentFlag = props.location.state?.currentFlag || localStorage.getItem('currentFlag');
  const [filterWidth, setFilterWidth] = useState(220);
  const [viewWidth, setViewWidth] = useState(220);
  const [filterConfig, setFilterConfig] = useState({});
  const [fieldWidth, setFieldWidth] = useState(220);
  const [userId, setUserId] = useState(null);
  const [campaignTreeData, setCampaignTreeData] = useState([]);
  const [deptPath, setDeptPath] = useState(getDeptPath());
  const [campaignList, setCampaignList] = useState([]);
  const [computeFlag, setComputeFlag] = useState(true);
  const [createInfo, setCreateInfo] = useState(JSON.parse(localStorage.getItem('createInfo')) || {});
  const timer = useRef(null);
  const cache =
    props.location.state || (localStorage.getItem('cacheChart') && JSON.parse(localStorage.getItem('cacheChart')));
  const curFlag = currentFlag === 'edit' || !currentFlag;

  useEffect(() => {
    init();
    return () => {
      timer.current && clearTimeout(timer.current);
      localStorage.removeItem('cacheChart');
      localStorage.removeItem('createInfo');
      localStorage.removeItem('currentFlag');
    };
  }, [createInfo]);
  const saveRecordInfo = async (id, userId) => {
    await campaignsService.saveUserOperationRecord({
      targetId: id,
      id: curFlag ? info?.recentUserOperationRecord?.id : id,
      targetType: 'CHART',
      type: 'RECENT',
      createUserId: userId,
      updateUserId: userId,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });
  };
  const deepConfig = (data) => {
    return _.forOwn(data, (value, key) => {
      if (_.isArray(value)) {
        data[key] = [];
      } else if (_.isObject(value)) {
        deepConfig(value);
      }
    });
  };
  const init = async () => {
    try {
      const userInfo = await userService.getCurrentUser();
      setUserId(userInfo.id);
      dispatch({ initLoading: true });
      // let info = await AnalysisCenterService.getChartConfig(props.match.params.id);
      let info = curFlag
        ? await AnalysisCenterService.getChartConfigV2({
            id: Number(props.match.params.id),
            deptId: cache?.deptId || window.getDeptId()
          })
        : createInfo;
      setDeptPath(getDeptPath(info?.deptId));
      if (info?.chartConfig?.campaignFilters) {
        if (cache?.boardChart === 'campaigns') {
          const result = await campaignsService.getCampaignsV2({
            id: Number(cache.campaignId),
            deptId: info?.deptId || window.getDeptId()
          });
          if (!_.isNil(props.match.params.id)) {
            const treeResult = [
              {
                title: `${t('analysisCenter-t0LBykkAlnxQ')} ${result.name} [${result.id}]`,
                key: result.id,
                children: info?.chartConfig?.campaignFilters
                  ? info?.chartConfig?.campaignFilters.map((item) => {
                      return {
                        title: `${t('analysisCenter-ErZPYtRR5Xne')} [${item.id}] ${item.name}`,
                        key: item.id
                      };
                    })
                  : []
              }
            ];
            setCampaignTreeData(treeResult);
          }
        } else {
          if (!_.isNil(props.match.params.id)) {
            const treeResult = info?.chartConfig?.campaignFilters
              ? info?.chartConfig?.campaignFilters.map((item) => {
                  return {
                    title: `${t('analysisCenter-ErZPYtRR5Xne')} [${item.id}] ${item.name}`,
                    key: item.id
                  };
                })
              : [];

            setCampaignTreeData(treeResult);
          }
        }
      } else {
        if (localStorage.getItem('cacheChart')) {
          const cache = JSON.parse(localStorage.getItem('cacheChart'));
          if (cache?.boardChart === 'campaigns' && cache.campaignId) {
            const result = await campaignsService.getCampaignsV2({
              id: Number(cache.campaignId),
              deptId: info?.deptId || window.getDeptId()
            });

            const res = await campaignsService.getCampaignFilter({
              type: 'CAMPAIGNS',
              id: Number(cache?.campaignId)
            });

            setCampaignList(res);

            if (!_.isNil(props.match.params.id) || createInfo) {
              const treeResult = [
                {
                  title: `${t('analysisCenter-t0LBykkAlnxQ')} [${result.id}] ${result.name}`,
                  key: result.id,
                  children:
                    res && res.length
                      ? res.map((item) => {
                          return {
                            title: `${t('analysisCenter-ErZPYtRR5Xne')} [${item.id}] ${item.name}`,
                            key: item.id
                          };
                        })
                      : []
                }
              ];
              setCampaignTreeData(treeResult);
            }
          } else if (cache?.boardChart === 'flowCanvas' && cache.campaignId) {
            const res = await campaignsService.getCampaignFilter({
              type: 'FLOW_CANVAS',
              id: Number(cache?.campaignId)
            });

            setCampaignList(res);

            if (!_.isNil(props.match.params.id || createInfo)) {
              const treeResult = [
                {
                  title: `${t('analysisCenter-ErZPYtRR5Xne')} [${res[0].id}] ${res[0].name}`,
                  key: res[0].id
                }
              ];

              setCampaignTreeData(treeResult);
            }
          }
        } else {
          if (cache?.boardChart === 'campaigns' && cache.campaignId) {
            localStorage.setItem('cacheChart', JSON.stringify(cache));
            const result = await campaignsService.getCampaignsV2({
              id: Number(cache.campaignId),
              deptId: info?.deptId || window.getDeptId()
            });

            // const res = await campaignsService.findCampaigns([
            //   {
            //     operator: 'EQ',
            //     propertyName: 'campaignsId',
            //     value: Number(props.location.state?.campaignId)
            //   }
            // ]);

            const res = await campaignsService.getCampaignFilter({
              type: 'CAMPAIGNS',
              id: Number(cache?.campaignId)
            });

            setCampaignList(res);

            if (!_.isNil(props.match.params.id) || createInfo) {
              const treeResult = [
                {
                  title: `${t('analysisCenter-t0LBykkAlnxQ')} [${result.id}] ${result.name}`,
                  key: result.id,
                  children:
                    res && res.length
                      ? res.map((item) => {
                          return {
                            title: `${t('analysisCenter-ErZPYtRR5Xne')} [${item.id}] ${item.name}`,
                            key: item.id
                          };
                        })
                      : []
                }
              ];
              setCampaignTreeData(treeResult);
            }
          } else if (cache?.boardChart === 'flowCanvas' && cache.campaignId) {
            localStorage.setItem('cacheChart', JSON.stringify(cache));
            const res = await campaignsService.getCampaignFilter({
              type: 'FLOW_CANVAS',
              id: Number(cache?.campaignId)
            });

            setCampaignList(res);

            if (!_.isNil(props.match.params.id) || createInfo) {
              const treeResult = [
                {
                  title: `${t('analysisCenter-ErZPYtRR5Xne')} [${res[0].id}] ${res[0].name}`,
                  key: res[0].id
                }
              ];

              setCampaignTreeData(treeResult);
            }
          }
        }
      }

      const timeFilter = GetQueryString('timeFilter');
      // 设置状态来判定是否第一次进入页面
      if (timeFilter && !localStorage.getItem('timeFilter')) {
        const timeFilterArr = timeFilter.split(',');
        localStorage.setItem('timeFilter', JSON.stringify(timeFilter));
        info = {
          ...info,
          dateRange2: [
            {
              type: 'ABSOLUTE',
              timestamp: parseInt(timeFilterArr[0]),
              times: 30,
              timeTerm: 'DAY',
              isPast: true
            },
            {
              type: 'ABSOLUTE',
              timestamp: parseInt(timeFilterArr[1]),
              times: 0,
              timeTerm: 'DAY',
              isPast: true
            }
          ]
        };
        setFilterConfig({
          startTime: timeFilterArr[0],
          endTime: timeFilterArr[1]
        });
      }
      const metricsList = await IndexManagementService.findAllByChart(info);
      const { chartType, filter, chartConfig, dateRange2 } = info;
      if (!_.isEmpty(filter)) {
        filterData[chartType] = filter;
      } else {
        filterData = _.forOwn(filterData, (value, key) => {
          if (_.isArray(value)) {
            filterData[key] = [];
          }
        });
      }
      if (!_.isEmpty(chartConfig)) {
        configData[chartType] = chartConfig;
      } else {
        configData = deepConfig(configData);
      }
      if (!_.isEmpty(chartType)) {
        selectedChart = chartType;
      } else {
        responseData = deepConfig(responseData);
      }
      if (!_.isEmpty(dateRange2)) {
        info.dateRange2 = dateRange2;
        // setDateRange(dateRange)
      }
      const data = await AnalysisCenterService.getTableSchemaList([
        {
          operator: 'IN',
          propertyName: 'table.id',
          value: info.tables.map((n) => n.id).join()
        }
      ]);
      const tableSchemaList = _.groupBy(data, 'table.id');
      let timeSeriesSchemas = [];
      if (info.isBusinessTable === 1) {
        timeSeriesSchemas = await AnalysisCenterService.getTimeSeriesSchema({
          scenario: info.scenario,
          tableIds: _.keys(tableSchemaList)
        });
      }
      const functionArr = await AnalysisCenterService.getFunctionValues();
      const functionObj = functionArr.reduce((obj, n) => {
        obj[n.name] = n.value;
        return obj;
      }, {});
      dispatch({
        tableSchemaList,
        initLoading: false,
        info,
        functionObj,
        selectedChart,
        metricsList,
        timeSeriesSchemas,
        filterCount: info.chartConfig?.limit || 10,
        grandchildData: info.chartConfig?.isMerged || 'flated'
      });
      setLoad(!load);
    } catch (error) {
      console.error('🚀 ~ init ~ error:', error);
      dispatch({ initLoading: false });
    }
  };

  function GetQueryString(name) {
    const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
    const r = window.location.search.substr(1).match(reg); // search,查询？后面的参数，并匹配正则
    if (r != null) return unescape(r[2]);
    return null;
  }

  // 计算
  useEffect(() => {
    if (props.match.params.id && info.chartType) {
      compute({});
    }
  }, [load]);

  const moduleComponents = useMemo(() => {
    const Chart = lazy(() => import(`./components/${selectedChart.toLowerCase()}/chart/index`));
    const Fill = lazy(() => import(`./components/${selectedChart.toLowerCase()}/fill/index`));
    return { chart: <Chart />, fill: <Fill /> };
  }, [selectedChart]);

  const doRequest = async (data) => {
    responseData[selectedChart] = { rowList: null };
    try {
      const res = await AnalysisCenterService.calcChartResult(data);
      if (!res) {
        dispatch({ loading: false });
        setComputeFlag(true);
      } else if (res.header.code === 0) {
        if (res.body.errorCode === 1) {
          message.info(t('analysisCenter-NnTGOKOqoiig'));
        }
        if (!_.isEmpty(res.body.chartResult)) {
          if (res.body?.tips) message.warning(res.body.tips);
          const _dataSource = _.cloneDeep(res.body.chartResult);
          if (_dataSource.rowList && _dataSource.rowList.length) {
            _dataSource.rowList.forEach((item) => {
              item.displayName = _.isEmpty(_dataSource.axis?.stackField)
                ? item.METRIC_NAME
                : `${item[_dataSource.axis?.stackField.dataIndex]}-${item.METRIC_NAME}`;
            });
          }
          if (selectedChart === 'GROUP_TABLE') {
            _dataSource.columnList = _dataSource.columnNameList;
          }
          setComputeFlag(false);
          responseData[selectedChart] = _dataSource;
        }
        dispatch({ loading: false });
      } else if (res.header.code === 210) {
        // dispatch({loading: false});
        timer.current = setTimeout(async () => {
          data.forceCalc = false;
          await doRequest(data);
          setComputeFlag(false);
        }, 3000);
      } else if (res.header.code === 1) {
        if (res.header.message) {
          message.info(res.header.message);
          setComputeFlag(true);
        }
        responseData[selectedChart] = { rowList: null };

        dispatch({ loading: false });
      }
    } catch (error) {
      // responseData[selectedChart] = {rowList: [], columnList: []};
      dispatch({ loading: false });
      setComputeFlag(true);
    }
  };

  const labelFilterData = (filterArr) => {
    const _filertList = _.cloneDeep(filterArr);

    _filertList.forEach((item) => {
      if (item.type === 'USER_TAG') {
        item.userLabelList.forEach((userLabelItem) => {
          userLabelItem.label.filters.forEach((filterItem) => {
            filterItem.filters.forEach((filterItem2) => {
              if (_.isArray(filterItem2.value) && typeof filterItem2.value[0] === 'string') {
                const filterArrayRes = filterItem2.value.map((item) => {
                  const itemRes = item.match(/\[(.+?)\]/g);
                  item = itemRes ? RegExp.$1 : item;
                  return item;
                });
                filterItem2.showValue = filterItem2.showValue || filterItem2.value;
                filterItem2.value = filterArrayRes;
              } else {
                const result =
                  filterItem2.value &&
                  !_.isArray(filterItem2.value) &&
                  filterItem2.value.toString().match(/\[(.+?)\]/g);
                filterItem2.showValue = filterItem2.showValue || filterItem2.value;
                filterItem2.value = result ? RegExp.$1 : filterItem2.value;
              }
            });
          });
        });
      }
    });

    return _filertList;
  };
  const compute = async (obj) => {
    const data = {
      ...info,
      extendChartType:
        cache?.boardChart || localStorage.getItem('isActivityAnalysis') ? 'CREATE_CAMPAIGN_CHART' : undefined,
      chartConfig: configData[selectedChart],
      filter: labelFilterData(filterData[selectedChart]),
      chartType: selectedChart,
      ...obj,
      argChange: true
    };
    // 如果没有设置数据显示格式默认展示千分位数据格式
    const valueListFormat = configData[selectedChart]?.valueList.map((item) => {
      if (!item.numberConfig) {
        item.numberConfig = {
          format: 'NUMBER',
          decimalSeparator: 0,
          thousandsSeparator: true
        };
      }
      return item;
    });
    data.chartConfig = { ...configData[selectedChart], valueList: valueListFormat };
    if (!data.chartConfig.campaignFilters && cache?.campaignId) {
      data.chartConfig = { ...data.chartConfig, campaignFilters: campaignList };
    }
    const dataModelId = !propsDataModelId ? props.location.state?.dataModelId || null : propsDataModelId;
    if (info?.isBusinessTable === 2 && dataModelId) {
      data.chartConfig = { ...data.chartConfig, dataModelId: props.location.state?.dataModelId || propsDataModelId };
    }

    const usedTableSchemaFilter = _.filter(configData[selectedChart]?.axisList, (item) => item.type === 'TABLE_SCHEMA');
    const usedTableSchemaFilterOfTable = _.filter(
      selectedChart === 'TABLE' ? configData[selectedChart]?.rowList : [],
      (item) => item.type === 'TABLE_SCHEMA'
    );
    const usedTimeSeriesSchemas = _.filter(timeSeriesSchemas, (item) =>
      _.find(
        selectedChart !== 'TABLE' ? usedTableSchemaFilter : usedTableSchemaFilterOfTable,
        (filter) => filter?.tableSchema?.table?.id === item?.table?.id
      )
    );
    if (selectedChart === 'COLUMN' || selectedChart === 'LINE') {
      const axisTimeConfig = configData[selectedChart]?.axisList?.[0]?.timeConfig;
      const axisList = _.cloneDeep(configData[selectedChart]?.axisList?.[0] || []);
      const valueList = _.cloneDeep(configData[selectedChart]?.valueList);
      if (
        axisList.type === 'TABLE_SCHEMA' &&
        timeType.includes(axisList.tableSchema.dataType) &&
        axisList.timeConfig?.timeTerm
      ) {
        if (!_.isEmpty(usedTimeSeriesSchemas)) {
          const _filertValueList = valueList.map((item) => {
            item.compareTimeConfig = axisTimeConfig;
            return item;
          });
          data.chartConfig = { ...configData[selectedChart], valueList: _filertValueList };
        }
      }
    }
    if (selectedChart === 'TABLE') {
      const rowListTimeConfig = configData[selectedChart]?.rowList?.[0]?.timeConfig;
      const rowList = _.cloneDeep(configData[selectedChart]?.rowList?.[0] || []);
      const valueList = _.cloneDeep(configData[selectedChart]?.valueList);
      if (
        rowList.type === 'TABLE_SCHEMA' &&
        timeType.includes(rowList.tableSchema.dataType) &&
        rowList.timeConfig?.timeTerm
      ) {
        if (!_.isEmpty(usedTimeSeriesSchemas)) {
          const _filertValueList = valueList.map((item) => {
            item.compareTimeConfig = rowListTimeConfig;
            return item;
          });
          data.chartConfig = { ...configData[selectedChart], valueList: _filertValueList };
        }
      }
    }
    dispatch({ loading: true });
    await doRequest(_.cloneDeep(data));
    if (_.isEmpty(createInfo) || curFlag) saveRecordInfo(props.match.params.id, userId);
  };

  const onSave = async (flag) => {
    try {
      dispatch({ initLoading: true });
      const data = {
        ...info,
        chartConfig: configData[selectedChart],
        filter: labelFilterData(filterData[selectedChart]),
        chartType: selectedChart
      };
      // 如果没有设置数据显示格式默认展示千分位数据格式
      const valueListFormat = configData[selectedChart]?.valueList.map((item) => {
        if (!item.numberConfig) {
          item.numberConfig = {
            format: 'NUMBER',
            decimalSeparator: 0,
            thousandsSeparator: true
          };
        }
        return item;
      });
      data.chartConfig = { ...configData[selectedChart], valueList: valueListFormat };
      if (!data.chartConfig.campaignFilters && cache?.campaignId) {
        data.chartConfig = {
          ...data.chartConfig,
          campaignFilters: campaignList
        };
      }
      if (data.chartType === 'DONUT') {
        data.chartConfig = {
          ...data.chartConfig,
          limit: state.filterCount
        };
      }
      if (data.chartType === 'GROUP_TABLE') {
        data.chartConfig = {
          ...data.chartConfig,
          isMerged: state.grandchildData
        };
      }
      if (flag) {
        data.name += t('analysisCenter-qmpG2y5bPaS7');
        delete data.id;
      }
      const dataModelId = !propsDataModelId ? props.location.state?.dataModelId || null : propsDataModelId;
      if (info?.isBusinessTable === 2 && dataModelId) {
        data.chartConfig = { ...data.chartConfig, dataModelId: props.location.state?.dataModelId || propsDataModelId };
      }
      data.deptId = flag
        ? window.getDeptId()
        : props.campaign?.deptId || info?.deptId || createInfo?.deptId || window.getDeptId();
      const res = await AnalysisCenterService.saveChartConfig(data);
      dispatch({ initLoading: false });

      if (cache?.campaignId) {
        // const { widgets, version } = props.location.state
        // const _widgets = _.cloneDeep(widgets)
        // _widgets.push({
        //   x: (_widgets.length * 4) % (12),
        //   y: widgets[widgets?.length - 1]?.y + 100 || 0,
        //   w: 4,
        //   h: 3,
        //   isResizable: true,
        //   i: `${res.id}`
        // });
        let detailObj;
        if (cache?.boardChart === 'campaigns') {
          detailObj = await campaignsService.getCampaignsV2({
            id: Number(cache.campaignId),
            deptId: info?.deptId || window.getDeptId()
          });
          localStorage.setItem('activityCache', true);
          message.success(
            <div style={{ display: 'inline-block' }}>
              <span>【{data.name}】</span>
              {t('analysisCenter-ZiyeRqSwidg2')}
              <span
                style={{ color: 'var(--ant-primary-color)', cursor: 'pointer' }}
                onClick={() => props.history.push(`/aimarketer/home/<USER>/detail/${cache.campaignId}`)}
              >
                【{detailObj.name}】
              </span>
              {t('analysisCenter-t0LBykkAlnxQ')}
            </div>
          );
          saveRecordInfo(curFlag ? props.match.params.id : res.id, userId);
        } else {
          if (cache.scenarioId !== info?.scenario?.id) {
            message.error(t('analysisCenter-NjLzwprS8b9m'));
            throw new Error(t('analysisCenter-NjLzwprS8b9m'));
          }
          detailObj = await campaignV2Service.get(cache.campaignId);
          localStorage.setItem('activityCache', true);
          message.success(
            <div style={{ display: 'inline-block' }}>
              <span>【{data.name}】</span>
              {t('analysisCenter-ZiyeRqSwidg2')}
              <span
                style={{ color: 'var(--ant-primary-color)', cursor: 'pointer' }}
                onClick={() => props.history.push(`/aimarketer/home/<USER>/detail?id=${cache.campaignId}`)}
              >
                【{detailObj.name}】
              </span>
              {t('analysisCenter-ErZPYtRR5Xne')}
            </div>
          );
        }

        // localStorage.setItem('campaignsChart', res.id);

        // localStorage.setItem(
        //   'campaignsChart',
        //   JSON.stringify({
        //     boardType: props.location.state.boardChart,
        //     campaignId: res.id,
        //     id: props.location.state?.campaignId
        //   })
        // );

        // const version = cache.version;

        // const _widgets = _.cloneDeep(widgets);

        const selectValue = await campaignV2Service.campaignV2ChartBoardListBy([
          {
            operator: 'EQ',
            propertyName: 'campaignId',
            value: cache?.campaignId
          }
        ]);

        const widgets = selectValue.length ? selectValue[0].widgets : [];

        const _widgets = widgets;

        if (!_.isEmpty(_widgets) && !_widgets.find((item) => Number(item.i) === Number(res.id))) {
          _widgets.push({
            x: (_widgets.length * 4) % 12,
            y: widgets[widgets?.length - 1]?.y + 100 || 0,
            w: 4,
            h: 3,
            isResizable: true,
            i: `${res.id}`
          });

          await campaignV2Service.saveCampaignV2ChartBoard({
            widgets: _widgets,
            campaignId: cache?.campaignId,
            version: selectValue[0].version,
            type: cache?.boardChart === 'campaigns' ? 'CAMPAIGNS' : 'FLOW_CANVAS',
            id: cache?.id
          });
        } else if (_.isEmpty(_widgets)) {
          _widgets.push({
            x: (_widgets.length * 4) % 12,
            y: widgets[widgets?.length - 1]?.y + 100 || 0,
            w: 4,
            h: 3,
            isResizable: true,
            i: `${res.id}`
          });

          await campaignV2Service.saveCampaignV2ChartBoard({
            widgets: _widgets,
            campaignId: cache?.campaignId,
            version: cache.version,
            type: cache?.boardChart === 'campaigns' ? 'CAMPAIGNS' : 'FLOW_CANVAS',
            id: cache?.id
          });
        }
        saveRecordInfo(curFlag ? props.match.params.id : res.id, userId);
      } else {
        message.success(t('analysisCenter-OUU9aosnPk0n'));
        saveRecordInfo(curFlag ? props.match.params.id : res.id, userId);
        props.history.push('/aimarketer/home/<USER>/database');
      }
    } catch (error) {
      console.error(error);
      dispatch({ initLoading: false });
    }
  };

  const changeName = (data) => {
    info.name = data;
    dispatch({});
    // if(data.length <= 18){

    // }else{
    //   message.warn('标题最多18个字', 1)
    // }
  };

  // const changeRemark = (data) => {
  //   info.remark = data;
  //   dispatch({});
  // };

  const onFilterResize = _.throttle((e, data) => {
    setFilterWidth(data.size.width);
  }, 100);
  const onViewResize = _.throttle((e, data) => {
    setViewWidth(data.size.width);
  }, 100);
  const onFieldResize = _.throttle((e, data) => {
    setFieldWidth(data.size.width);
  }, 100);

  const setSelectTime = (value) => {
    info.dateRange2 = value;
    dispatch({});
  };

  const usedTableSchemaFilter = _.filter(filterData[selectedChart], (item) => item.type === 'TABLE_SCHEMA');

  const usedTimeSeriesSchemas = _.filter(timeSeriesSchemas, (item) =>
    _.find(usedTableSchemaFilter, (filter) => filter?.tableSchema?.table?.id === item?.table?.id)
  );

  const content = _.map(usedTimeSeriesSchemas, (item) => {
    return <div key={item.id}>{`${item?.table?.displayName}.${item.displayName}`}</div>;
  });

  const clickIcon = async () => {
    // props.history.push(`${window.location.pathname}?fullScreen=true`)
    setFilterConfig({});
    init();
  };

  const exitQuit = () => {
    props.history.go(-1);
  };

  const onChartTypeChange = (keywords) => {
    const _configData = _.cloneDeep(configData);
    const _filterData = _.cloneDeep(filterData);
    if (selectedChart === keywords) {
      return;
    }
    const preChartTypeObj = _configData[selectedChart];
    const chartTypeObj = _configData[keywords];

    if (selectedChart === 'BUBBLE') {
      preChartTypeObj.axisList = undefined;
    }

    if (keywords === 'BUBBLE') {
      chartTypeObj.axisList = undefined;
    }

    let preCacheStatus = true;
    let cacheStatus = true;

    const arr = [];
    for (const key in preChartTypeObj) {
      const value = preChartTypeObj[key];
      if (_.isArray(value)) {
        if (value && value.length) {
          arr.push(value);
        }
      }
    }

    if (!arr.length) preCacheStatus = false;

    for (const key in chartTypeObj) {
      const value = chartTypeObj[key];
      if (_.isArray(value)) {
        if (value && value.length) {
          cacheStatus = false;
        }
      }
    }
    if (preCacheStatus && cacheStatus) {
      if (selectedChart === 'TABLE') {
        if (keywords === 'COLUMN' || keywords === 'LINE') {
          chartTypeObj.axisList = preChartTypeObj.rowList;
          chartTypeObj.legendList = preChartTypeObj.columnList.slice(0, 1);
          chartTypeObj.valueList = preChartTypeObj.valueList;

          const arr1 = [];
          let result = [];

          if (_filterData[selectedChart].length) {
            _filterData[selectedChart].forEach((item) => {
              if (item.dragType === 'COLUMN') {
                arr1.push(item);
              }
            });

            result = [..._filterData[selectedChart].filter((item) => item.dragType !== 'COLUMN'), arr1[0]];

            _filterData[keywords] = _.cloneDeep(
              _filterData[selectedChart].length === 1 ? _filterData[selectedChart] : result.filter((item) => item)
            );

            _filterData[keywords].forEach((item) => {
              if (item.dragType === 'ROW') item.dragType = 'AXIS';
              if (item.dragType === 'COLUMN') item.dragType = 'LEGEND';
            });
          }
        } else if (keywords === 'BUBBLE') {
          chartTypeObj.xvalueList = preChartTypeObj.rowList.map((item) => {
            return {
              ...item,
              function: item.function || 'COUNT'
            };
          });
          chartTypeObj.legendList = preChartTypeObj.columnList.slice(0, 1);
          chartTypeObj.yvalueList = preChartTypeObj.valueList.slice(0, 1);

          const arr1 = [];
          const arr2 = [];
          let result = [];

          if (_filterData[selectedChart].length) {
            _filterData[selectedChart].forEach((item) => {
              if (item.dragType === 'VALUE') {
                arr1.push(item);
              } else if (item.dragType === 'COLUMN') {
                arr2.push(item);
              }
            });

            result = [
              ..._filterData[selectedChart].filter((item) => item.dragType !== 'VALUE' && item.dragType !== 'COLUMN'),
              arr2[0],
              arr1[0]
            ];

            _filterData[keywords] = _.cloneDeep(
              _filterData[selectedChart].length === 1 ? _filterData[selectedChart] : result.filter((item) => item)
            );

            _filterData[keywords].forEach((item) => {
              if (item.dragType === 'ROW') {
                item.dragType = 'X_VALUE';
              } else if (item.dragType === 'COLUMN') {
                item.dragType = 'LEGEND';
              } else if (item.dragType === 'VALUE') {
                item.dragType = 'Y_VALUE';
              }
            });
          }
        } else if (keywords === 'DONUT') {
          chartTypeObj.legendList = preChartTypeObj.columnList.slice(0, 1);
          chartTypeObj.valueList = preChartTypeObj.valueList.slice(0, 1);

          const arr1 = [];
          const arr2 = [];
          let result = [];

          if (_filterData[selectedChart].length) {
            _filterData[selectedChart].forEach((item) => {
              if (item.dragType === 'VALUE') {
                arr1.push(item);
              } else if (item.dragType === 'COLUMN') {
                arr2.push(item);
              }
            });

            result = [
              ..._filterData[selectedChart].filter(
                (item) => item.dragType !== 'VALUE' && item.dragType !== 'ROW' && item.dragType !== 'COLUMN'
              ),
              arr2[0],
              arr1[0]
            ];

            _filterData[keywords] = _.cloneDeep(
              _filterData[selectedChart].length === 1 ? _filterData[selectedChart] : result.filter((item) => item)
            );

            _filterData[keywords].forEach((item) => {
              if (item.dragType === 'COLUMN') item.dragType = 'LEGEND';
            });

            // _filterData[keywords] = _filterData[keywords].filter(
            //   (item2) => item2.key === chartTypeObj?.valueList[0]?.key || item2.dragType === 'FILTER'
            // );
          }
        } else if (keywords === 'GROUP_TABLE') {
          chartTypeObj.valueList = _.filter(
            preChartTypeObj.valueList,
            (item) =>
              ['DOUBLE', 'INT', 'LONG'].includes(item?.tableSchema?.dataType) ||
              userContrastTypeList.includes(item?.type)
          );
          chartTypeObj.columnList = _.filter(
            preChartTypeObj.rowList,
            (item) => indexType.includes(item?.tableSchema?.dataType) || userContrastTypeList.includes(item?.type)
          );
          if (_filterData[selectedChart].length) {
            // 当切换到分组表将不匹配限制属性的过滤条件去除
            _filterData[keywords] = _filterData[selectedChart].filter((item2) => {
              if (!userContrastTypeList.includes(item2?.type)) {
                if (item2.dragType === 'VALUE') {
                  return ['DOUBLE', 'INT', 'LONG'].includes(item2.tableSchema.dataType);
                } else if (item2.dragType === 'ROW') {
                  return indexType.includes(item2.tableSchema.dataType);
                } else {
                  return false;
                }
              } else {
                return item2;
              }
            });
          }
        } else {
          chartTypeObj.valueList = preChartTypeObj.valueList.slice(0, 1);
          _filterData[keywords] = _.cloneDeep(_filterData[selectedChart])
            .filter((item) => item.dragType === 'VALUE' || item.dragType === 'FILTER')
            .filter((item2) => item2.key === chartTypeObj?.valueList[0]?.key || item2.dragType === 'FILTER');
        }
      } else if (selectedChart === 'COLUMN' || selectedChart === 'LINE') {
        if (keywords === 'TABLE') {
          chartTypeObj.rowList = preChartTypeObj.axisList;
          chartTypeObj.columnList = preChartTypeObj.legendList;
          chartTypeObj.valueList = preChartTypeObj.valueList;

          _filterData[keywords] = _.cloneDeep(_filterData[selectedChart]);

          _filterData[keywords].forEach((item) => {
            if (item.dragType === 'AXIS') item.dragType = 'ROW';
            if (item.dragType === 'LEGEND') item.dragType = 'COLUMN';
          });
        } else if (keywords === 'BUBBLE') {
          chartTypeObj.xvalueList = preChartTypeObj.axisList.map((item) => {
            return {
              ...item,
              function: item.function || 'COUNT'
            };
          });
          chartTypeObj.legendList = preChartTypeObj.legendList;
          chartTypeObj.yvalueList = preChartTypeObj.valueList.slice(0, 1);

          const arr1 = [];
          let result = [];

          if (_filterData[selectedChart].length) {
            _filterData[selectedChart].forEach((item) => {
              if (item.dragType === 'VALUE') {
                arr1.push(item);
              }
            });

            result = [..._filterData[selectedChart].filter((item) => item.dragType !== 'VALUE'), arr1[0]];

            _filterData[keywords] = _.cloneDeep(
              _filterData[selectedChart].length === 1 ? _filterData[selectedChart] : result.filter((item) => item)
            );

            _filterData[keywords].forEach((item) => {
              if (item.dragType === 'AXIS') item.dragType = 'X_VALUE';
              if (item.dragType === 'VALUE') item.dragType = 'Y_VALUE';
            });
          }
        } else if (keywords === 'DONUT') {
          chartTypeObj.legendList = preChartTypeObj.legendList;
          chartTypeObj.valueList = preChartTypeObj.valueList.slice(0, 1);

          const arr1 = [];
          let result = [];

          if (_filterData[selectedChart].length) {
            _filterData[selectedChart].forEach((item) => {
              if (item.dragType === 'VALUE') {
                arr1.push(item);
              }
            });

            result = [
              ..._filterData[selectedChart].filter((item) => item.dragType !== 'VALUE' && item.dragType !== 'AXIS'),
              arr1[0]
            ];

            _filterData[keywords] = _.cloneDeep(
              _filterData[selectedChart].length === 1 ? _filterData[selectedChart] : result.filter((item) => item)
            );

            // _filterData[keywords] = _filterData[keywords].filter(
            //   (item2) => item2.key === chartTypeObj?.valueList[0]?.key || item2.dragType === 'FILTER'
            // );
          }
        } else if (keywords === 'COLUMN' || keywords === 'LINE') {
          chartTypeObj.axisList = preChartTypeObj.axisList;
          chartTypeObj.legendList = preChartTypeObj.legendList;
          chartTypeObj.valueList = preChartTypeObj.valueList;

          _filterData[keywords] = _filterData[selectedChart];
        } else if (keywords === 'GROUP_TABLE') {
          chartTypeObj.valueList = _.filter(
            preChartTypeObj.valueList,
            (item) =>
              ['DOUBLE', 'INT', 'LONG'].includes(item?.tableSchema?.dataType) ||
              userContrastTypeList.includes(item?.type)
          );
          chartTypeObj.columnList = _.filter(
            preChartTypeObj.axisList,
            (item) => indexType.includes(item?.tableSchema?.dataType) || userContrastTypeList.includes(item?.type)
          );
          if (_filterData[selectedChart].length) {
            // 当切换到分组表将不匹配限制属性的过滤条件去除
            _filterData[keywords] = _filterData[selectedChart].filter((item2) => {
              if (!userContrastTypeList.includes(item2?.type)) {
                if (item2.dragType === 'VALUE') {
                  return ['DOUBLE', 'INT', 'LONG'].includes(item2.tableSchema.dataType);
                } else if (item2.dragType === 'AXIS') {
                  return indexType.includes(item2.tableSchema.dataType);
                } else {
                  return false;
                }
              } else {
                return item2;
              }
            });
          }
        } else {
          chartTypeObj.valueList = preChartTypeObj.valueList.slice(0, 1);

          _filterData[keywords] = _filterData[selectedChart]
            .filter((item) => item.dragType === 'VALUE' || item.dragType === 'FILTER')
            .filter((item2) => item2.key === chartTypeObj?.valueList[0]?.key || item2.dragType === 'FILTER');
        }
      } else if (selectedChart === 'BUBBLE') {
        if (keywords === 'TABLE') {
          chartTypeObj.rowList = preChartTypeObj.xvalueList;
          chartTypeObj.valueList = preChartTypeObj.yvalueList;
          chartTypeObj.columnList = preChartTypeObj.legendList;

          const res = _.cloneDeep(_filterData[selectedChart]);
          // _filterData[keywords] = _.cloneDeep(_filterData[selectedChart]);
          const newRes = res.filter((item) => item.dragType !== 'VALUE');
          newRes.forEach((item) => {
            if (item.dragType === 'LEGEND') item.dragType = 'COLUMN';
            if (item.dragType === 'X_VALUE') item.dragType = 'ROW';
            if (item.dragType === 'Y_VALUE') item.dragType = 'VALUE';
          });

          _filterData[keywords] = newRes;
        } else if (keywords === 'COLUMN' || keywords === 'LINE') {
          chartTypeObj.axisList = preChartTypeObj.xvalueList;
          chartTypeObj.legendList = preChartTypeObj.legendList;
          chartTypeObj.valueList = preChartTypeObj.yvalueList;

          _filterData[keywords] = _.cloneDeep(_filterData[selectedChart]);
          const newFilterData = _filterData[keywords].filter((item) => item.dragType !== 'VALUE');
          newFilterData.forEach((item) => {
            if (item.dragType === 'X_VALUE') item.dragType = 'AXIS';
            if (item.dragType === 'Y_VALUE') item.dragType = 'VALUE';
          });

          _filterData[keywords] = newFilterData;
        } else if (keywords === 'DONUT') {
          chartTypeObj.legendList = preChartTypeObj.legendList;
          chartTypeObj.valueList = preChartTypeObj.yvalueList;

          _filterData[keywords] = _.cloneDeep(_filterData[selectedChart]);

          const newFilterData = _filterData[keywords].filter(
            (item) => item.dragType !== 'VALUE' && item.dragType !== 'X_VALUE'
          );
          newFilterData.forEach((item) => {
            if (item.dragType === 'Y_VALUE') item.dragType = 'VALUE';
          });
          _filterData[keywords] = newFilterData;
        } else {
          chartTypeObj.valueList = preChartTypeObj.yvalueList;
          _filterData[keywords] = _filterData[selectedChart].filter(
            (item) => item.dragType === 'Y_VALUE' || item.dragType === 'FILTER'
          );
        }
      } else if (selectedChart === 'DONUT') {
        if (keywords === 'TABLE') {
          chartTypeObj.valueList = preChartTypeObj.valueList;
          chartTypeObj.columnList = preChartTypeObj.legendList;

          _filterData[keywords] = _.cloneDeep(_filterData[selectedChart]);
          _filterData[keywords].forEach((item) => {
            if (item.dragType === 'LEGEND') item.dragType = 'COLUMN';
          });
        } else if (keywords === 'COLUMN' || keywords === 'LINE') {
          chartTypeObj.legendList = preChartTypeObj.legendList;
          chartTypeObj.valueList = preChartTypeObj.valueList;

          _filterData[keywords] = _.cloneDeep(_filterData[selectedChart]);
        } else if (keywords === 'BUBBLE') {
          chartTypeObj.legendList = preChartTypeObj.legendList;
          chartTypeObj.yvalueList = preChartTypeObj.valueList;

          _filterData[keywords] = _.cloneDeep(_filterData[selectedChart]);
          _filterData[keywords].forEach((item) => {
            if (item.dragType === 'VALUE') item.dragType = 'Y_VALUE';
          });
        } else if (keywords === 'GROUP_TABLE') {
          chartTypeObj.valueList = _.filter(
            preChartTypeObj.valueList,
            (item) =>
              ['DOUBLE', 'INT', 'LONG'].includes(item?.tableSchema?.dataType) ||
              userContrastTypeList.includes(item?.type)
          );
          chartTypeObj.columnList = _.filter(
            preChartTypeObj.legendList,
            (item) => indexType.includes(item?.tableSchema?.dataType) || userContrastTypeList.includes(item?.type)
          );
          if (_filterData[selectedChart].length) {
            // 当切换到分组表将不匹配限制属性的过滤条件去除
            _filterData[keywords] = _filterData[selectedChart].filter((item2) => {
              if (!userContrastTypeList.includes(item2?.type)) {
                if (item2.dragType === 'VALUE') {
                  return ['DOUBLE', 'INT', 'LONG'].includes(item2.tableSchema.dataType);
                } else {
                  return indexType.includes(item2.tableSchema.dataType);
                }
              } else {
                return item2;
              }
            });
          }
        } else {
          chartTypeObj.valueList = preChartTypeObj.valueList;
          _filterData[keywords] = _filterData[selectedChart].filter(
            (item) => item.dragType === 'VALUE' || item.dragType === 'FILTER'
          );
        }
      } else if (selectedChart === 'CARD' || selectedChart === 'FUNNEL') {
        if (keywords === 'BUBBLE') {
          chartTypeObj.yvalueList = preChartTypeObj.valueList;
          _filterData[keywords] = _.cloneDeep(_filterData[selectedChart]);
          _filterData[keywords].forEach((item) => {
            if (item.dragType === 'VALUE') item.dragType = 'Y_VALUE';
          });
        } else if (keywords === 'GROUP_TABLE') {
          chartTypeObj.valueList = _.filter(
            preChartTypeObj.valueList,
            (item) =>
              ['DOUBLE', 'INT', 'LONG'].includes(item?.tableSchema?.dataType) ||
              userContrastTypeList.includes(item?.type)
          );
          if (_filterData[selectedChart].length) {
            // 当切换到分组表将不匹配限制属性的过滤条件去除
            _filterData[keywords] = _filterData[selectedChart].filter((item2) => {
              if (!userContrastTypeList.includes(item2?.type)) {
                if (item2.dragType === 'VALUE') {
                  return ['DOUBLE', 'INT', 'LONG'].includes(item2.tableSchema.dataType);
                } else {
                  return false;
                }
              } else {
                return item2;
              }
            });
          }
        } else {
          chartTypeObj.valueList = preChartTypeObj.valueList;
          _filterData[keywords] = _filterData[selectedChart];
        }
      } else if (selectedChart === 'GROUP_TABLE') {
        if (keywords === 'COLUMN' || keywords === 'LINE') {
          chartTypeObj.axisList = preChartTypeObj.columnList.slice(0, 1);
          chartTypeObj.valueList = preChartTypeObj.valueList;

          const arr1 = [];
          let result = [];

          if (_filterData[selectedChart].length) {
            _filterData[selectedChart].forEach((item) => {
              if (item.dragType === 'COLUMN') {
                arr1.push(item);
              }
            });

            result = [..._filterData[selectedChart].filter((item) => item.dragType !== 'COLUMN'), arr1[0]];

            _filterData[keywords] = _.cloneDeep(
              _filterData[selectedChart].length === 1 ? _filterData[selectedChart] : result.filter((item) => item)
            );

            _filterData[keywords].forEach((item) => {
              if (item.dragType === 'COLUMN') item.dragType = 'AXIS';
            });
          }
        } else if (keywords === 'BUBBLE') {
          chartTypeObj.xvalueList = preChartTypeObj.columnList.map((item) => {
            return {
              ...item,
              function: item.function || 'COUNT'
            };
          });
          chartTypeObj.legendList = preChartTypeObj.columnList.slice(0, 1);
          chartTypeObj.yvalueList = preChartTypeObj.valueList.slice(0, 1);

          const arr1 = [];
          const arr2 = [];
          let result = [];

          if (_filterData[selectedChart].length) {
            _filterData[selectedChart].forEach((item) => {
              if (item.dragType === 'VALUE') {
                arr1.push(item);
              } else if (item.dragType === 'COLUMN') {
                arr2.push(item);
              }
            });

            result = [
              ..._filterData[selectedChart].filter((item) => item.dragType !== 'VALUE' && item.dragType !== 'COLUMN'),
              arr2[0],
              arr1[0]
            ];

            _filterData[keywords] = _.cloneDeep(
              _filterData[selectedChart].length === 1 ? _filterData[selectedChart] : result.filter((item) => item)
            );

            _filterData[keywords].forEach((item) => {
              if (item.dragType === 'ROW') {
                item.dragType = 'X_VALUE';
              } else if (item.dragType === 'COLUMN') {
                item.dragType = 'LEGEND';
              } else if (item.dragType === 'VALUE') {
                item.dragType = 'Y_VALUE';
              }
            });
          }
        } else if (keywords === 'DONUT') {
          chartTypeObj.legendList = preChartTypeObj.columnList.slice(0, 1);
          chartTypeObj.valueList = preChartTypeObj.valueList.slice(0, 1);

          const arr1 = [];
          const arr2 = [];
          let result = [];

          if (_filterData[selectedChart].length) {
            _filterData[selectedChart].forEach((item) => {
              if (item.dragType === 'VALUE') {
                arr1.push(item);
              } else if (item.dragType === 'COLUMN') {
                arr2.push(item);
              }
            });

            result = [
              ..._filterData[selectedChart].filter((item) => item.dragType !== 'VALUE' && item.dragType !== 'COLUMN'),
              arr2[0],
              arr1[0]
            ];

            _filterData[keywords] = _.cloneDeep(
              _filterData[selectedChart].length === 1 ? _filterData[selectedChart] : result.filter((item) => item)
            );

            _filterData[keywords].forEach((item) => {
              if (item.dragType === 'COLUMN') item.dragType = 'LEGEND';
            });

            // _filterData[keywords] = _filterData[keywords].filter(
            //   (item2) => item2.key === chartTypeObj?.valueList[0]?.key || item2.dragType === 'FILTER'
            // );
          }
        } else if (keywords === 'TABLE') {
          chartTypeObj.rowList = preChartTypeObj.columnList.slice(0, 1);
          chartTypeObj.valueList = preChartTypeObj.valueList;
          _filterData[keywords] = _.cloneDeep(_filterData[selectedChart]);
          _filterData[keywords].forEach((item) => {
            if (item.dragType === 'COLUMN') item.dragType = 'ROW';
          });
        } else {
          chartTypeObj.valueList = preChartTypeObj.valueList.slice(0, 1);

          _filterData[keywords] = _filterData[selectedChart]
            .filter((item) => item.dragType === 'VALUE' || item.dragType === 'FILTER')
            .filter((item2) => item2.key === chartTypeObj?.valueList[0]?.key || item2.dragType === 'FILTER');
        }
      }
      dispatch({
        selectedChart: keywords,
        configData: _configData,
        filterData: _filterData
      });
    } else {
      dispatch({ selectedChart: keywords });
    }
  };

  // const onChartTypeChange = (keywords) => {
  //   dispatch({ selectedChart: keywords });
  // };

  return (
    <Spin spinning={state.initLoading}>
      <div className="createChart">
        <header>
          <div className="left">
            <span className="titleBack" onClick={exitQuit}>
              <svg t="1574939230608" className="back" viewBox="0 0 1024 1024" p-id="1098" width="23" height="23">
                <path
                  d="M156.608 487.8592c1.156267-1.137067 2.648533-1.6224 3.918933-2.558933l306.666667-301.569067c13.195733-12.970667 34.586667-12.970667 47.781333 0 13.194667 12.978133 13.194667 34.013867 0 46.987733L263.3024 478.210133l579.2032 0c18.978133 0 34.362667 15.128533 34.362667 33.789867 0 18.6624-15.384533 33.793067-34.362667 33.793067L263.3024 545.793067l251.671467 247.486933c13.194667 12.971733 13.194667 34.010667 0 46.984533-13.194667 12.974933-34.586667 12.974933-47.781333 0l-306.666667-301.5616c-1.269333-0.939733-2.762667-1.421867-3.918933-2.562133-6.334933-6.2304-9.240533-14.340267-9.477333-22.5024C147.367467 502.200533 150.273067 494.090667 156.608 487.8592L156.608 487.8592zM156.608 487.8592"
                  p-id="1099"
                />
              </svg>
            </span>
            <div className="leftHandleWrapper">
              <Paragraph
                className="big"
                ellipsis={{ rows: 1 }}
                style={{ width: 310 }}
                editable={{ onChange: changeName }}
              >
                {info?.name}
              </Paragraph>
              {/* <Paragraph className="small" style={{width: 160}} editable={{ onChange: changeRemark }}>{info.remark }</Paragraph> */}
            </div>
          </div>
          <div className="right">
            {info?.scenario || createInfo?.scenario ? (
              <div className="selectedScenario">
                {t('analysisCenter-StkHr7Mxsq2b')}
                <span>{info?.scenario?.name}</span>
              </div>
            ) : null}
            <div className=" max-w-[320px] overflow-hidden text-ellipsis whitespace-nowrap mr-[24px]">
              {t('analysisCenter-GtMNqeLMcta9')}
              <Tooltip title={deptPath} placement="topLeft">
                <span>{deptPath}</span>
              </Tooltip>
            </div>
            {info?.isBusinessTable !== 2 && (
              <div className="timesRange">
                <span>{t('analysisCenter-EiICTadGtJ0v')}</span>
                <SelectTime data={info?.dateRange2} showTime style={{ width: 335 }} onChange={setSelectTime} />
                {!_.isEmpty(content) && (
                  <Popover placement="bottom" content={content} trigger="hover">
                    <div style={{ paddingLeft: 10, paddingRight: 10 }} className="">
                      <a>
                        <span>{t('analysisCenter-hGuAACEt8W61')}</span>
                        <DownOutlined style={{ fontSize: 12 }} />{' '}
                      </a>
                    </div>
                  </Popover>
                )}
              </div>
            )}
            <Space className="btnGroup">
              <Button type="primary" loading={state.loading} onClick={() => compute({ forceCalc: true })}>
                {t('analysisCenter-0WCVHB29IdUY')}
              </Button>
              {cache?.campaignId || cache || localStorage.getItem('isActivityAnalysis') ? (
                <Button
                  disabled={curFlag ? !_.isEmpty(filterConfig) : computeFlag}
                  onClick={() => onSave(false)}
                  // loading={state.initLoading}
                  buttonsRender={([leftButton, rightButton]) => [
                    React.cloneElement(leftButton, {
                      loading: state.initLoading,
                      key: '11'
                    }),
                    React.cloneElement(rightButton, {
                      loading: state.initLoading,
                      key: '22'
                    })
                  ]}
                  type="primary"
                >
                  {curFlag ? t('analysisCenter-WtVUIds6xa5q') : t('analysisCenter-JPY6HezVzDif')}
                </Button>
              ) : (
                <Dropdown.Button
                  disabled={curFlag ? !_.isEmpty(filterConfig) : computeFlag}
                  icon={<DownOutlined />}
                  onClick={() => onSave(false)}
                  // loading={state.initLoading}
                  buttonsRender={([leftButton, rightButton]) => [
                    React.cloneElement(leftButton, {
                      loading: state.initLoading,
                      key: '11'
                    }),
                    React.cloneElement(rightButton, {
                      loading: state.initLoading,
                      key: '22'
                    })
                  ]}
                  type="primary"
                  menu={{
                    items: [
                      {
                        label: t('analysisCenter-Z9UNeOUW7Qxo'),
                        key: '1',
                        onClick: () => onSave(true)
                      }
                    ]
                  }}
                >
                  {curFlag ? t('analysisCenter-WtVUIds6xa5q') : t('analysisCenter-JPY6HezVzDif')}
                </Dropdown.Button>
              )}
            </Space>
          </div>
        </header>
        <div className="content">
          <DataContext.Provider value={{ state, dispatch }}>
            <DndProvider backend={HTML5Backend}>
              <div className="mainChart" style={{ minWidth: 300 }}>
                {!_.isEmpty(filterConfig) && (
                  <div className="term">
                    <span className="title">{t('analysisCenter-ilctPFCki0ZW')}</span>
                    <Card style={{ width: '348px', height: '32px' }}>
                      <p>
                        {dayjs(parseInt(filterConfig.startTime)).format('YYYY-MM-DD HH:mm:ss')} ~{' '}
                        {dayjs(parseInt(filterConfig.endTime)).format('YYYY-MM-DD HH:mm:ss')}{' '}
                        <CloseOutlined onClick={clickIcon} />
                      </p>
                    </Card>
                  </div>
                )}
                <Suspense fallback={<div>{t('analysisCenter-VyXGwsD6jjwL')}</div>}>{moduleComponents.chart}</Suspense>
              </div>
              {filterFold ? (
                <div className="foldWrap">
                  <div className="foldIcon" onClick={() => dispatch({ filterFold: false })}>
                    <DoubleLeftOutlined style={{ fontSize: 12 }} />
                  </div>
                  <div>
                    <div className="foldContent">
                      <FilterOutlined className="rotate" />
                      <span className="rotate" style={{ marginTop: 8 }}>
                        {t('analysisCenter-uDRCDw9xo116')}
                      </span>
                      <span className="rotate">{t('analysisCenter-9VtHImF9qKhz')}</span>
                      <span className="rotate">{t('analysisCenter-YBhGSe193LyK')}</span>
                    </div>
                  </div>
                </div>
              ) : (
                <Resizable
                  width={filterWidth}
                  height={0}
                  axis="x"
                  minConstraints={[240]}
                  maxConstraints={[400]}
                  onResize={onFilterResize}
                  // handle
                  resizeHandles={['w']}
                  draggableOpts={{ enableUserSelectHack: false }}
                >
                  <div className="filter" style={{ width: filterWidth }}>
                    <Suspense fallback={<div>{t('analysisCenter-VyXGwsD6jjwL')}</div>}>
                      <Filter setFilterWidth={setFilterWidth} />
                      {cache?.campaignId && (
                        <div className="p-[16px] pl-[12px] campaignDateTree">
                          <div className="font-[600] mb-[8px]">{t('analysisCenter-2FHJr8SQb7AL')}</div>
                          <Tree
                            treeData={campaignTreeData}
                            selectable={false}
                            className={cache?.boardChart === 'campaigns' ? 'bg-[#FAFAFA]' : 'treeNoopNone bg-[#FAFAFA]'}
                          />
                        </div>
                      )}
                    </Suspense>
                  </div>
                </Resizable>
              )}
              {visualFold ? (
                <div className="foldWrap">
                  <div className="foldIcon" onClick={() => dispatch({ visualFold: false })}>
                    <DoubleLeftOutlined style={{ fontSize: 12 }} onClick={() => dispatch({ visualFold: false })} />
                  </div>
                  <div>
                    <div className="foldContent">
                      <FilterOutlined className="rotate" />
                      <span className="rotate" style={{ marginTop: 8 }}>
                        {t('analysisCenter-HbnCeaEtoapT')}
                      </span>
                      <span className="rotate">{t('analysisCenter-2mTXBmxtW8by')}</span>
                      <span className="rotate">{t('analysisCenter-8t4THoxXt353')}</span>
                    </div>
                  </div>
                </div>
              ) : (
                <Resizable
                  width={viewWidth}
                  height={0}
                  axis="x"
                  minConstraints={[200]}
                  maxConstraints={[400]}
                  onResize={onViewResize}
                  // handle
                  resizeHandles={['w']}
                  draggableOpts={{ enableUserSelectHack: false }}
                >
                  <div className="chartListAndFill" style={{ width: viewWidth }}>
                    <div className="titileStyle">
                      <div className="title">
                        <BarChartOutlined />
                        <span style={{ marginLeft: 8 }}>{t('analysisCenter-RmNRnPUeNaLC')}</span>
                      </div>
                      <div className="hideIconWrap">
                        <DoubleRightOutlined
                          style={{ float: 'right', fontSize: 12 }}
                          onClick={() => dispatch({ visualFold: true })}
                        />
                      </div>
                    </div>
                    {/* <div className="titileStyle">可视化</div> */}
                    <div className="chartList">
                      {_.map(iconSvg, (item) => {
                        return (
                          <div
                            className="chartListIcon"
                            key={item.keywords}
                            onClick={() => onChartTypeChange(item.keywords)}
                          >
                            {selectedChart === item.keywords ? item.normal : item.gray}
                          </div>
                        );
                      })}
                    </div>
                    <div className="fillScroll">
                      <Suspense fallback={<div>{t('analysisCenter-VyXGwsD6jjwL')}</div>}>
                        {moduleComponents.fill}
                      </Suspense>
                    </div>
                  </div>
                </Resizable>
              )}
              {dataSourceFold ? (
                <div className="foldWrap">
                  <div className="foldIcon" onClick={() => dispatch({ dataSourceFold: false })}>
                    <DoubleLeftOutlined style={{ fontSize: 12 }} onClick={() => dispatch({ dataSourceFold: false })} />
                  </div>
                  <div>
                    <div className="foldContent">
                      <FilterOutlined className="rotate" />
                      <span className="rotate" style={{ marginTop: 8 }}>
                        {t('analysisCenter-ye94lm4JKtCZ')}
                      </span>
                      <span className="rotate">{t('analysisCenter-QAEorUZzvVdE')}</span>
                    </div>
                  </div>
                </div>
              ) : (
                <Resizable
                  width={fieldWidth}
                  height={0}
                  axis="x"
                  minConstraints={[200]}
                  maxConstraints={[400]}
                  onResize={onFieldResize}
                  // handle
                  resizeHandles={['w']}
                  draggableOpts={{ enableUserSelectHack: false }}
                >
                  <div className="fieldList" style={{ width: fieldWidth }}>
                    <DataSource createInfo={createInfo} curFlag={curFlag} />
                  </div>
                </Resizable>
              )}
            </DndProvider>
            {formatVisible && <FormatModal />}
            {createVisible && (
              <AddChart
                visible={createVisible}
                createInfo={createInfo}
                modelId={props.location.state?.dataModelId || propsDataModelId}
                action={() => {
                  dispatch({ createVisible: false });
                }}
                dispatch={dispatch}
                setPropsInfo={setCreateInfo}
              />
            )}
          </DataContext.Provider>
        </div>
      </div>
    </Spin>
  );
};
