.dataSource {
    .ant-input-affix-wrapper {
        border-radius: 6px !important;
    }

    .anticon-search {
        color: rgba(0, 0, 0, 0.45);
    }

    height: calc(100% - 50px);
    // height: calc(100vh - 60px);
    padding: 8px 8px;

    .labelStyle {
        margin-bottom: 5px;
        color: black;
    }

    .inputStyle {
        margin-bottom: 10px;
    }

    .back_create {
        width: 100%;
        height: 32px;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: rgba(255, 255, 255, 1);
        padding: 0px 12px;
        line-height: 32px;
        border-radius: 6px;
        cursor: pointer;

        span:first-child {
            max-width: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 14px;
        }

        >div {
            flex: 1;
            display: flex;
            justify-content: flex-end;
        }
    }

    .titileStyle {
        font-size: 16px;
        font-weight: 600;
        padding: 0 0 12px 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .hideIconWrap {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
    }

    .tabsStyle {
        display: flex;

        .tabStyle {
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 32px;
            align-items: center;

            svg {
                // margin: 5px;
            }

            flex: 1;
            text-align: center;
            cursor:pointer;

            .nameStyle {
                font-size: 14px;
            }
        }
    }

    .boxName {
        font-size: 12px;
    }

    .treeWrapper {
        padding-bottom: 40px;
        // height: calc(100vh - 245px);
        overflow: auto;
        margin-top: 8px;

        .ant-tree {
            background: none;
        }

        .tableSchemaTree {
            .ant-tree-treenode-switcher-close {
                max-width: 100%;

                .ant-tree-node-content-wrapper {
                    max-width: calc(100% - 24px);
                }
            }

            .ant-tree-node-content-wrapper-normal {
                margin-left: -12px;
            }

            .ant-tree-title {
                font-size: 14px;

                .boxName {
                    font-size: 12px;
                    max-width: calc(100% - 20px);
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }

    }
}