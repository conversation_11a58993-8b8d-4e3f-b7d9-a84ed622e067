// @import 'assets/css/variable.scss';

.createChart {
  height: calc(100vh);

  header {
    display: flex;
    height: 56px;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    margin: 0 -24px;
    padding: 0 24px;
    box-shadow: 0 1px 2px 0 rgba(220, 226, 230, 0.8), 0 1px 8px 0 rgba(220, 226, 230, 0.59);

    .left {
      // font-size: 18px;
      display: flex;
      align-items: center;
      margin: 0;

      .titleBack {
        cursor: pointer;
      }

      .leftHandleWrapper {
        margin: 10px 0 0 20px;

        .ant-typography.big {
          // margin-bottom: 14px;
          font-size: 16px;
          font-weight: bold;

          textarea {
            font-size: 16px;
            font-weight: bold;
          }
        }
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      min-width: 865px;

      .selectedScenario {
        margin-right: 24px;
      }

      .btnGroup {
        .ant-btn {
          border-radius: 6px;
        }
      }

      .timesRange {
        display: flex;
        align-items: center;
        padding-right: 20px;

        .ant-input-group.ant-input-group-compact>:first-child {
          border-top-left-radius: 6px;
          border-bottom-left-radius: 6px;
        }

        .site-input-right {
          border-top-right-radius: 6px;
          border-bottom-right-radius: 6px;
        }
      }
    }
  }

  .content {
    background-color: white;
    display: flex;
    height: calc(100% - 58px);
    border-top: 1px solid #F0F0F0;
    margin: 0 -24px;

    .campaignDateTree {
      .ant-tree {
        .ant-tree-node-content-wrapper {
          padding: 0;
          cursor: text;
        }

        .ant-tree-title {
          display: inline-block;
          width: 186px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }

      .treeNoopNone {
        .ant-tree-switcher-noop {
          display: none;
        }
      }
    }

    .foldWrap {
      display: flex;
      flex-direction: column;
      width: 48px;
      align-items: center;
      border-left: 1px solid #F0F0F0;
      background-color: #FAFAFA;

      .rotate {
        transform: rotate(90deg);
      }

      .foldIcon {
        width: 24px;
        height: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 12px;
        margin-bottom: 34px;
        cursor: pointer;
      }

      .foldContent {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .mainChart {
      flex: 1;

      .term {
        margin-left: 24px;
        display: flex;

        .title {
          line-height: 32px;
        }

        .ant-card>.ant-card-body {
          padding: 0;
          line-height: 32px;
          padding-left: 8px;

          .anticon-close {
            font-size: 12px;
            margin-left: 9px;
            cursor: pointer;
          }
        }
      }

      // >div{
      //   display: flex;
      //   justify-content: center;
      //   align-items: center;
      // }
    }

    .react-resizable {
      position: relative;
      background-clip: padding-box;
    }

    .react-resizable-handle {
      // background-color: red;
      position: absolute;
      width: 2px;
      height: 100%;
      top: 0;
      left: -2px;
      cursor: col-resize;
      z-index: 1;
      padding: 0;
    }

    .react-resizable-handle:hover {
      background-color: $primary_color;
    }

    .react-resizable-handle-w {
      top: 10px;
      transform: rotate(0deg)
    }

    .filter {
      width: 260px !important;
      background-color: #FAFAFA;
      border-left: solid 1px #F0F0F0;
      overflow-y: auto;
    }

    .chartListAndFill {
      min-width: 240px !important;
      display: flex;
      flex-direction: column;
      background-color: #FAFAFA;

      ::-webkit-scrollbar {
        width: 4px;
        height: 4px;
        /**/
      }

      border-left: solid 1px #F0F0F0;

      .titileStyle {
        font-size: 16px;
        font-weight: 600;
        padding: 8px 0 12px 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .hideIconWrap {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }

      .chartList {
        padding: 5px;
        display: flex;
        flex-wrap: wrap;

        .chartListIcon {
          margin: 11px 14px;
          cursor: pointer;
        }

        .chartListIcon:hover {
          background-color: #BFBFBF;
        }
      }

      .fillScroll {
        flex: 1;
        overflow-y: auto;

        .tableFillStyle {
          .dropStyle {
            margin-bottom: 24px;
          }
        }
      }
    }

    .fieldList {
      width: 240px !important;
      background-color: #FAFAFA;
      flex-direction: column;

      ::-webkit-scrollbar {
        width: 4px;
        height: 4px;
        /**/
      }

      border-left: solid 1px #F0F0F0;

      .date-select {
        padding: 3px 10px;
      }
    }
  }
}

.dateSelectModal {
  width: 600px;

  .modalContent {
    position: relative;
    height: 360px;

    .buttonTopGroup {
      .relativeButton {
        text-align: center;
        line-height: 24px;
        width: 124px;
        height: 24px;
        border-radius: 5px;
        border: 1px solid #BFBFBF;
        margin: 8px 0;
        cursor: pointer;
      }

      .active {
        border: 1px solid $primary_color;
      }
    }

    .buttonBottomGroup {
      display: flex;
      align-items: center;

      .ant-input-number {
        height: 24px;
        margin-left: 20px;

        .ant-input-number-input-wrap {
          height: 24px;

          .ant-input-number-input {
            height: 24px;
          }
        }
      }

      .relativeButton {
        text-align: center;
        line-height: 24px;
        width: 124px;
        height: 24px;
        border-radius: 5px;
        border: 1px solid #BFBFBF;
        margin: 8px 0;
        cursor: pointer;
      }

      .active {
        border: 1px solid $primary_color;
      }
    }

    footer {
      position: absolute;
      bottom: 0;
      right: 20px;
      margin-bottom: 0;
      margin-top: 20px;
      text-align: right;

      .cancel {
        margin-right: 20px;
      }
    }
  }
}