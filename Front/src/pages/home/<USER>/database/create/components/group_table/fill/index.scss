.tableFillStyle{
    padding: 8px;

    .ant-input-affix-wrapper, .ant-select-selector, .ant-picker, .ant-input-number {
        border-radius: 6px !important;
    }

    .dropStyle{
        border: 1px dashed #1890ff;
        border-radius: 6px;
        background: #fff;
        text-align: center;
        margin-top: 8px;
        margin-bottom: 10px;
        min-height: 36px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        padding: 0 5px;
        // font-size: 12px;
        .tagStyle {
           width: 100%;
           margin: 5px;
           padding: 0 5px;
           height: 24px;
           background-color:#F5F5F5;
           display: flex;
        //    justify-content: space-between;
           align-items: center;
           border-radius: 3px;
           &:hover{
            background-color: $active_color !important;
           }
           .nameStyle{
            font-size: 14px;
               flex: 1;
               text-align: left;
               overflow: hidden; //超出的文本隐藏
               text-overflow: ellipsis; //溢出用省略号显示
               white-space: nowrap; //溢出不换行
           }

           .toolbarStyle{
               display: none;
                .ant-dropdown-trigger{
                    height: 14px;
                    .downOutlinedStyle{
                        position: relative;
                        bottom: 1px;
                        font-size: 12px;
                        cursor: pointer;
                        color: rgba(0,0,0,.45);
                    }
                }
               .closeCircle{
                    font-size: 12px;
                    margin-left: 8px;
                    color: rgba(0,0,0,.45);
                    cursor: pointer;
               }
           }
        }

        .tagStyle:hover {
            .toolbarStyle{
               display: flex;
                .ant-dropdown-trigger{
                    height: 14px;
                    .downOutlinedStyle{
                        position: relative;
                        bottom: 1px;
                        font-size: 12px;
                        cursor: pointer;
                        color: rgba(0,0,0,.45);
                    }
                }
               .closeCircle{
                    font-size: 12px;
                    margin-left: 8px;
                    color: rgba(0,0,0,.45);
                    cursor: pointer;
               }
           }
        }
    }
    .sumStyle{
        margin-top: 20px;
        margin-bottom: 7px;
    }
    .sumBoundary{
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .sumBoundaryText{
            font-size: 14px !important;
            padding: 0 11px 0 9px;
        }
        .ant-input-number{
            flex: 1;
            // input{
            //     font-size: 12px;
            // }
        }
    }
    .statisticsStyle{
        display: flex;
        margin-bottom: 10px;
        align-items: center;
        .statisticsCheckbox{
            // >span:last-child{
            //     font-size: 12px;
            // }
        }
        .statisticsSelect{
            // .ant-select-selection-selected-value{
            //     font-size: 12px;
            // }
        }

    }
}

.statisticsSelectDropdown{
    // .ant-select-dropdown-menu-item{
    //     font-size: 12px;
    // }
}