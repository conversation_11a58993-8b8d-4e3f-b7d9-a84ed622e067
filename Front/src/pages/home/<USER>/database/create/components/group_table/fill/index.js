import { CloseOutlined, DownOutlined } from '@ant-design/icons';
import { Dropdown, Menu } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useMemo } from 'react';
import { useDrop } from 'react-dnd';
import { t } from 'utils/translation';
import { v4 as getUuid } from 'uuid';
import {
  dateStepTerm,
  dragType,
  indexType,
  tableSchemaStatisticsType,
  timeFilter,
  userContrastTypeList
} from '../../../../config';
import { DataContext } from '../../../context';
import filterConfig from '../../../filterConfig';
import './index.scss';

const { SubMenu } = Menu;

const getStyle = (canDrop, isOver) => {
  let color = '';
  if (canDrop && isOver) {
    color = 'var(--ant-primary-color)';
  } else if (canDrop) {
    color = '#fc8e44';
  } else {
    color = '#D7D7D7';
  }
  return color;
};

const dateStepTermObj = dateStepTerm.reduce((obj, n) => {
  obj[n.value] = n.label;
  return obj;
}, {});

const limit = {
  columnList: 10000,
  valueList: 200000
};

const Fill = () => {
  const {
    state: { configData, selectedChart, filterData, functionObj },
    dispatch
  } = useContext(DataContext);
  const { columnList, valueList, rowStatistics, columnStatistics, lineLimit, columnLimit } = configData[selectedChart];

  const isHaveSort = () => {
    if (valueList.some((item) => item.orderBy)) return false;
    return true;
  };
  const [{ canDropColumn, isOverColumn }, dropColumn] = useDrop({
    accept: 'box',
    drop: (item) => {
      const { dataType, name, key, dataSource } = item;
      const filterId = getUuid();
      const defaultSort = isHaveSort();
      columnList.push({
        filterId,
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        function: 'COUNT',
        timeConfig: { timeTerm: 'DAY' },
        orderBy: defaultSort && !columnList.some((n) => n.orderBy) ? 'ASC' : null
      });
      const filterInfo = {
        key,
        type: dataType,
        tableSchema: dataSource,
        filterType: 'ADVANCED',
        // dataType === 'TABLE_SCHEMA' &&
        // (dataSource.dataType === 'DATETIME' ||
        //   dataSource.dataType === 'DATE' ||
        //   dataSource.dataType === 'TIMESTAMP' ||
        //   dataSource.dataType === 'HIVE_DATE' ||
        //   dataSource.dataType === 'HIVE_TIMESTAMP')
        //   ? 'ADVANCED'
        //   : 'BASIC',
        connector: 'AND',
        comparisonList: [],
        campaignList: [],
        campaignNodeList: [],
        userLabelList: [],
        name,
        dragType: dragType.columnList,
        filterId,
        checkedBasicData: [],
        filters: _.isEmpty(dataSource)
          ? []
          : [
              {
                operator:
                  dataType === 'TABLE_SCHEMA' &&
                  (dataSource.dataType === 'DATETIME' ||
                    dataSource.dataType === 'DATE' ||
                    dataSource.dataType === 'TIMESTAMP' ||
                    dataSource.dataType === 'HIVE_DATE' ||
                    dataSource.dataType === 'HIVE_TIMESTAMP')
                    ? 'BETWEEN'
                    : filterConfig.typeOperator[dataSource.dataType][0],
                fieldValue: ''
              },
              { operator: null, fieldValue: '' }
            ]
      };
      filterData[selectedChart].push(filterInfo);
      dispatch({});
      return { name: 'Column', data: filterInfo };
    },
    canDrop: (item) => {
      const { dataType, key, dataSource } = item;
      return isAuth(null, 'columnList', dataType, key, dataSource.dataType, dataSource);
    },
    collect: (monitor) => ({
      isOverColumn: monitor.isOver(),
      canDropColumn: monitor.canDrop()
    })
  });

  const [{ canDropValue, isOverValue }, dropValue] = useDrop({
    accept: 'box',
    drop: (item) => {
      const { dataType, name, dataSource, key } = item;
      const filterId = getUuid();
      valueList.push({
        filterId,
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        metrics: dataSource,
        function: userContrastTypeList.includes(dataType) ? 'COUNT' : 'SUM',
        timeConfig: { timeTerm: 'DAY' }
      });
      const filterInfo = {
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        filterType: 'ADVANCED',
        connector: 'AND',
        comparisonList: [],
        campaignList: [],
        campaignNodeList: [],
        userLabelList: [],
        dragType: dragType.valueList,
        filterId,
        checkedBasicData: [],
        filters: _.isEmpty(dataSource)
          ? []
          : [
              {
                operator:
                  dataType === 'TABLE_SCHEMA' &&
                  (dataSource.dataType === 'DOUBLE' || dataSource.dataType === 'INT' || dataSource.dataType === 'LONG')
                    ? 'EQ'
                    : filterConfig.typeOperator[dataSource.dataType][0],
                fieldValue: ''
              },
              { operator: null, fieldValue: '' }
            ]
      };
      if (dataType !== 'METRICS') {
        filterData[selectedChart].push(filterInfo);
      }
      dispatch({});
      return { name: 'Value', data: filterInfo };
    },
    canDrop: (item) => {
      const { dataType, key, dataSource } = item;
      return isAuth(null, 'valueList', dataType, key, dataSource.dataType);
    },
    collect: (monitor) => ({
      isOverValue: monitor.isOver(),
      canDropValue: monitor.canDrop()
    })
  });

  const columnStyle = useMemo(() => {
    return { borderColor: getStyle(canDropColumn, isOverColumn) };
  }, [canDropColumn, isOverColumn]);

  const metricStyle = useMemo(() => {
    return { borderColor: getStyle(canDropValue, isOverValue) };
  }, [canDropValue, isOverValue]);

  const isAuth = (from, to, type, key, sonType) => {
    let flag = true;
    if (configData[selectedChart][to].length >= limit[to]) {
      return false;
    }
    const index = configData[selectedChart][to].findIndex((n) => n.type === type && n.key === key);
    if (index !== -1) {
      return false;
    }
    const columnIndex = columnList.findIndex((n) => n.key === key);
    const valueIndex = valueList.findIndex((n) => n.key === key);
    if (userContrastTypeList.includes(type)) {
      if (to === 'valueList') {
        if (!from) {
          flag = valueIndex === -1 && columnIndex === -1;
        } else if (from === 'rowList') {
          flag = valueIndex === -1;
        }
      } else {
        if (from !== 'valueList') {
          flag = valueIndex === -1;
        }
      }
    } else if (type === 'TABLE_SCHEMA') {
      if ((!from || from === 'valueList') && to !== 'valueList') {
        flag = indexType.includes(sonType);
      } else if (!from && to === 'valueList') {
        flag = ['DOUBLE', 'INT', 'LONG'].includes(sonType);
      }
    } else if (type === 'METRICS') {
      if (to === 'valueList') {
        flag = to === 'valueList' && valueIndex === -1 && ['DOUBLE', 'INT', 'LONG'].includes(sonType);
      } else {
        flag = indexType.includes(sonType) && columnIndex === -1;
      }
    }
    return flag;
  };

  useEffect(
    () => {
      if (!_.isEmpty(valueList)) {
        const filterValueList =
          _.filter(
            valueList,
            (item) =>
              ['DOUBLE', 'INT', 'LONG'].includes(item?.tableSchema?.dataType) ||
              userContrastTypeList.includes(item?.type)
          ) || valueList;
        const _configData = {
          ...configData,
          [selectedChart]: {
            ...configData[selectedChart],
            valueList: filterValueList
          }
        };
        dispatch({ configData: _configData });
      }
      if (!_.isEmpty(columnList)) {
        // const valueListCopy = valueList.map((item) => _.omit(item, 'orderBy'));
        // const rowListCopy = rowList.map((item) => _.omit(item, 'orderBy'));
        const filterColumnList =
          _.filter(
            columnList,
            (item) => indexType.includes(item?.tableSchema?.dataType) || userContrastTypeList.includes(item?.type)
          ) || columnList;
        const _configData = {
          ...configData,
          [selectedChart]: {
            ...configData[selectedChart],
            columnList: filterColumnList
          }
        };
        dispatch({ configData: _configData });
      }
    },
    [JSON.stringify(columnList)],
    [JSON.stringify(valueList)]
  );

  const fillComponent = useMemo(() => {
    const removeItem = (type, filterId) => {
      const index = configData[selectedChart][type].findIndex((n) => n.filterId === filterId);

      if (index > -1) {
        // configData[selectedChart][type].splice(index, 1);
        configData[selectedChart][type] = configData[selectedChart][type].filter(
          (item, filterIndex) => filterIndex !== index
        );

        const delFilterIndex = filterData[selectedChart].findIndex((n) => n.filterId === filterId);

        if (delFilterIndex > -1) {
          // filterData[selectedChart].splice(filterIndex, 1);
          filterData[selectedChart] = filterData[selectedChart].filter(
            (item, filterIndex) => filterIndex !== delFilterIndex
          );
        }

        dispatch({});
      }
    };

    // const moveTo = (from, to, { filterId, type, key }) => {
    //   console.log(from, to, { filterId, type, key });
    //   if (isAuth(from, to, type, key)) {
    //     const index = configData[selectedChart][from].findIndex(
    //       (n) => n.filterId === filterId
    //     );
    //     const info = { ...configData[selectedChart][from][index] };
    //     if (to === 'valueList') {
    //       info.function = info.function || 'COUNT';
    //     } else {
    //       info.timeConfig = info.timeConfig || { timeTerm: 'DAY' };
    //     }
    //     configData[selectedChart][to].push(info);
    //     configData[selectedChart][from].splice(index, 1);
    //     const filterInfo = filterData[selectedChart].find(
    //       (n) => n.filterId === filterId
    //     );
    //     if (filterInfo) filterInfo.dragType = dragType[to];
    //     dispatch({});
    //   }
    // };

    const doCompute = (from, filterId, value) => {
      const info = configData[selectedChart][from].find((n) => n.filterId === filterId);
      info.function = value;
      dispatch({});
    };

    const getStatisticsType = (from, filterId, type, tableSchema) => {
      if (userContrastTypeList.includes(type)) {
        return Object.entries(functionObj)
          .filter((w) => w[0] === 'COUNT')
          .map((n) => (
            <Menu.Item onClick={() => doCompute(from, filterId, n[0])} key={n[0]}>
              {n[1]}
            </Menu.Item>
          ));
      } else if (type === 'TABLE_SCHEMA') {
        return Object.entries(functionObj)
          .filter(
            (w) =>
              tableSchemaStatisticsType[tableSchema.dataType] &&
              tableSchemaStatisticsType[tableSchema.dataType].includes(w[0])
          )
          .map((n) => (
            <Menu.Item onClick={() => doCompute(from, filterId, n[0])} key={n[0]}>
              {n[1]}
            </Menu.Item>
          ));
      }
    };
    const editFormat = (target, filterId) => {
      const info = configData[selectedChart][target].find((n) => n.filterId === filterId);
      dispatch({
        formatVisible: true,
        formatValue: { target, filterId, numberConfig: info?.numberConfig }
      });
    };
    const doDateStepTerm = (from, filterId, value) => {
      const info = configData[selectedChart][from].find((n) => n.filterId === filterId);
      info.timeConfig = { timeTerm: value };
      dispatch({});
    };

    const getDateStepTerm = (from, filterId) => {
      return (
        <SubMenu title={t('analysisCenter-fKFVR8wcSRVv')}>
          {dateStepTerm.map((n) => (
            <Menu.Item onClick={() => doDateStepTerm(from, filterId, n.value)} key={n.value}>
              {n.label}
            </Menu.Item>
          ))}
        </SubMenu>
      );
    };

    const setSort = (type, filterId, value) => {
      const newConfigData = _.cloneDeep(configData);
      if (type === 'columnList') {
        newConfigData[selectedChart].valueList = newConfigData[selectedChart].valueList.map((item) => {
          return { ...item, orderBy: null };
        });
        newConfigData[selectedChart].columnList = newConfigData[selectedChart].columnList.map((item) => {
          if (item.filterId !== filterId) {
            return { ...item, orderBy: null };
          }
          return item;
        });
      } else if (type === 'valueList') {
        newConfigData[selectedChart].columnList = newConfigData[selectedChart].columnList.map((item) => {
          return { ...item, orderBy: null };
        });
        newConfigData[selectedChart].valueList = newConfigData[selectedChart].valueList.map((item) => {
          if (item.filterId !== filterId) {
            return { ...item, orderBy: null };
          }
          return item;
        });
      }
      newConfigData[selectedChart][type].find((n) => n.filterId === filterId).orderBy = value;
      dispatch({ configData: newConfigData });
    };

    const getSort = (from, filterId, orderBy) => {
      const sortOptions = [
        { order: 'ASC', label: t('analysisCenter-FLMIzYqPr1Lb'), key: 1 },
        { order: 'DESC', label: t('analysisCenter-67SdsI3x6VT8'), key: 2 },
        { order: null, label: t('analysisCenter-lyob81ZybeXK'), key: 3 }
      ];

      return (
        <SubMenu title={t('analysisCenter-AsQVQagib3e5')}>
          {sortOptions.map(({ order, label, key }) => (
            <Menu.Item onClick={() => setSort(from, filterId, order)} key={key}>
              <span
                style={{
                  color: orderBy === order && 'var(--ant-primary-color)'
                }}
              >
                {label}
              </span>
            </Menu.Item>
          ))}
        </SubMenu>
      );
    };
    const moreFunction = (from, { filterId, type, tableSchema, orderBy }) => {
      const menu = (
        <Menu style={{ width: 100 }}>
          <Menu.Item onClick={() => removeItem(from, filterId)}>{t('analysisCenter-mcsNO1rgLO3Z')}</Menu.Item>
          {from === 'columnList' && getSort(from, filterId, orderBy)}
          {from === 'valueList' && !_.isEmpty(columnList) && getSort(from, filterId, orderBy)}
          {from !== 'valueList' &&
            type === 'TABLE_SCHEMA' &&
            timeFilter.includes(tableSchema.dataType) &&
            getDateStepTerm(from, filterId)}
          {from === 'valueList' && getStatisticsType(from, filterId, type, tableSchema)}
          {from === 'valueList' && (
            <Menu.Item onClick={() => editFormat(from, filterId)}>{t('analysisCenter-Lvajwz8YypES')}</Menu.Item>
          )}
        </Menu>
      );
      return (
        <>
          <Dropdown trigger={['click']} overlay={menu}>
            <div onClick={(e) => e.preventDefault()}>
              <DownOutlined className="downOutlinedStyle" />
            </div>
          </Dropdown>
          <CloseOutlined className="closeCircle" onClick={() => removeItem(from, filterId)} />
        </>
      );
    };
    return (
      <>
        <div className="dropTitle">{t('analysisCenter-ZwNEDFdMycWl')}</div>
        <div className="dropStyle" ref={dropColumn} style={{ ...columnStyle }}>
          {columnList.length > 0 ? (
            columnList.map((n) => {
              const name = `${n.name}${
                n.type === 'TABLE_SCHEMA' && timeFilter.includes(n.tableSchema.dataType) && n.timeConfig?.timeTerm
                  ? `(${t('analysisCenter-tPPTzw5K0O9o')}${dateStepTermObj[n.timeConfig.timeTerm]})`
                  : ''
              }${n.orderBy ? `(${n.orderBy === 'ASC' ? t('analysisCenter-FLMIzYqPr1Lb') : t('analysisCenter-67SdsI3x6VT8')})` : `(${t('analysisCenter-PhYRp5ppAGz8')})`}`;
              return (
                <div key={n.key} className="tagStyle">
                  <div title={name} className="nameStyle">
                    {name}
                  </div>
                  <div className="toolbarStyle">{moreFunction('columnList', n)}</div>
                </div>
              );
            })
          ) : (
            <div>{t('analysisCenter-6uInVyeQ8yxN')}</div>
          )}
        </div>
        <div className="dropTitle">{t('analysisCenter-ZGrfMXPi7QiT')}</div>
        <div className="dropStyle" ref={dropValue} style={{ ...metricStyle }}>
          {valueList.length > 0 ? (
            valueList.map((n) => {
              const name = `${n.name}${n.type !== 'METRICS' && n.function ? `(${functionObj[n.function]})` : ''}${n.orderBy ? `(${n.orderBy === 'ASC' ? t('analysisCenter-FLMIzYqPr1Lb') : t('analysisCenter-67SdsI3x6VT8')})` : `(${t('analysisCenter-PhYRp5ppAGz8')})`}`;
              return (
                <div key={n.key} className="tagStyle">
                  <div title={name} className="nameStyle">
                    {name}
                  </div>
                  <div className="toolbarStyle">{moreFunction('valueList', n)}</div>
                </div>
              );
            })
          ) : (
            <div>{t('analysisCenter-6uInVyeQ8yxN')}</div>
          )}
        </div>
      </>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    JSON.stringify(columnList),
    JSON.stringify(valueList),
    rowStatistics,
    columnStatistics,
    lineLimit,
    columnLimit,
    configData,
    selectedChart,
    filterData,
    functionObj,
    dispatch,
    metricStyle,
    columnStyle,
    dropColumn,
    dropValue
  ]);

  return <div className="tableFillStyle">{fillComponent}</div>;
};

export default Fill;
