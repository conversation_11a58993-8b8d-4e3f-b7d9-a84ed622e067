import { CloseOutlined, DownOutlined } from '@ant-design/icons';
import { Dropdown, Menu } from 'antd';
import _ from 'lodash';
import React, { useContext, useMemo } from 'react';
import { useDrop } from 'react-dnd';
import { t } from 'utils/translation';
import { v4 as getUuid } from 'uuid';
import { dragType, tableSchemaStatisticsType, userContrastTypeList } from '../../../../config';
import { DataContext } from '../../../context';
import filterConfig from '../../../filterConfig';
import './index.scss';

const getStyle = (canDrop, isOver) => {
  let color = '';
  if (canDrop && isOver) {
    color = 'var(--ant-primary-color)';
  } else if (canDrop) {
    color = '#fc8e44';
  } else {
    color = '#D7D7D7';
  }
  return color;
};

const limit = {
  valueList: 1
};

const Fill = () => {
  const {
    state: { configData, selectedChart, filterData, functionObj },
    dispatch
  } = useContext(DataContext);
  const { valueList } = configData[selectedChart];

  const [{ canDropValue, isOverValue }, dropValue] = useDrop({
    accept: 'box',
    drop: (item) => {
      const { dataType, name, dataSource, key } = item;
      const filterId = getUuid();
      valueList.push({
        filterId,
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        metrics: dataSource,
        function: dataType !== 'METRICS' ? 'COUNT' : undefined,
        numberConfig: {
          format: 'NUMBER',
          decimalSeparator: 0,
          thousandsSeparator: true
        }
      });
      const filterInfo = {
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        filterType: 'ADVANCED',
        connector: 'AND',
        comparisonList: [],
        campaignList: [],
        campaignNodeList: [],
        userLabelList: [],
        dragType: dragType.valueList,
        filterId,
        checkedBasicData: [],
        filters: _.isEmpty(dataSource)
          ? []
          : [
              {
                operator:
                  dataType === 'TABLE_SCHEMA' &&
                  (dataSource.dataType === 'DATETIME' ||
                    dataSource.dataType === 'DATE' ||
                    dataSource.dataType === 'TIMESTAMP' ||
                    dataSource.dataType === 'HIVE_DATE' ||
                    dataSource.dataType === 'HIVE_TIMESTAMP')
                    ? 'BETWEEN'
                    : filterConfig.typeOperator[dataSource.dataType][0],
                fieldValue: ''
              },
              { operator: null, fieldValue: '' }
            ]
      };
      if (dataType !== 'METRICS') {
        filterData[selectedChart].push(filterInfo);
      }
      dispatch({});
      return { name: 'Value', data: filterInfo };
    },
    canDrop: (item) => {
      const { dataType, key } = item;
      return isAuth(null, 'valueList', dataType, key);
    },
    collect: (monitor) => {
      return {
        isOverValue: monitor.isOver(),
        canDropValue: monitor.canDrop()
      };
    }
  });

  const metricStyle = useMemo(() => {
    return { borderColor: getStyle(canDropValue, isOverValue) };
  }, [canDropValue, isOverValue]);

  const isAuth = (from, to, type, key) => {
    const flag = true;
    if (configData[selectedChart][to].length >= limit[to]) {
      return false;
    }
    const index = configData[selectedChart][to].findIndex((n) => n.type === type && n.key === key);
    if (index !== -1) {
      return false;
    }
    return flag;
  };

  const fillComponent = useMemo(() => {
    const removeItem = (type, filterId) => {
      const index = configData[selectedChart][type].findIndex((n) => n.filterId === filterId);
      if (index > -1) {
        // configData[selectedChart][type].splice(index, 1);
        configData[selectedChart][type] = configData[selectedChart][type].filter(
          (item, filterIndex) => filterIndex !== index
        );

        const delFilterIndex = filterData[selectedChart].findIndex((n) => n.filterId === filterId);

        if (delFilterIndex > -1) {
          // filterData[selectedChart].splice(filterIndex, 1);
          filterData[selectedChart] = filterData[selectedChart].filter(
            (item, filterIndex) => filterIndex !== delFilterIndex
          );
        }

        dispatch({});
      }
    };

    const doCompute = (from, filterId, value) => {
      const info = configData[selectedChart][from].find((n) => n.filterId === filterId);
      info.function = value;
      dispatch({});
    };

    const getStatisticsType = (from, filterId, type, tableSchema) => {
      if (userContrastTypeList.includes(type)) {
        return Object.entries(functionObj)
          .filter((w) => w[0] === 'COUNT')
          .map((n) => (
            <Menu.Item onClick={() => doCompute(from, filterId, n[0])} key={n[0]}>
              {n[1]}
            </Menu.Item>
          ));
      } else if (type === 'TABLE_SCHEMA') {
        return Object.entries(functionObj)
          .filter(
            (w) =>
              tableSchemaStatisticsType[tableSchema.dataType] &&
              tableSchemaStatisticsType[tableSchema.dataType].includes(w[0])
          )
          .map((n) => (
            <Menu.Item onClick={() => doCompute(from, filterId, n[0])} key={n[0]}>
              {n[1]}
            </Menu.Item>
          ));
      }
    };

    const editFormat = (target, filterId) => {
      const info = configData[selectedChart][target].find((n) => n.filterId === filterId);
      dispatch({
        formatVisible: true,
        formatValue: { target, filterId, numberConfig: info?.numberConfig }
      });
    };

    const moreFunction = (from, filterId, type, tableSchema) => {
      const menu = (
        <Menu style={{ width: 100 }}>
          <Menu.Item onClick={() => removeItem(from, filterId)}>{t('analysisCenter-mcsNO1rgLO3Z')}</Menu.Item>
          {from === 'valueList' && getStatisticsType(from, filterId, type, tableSchema)}
          {from === 'valueList' && (
            <Menu.Item onClick={() => editFormat(from, filterId)}>{t('analysisCenter-Lvajwz8YypES')}</Menu.Item>
          )}
        </Menu>
      );
      return (
        <>
          <Dropdown trigger={['click']} overlay={menu}>
            <a onClick={(e) => e.preventDefault()}>
              <DownOutlined className="downOutlinedStyle" />
            </a>
          </Dropdown>
          <CloseOutlined className="closeCircle" onClick={() => removeItem(from, filterId)} />
        </>
      );
    };
    return (
      <>
        <div className="dropTitle">{t('analysisCenter-HVHcCbQG1m3H')}</div>
        <div className="dropStyle" ref={dropValue} style={{ ...metricStyle }}>
          {valueList.length > 0 ? (
            valueList.map((n) => {
              const name = `${n.name}${n.type !== 'METRICS' && n.function ? `(${functionObj[n.function]})` : ''}`;
              let title = name;
              if (n.numberConfig) {
                title += `(${t('analysisCenter-bIgCeXBrhLf2')}`;
                if (n.numberConfig.format === 'NUMBER') {
                  title = `${title}${t('analysisCenter-m0CHBvhfxdbR')} ${
                    n.numberConfig.decimalSeparator
                  }，${t('analysisCenter-31Y11DTz48ws')} ${n.numberConfig.thousandsSeparator ? t('analysisCenter-RYA9pCfnAGut') : t('analysisCenter-8B7i6jFmQkim')}`;
                } else if (n.numberConfig.format === 'PERCENT') {
                  title = `${title}${t('analysisCenter-xRywGtK8LO0u')} ${n.numberConfig.decimalSeparator}`;
                }
                title += ')';
              }
              return (
                <div key={n.key} className="tagStyle">
                  <div title={title} className="nameStyle">
                    {name}
                  </div>
                  <div className="toolbarStyle">{moreFunction('valueList', n.filterId, n.type, n.tableSchema)}</div>
                </div>
              );
            })
          ) : (
            <div>{t('analysisCenter-QZmzAoHPjaAY')}</div>
          )}
        </div>
      </>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(valueList), configData, dispatch, dropValue, filterData, functionObj, metricStyle, selectedChart]);

  return <div className="tableFillStyle">{fillComponent}</div>;
};

export default Fill;
