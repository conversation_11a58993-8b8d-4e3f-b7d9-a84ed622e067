import { Pie } from '@ant-design/charts';
import { Empty, Select, Spin } from 'antd';
import _ from 'lodash';
import React, { useMemo, useState } from 'react';
import { numConvert } from 'utils/universal';
import { formatNumber } from '../../../../config';
// import _ from 'lodash';
import { t } from 'utils/translation';

const filterCountMap = [5, 10, 20, 30, 50, 100];

export default (props) => {
  const {
    dataSource: { rowList, axis, total: allltotal },
    loading,
    dispatch
  } = props;
  const [filterCount, setFilterCount] = useState(props?.filterCount || 10);
  const sliceRowList = (rowList || []).slice(0, filterCount);
  const cliceRowListCount = _.sumBy(sliceRowList, axis?.yfield?.dataIndex);
  filterCount > (rowList || []).length
    ? sliceRowList
    : sliceRowList.push({
        COL0: '其它...',
        METRIC0: allltotal - cliceRowListCount
      });

  const result = useMemo(() => {
    // let total = _.reduce(
    //   rowList,
    //   (sum, n) => {
    //     let typeN = typeof n[axis?.yfield?.dataIndex];
    //     if (typeN === 'number') {
    //       return sum + n[axis?.yfield?.dataIndex];
    //     }
    //     return 0;
    //   },
    //   0
    // );
    const config = {
      forceFit: true,
      title: {
        visible: false,
        text: t('analysisCenter-8okfBn3gtUNx')
      },
      description: {
        visible: true,
        text: `${axis?.yfield?.title}${axis?.numberConfig?.format === 'PERCENT' ? t('analysisCenter-Nh6nq7sFmZpF') : ''}`
      },
      radius: 0.7,
      innerRadius: 0.7,
      padding: 'auto',
      data: sliceRowList.map((item) => {
        return {
          ...item,
          [axis?.xfield?.dataIndex]: `${item[axis?.xfield?.dataIndex]}`
        };
      }),
      statistic: {
        title: {
          formatter: () => {
            return `${t('analysisCenter-AEtJu6rT5pI6')}${axis?.numberConfig?.format === 'PERCENT' ? t('analysisCenter-Nh6nq7sFmZpF') : ''}`;
          },
          offsetY: -10,
          style: {
            fontSize: '14px'
          }
        },
        content: {
          formatter: () => {
            // return formatNumber(total, axis?.numberConfig);
            return parseInt(formatNumber(allltotal, axis?.numberConfig)) < 1000
              ? formatNumber(allltotal, axis?.numberConfig)
              : `${numConvert(formatNumber(allltotal, axis?.numberConfig))}+`;
          },
          style: {
            fontSize: '24px'
          }
        }
      },
      meta: {
        value: {
          formatter: function formatter(v) {
            return ''.concat(v, ' \xA5');
          }
        }
      },
      tooltip: {
        customContent: (title, data) => {
          let sum = 0;
          rowList.forEach((val) => (sum += val[axis?.yfield?.dataIndex]));
          return `<div style="padding: 12px 4px;list-style:none;"><span style="background-color:${
            data[0]?.color
          };" class="g2-tooltip-marker"></span>${
            data[0]?.data[axis?.xfield?.dataIndex]
          }<span style='margin-left: 24px'>${formatNumber(
            data[0]?.data[axis?.yfield?.dataIndex],
            axis?.numberConfig
          )}<span>（${parseFloat(
            ((data[0]?.data[axis?.yfield?.dataIndex] / allltotal) * 100).toFixed(2)
          )}%）</span></div>`;
        },
        enterable: true
      },
      angleField: axis?.yfield?.dataIndex,
      colorField: axis?.xfield?.dataIndex,
      style: { height: '100%', overflow: 'hidden' },
      legend: {
        flipPage: true,
        radio: false,
        itemHeight: 30,
        showCrosshairs: false,
        position: 'bottom'
      },
      label:
        filterCount > 20
          ? false
          : {
              layout: '',
              formatter: (data) => {
                return filterCount > 10
                  ? `${parseFloat(data.percent * 100).toFixed(2)}%`
                  : `${data[axis?.xfield?.dataIndex]}   ${parseFloat(data.percent * 100).toFixed(2)}%`;
              },
              offset: 10
            }
    };

    if (_.isArray(rowList) && rowList.length === 0) {
      return (
        <div className="mainChartSpin">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      );
    } else if (rowList === null || rowList === undefined) {
      return (
        <div className="mainChartSpin">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={<span>{t('analysisCenter-2aKsgUcrUHpl')}</span>} />
        </div>
      );
    } else if (rowList && rowList?.length > 0 && !_.isNumber(rowList[0][axis?.yfield?.dataIndex])) {
      return (
        <div className="mainChartSpin">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={<span>{t('analysisCenter-2aKsgUcrUHpl')}</span>} />
        </div>
      );
    }
    return (
      <div className="w-[100%] h-[100%]">
        <div className="absolute right-10 top-0 z-[1000]">
          <Select
            value={filterCount}
            optionLabelProp="label"
            style={{ width: 100 }}
            onChange={(value) => {
              setFilterCount(value);
              dispatch &&
                dispatch({
                  filterCount: value
                });
            }}
          >
            {filterCountMap.map((item, index) => (
              <Select.Option
                key={index}
                value={item}
                label={`${t('analysisCenter-LFLk4qp0km15')}${item}${t('analysisCenter-SeFy8ti7KJD3')}`}
              >
                {item}
              </Select.Option>
            ))}
          </Select>
        </div>
        <Pie {...config} />
      </div>
    );
  }, [rowList, axis, filterCount]);
  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        overflow: 'auto',
        padding: 5,
        position: 'relative'
      }}
    >
      {loading ? (
        <div className="mainChartSpin">
          <Spin spinning />{' '}
        </div>
      ) : (
        result
      )}
    </div>
  );
};
