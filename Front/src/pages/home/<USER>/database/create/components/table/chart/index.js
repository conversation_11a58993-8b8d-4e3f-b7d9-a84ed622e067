import React, { useContext } from 'react';
// import { Spin } from 'antd';
import { DataContext } from '../../../context';
import TableChart from './chart';
import './index.scss';

const Chart = () => {
  const {
    state: { loading, responseData, selectedChart }
  } = useContext(DataContext);

  return (
    <div style={{ padding: 10, width: '100%', height: '100%' }}>
      {<TableChart dataSource={responseData[selectedChart]} loading={loading} />}
    </div>
  );
};

export default Chart;
