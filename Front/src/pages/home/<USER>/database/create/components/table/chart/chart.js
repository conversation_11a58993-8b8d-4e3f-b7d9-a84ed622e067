import { Empty, Spin, Table } from 'antd';
import _ from 'lodash';
import React, { useMemo } from 'react';
import thousands from 'utils/thousands';
import { t } from 'utils/translation';
import './index.scss';

export default ({ dataSource: { rowList, columnList }, loading }) => {
  const processingDataFormat = (v, numberConfig) => {
    if (numberConfig && typeof v === 'number') {
      if (numberConfig?.format === 'PERCENT') {
        return `${(v * 100).toFixed(numberConfig?.decimalSeparator || 0)}%`;
      } else {
        if (numberConfig?.thousandsSeparator) {
          return thousands(v.toFixed(numberConfig?.decimalSeparator || 0));
        } else {
          return v.toFixed(numberConfig?.decimalSeparator || 0);
        }
      }
    } else {
      return v;
    }
  };
  // 递归设置每列中的数据格式
  const processedData = (formatDataColum, data) => {
    formatDataColum.forEach((item) => {
      if (item.children) {
        processedData(item.children, data);
      } else {
        if (item?.numberConfig) {
          data.forEach((w) => {
            w[item.dataIndex] = processingDataFormat(w[item.dataIndex], item?.numberConfig);
          });
        }
      }
    });
    return data;
  };
  const result = useMemo(() => {
    if (_.isArray(rowList) && rowList.length === 0) {
      return (
        <div className="mainChartSpin">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      );
    } else if (rowList === null || rowList === undefined) {
      return (
        <div className="mainChartSpin">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={<span>{t('analysisCenter-2aKsgUcrUHpl')}</span>} />
        </div>
      );
    }
    // 通过设置值的显示类型展示千位分割 or 百分比
    const newDataSource = rowList.filter((w) => w.key !== 'row_stat') || [];
    const formatDataColum = _.cloneDeep(columnList);
    const decimalSeparatorData = processedData(formatDataColum, newDataSource);
    // 将数据中返回的boolean类型转换为字符串显示
    const transformedList = _.map(decimalSeparatorData, (obj) => {
      for (const key in obj) {
        if (_.isBoolean(obj[key])) {
          obj[key] = obj[key] ? 'true' : 'false';
        }
      }
      return obj;
    });

    const data = _.cloneDeep(columnList);
    const keys = [];
    data &&
      data.forEach((item) => {
        const loop = (data1) => {
          const child = data1.children;
          data1.align = 'center';
          if (child) {
            for (let i = 0; i < child.length; i++) {
              loop(child[i]);
            }
          } else {
            data1.width = Math.max(`${data1.title}`.length * 15 + 20, 100);
            data1.ellipsis = { showTitle: true };
            keys.push(data1.dataIndex);
            // if (i === 0) {
            //   data1.defaultSortOrder = 'ascend';
            // }
            data1.sorter = (a, b) => {
              if (data1.dataIndex.indexOf('_METRIC') > -1) {
                const aValue = !isNaN(a[data1.dataIndex]) ? a[data1.dataIndex] : 0;
                const bValue = !isNaN(b[data1.dataIndex]) ? b[data1.dataIndex] : 0;
                return aValue - bValue;
              }
              const numberA = a[data1.dataIndex];
              const numberB = b[data1.dataIndex];
              const res = `${numberA}`.localeCompare(numberB);

              return _.isNumber(numberA) && _.isNumber(numberB) ? numberA - numberB : res;
            };
          }
        };
        loop(item);
      });
    return (
      <Table
        columns={data || []}
        dataSource={transformedList || []}
        pagination={false}
        bordered
        size="middle"
        summary={() => {
          const info = rowList.find((w) => w.key === 'row_stat');
          if (info) {
            return (
              <Table.Summary.Row>
                {keys.map((n) => (
                  <Table.Summary.Cell key={n}>{info[n]}</Table.Summary.Cell>
                ))}
              </Table.Summary.Row>
            );
          }
          return null;
        }}
        // scroll={{ x: ref.current?.parentNode.clientWidth }}
        // scroll={{ x: 200 }}
        scroll
      />
    );
  }, [rowList, columnList]);

  return (
    <div className="chatTableWrapper" style={{ width: '100%', height: '100%', overflow: 'auto' }}>
      {loading ? (
        <div className="mainChartSpin">
          <Spin spinning />{' '}
        </div>
      ) : (
        result
      )}
    </div>
  );
};
