import { Scatter } from '@ant-design/charts';
import { Empty, Spin } from 'antd';
import _ from 'lodash';
import React, { useMemo } from 'react';
import { t } from 'utils/translation';

export default ({ dataSource: { rowList, axis }, loading }) => {
  const result = useMemo(() => {
    // const data = _.map(rowList, item => ({ type: item.title, value: item.value }));
    if (_.isArray(rowList) && rowList.length === 0) {
      return (
        <div className="mainChartSpin">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      );
    } else if (rowList === null || rowList === undefined) {
      return (
        <div className="mainChartSpin">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={<span>{t('analysisCenter-Ua0b1OF83YRu')}</span>} />
        </div>
      );
    }
    // else if (!rowList) {
    //   return null;
    // }

    const config = {
      data: rowList,
      xField: axis?.xfield?.dataIndex,
      yField: axis?.yfield?.dataIndex,
      sizeField: axis?.stackField?.dataIndex,
      pointSize: [2, 16],
      colorField: axis?.legendField?.dataIndex,
      // xAxis: {
      //   visible: true,
      //   // max: 5,
      //   // min: -25
      //   label: {
      //     visible: true,
      //     autoRotate: true,
      //     autoHide: true
      //   },
      //   title: {
      //     visible: true,
      //     text: axis?.xfield?.title
      //   }
      // },
      // yAxis: {
      //   visible: true,
      //   label: {
      //     visible: true
      //   },
      //   title: {
      //     visible: true,
      //     text: axis?.yfield?.title
      //   }
      // },
      tooltip: {
        fields: [axis?.xfield?.dataIndex, axis?.yfield?.dataIndex, axis?.stackField?.dataIndex],
        customContent: (title, data) => {
          const listItemDom = data
            .map((v) => {
              // 找到axis里对应的翻译
              const targetKey = _.findKey(axis, (o) => {
                return o.dataIndex === v.name;
              });
              return `<li class="g2-tooltip-list-item">${axis[targetKey].title}: ${v.value}</li>`;
            })
            .join('');

          return `<div>
            <ul class="g2-tooltip-list">
              ${listItemDom}
            </ul>
            </div>`;
        },
        enterable: true
      },
      legend: {
        flipPage: true,
        itemHeight: 30
      }
      // tooltip: {
      //   fields: [axis?.xfield?.dataIndex, axis?.yfield?.dataIndex, axis?.stackField?.dataIndex],
      //   formatter: (data) => {
      //     const name = axis?.stackField?.dataIndex;
      //     const value = axis?.yfield?.dataIndex;
      //     const title = axis?.yfield?.title;
      //     console.log(data, axis, 999);
      //     return { name: name ? data[name] : title, value: data[value] };
      //   }
      // }
    };
    // const config = {
    //   data: rowList,
    //   xField: 'change in female rate',
    //   yField: 'change in male rate',
    //   sizeField: 'pop',
    //   pointSize: [4, 30],
    //   colorField: 'continent',
    //   xAxis: {
    //     visible: true,
    //     // max: 5,
    //     // min: -25
    //     label: {
    //       visible: true,
    //       autoRotate: true,
    //       autoHide: true
    //     },
    //     title: {
    //       visible: true,
    //       text: 'change in female rate'
    //     }
    //   },
    //   yAxis: {
    //     visible: true,
    //     label: {
    //       visible: true
    //     },
    //     title: {
    //       visible: true,
    //       text: 'change in male rate'
    //     }
    //   },
    //   tooltip: {
    //     visible: true
    //     // formatter: (xField, yField) => {
    //     //   console.log('x', xField);
    //     //   console.log('y', yField);
    //     //   return { name: 1, value: 2 };
    //     // }
    //   }
    // };
    return <Scatter {...config} />;
  }, [rowList, axis]);
  return (
    <div style={{ width: '100%', height: '100%', overflow: 'auto', padding: 5 }}>
      {loading ? (
        <div className="mainChartSpin">
          <Spin spinning />{' '}
        </div>
      ) : (
        result
      )}
    </div>
  );
};
