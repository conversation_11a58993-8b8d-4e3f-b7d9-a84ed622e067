@import "~react-grid-layout/css/styles.css";
@import "~react-resizable/css/styles.css";

.mainChartSpin{
  width: 100%;
  height: calc(100% - 50px);
  display: flex;
  justify-content: center;
  align-items: center;
}

.chatTableWrapper{
  .ant-table-content{
    font-size: 12px;
  }
  .ant-table-cell {
    text-align: center;
  }
  ::-webkit-scrollbar{
    width:4px;
    height:4px;
    /**/
  }
}

.chatTableWrapper::-webkit-scrollbar{
  width:4px;
  height:4px;
  /**/
}