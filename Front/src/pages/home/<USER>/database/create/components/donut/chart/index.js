import React, { useContext } from 'react';
import { DataContext } from '../../../context';
import DonutChart from './chart';
import './index.scss';

const Chart = () => {
  const {
    state: { responseData, selectedChart, loading, filterCount },
    dispatch
  } = useContext(DataContext);

  return (
    <div style={{ padding: 10, width: '100%', height: '100%' }}>
      <DonutChart
        dataSource={responseData[selectedChart]}
        loading={loading}
        filterCount={filterCount}
        dispatch={dispatch}
      />
    </div>
  );
};

export default Chart;
