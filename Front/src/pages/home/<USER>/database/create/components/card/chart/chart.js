import { Empty, Spin, Statistic } from 'antd';
import _ from 'lodash';
import React, { useMemo } from 'react';
import { t } from 'utils/translation';

export default ({ dataSource: { rowList, axis }, loading }) => {
  const result = useMemo(() => {
    if (_.isArray(rowList) && rowList.length === 0) {
      return (
        <div className="mainChartSpin">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      );
    } else if (rowList === null) {
      return (
        <div className="mainChartSpin">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={<span>{t('analysisCenter-2aKsgUcrUHpl')}</span>} />
        </div>
      );
    }
    let title = rowList && rowList[0]?.title;
    let value = rowList && rowList[0]?.value;
    const groupSeparator = axis?.numberConfig?.thousandsSeparator ? ',' : '';
    const precision = axis?.numberConfig?.decimalSeparator || 0;
    if (axis?.numberConfig?.format === 'PERCENT') {
      value *= 100;
      title += ` ${t('analysisCenter-Nh6nq7sFmZpF')}`;
    }

    return (
      <div className="mainChartSpin">
        <Statistic groupSeparator={groupSeparator} precision={precision} title={title} value={value} />
      </div>
    );
  }, [rowList, axis]);
  return (
    <div style={{ width: '100%', height: '100%', overflow: 'auto' }}>
      {loading ? (
        <div className="mainChartSpin">
          <Spin spinning />{' '}
        </div>
      ) : (
        result
      )}
    </div>
  );
};
