import { CloseOutlined, DownOutlined } from '@ant-design/icons';
import { Dropdown, Menu } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useMemo } from 'react';
import { useDrop } from 'react-dnd';
import { t } from 'utils/translation';
import { v4 as getUuid } from 'uuid';
import { dateStepTerm, dragType, tableSchemaStatisticsType, timeType, userContrastTypeList } from '../../../../config';
import { DataContext } from '../../../context';
import filterConfig from '../../../filterConfig';
import './index.scss';

const { SubMenu } = Menu;

const getStyle = (canDrop, isOver) => {
  let color = '';
  if (canDrop && isOver) {
    color = 'var(--ant-primary-color)';
  } else if (canDrop) {
    color = '#fc8e44';
  } else {
    color = '#D7D7D7';
  }
  return color;
};

const limit = {
  axisList: 1,
  legendList: 1,
  valueList: 5
};

const dateStepTermObj = dateStepTerm.reduce((obj, n) => {
  obj[n.value] = n.label;
  return obj;
}, {});

const Fill = () => {
  const {
    state: { configData, selectedChart, filterData, functionObj },
    dispatch
  } = useContext(DataContext);
  const { axisList, legendList, valueList } = configData[selectedChart];

  // 如果有图例变动，就把x、y的排序都删除掉
  useEffect(() => {
    if (!_.isEmpty(legendList)) {
      const valueListCopy = valueList.map((item) => _.omit(item, 'orderBy'));
      // const axisListCopy = axisList.map((item) => _.omit(item, 'orderBy'));
      const _configData = {
        ...configData,
        [selectedChart]: {
          ...configData[selectedChart],
          valueList: valueListCopy,
          // axisList: axisListCopy,
          legendList: configData[selectedChart].legendList.map((item) => {
            return _.omit(item, 'orderBy');
          })
        }
      };
      dispatch({ configData: _configData });
    }
  }, [JSON.stringify(legendList)]);

  /** 如果图例是有值或者Y轴有排序 */
  const isHaveSort = () => {
    if (valueList.some((item) => item.orderBy)) return false;
    return true;
  };

  const [{ canDropRow, isOverRow }, dropRow] = useDrop({
    accept: 'box',
    drop: (item) => {
      const { dataType, name, key, dataSource } = item;
      const filterId = getUuid();
      const defaultSort = isHaveSort();
      axisList.push({
        filterId,
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        function: 'COUNT',
        timeConfig: { timeTerm: 'DAY' },
        orderBy: defaultSort ? 'ASC' : null
      });
      const filterInfo = {
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        filterType: 'ADVANCED',
        // dataType === 'TABLE_SCHEMA' &&
        // (dataSource.dataType === 'DATETIME' ||
        //   dataSource.dataType === 'DATE' ||
        //   dataSource.dataType === 'TIMESTAMP' ||
        //   dataSource.dataType === 'HIVE_DATE' ||
        //   dataSource.dataType === 'HIVE_TIMESTAMP')
        //   ? 'ADVANCED'
        //   : 'BASIC',
        connector: 'AND',
        comparisonList: [],
        campaignList: [],
        campaignNodeList: [],
        userLabelList: [],
        dragType: dragType.axisList,
        filterId,
        checkedBasicData: [],
        filters: _.isEmpty(dataSource)
          ? []
          : [
              {
                operator:
                  dataType === 'TABLE_SCHEMA' &&
                  (dataSource.dataType === 'DATETIME' ||
                    dataSource.dataType === 'DATE' ||
                    dataSource.dataType === 'TIMESTAMP' ||
                    dataSource.dataType === 'HIVE_DATE' ||
                    dataSource.dataType === 'HIVE_TIMESTAMP')
                    ? 'BETWEEN'
                    : filterConfig.typeOperator[dataSource.dataType][0],
                fieldValue: ''
              },
              { operator: null, fieldValue: '' }
            ]
      };
      filterData[selectedChart].push(filterInfo);
      if (defaultSort) {
        const newConfigData = _.cloneDeep(configData);
        newConfigData[selectedChart].valueList = newConfigData[selectedChart].valueList.map((item) => {
          return { ...item, orderBy: null };
        });
        dispatch({ configData: newConfigData });
      } else {
        dispatch({});
      }
      return { name: 'AXIS', data: filterInfo };
    },
    canDrop: (item) => {
      const { dataType, key } = item;
      return isAuth(null, 'axisList', dataType, key);
    },
    collect: (monitor) => {
      return {
        isOverRow: monitor.isOver(),
        canDropRow: monitor.canDrop()
      };
    }
  });

  const [{ canDropLegend, isOverLegend }, dropLegend] = useDrop({
    accept: 'box',
    drop: (item) => {
      const { dataType, name, key, dataSource } = item;
      const filterId = getUuid();
      legendList.push({
        filterId,
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        function: 'COUNT',
        timeConfig: { timeTerm: 'DAY' }
      });
      const filterInfo = {
        key,
        type: dataType,
        tableSchema: dataSource,
        filterType: 'ADVANCED',
        // dataType === 'TABLE_SCHEMA' &&
        // (dataSource.dataType === 'DATETIME' ||
        //   dataSource.dataType === 'DATE' ||
        //   dataSource.dataType === 'TIMESTAMP' ||
        //   dataSource.dataType === 'HIVE_DATE' ||
        //   dataSource.dataType === 'HIVE_TIMESTAMP')
        //   ? 'ADVANCED'
        //   : 'BASIC',
        connector: 'AND',
        comparisonList: [],
        campaignList: [],
        campaignNodeList: [],
        userLabelList: [],
        name,
        dragType: dragType.legendList,
        filterId,
        checkedBasicData: [],
        filters: _.isEmpty(dataSource)
          ? []
          : [
              {
                operator:
                  dataType === 'TABLE_SCHEMA' &&
                  (dataSource.dataType === 'DATETIME' ||
                    dataSource.dataType === 'DATE' ||
                    dataSource.dataType === 'TIMESTAMP' ||
                    dataSource.dataType === 'HIVE_DATE' ||
                    dataSource.dataType === 'HIVE_TIMESTAMP')
                    ? 'BETWEEN'
                    : filterConfig.typeOperator[dataSource.dataType][0],
                fieldValue: ''
              },
              { operator: null, fieldValue: '' }
            ]
      };
      filterData[selectedChart].push(filterInfo);
      dispatch({});
      return { name: 'legend', data: filterInfo };
    },
    canDrop: (item) => {
      const { dataType, key } = item;
      return isAuth(null, 'legendList', dataType, key);
    },
    collect: (monitor) => ({
      isOverLegend: monitor.isOver(),
      canDropLegend: monitor.canDrop()
    })
  });

  const [{ canDropValue, isOverValue }, dropValue] = useDrop({
    accept: 'box',
    drop: (item) => {
      const { dataType, name, dataSource, key } = item;
      const filterId = getUuid();
      valueList.push({
        filterId,
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        metrics: dataSource,
        function: 'COUNT',
        timeConfig: { timeTerm: 'DAY' }
      });
      const filterInfo = {
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        filterType: 'ADVANCED',
        connector: 'AND',
        comparisonList: [],
        campaignList: [],
        campaignNodeList: [],
        userLabelList: [],
        dragType: dragType.valueList,
        filterId,
        checkedBasicData: [],
        filters: _.isEmpty(dataSource)
          ? []
          : [
              {
                operator:
                  dataType === 'TABLE_SCHEMA' &&
                  (dataSource.dataType === 'DATETIME' ||
                    dataSource.dataType === 'DATE' ||
                    dataSource.dataType === 'TIMESTAMP' ||
                    dataSource.dataType === 'HIVE_DATE' ||
                    dataSource.dataType === 'HIVE_TIMESTAMP')
                    ? 'BETWEEN'
                    : filterConfig.typeOperator[dataSource.dataType][0],
                fieldValue: ''
              },
              { operator: null, fieldValue: '' }
            ]
      };
      if (dataType !== 'METRICS') {
        filterData[selectedChart].push(filterInfo);
      }
      dispatch({});
      return { name: 'VALUE', data: filterInfo };
    },
    canDrop: (item) => {
      const { dataType, key } = item;
      return isAuth(null, 'valueList', dataType, key);
    },
    collect: (monitor) => ({
      isOverValue: monitor.isOver(),
      canDropValue: monitor.canDrop()
    })
  });

  const axisStyle = useMemo(() => {
    return { borderColor: getStyle(canDropRow, isOverRow) };
  }, [canDropRow, isOverRow]);

  const legendStyle = useMemo(() => {
    return { borderColor: getStyle(canDropLegend, isOverLegend) };
  }, [canDropLegend, isOverLegend]);

  const metricStyle = useMemo(() => {
    return { borderColor: getStyle(canDropValue, isOverValue) };
  }, [canDropValue, isOverValue]);

  const isAuth = (from, to, type, key) => {
    let flag = true;
    if (configData[selectedChart][to].length >= limit[to]) {
      return false;
    }
    const index = configData[selectedChart][to].findIndex((n) => n.type === type && n.key === key);
    if (index !== -1) {
      return false;
    }
    const legendIndex = legendList.findIndex((n) => n.key === key);
    const axisIndex = axisList.findIndex((n) => n.key === key);
    const valueIndex = valueList.findIndex((n) => n.key === key);
    if (userContrastTypeList.includes(type)) {
      if (to === 'valueList') {
        if (!from) {
          flag = axisIndex === -1 && legendIndex === -1;
        } else if (from === 'axisList') {
          flag = legendIndex === -1;
        } else if (from === 'legendList') {
          flag = axisIndex === -1;
        }
      } else {
        if (from !== 'valueList') {
          flag = valueIndex === -1;
        }
      }
    } else if (type === 'TABLE_SCHEMA') {
      if ((!from || from === 'valueList') && to !== 'valueList') {
        flag = axisIndex === -1 && legendIndex === -1;
      }
    } else if (type === 'METRICS') {
      flag = to === 'valueList' && valueIndex === -1;
    }
    return flag;
  };

  const fillComponent = useMemo(() => {
    const removeItem = (type, filterId) => {
      const index = configData[selectedChart][type].findIndex((n) => n.filterId === filterId);
      if (index > -1) {
        // configData[selectedChart][type].splice(index, 1);
        configData[selectedChart][type] = configData[selectedChart][type].filter(
          (item, filterIndex) => filterIndex !== index
        );

        const delFilterIndex = filterData[selectedChart].findIndex((n) => n.filterId === filterId);

        if (delFilterIndex > -1) {
          // filterData[selectedChart].splice(filterIndex, 1);
          filterData[selectedChart] = filterData[selectedChart].filter(
            (item, filterIndex) => filterIndex !== delFilterIndex
          );
        }

        dispatch({});
      }
    };

    const moveTo = (from, to, { filterId, type, key }) => {
      if (isAuth(from, to, type, key)) {
        const index = configData[selectedChart][from].findIndex((n) => n.filterId === filterId);
        const info = { ...configData[selectedChart][from][index] };
        if (to === 'valueList') {
          info.function = info.function || 'COUNT';
        } else {
          info.timeConfig = info.timeConfig || { timeTerm: 'DAY' };
        }
        configData[selectedChart][to].push(info);
        configData[selectedChart][from].splice(index, 1);
        const filterInfo = filterData[selectedChart].find((n) => n.filterId === filterId);
        if (filterInfo) filterInfo.dragType = dragType[to];
        dispatch({});
      }
    };

    const doCompute = (from, filterId, value) => {
      const info = configData[selectedChart][from].find((n) => n.filterId === filterId);
      info.function = value;
      dispatch({});
    };

    const getStatisticsType = (from, filterId, type, tableSchema) => {
      if (userContrastTypeList.includes(type)) {
        return Object.entries(functionObj)
          .filter((w) => w[0] === 'COUNT')
          .map((n) => (
            <Menu.Item onClick={() => doCompute(from, filterId, n[0])} key={n[0]}>
              {n[1]}
            </Menu.Item>
          ));
      } else if (type === 'TABLE_SCHEMA') {
        return Object.entries(functionObj)
          .filter(
            (w) =>
              tableSchemaStatisticsType[tableSchema.dataType] &&
              tableSchemaStatisticsType[tableSchema.dataType].includes(w[0])
          )
          .map((n) => (
            <Menu.Item onClick={() => doCompute(from, filterId, n[0])} key={n[0]}>
              {n[1]}
            </Menu.Item>
          ));
      }
    };

    const doDateStepTerm = (from, filterId, value) => {
      const info = configData[selectedChart][from].find((n) => n.filterId === filterId);
      info.timeConfig = { timeTerm: value };
      dispatch({});
    };

    const getDateStepTerm = (from, filterId) => {
      return (
        <SubMenu title={t('analysisCenter-fKFVR8wcSRVv')}>
          {dateStepTerm.map((n) => (
            <Menu.Item onClick={() => doDateStepTerm(from, filterId, n.value)} key={n.value}>
              {n.label}
            </Menu.Item>
          ))}
        </SubMenu>
      );
    };
    const editFormat = (target, filterId) => {
      const info = configData[selectedChart][target].find((n) => n.filterId === filterId);
      dispatch({
        formatVisible: true,
        formatValue: { target, filterId, numberConfig: info?.numberConfig }
      });
    };
    const moreFunction = (from, { filterId, type, tableSchema, key, orderBy }) => {
      const menu = (
        <Menu style={{ width: 100 }}>
          <Menu.Item onClick={() => removeItem(from, filterId)}>{t('analysisCenter-mcsNO1rgLO3Z')}</Menu.Item>
          <SubMenu title={t('analysisCenter-HxyZ966KwLUE')}>
            <Menu.Item hidden={from === 'axisList'} onClick={() => moveTo(from, 'axisList', { type, filterId, key })}>
              {t('analysisCenter-t7n1AicPtiJ9')}
            </Menu.Item>
            <Menu.Item
              hidden={from === 'legendList'}
              onClick={() => moveTo(from, 'legendList', { type, filterId, key })}
            >
              {t('analysisCenter-2956YfZZbuE4')}
            </Menu.Item>
            <Menu.Item hidden={from === 'valueList'} onClick={() => moveTo(from, 'valueList', { type, filterId, key })}>
              {t('analysisCenter-AXi0w5nIzZVb')}
            </Menu.Item>
          </SubMenu>
          {from === 'axisList' && getSort(from, filterId, orderBy)}
          {from !== 'valueList' &&
            type === 'TABLE_SCHEMA' &&
            timeType.includes(tableSchema.dataType) &&
            getDateStepTerm(from, filterId)}
          {from === 'valueList' && getStatisticsType(from, filterId, type, tableSchema)}
          {/* value根据图例做限制，有图例不可以排序 */}
          {from === 'valueList' && _.isEmpty(legendList) && getSort(from, filterId, orderBy)}
          {from === 'valueList' && (
            <Menu.Item onClick={() => editFormat(from, filterId)}>{t('analysisCenter-Lvajwz8YypES')}</Menu.Item>
          )}
        </Menu>
      );
      return (
        <>
          <Dropdown trigger={['click']} overlay={menu}>
            <a onClick={(e) => e.preventDefault()}>
              <DownOutlined className="downOutlinedStyle" />
            </a>
          </Dropdown>
          <CloseOutlined className="closeCircle" onClick={() => removeItem(from, filterId)} />
        </>
      );
    };

    const setSort = (type, filterId, value) => {
      const newConfigData = _.cloneDeep(configData); // 创建新的对象
      if (type === 'axisList') {
        newConfigData[selectedChart].valueList = newConfigData[selectedChart].valueList.map((item) => {
          return { ...item, orderBy: null };
        });
      } else if (type === 'valueList') {
        newConfigData[selectedChart].axisList = newConfigData[selectedChart].axisList.map((item) => {
          return { ...item, orderBy: null };
        });
        const _valueList = newConfigData[selectedChart].valueList.map((item) => {
          if (item.filterId !== filterId) {
            return { ...item, orderBy: null };
          }
          return item;
        });
        newConfigData[selectedChart][type].find((n) => n.filterId === filterId).orderBy = value;
        const _sortValueList = _valueList.sort((a, b) => {
          if (a.orderBy && !b.orderBy) {
            return -1;
          } else if (!a.orderBy && b.orderBy) {
            return 1;
          } else {
            return 0;
          }
        });
        newConfigData[selectedChart].valueList = _sortValueList;
      }
      newConfigData[selectedChart][type].find((n) => n.filterId === filterId).orderBy = value;
      dispatch({ configData: newConfigData });
    };

    const getSort = (from, filterId, orderBy) => {
      const sortOptions = [
        { order: 'ASC', label: t('analysisCenter-FLMIzYqPr1Lb'), key: 1 },
        { order: 'DESC', label: t('analysisCenter-67SdsI3x6VT8'), key: 2 },
        { order: null, label: t('analysisCenter-lyob81ZybeXK'), key: 3 }
      ];

      return (
        <SubMenu title={t('analysisCenter-AsQVQagib3e5')}>
          {sortOptions.map(({ order, label, key }) => (
            <Menu.Item onClick={() => setSort(from, filterId, order)} key={key}>
              <span
                style={{
                  color: orderBy === order && 'var(--ant-primary-color)'
                }}
              >
                {label}
              </span>
            </Menu.Item>
          ))}
        </SubMenu>
      );
    };

    return (
      <>
        <div className="dropTitle">{t('analysisCenter-C2hStUaLbLdS')}</div>
        <div className="dropStyle" ref={dropRow} style={{ ...axisStyle }}>
          {axisList.length > 0 ? (
            axisList.map((n) => {
              const name = `${n.name}${
                n.type === 'TABLE_SCHEMA' && timeType.includes(n.tableSchema.dataType) && n.timeConfig?.timeTerm
                  ? `(粒度：${dateStepTermObj[n.timeConfig.timeTerm]})`
                  : ''
              }${n.orderBy ? `(${n.orderBy === 'ASC' ? t('analysisCenter-FLMIzYqPr1Lb') : t('analysisCenter-67SdsI3x6VT8')})` : `(${t('analysisCenter-PhYRp5ppAGz8')})`}`;
              return (
                <div key={n.key} className="tagStyle">
                  <div title={name} className="nameStyle">
                    {name}
                  </div>
                  <div className="toolbarStyle">{moreFunction('axisList', n)}</div>
                </div>
              );
            })
          ) : (
            <div>{t('analysisCenter-6uInVyeQ8yxN')}</div>
          )}
        </div>
        <div className="dropTitle">{t('analysisCenter-dxlKo6r7hXzq')}</div>
        <div className="dropStyle" ref={dropValue} style={{ ...metricStyle }}>
          {valueList.length > 0 ? (
            valueList.map((n) => {
              const name = `${n.name}${n.type !== 'METRICS' && n.function ? `(${functionObj[n.function]})` : ''}${
                n.orderBy
                  ? `(${n.orderBy === 'ASC' ? t('analysisCenter-FLMIzYqPr1Lb') : t('analysisCenter-67SdsI3x6VT8')})`
                  : `(${t('analysisCenter-PhYRp5ppAGz8')})`
              }`;
              return (
                <div key={n.key} className="tagStyle">
                  <div title={name} className="nameStyle">
                    {name}
                  </div>
                  <div className="toolbarStyle">{moreFunction('valueList', n)}</div>
                </div>
              );
            })
          ) : (
            <div>{t('analysisCenter-6uInVyeQ8yxN')}</div>
          )}
        </div>
        <div className="dropTitle">{t('analysisCenter-48rM4hMvisiE')}</div>
        <div className="dropStyle" ref={dropLegend} style={{ ...legendStyle }}>
          {legendList.length > 0 ? (
            legendList.map((n) => {
              const name = `${n.name}${
                n.type === 'TABLE_SCHEMA' && timeType.includes(n.tableSchema.dataType) && n.timeConfig?.timeTerm
                  ? `(${t('analysisCenter-tPPTzw5K0O9o')}${dateStepTermObj[n.timeConfig.timeTerm]})`
                  : ''
              }`;
              return (
                <div key={n.key} className="tagStyle">
                  <div title={name} className="nameStyle">
                    {name}
                  </div>
                  <div className="toolbarStyle">{moreFunction('legendList', n)}</div>
                </div>
              );
            })
          ) : (
            <div>{t('analysisCenter-6uInVyeQ8yxN')}</div>
          )}
        </div>
      </>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    JSON.stringify(axisList),
    JSON.stringify(legendList),
    JSON.stringify(valueList),
    axisStyle,
    configData,
    dispatch,
    dropLegend,
    dropRow,
    dropValue,
    filterData,
    functionObj,
    legendStyle,
    metricStyle,
    selectedChart
  ]);

  return <div className="tableFillStyle">{fillComponent}</div>;
};

export default Fill;
