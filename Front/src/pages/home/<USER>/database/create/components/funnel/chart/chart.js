import { Empty, Spin } from 'antd';
import React, { useMemo } from 'react';
import { t } from 'utils/translation';

import { Funnel } from '@ant-design/charts';
import _ from 'lodash';

export default ({ dataSource: { rowList }, loading }) => {
  const result = useMemo(() => {
    if (rowList === null || rowList === undefined) {
      return (
        <div className="mainChartSpin">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={<span>{t('analysisCenter-2aKsgUcrUHpl')}</span>} />
        </div>
      );
    } else if (_.isArray(rowList) && (rowList.length === 0 || !rowList.some((n) => n.value > 0))) {
      return (
        <div className="mainChartSpin">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      );
    }
    const config = {
      // forceFit: false,
      // // width: '100%',
      // height: 200,
      appendPadding: [10, 170, 10, 100],
      data: rowList,
      xField: 'action',
      yField: 'value'
    };
    return <Funnel {...config} />;
  }, [rowList]);
  return (
    <div style={{ width: '100%', height: '100%', overflow: 'auto', padding: 5 }}>
      {loading ? (
        <div className="mainChartSpin">
          <Spin spinning />{' '}
        </div>
      ) : (
        result
      )}
    </div>
  );
};
