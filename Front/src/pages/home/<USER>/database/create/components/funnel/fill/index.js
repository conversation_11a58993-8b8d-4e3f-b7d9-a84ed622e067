import { CloseOutlined, DownOutlined } from '@ant-design/icons';
import { Dropdown, Menu } from 'antd';
import _ from 'lodash';
import React, { useContext, useMemo } from 'react';
import { useDrop } from 'react-dnd';
import { t } from 'utils/translation';
import { v4 as getUuid } from 'uuid';
import { dragType, tableSchemaStatisticsType, userContrastTypeList } from '../../../../config';
import { DataContext } from '../../../context';
import filterConfig from '../../../filterConfig';
import './index.scss';

const getStyle = (canDrop, isOver) => {
  let color = '';
  if (canDrop && isOver) {
    color = 'var(--ant-primary-color)';
  } else if (canDrop) {
    color = '#fc8e44';
  } else {
    color = '#D7D7D7';
  }
  return color;
};

const limit = {
  valueList: 30
};

const Fill = () => {
  const {
    state: { configData, selectedChart, filterData, functionObj },
    dispatch
  } = useContext(DataContext);
  const { valueList } = configData[selectedChart];

  const [{ canDropValue, isOverValue }, dropValue] = useDrop({
    accept: 'box',
    drop: (item) => {
      const { dataType, name, dataSource, key } = item;
      const filterId = getUuid();
      valueList.push({
        filterId,
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        metrics: dataSource,
        function: dataType !== 'METRICS' ? 'COUNT' : undefined
      });
      const filterInfo = {
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        filterType: 'ADVANCED',
        connector: 'AND',
        comparisonList: [],
        campaignList: [],
        campaignNodeList: [],
        userLabelList: [],
        dragType: dragType.valueList,
        filterId,
        checkedBasicData: [],
        filters: _.isEmpty(dataSource)
          ? []
          : [
              {
                operator:
                  dataType === 'TABLE_SCHEMA' &&
                  (dataSource.dataType === 'DATETIME' ||
                    dataSource.dataType === 'DATE' ||
                    dataSource.dataType === 'TIMESTAMP' ||
                    dataSource.dataType === 'HIVE_DATE' ||
                    dataSource.dataType === 'HIVE_TIMESTAMP')
                    ? 'BETWEEN'
                    : filterConfig.typeOperator[dataSource.dataType][0],
                fieldValue: ''
              },
              { operator: null, fieldValue: '' }
            ]
      };
      if (dataType !== 'METRICS') {
        filterData[selectedChart].push(filterInfo);
      }
      dispatch({});
      return { name: 'Value', data: filterInfo };
    },
    canDrop: (item) => {
      const { dataType, key } = item;
      return isAuth(null, 'valueList', dataType, key);
    },
    collect: (monitor) => {
      return {
        isOverValue: monitor.isOver(),
        canDropValue: monitor.canDrop()
      };
    }
  });

  const metricStyle = useMemo(() => {
    return { borderColor: getStyle(canDropValue, isOverValue) };
  }, [canDropValue, isOverValue]);

  const isAuth = (from, to, type, key) => {
    const flag = true;
    if (configData[selectedChart][to].length >= limit[to]) {
      return false;
    }
    const index = configData[selectedChart][to].findIndex((n) => n.type === type && n.key === key);
    if (index !== -1) {
      return false;
    }
    return flag;
  };

  const fillComponent = useMemo(() => {
    const removeItem = (type, filterId) => {
      const index = configData[selectedChart][type].findIndex((n) => n.filterId === filterId);
      if (index > -1) {
        // configData[selectedChart][type].splice(index, 1);
        configData[selectedChart][type] = configData[selectedChart][type].filter(
          (item, filterIndex) => filterIndex !== index
        );

        const delFilterIndex = filterData[selectedChart].findIndex((n) => n.filterId === filterId);

        if (delFilterIndex > -1) {
          // filterData[selectedChart].splice(filterIndex, 1);
          filterData[selectedChart] = filterData[selectedChart].filter(
            (item, filterIndex) => filterIndex !== delFilterIndex
          );
        }

        dispatch({});
      }
    };

    const doCompute = (from, filterId, value) => {
      const info = configData[selectedChart][from].find((n) => n.filterId === filterId);
      info.function = value;
      dispatch({});
    };

    const getStatisticsType = (from, filterId, type, tableSchema) => {
      if (userContrastTypeList.includes(type)) {
        return Object.entries(functionObj)
          .filter((w) => w[0] === 'COUNT')
          .map((n) => (
            <Menu.Item onClick={() => doCompute(from, filterId, n[0])} key={n[0]}>
              {n[1]}
            </Menu.Item>
          ));
      } else if (type === 'TABLE_SCHEMA') {
        return Object.entries(functionObj)
          .filter(
            (w) =>
              tableSchemaStatisticsType[tableSchema.dataType] &&
              tableSchemaStatisticsType[tableSchema.dataType].includes(w[0])
          )
          .map((n) => (
            <Menu.Item onClick={() => doCompute(from, filterId, n[0])} key={n[0]}>
              {n[1]}
            </Menu.Item>
          ));
      }
    };

    const moreFunction = (from, filterId, type, tableSchema) => {
      const menu = (
        <Menu style={{ width: 100 }}>
          <Menu.Item onClick={() => removeItem(from, filterId)}>{t('analysisCenter-mcsNO1rgLO3Z')}</Menu.Item>
          {/* <SubMenu title="移动到">
          <Menu.Item hidden={from === 'legendList'} onClick={() => moveTo(filterId, from, 'legendList', type)}>图例 Legend</Menu.Item>
          <Menu.Item hidden={from === 'valueList'} onClick={() => moveTo(filterId, from, 'valueList', type)}>值 Value</Menu.Item>
        </SubMenu> */}
          {from === 'valueList' && getStatisticsType(from, filterId, type, tableSchema)}
        </Menu>
      );
      return (
        <>
          <Dropdown trigger={['click']} overlay={menu}>
            <a onClick={(e) => e.preventDefault()}>
              <DownOutlined className="downOutlinedStyle" />
            </a>
          </Dropdown>
          <CloseOutlined className="closeCircle" onClick={() => removeItem(from, filterId)} />
        </>
      );
    };
    return (
      <>
        <div className="dropTitle">{t('analysisCenter-HVHcCbQG1m3H')}</div>
        <div className="dropStyle" ref={dropValue} style={{ ...metricStyle }}>
          {valueList.length > 0 ? (
            valueList.map((n) => (
              <div key={n.key} className="tagStyle">
                <div title={`${n.name}${n.function && `(${functionObj[n.function]})`}`} className="nameStyle">
                  {n.name}
                  {n.function && `(${functionObj[n.function]})`}
                </div>
                <div className="toolbarStyle">{moreFunction('valueList', n.filterId, n.type, n.tableSchema)}</div>
              </div>
            ))
          ) : (
            <div>{t('analysisCenter-f4dcomsIYLZt')}</div>
          )}
        </div>
      </>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(valueList), configData, dispatch, dropValue, filterData, functionObj, metricStyle, selectedChart]);

  return <div className="tableFillStyle">{fillComponent}</div>;
};

export default Fill;
