import { DownloadOutlined } from '@ant-design/icons';
import { Button, Dropdown, Empty, Radio, Spin, Table, Tooltip } from 'antd';
import iconCsv from 'assets/images/icon-csv.png';
import Vector from 'assets/images/icon-excel.png';
import _ from 'lodash';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import AnalysisCenterService from 'service/analysisCenterService';
import thousands from 'utils/thousands';
import { t } from 'utils/translation';
import { transformUrl } from 'utils/universal';
import './index.scss';

// 处理数据rowSpan函数
const formatData = (array, key, mode) => {
  if (array.length === 0 || array.length === 1 || mode === 'flated' || !key) return array;
  const processedData = [...array];
  let prevFinishNum;
  let currentIndexForSameProperty;

  for (let i = 0; i < processedData.length; i++) {
    const currentItem = processedData[i];

    if (i === 0 || currentItem[key] !== prevFinishNum) {
      // 新的组开始，重置计数器，并将上一组最后一项的rowSpan设置好
      if (prevFinishNum !== undefined && currentIndexForSameProperty !== undefined) {
        processedData[currentIndexForSameProperty].rowSpan = i - currentIndexForSameProperty;
      }
      currentIndexForSameProperty = i;
      currentItem.rowSpan = 1;
      currentItem.opcaty = '0'; // 标识是不是被合并的行
    } else {
      // 相同组内，累加rowSpan
      processedData[currentIndexForSameProperty].rowSpan += 1;
      currentItem.opcaty = '1'; // 标识是被合并的行
    }

    prevFinishNum = currentItem[key];
  }

  // 处理数组的最后一项
  if (currentIndexForSameProperty !== undefined && currentIndexForSameProperty < processedData.length - 1) {
    processedData[currentIndexForSameProperty].rowSpan = processedData.length - currentIndexForSameProperty;
  }

  return processedData;
};
export default ({ dataSource: { rowsValueList, columnList }, grandchildData, loading, dispatch }) => {
  const [mode, setMode] = useState(grandchildData || 'flated');
  const [dataSourceList, setDataSourceList] = useState([]);
  const [tableDatasource, setTableDatasource] = useState([]);
  const tableDatasourceRef = useRef(tableDatasource);
  const [columnsWithOnCell, setColumnsWithOnCell] = useState([]);
  const [exportLoading, setExportLoading] = useState(false);
  const [tableParams, setTableParams] = useState({
    page: 1,
    pageSize: 20,
    totalElements: rowsValueList?.length || 0
  });

  const exportParams = {
    chartType: 'GROUP_TABLE',
    groupTableChartResultVo: {
      columnNameList: _.cloneDeep(columnList),
      rowsValueList: _.cloneDeep(rowsValueList)
    },
    projectId: localStorage.getItem('projectId')
  };

  const exportData = async (key) => {
    exportParams.type = key;
    try {
      setExportLoading(true);
      const res = await AnalysisCenterService.exportGroupTable(exportParams);
      if (res.flag === 'SUCCESS') {
        window.location.href = transformUrl(res.path);
      }
      setExportLoading(false);
    } catch (err) {
      setExportLoading(false);
      console.error(err.message);
    }
  };

  const items = [
    {
      key: 'xlsx',
      label: (
        <div className="items" onClick={() => exportData(2)}>
          <img src={Vector} alt="" /> {t('analysisCenter-tIb3Yd0epsVd')}
        </div>
      )
    },
    {
      key: 'csv',
      label: (
        <div className="items" onClick={() => exportData(1)}>
          <img alt="" src={iconCsv} />
          {t('analysisCenter-GxS5rXvISkSa')}
        </div>
      )
    }
  ];
  const processingDataFormat = (v, numberConfig) => {
    if (numberConfig && typeof v === 'number') {
      if (numberConfig?.format === 'PERCENT') {
        return `${(v * 100).toFixed(numberConfig?.decimalSeparator || 0)}%`;
      } else {
        if (numberConfig?.thousandsSeparator) {
          return thousands(v.toFixed(numberConfig?.decimalSeparator || 0));
        } else {
          return v.toFixed(numberConfig?.decimalSeparator || 0);
        }
      }
    } else {
      return v;
    }
  };
  const getNewDataSource = (data) => {
    const formatDataCol = _.cloneDeep(columnList) || [];
    formatDataCol.forEach((item) => {
      if (item?.numberConfig) {
        data.forEach((w) => {
          w[item.fieldName] = processingDataFormat(w[item.fieldName], item?.numberConfig);
        });
      }
    });
    return data || [];
  };
  useEffect(
    () => {
      const data = _.cloneDeep(columnList);
      const doubleColumns = data && data.filter((col) => col.dataType === 'DOUBLE').map((col) => col.fieldName);
      let changeData = [...tableDatasourceRef.current];
      data &&
        data.map((item, index) => {
          item.align = 'center';
          item.dataIndex = item.fieldName;
          item.title = item.displayName;
          item.width = Math.max(`${item.title}`.length * 15 + 20, 100);
          item.ellipsis = { showTitle: true };
          item.sorter =
            mode === 'merged' && index !== 0
              ? false
              : (a, b) => {
                  if (item.dataIndex.indexOf('_METRIC') > -1) {
                    const aValue = !isNaN(a[item.dataIndex]) ? a[item.dataIndex] : 0;
                    const bValue = !isNaN(b[item.dataIndex]) ? b[item.dataIndex] : 0;
                    return aValue - bValue;
                  } else if (['TIME,DATE'].includes(item.dataType)) {
                    const dateA = new Date(a[item.dataIndex]);
                    const dateB = new Date(b[item.dataIndex]);
                    return dateA - dateB;
                  }
                  const numberA = a[item.dataIndex];
                  const numberB = b[item.dataIndex];
                  const res = `${numberA}`.localeCompare(numberB);

                  return _.isNumber(numberA) && _.isNumber(numberB) ? numberA - numberB : res;
                };
          return { ...item };
        });
      const dataMap =
        (rowsValueList && mode === 'merged' ? _.sortBy(rowsValueList, data?.[0]?.dataIndex || '') : rowsValueList) ||
        [];
      const tableDatasource =
        dataMap.map((row) => {
          const newRow = { ...row };
          doubleColumns.forEach((columnName) => {
            if (typeof newRow[columnName] === 'number') {
              newRow[columnName] = parseFloat(newRow[columnName].toFixed(2));
            }
          });
          return newRow;
        }) || [];
      const cloneDeepList = _.cloneDeep(tableDatasource);
      changeData = [...cloneDeepList];
      const startIndex = (tableParams.page - 1) * tableParams.pageSize;
      const endIndex = tableParams.page * tableParams.pageSize;
      setTableDatasource(tableDatasource || []);
      if (mode === 'merged') {
        const newDataSourceList = formatData(
          cloneDeepList.slice(startIndex, endIndex),
          columnList?.[0]?.fieldName || '',
          mode
        );
        changeData.splice(startIndex, endIndex - startIndex, ...newDataSourceList);
        setDataSourceList(getNewDataSource(changeData) || []);
      } else {
        setDataSourceList(getNewDataSource(tableDatasource) || []);
      }
      const newColumns = data?.length
        ? data.map((item, index) => {
            const originalColumn = { ...item };
            data.length > 7 && index === 0 ? (originalColumn.fixed = 'left') : null;
            tableDatasource.length > 1 && mode !== 'flated' && index === 0
              ? (originalColumn.onCell = (record) => {
                  if (record.rowSpan > 1) {
                    return {
                      rowSpan: record.rowSpan
                    };
                  } else if (record.opcaty !== '0') {
                    return {
                      rowSpan: 0
                    };
                  }
                })
              : null;
            return originalColumn;
          })
        : [];
      setColumnsWithOnCell(newColumns);
    },
    [mode, columnList, rowsValueList, tableParams],
    tableDatasourceRef
  );
  const handleModeChange = (e) => {
    setMode(e.target.value);
    dispatch && dispatch({ grandchildData: e.target.value });
    setTableParams({
      ...tableParams,
      order: null, // 如果有记录排序方向的字段，一并重置
      sortField: '',
      sortOrder: null
    });
  };

  const handleTableChange = (pagination) => {
    const { current, pageSize } = pagination;
    setDataSourceList(tableDatasource || []);
    // 更新tableParams
    setTableParams({
      ...tableParams,
      page: current,
      pageSize
    });
  };
  const result = useMemo(() => {
    if (_.isArray(columnList) && columnList.length === 0) {
      return (
        <div className="mainChartSpin">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      );
    } else if (columnList === null || columnList === undefined) {
      return (
        <div className="mainChartSpin">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={<span>{t('analysisCenter-Ua0b1OF83YRu')}</span>} />
        </div>
      );
    }
  }, [rowsValueList, columnList, mode]);
  const SummaryRow = ({ columns }) => {
    const summaryMethod = (data, column) => {
      let sum;
      const { page, pageSize } = tableParams;
      const newData = data.slice((page - 1) * pageSize, page * pageSize);
      if (column.dataIndex && (column.dataType === 'INT' || column.dataType === 'LONG')) {
        sum = newData.reduce((acc, cur) => acc + Number(cur[column.dataIndex]), 0);
      } else if (column.dataType === 'DOUBLE' && column.dataIndex) {
        sum = parseFloat(newData.reduce((acc, cur) => acc + Number(cur[column.dataIndex]), 0).toFixed(2));
      } else if (column.dataIndex && typeof newData[0][column.dataIndex] === 'number') {
        sum = newData.reduce((acc, cur) => acc + Number(cur[column.dataIndex]), 0);
      }
      return processingDataFormat(sum, column?.numberConfig) || '';
    };
    return (
      <>
        {rowsValueList.length ? (
          <Table.Summary.Row>
            {columns.map((column, index) => (
              <Table.Summary.Cell index={index}>
                {index === 0 ? t('analysisCenter-ZDOfPvQE7Fgq') : summaryMethod(rowsValueList, column)}
              </Table.Summary.Cell>
            ))}
            {/* 添加更多Cell，与列数匹配 */}
          </Table.Summary.Row>
        ) : null}
      </>
    );
  };
  return (
    <div className="chatTableWrapper" style={{ width: '100%', height: '100%', overflow: 'auto' }}>
      {loading ? (
        <div className="mainChartSpin">
          <Spin spinning />{' '}
        </div>
      ) : columnList && columnList.length ? (
        <>
          <div className="chatTableWrapper_header">
            <Radio.Group onChange={handleModeChange} defaultValue={mode} value={mode} style={{ marginBottom: 8 }}>
              <Radio.Button value="flated" checked={mode === 'flated'}>
                {t('analysisCenter-gqEB3BGq4nxL')}
              </Radio.Button>
              <Radio.Button value="merged" checked={mode === 'merged'}>
                {t('analysisCenter-5j8856W2zSju')}
              </Radio.Button>
            </Radio.Group>
            <Dropdown menu={{ items }} placement="bottom" arrow trigger={['click']} className="export">
              <Tooltip title={t('analysisCenter-5R1Rh71Jxa76')}>
                <Button icon={<DownloadOutlined />} loading={exportLoading}>
                  {t('analysisCenter-eWvZMKIT1yC3')}
                </Button>
              </Tooltip>
            </Dropdown>
          </div>
          <Table
            columns={columnsWithOnCell || []}
            dataSource={dataSourceList || []}
            rowKey={(record) => record.key}
            scroll={{ x: 'max-content' }}
            pagination={{
              current: tableParams.page,
              pageSize: tableParams.pageSize,
              total: tableParams.totalElements,
              showTotal: (e) => `${t('analysisCenter-dTMxhSuFe1xG')} ${e} ${t('analysisCenter-U8m5WUfDx3iL')}`,
              pageSizeOptions: ['10', '20', '50'],
              showQuickJumper: true
            }}
            summary={() => <SummaryRow columns={columnsWithOnCell} data={dataSourceList} />}
            hideOnSinglePage
            bordered
            size="middle"
            onChange={handleTableChange}
          />
        </>
      ) : (
        result
      )}
    </div>
  );
};
