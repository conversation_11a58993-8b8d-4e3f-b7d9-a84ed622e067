.tableFillStyle {
    padding: 8px;

    .dropStyle {
        border: 1px dashed #1890ff;
        text-align: center;
        margin-bottom: 10px;
        min-height: 36px;
        font-size: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        padding: 0 5px;

        .tagStyle {
            margin: 5px;
            padding: 0 5px;
            height: 24px;
            background-color: #F5F5F5;
            display: flex;
            align-items: center;
            border-radius: 3px;

            &:hover {
                background-color: #aad6ff;
            }

            .nameStyle {
                font-size: 14px;
                flex: 1;
                text-align: left;
                overflow: hidden; //超出的文本隐藏
                text-overflow: ellipsis; //溢出用省略号显示
                white-space: nowrap; //溢出不换行
            }

            // .toolbarStyle{
            //     display: flex;
            //      .ant-dropdown-trigger{
            //          height: 14px;
            //          .downOutlinedStyle{
            //              font-size: 14px;
            //          }
            //      }
            //     .closeCircle{
            //          font-size: 14px;
            //          margin-left: 6px;
            //          cursor: pointer;
            //     }
            // }
        }
    }

    .sumStyle {
        margin-top: 20px;
        margin-bottom: 7px;
    }

    .sumBoundary {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .sumBoundaryText {
            font-size: 12px;
            padding: 0 11px 0 9px;
        }

        .ant-input-number {
            flex: 1;

            input {
                font-size: 12px;
            }
        }
    }

    .statisticsStyle {
        display: flex;
        margin-bottom: 10px;

        .statisticsCheckbox {
            >span:last-child {
                font-size: 12px;
            }
        }

        .statisticsSelect {
            .ant-select-selection-selected-value {
                font-size: 12px;
            }
        }

    }
}

.statisticsSelectDropdown {
    .ant-select-dropdown-menu-item {
        font-size: 12px;
    }
}