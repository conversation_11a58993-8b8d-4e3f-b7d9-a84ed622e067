import { CloseOutlined, DownOutlined } from '@ant-design/icons';
import { Dropdown, Menu } from 'antd';
import _ from 'lodash';
import React, { useContext, useMemo } from 'react';
import { useDrop } from 'react-dnd';
import { t } from 'utils/translation';
import { v4 as getUuid } from 'uuid';
import { dateStepTerm, dragType, tableSchemaStatisticsType, timeType, userContrastTypeList } from '../../../../config';
import { DataContext } from '../../../context';
import filterConfig from '../../../filterConfig';
import './index.scss';

const { SubMenu } = Menu;

const getStyle = (canDrop, isOver) => {
  let color = '';
  if (canDrop && isOver) {
    color = 'var(--ant-primary-color)';
  } else if (canDrop) {
    color = '#fc8e44';
  } else {
    color = '#D7D7D7';
  }
  return color;
};

const dateStepTermObj = dateStepTerm.reduce((obj, n) => {
  obj[n.value] = n.label;
  return obj;
}, {});

const limit = {
  legendList: 1,
  xvalueList: 1,
  yvalueList: 1,
  valueList: 1
};

const FillBubble = () => {
  const {
    state: { configData, selectedChart, filterData, functionObj },
    dispatch
  } = useContext(DataContext);
  const { legendList, valueList, xvalueList, yvalueList } = configData[selectedChart];

  const [{ canDropLegend, isOverLegend }, dropLegend] = useDrop({
    accept: 'box',
    drop: (item) => {
      const { dataType, name, key, dataSource } = item;
      const filterId = getUuid();
      legendList.push({
        filterId,
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        function: 'COUNT',
        timeConfig: { timeTerm: 'DAY' }
      });
      const filterInfo = {
        key,
        type: dataType,
        tableSchema: dataSource,
        filterType: 'ADVANCED',
        // dataType === 'TABLE_SCHEMA' &&
        // (dataSource.dataType === 'DATETIME' ||
        //   dataSource.dataType === 'DATE' ||
        //   dataSource.dataType === 'TIMESTAMP' ||
        //   dataSource.dataType === 'HIVE_DATE' ||
        //   dataSource.dataType === 'HIVE_TIMESTAMP')
        //   ? 'ADVANCED'
        //   : 'BASIC',
        connector: 'AND',
        comparisonList: [],
        campaignList: [],
        campaignNodeList: [],
        userLabelList: [],
        name,
        dragType: dragType.legendList,
        filterId,
        checkedBasicData: [],
        filters: _.isEmpty(dataSource)
          ? []
          : [
              {
                operator:
                  dataType === 'TABLE_SCHEMA' &&
                  (dataSource.dataType === 'DATETIME' ||
                    dataSource.dataType === 'DATE' ||
                    dataSource.dataType === 'TIMESTAMP' ||
                    dataSource.dataType === 'HIVE_DATE' ||
                    dataSource.dataType === 'HIVE_TIMESTAMP')
                    ? 'BETWEEN'
                    : filterConfig.typeOperator[dataSource.dataType][0],
                fieldValue: ''
              },
              { operator: null, fieldValue: '' }
            ]
      };
      filterData[selectedChart].push(filterInfo);
      dispatch({});
      return { name: 'legend', data: filterInfo };
    },
    canDrop: (item) => {
      const { dataType, key } = item;
      return isAuth(null, 'legendList', dataType, key);
    },
    collect: (monitor) => ({
      isOverLegend: monitor.isOver(),
      canDropLegend: monitor.canDrop()
    })
  });

  const [{ canDropXValue, isOverXValue }, dropXValue] = useDrop({
    accept: 'box',
    drop: (item) => {
      const { dataType, name, dataSource, key } = item;
      const filterId = getUuid();
      xvalueList.push({
        filterId,
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        metrics: dataSource,
        function: 'COUNT',
        timeConfig: { timeTerm: 'DAY' }
      });

      const filterInfo = {
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        filterType: 'ADVANCED',
        connector: 'AND',
        comparisonList: [],
        campaignList: [],
        campaignNodeList: [],
        userLabelList: [],
        dragType: dragType.xvalueList,
        filterId,
        checkedBasicData: [],
        filters: _.isEmpty(dataSource)
          ? []
          : [
              {
                operator:
                  dataType === 'TABLE_SCHEMA' &&
                  (dataSource.dataType === 'DATETIME' ||
                    dataSource.dataType === 'DATE' ||
                    dataSource.dataType === 'TIMESTAMP' ||
                    dataSource.dataType === 'HIVE_DATE' ||
                    dataSource.dataType === 'HIVE_TIMESTAMP')
                    ? 'BETWEEN'
                    : filterConfig.typeOperator[dataSource.dataType][0],
                fieldValue: ''
              },
              { operator: null, fieldValue: '' }
            ]
      };
      if (dataType !== 'METRICS') {
        filterData[selectedChart].push(filterInfo);
      }

      dispatch({});
      return { name: 'X_VALUE', data: filterInfo };
    },
    canDrop: (item) => {
      const { dataType, key } = item;
      return isAuth(null, 'xvalueList', dataType, key);
    },
    collect: (monitor) => ({
      isOverXValue: monitor.isOver(),
      canDropXValue: monitor.canDrop()
    })
  });

  const [{ canDropYValue, isOverYValue }, dropYValue] = useDrop({
    accept: 'box',
    drop: (item) => {
      const { dataType, name, dataSource, key } = item;

      const filterId = getUuid();
      yvalueList.push({
        filterId,
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        metrics: dataSource,
        function: 'COUNT',
        timeConfig: { timeTerm: 'DAY' }
      });

      const filterInfo = {
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        filterType: 'ADVANCED',
        connector: 'AND',
        comparisonList: [],
        campaignList: [],
        campaignNodeList: [],
        userLabelList: [],
        dragType: dragType.yvalueList,
        filterId,
        checkedBasicData: [],
        filters: _.isEmpty(dataSource)
          ? []
          : [
              {
                operator:
                  dataType === 'TABLE_SCHEMA' &&
                  (dataSource.dataType === 'DATETIME' ||
                    dataSource.dataType === 'DATE' ||
                    dataSource.dataType === 'TIMESTAMP' ||
                    dataSource.dataType === 'HIVE_DATE' ||
                    dataSource.dataType === 'HIVE_TIMESTAMP')
                    ? 'BETWEEN'
                    : filterConfig.typeOperator[dataSource.dataType][0],
                fieldValue: ''
              },
              { operator: null, fieldValue: '' }
            ]
      };
      if (dataType !== 'METRICS') {
        filterData[selectedChart].push(filterInfo);
      }

      dispatch({});
      return { name: 'Y_VALUE', data: filterInfo };
    },
    canDrop: (item) => {
      const { dataType, key } = item;
      return isAuth(null, 'yvalueList', dataType, key);
    },
    collect: (monitor) => ({
      isOverYValue: monitor.isOver(),
      canDropYValue: monitor.canDrop()
    })
  });

  const [{ canDropValue, isOverValue }, dropValue] = useDrop({
    accept: 'box',
    drop: (item) => {
      const { dataType, name, dataSource, key } = item;
      const filterId = getUuid();
      valueList.push({
        filterId,
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        metrics: dataSource,
        function: 'COUNT',
        timeConfig: { timeTerm: 'DAY' }
      });
      const filterInfo = {
        key,
        name,
        type: dataType,
        tableSchema: dataSource,
        filterType: 'ADVANCED',
        connector: 'AND',
        comparisonList: [],
        campaignList: [],
        campaignNodeList: [],
        userLabelList: [],
        dragType: dragType.valueList,
        filterId,
        checkedBasicData: [],
        filters: _.isEmpty(dataSource)
          ? []
          : [
              {
                operator:
                  dataType === 'TABLE_SCHEMA' &&
                  (dataSource.dataType === 'DATETIME' ||
                    dataSource.dataType === 'DATE' ||
                    dataSource.dataType === 'TIMESTAMP' ||
                    dataSource.dataType === 'HIVE_DATE' ||
                    dataSource.dataType === 'HIVE_TIMESTAMP')
                    ? 'BETWEEN'
                    : filterConfig.typeOperator[dataSource.dataType][0],
                fieldValue: ''
              },
              { operator: null, fieldValue: '' }
            ]
      };
      if (dataType !== 'METRICS') {
        filterData[selectedChart].push(filterInfo);
      }
      dispatch({});
      return { name: 'VALUE', data: filterInfo };
    },
    canDrop: (item) => {
      const { dataType, key } = item;
      return isAuth(null, 'valueList', dataType, key);
    },
    collect: (monitor) => ({
      isOverValue: monitor.isOver(),
      canDropValue: monitor.canDrop()
    })
  });

  const xValueStyle = useMemo(() => {
    return { borderColor: getStyle(canDropXValue, isOverXValue) };
  }, [canDropXValue, isOverXValue]);

  const yValueStyle = useMemo(() => {
    return { borderColor: getStyle(canDropYValue, isOverYValue) };
  }, [canDropYValue, isOverYValue]);

  const legendStyle = useMemo(() => {
    return { borderColor: getStyle(canDropLegend, isOverLegend) };
  }, [canDropLegend, isOverLegend]);

  const metricStyle = useMemo(() => {
    return { borderColor: getStyle(canDropValue, isOverValue) };
  }, [canDropValue, isOverValue]);

  const isAuth = (from, to, type, key) => {
    let flag = true;
    if (configData[selectedChart][to].length >= limit[to]) {
      return false;
    }
    const index = configData[selectedChart][to].findIndex((n) => n.type === type && n.key === key);
    if (index !== -1) {
      return false;
    }
    // const legendIndex = legendList.findIndex(n => n.key === key);
    // const xValueIndex = xvalueList.findIndex(n => n.key === key);
    // const yValueIndex = yvalueList.findIndex(n => n.key === key);
    // const valueIndex = valueList.findIndex(n => n.key === key);

    if (userContrastTypeList.includes(type)) {
      // if (to === 'valueList') {
      //   if (!from) {
      //     flag = axisIndex === -1 && legendIndex === -1;
      //   } else if (from === 'axisList') {
      //     flag = legendIndex === -1;
      //   } else if (from === 'legendList') {
      //     flag = axisIndex === -1;
      //   }
      // } else {
      //   if (from !== 'valueList') {
      //     flag = valueIndex === -1;
      //   }
      // }
    } else if (type === 'TABLE_SCHEMA') {
      // if ((!from || from === 'valueList') && to !== 'valueList') {
      //   flag = axisIndex === -1 && legendIndex === -1;
      // }
    } else if (type === 'METRICS') {
      flag = ['valueList', 'xvalueList', 'yvalueList'].includes(to);
    }
    return flag;
  };

  const fillComponent = useMemo(() => {
    const removeItem = (type, filterId) => {
      const index = configData[selectedChart][type].findIndex((n) => n.filterId === filterId);
      if (index > -1) {
        // configData[selectedChart][type].splice(index, 1);
        configData[selectedChart][type] = configData[selectedChart][type].filter(
          (item, filterIndex) => filterIndex !== index
        );

        const delFilterIndex = filterData[selectedChart].findIndex((n) => n.filterId === filterId);

        if (delFilterIndex > -1) {
          // filterData[selectedChart].splice(filterIndex, 1);
          filterData[selectedChart] = filterData[selectedChart].filter(
            (item, filterIndex) => filterIndex !== delFilterIndex
          );
        }

        dispatch({});
      }
    };

    const moveTo = (from, to, { filterId, type, key }) => {
      if (isAuth(from, to, type, key)) {
        const index = configData[selectedChart][from].findIndex((n) => n.filterId === filterId);
        const info = { ...configData[selectedChart][from][index] };
        if (['valueList', 'xvalueList', 'yvalueList'].includes(to)) {
          info.function = info.function || 'COUNT';
        } else {
          info.timeConfig = info.timeConfig || { timeTerm: 'DAY' };
        }
        configData[selectedChart][to].push(info);
        configData[selectedChart][from].splice(index, 1);
        const filterInfo = filterData[selectedChart].find((n) => n.filterId === filterId);
        if (filterInfo) filterInfo.dragType = dragType[to];
        dispatch({});
      }
    };

    const doCompute = (from, filterId, value) => {
      const info = configData[selectedChart][from].find((n) => n.filterId === filterId);
      info.function = value;
      dispatch({});
    };

    const getStatisticsType = (from, filterId, type, tableSchema) => {
      if (userContrastTypeList.includes(type)) {
        return Object.entries(functionObj)
          .filter((w) => w[0] === 'COUNT')
          .map((n) => (
            <Menu.Item onClick={() => doCompute(from, filterId, n[0])} key={n[0]}>
              {n[1]}
            </Menu.Item>
          ));
      } else if (type === 'TABLE_SCHEMA') {
        return Object.entries(functionObj)
          .filter(
            (w) =>
              tableSchemaStatisticsType[tableSchema.dataType] &&
              tableSchemaStatisticsType[tableSchema.dataType].includes(w[0])
          )
          .map((n) => (
            <Menu.Item onClick={() => doCompute(from, filterId, n[0])} key={n[0]}>
              {n[1]}
            </Menu.Item>
          ));
      }
    };

    const doDateStepTerm = (from, filterId, value) => {
      const info = configData[selectedChart][from].find((n) => n.filterId === filterId);
      info.timeConfig = { timeTerm: value };
      dispatch({});
    };

    const getDateStepTerm = (from, filterId) => {
      return (
        <SubMenu title={t('analysisCenter-mvOwaD70J9jh')}>
          {dateStepTerm.map((n) => (
            <Menu.Item onClick={() => doDateStepTerm(from, filterId, n.value)} key={n.value}>
              {n.label}
            </Menu.Item>
          ))}
        </SubMenu>
      );
    };

    const moreFunction = (from, { filterId, type, tableSchema, key }) => {
      const menu = (
        <Menu style={{ width: 100 }}>
          <Menu.Item onClick={() => removeItem(from, filterId)}>{t('analysisCenter-mcsNO1rgLO3Z')}</Menu.Item>
          <SubMenu title={t('analysisCenter-HxyZ966KwLUE')}>
            <Menu.Item
              hidden={from === 'legendList'}
              onClick={() => moveTo(from, 'legendList', { type, filterId, key })}
            >
              {t('analysisCenter-7pXwDNaXtihK')}
            </Menu.Item>
            <Menu.Item
              hidden={from === 'xvalueList'}
              onClick={() => moveTo(from, 'xvalueList', { type, filterId, key })}
            >
              {t('analysisCenter-C2hStUaLbLdS')}
            </Menu.Item>
            <Menu.Item
              hidden={from === 'yvalueList'}
              onClick={() => moveTo(from, 'yvalueList', { type, filterId, key })}
            >
              {t('analysisCenter-dxlKo6r7hXzq')}
            </Menu.Item>
            <Menu.Item hidden={from === 'valueList'} onClick={() => moveTo(from, 'valueList', { type, filterId, key })}>
              {t('analysisCenter-iizhHROvEst3')}
            </Menu.Item>
          </SubMenu>
          {from === 'legendList' &&
            type === 'TABLE_SCHEMA' &&
            timeType.includes(tableSchema.dataType) &&
            getDateStepTerm(from, filterId)}
          {from !== 'legendList' && getStatisticsType(from, filterId, type, tableSchema)}
        </Menu>
      );
      return (
        <>
          <Dropdown trigger={['click']} overlay={menu}>
            <a onClick={(e) => e.preventDefault()}>
              <DownOutlined className="downOutlinedStyle" />
            </a>
          </Dropdown>
          <CloseOutlined className="closeCircle" onClick={() => removeItem(from, filterId)} />
        </>
      );
    };
    return (
      <>
        <div className="dropTitle">{t('analysisCenter-7pXwDNaXtihK')}</div>
        <div className="dropStyle" ref={dropLegend} style={{ ...legendStyle }}>
          {legendList.length > 0 ? (
            legendList.map((n) => {
              const name = `${n.name}${
                n.type === 'TABLE_SCHEMA' && timeType.includes(n.tableSchema.dataType) && n.timeConfig?.timeTerm
                  ? `(${t('analysisCenter-NyHZjBWBCPwf')}${dateStepTermObj[n.timeConfig.timeTerm]})`
                  : ''
              }`;
              return (
                <div key={n.key} className="tagStyle">
                  <div title={name} className="nameStyle">
                    {name}
                  </div>
                  <div className="toolbarStyle">{moreFunction('legendList', n)}</div>
                </div>
              );
            })
          ) : (
            <div>{t('analysisCenter-f4dcomsIYLZt')}</div>
          )}
        </div>
        <div className="dropTitle">{t('analysisCenter-C2hStUaLbLdS')}</div>
        <div className="dropStyle" ref={dropXValue} style={{ ...xValueStyle }}>
          {xvalueList.length > 0 ? (
            xvalueList.map((n) => {
              const name = `${n.name}${n.type !== 'METRICS' && n.function ? `(${functionObj[n.function]})` : ''}`;
              return (
                <div key={n.key} className="tagStyle">
                  <div title={name} className="nameStyle">
                    {name}
                  </div>
                  <div className="toolbarStyle">{moreFunction('xvalueList', n)}</div>
                </div>
              );
            })
          ) : (
            <div>{t('analysisCenter-f4dcomsIYLZt')}</div>
          )}
        </div>
        <div className="dropTitle">{t('analysisCenter-dxlKo6r7hXzq')}</div>
        <div className="dropStyle" ref={dropYValue} style={{ ...yValueStyle }}>
          {yvalueList.length > 0 ? (
            yvalueList.map((n) => {
              const name = `${n.name}${n.type !== 'METRICS' && n.function ? `(${functionObj[n.function]})` : ''}`;
              return (
                <div key={n.key} className="tagStyle">
                  <div title={name} className="nameStyle">
                    {name}
                  </div>
                  <div className="toolbarStyle">{moreFunction('yvalueList', n)}</div>
                </div>
              );
            })
          ) : (
            <div>{t('analysisCenter-f4dcomsIYLZt')}</div>
          )}
        </div>
        <div className="dropTitle">{t('analysisCenter-iizhHROvEst3')}</div>
        <div className="dropStyle" ref={dropValue} style={{ ...metricStyle }}>
          {valueList.length > 0 ? (
            valueList.map((n) => {
              const name = `${n.name}${n.type !== 'METRICS' && n.function ? `(${functionObj[n.function]})` : ''}`;
              return (
                <div key={n.key} className="tagStyle">
                  <div title={name} className="nameStyle">
                    {name}
                  </div>
                  <div className="toolbarStyle">{moreFunction('valueList', n)}</div>
                </div>
              );
            })
          ) : (
            <div>{t('analysisCenter-f4dcomsIYLZt')}</div>
          )}
        </div>
      </>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    JSON.stringify(xvalueList),
    JSON.stringify(yvalueList),
    JSON.stringify(legendList),
    JSON.stringify(valueList),
    configData,
    dispatch,
    dropLegend,
    dropXValue,
    dropYValue,
    dropValue,
    filterData,
    functionObj,
    legendStyle,
    metricStyle,
    xValueStyle,
    yValueStyle,
    selectedChart
  ]);

  return <div className="tableFillStyle">{fillComponent}</div>;
};

export default FillBubble;
