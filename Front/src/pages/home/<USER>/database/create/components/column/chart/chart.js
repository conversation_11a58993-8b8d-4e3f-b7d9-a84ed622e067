import { Column } from '@ant-design/charts';
import { Empty, Spin } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useMemo } from 'react';
import thousands from 'utils/thousands';
import { t } from 'utils/translation';

export default ({ dataSource: { rowList, axis, lineResults }, loading }) => {
  const formatValue = (v, name) => {
    let result;
    let str;
    lineResults &&
      lineResults.forEach((item) => {
        if (name === item.name || name.includes(item.name)) {
          const { numberConfig = {} } = item;
          if (typeof v === 'number') {
            if (numberConfig?.format === 'PERCENT') {
              result = `${(v * 100).toFixed(numberConfig?.decimalSeparator || 0)}`;
              str = `${name}(${t('analysisCenter-Nh6nq7sFmZpF')})`;
            } else {
              if (numberConfig?.thousandsSeparator) {
                result = thousands(v.toFixed(numberConfig?.decimalSeparator || 0));
              } else {
                result = v.toFixed(numberConfig?.decimalSeparator || 0);
              }
              str = name;
            }
          } else {
            result = v;
          }
        }
      });
    return [result || v, str || name];
  };
  const result = useMemo(() => {
    if (axis) {
      const config = {
        autoFit: true,
        data: rowList || [],
        // padding: 'auto',
        xField: axis?.xfield?.dataIndex,
        xAxis: {
          visible: true,
          title: {
            text: axis?.xfield?.title
          },
          label: {
            formatter: (data) => {
              if (
                axis?.xfield?.dataType === 'DATE' ||
                axis?.xfield?.dataType === 'DATETIME' ||
                axis?.xfield?.dataType === 'TIMESTAMP' ||
                axis?.xfield?.dataType === 'HIVE_DATE' ||
                axis?.xfield?.dataType === 'HIVE_TIMESTAMP'
              ) {
                if (axis?.xfield?.term === 'MONTH') {
                  return dayjs(data).format('YYYY-MM');
                } else if (axis?.xfield?.term === 'WEEK') {
                  return dayjs(data).format('MM-DD');
                } else if (axis?.xfield?.term === 'DAY') {
                  return dayjs(data).format('MM-DD');
                } else if (axis?.xfield?.term === 'HOUR') {
                  return dayjs(data).format('MM-DD HH');
                } else if (axis?.xfield?.term === 'MINUTE') {
                  return dayjs(data).format('MM-DD HH:mm');
                }
              }
              return data;
            }
          }
        },
        yField: axis?.yfield?.dataIndex,
        yAxis: {
          visible: true,
          min: 0,
          title: {
            text: axis?.yfield?.title
          },
          label: {
            formatter: (data) => {
              if (
                axis?.yfield?.dataType === 'DATE' ||
                axis?.yfield?.dataType === 'DATETIME' ||
                axis?.yfield?.dataType === 'TIMESTAMP' ||
                axis?.yfield?.dataType === 'HIVE_DATE' ||
                axis?.yfield?.dataType === 'HIVE_TIMESTAMP'
              ) {
                if (axis?.xfield?.term === 'MONTH') {
                  return dayjs(data).format('YYYY-MM');
                } else if (axis?.yfield?.term === 'WEEK') {
                  return dayjs(data).format('MM-DD');
                } else if (axis?.yfield?.term === 'DAY') {
                  return dayjs(data).format('MM-DD');
                } else if (axis?.yfield?.term === 'HOUR') {
                  return dayjs(data).format('MM-DD HH');
                } else if (axis?.yfield?.term === 'MINUTE') {
                  return dayjs(data).format('MM-DD HH:mm');
                }
              }
              return data;
            }
          }
        },
        isGroup: true,
        // color: ['#ae331b', '#1a6179'],
        // seriesField: axis?.stackField?.dataIndex || axis?.yfield?.dataIndex,
        groupField: 'METRIC_NAME',
        seriesField: 'displayName',
        isStack: true,
        tooltip: {
          enterable: true,
          formatter: (data) => {
            const value = axis?.yfield?.dataIndex;
            // return { name: name ? data['displayName'] : title, value: data[value] };
            return {
              name: formatValue(data[value], data.displayName)[1],
              value: formatValue(data[value], data.displayName)[0]
            };
          }
        },
        slider: {
          start: 0,
          end: 1,
          padding: [0, 24, 0, 0],
          handlerStyle: {
            width: 8
          }
        },
        maxColumnWidth: 30,
        legend: {
          visible: !!axis?.stackField?.dataIndex,
          flipPage: true,
          radio: false,
          position: 'bottom'
        },
        style: { height: '100%', overflow: 'hidden' }
      };
      if (_.isArray(rowList) && rowList.length === 0) {
        return (
          <div className="mainChartSpin">
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          </div>
        );
      } else if (rowList === null) {
        return (
          <div className="mainChartSpin">
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={<span>{t('analysisCenter-2aKsgUcrUHpl')}</span>} />
          </div>
        );
      }
      return <Column {...config} />;
    }
  }, [rowList, axis]);
  return (
    <div style={{ width: '100%', height: '100%', overflow: 'auto', padding: 5 }}>
      {loading ? (
        <div className="mainChartSpin">
          <Spin spinning />{' '}
        </div>
      ) : (
        result
      )}
    </div>
  );
};
