import React, { useContext } from 'react';
import { DataContext } from '../../../context';
import LineChart from './chart';
import './index.scss';

const Chart = () => {
  const {
    state: { responseData, selectedChart, loading }
  } = useContext(DataContext);

  return (
    <div style={{ padding: 10, width: '100%', height: '100%' }}>
      <LineChart dataSource={responseData[selectedChart]} loading={loading} />
    </div>
  );
};

export default Chart;
