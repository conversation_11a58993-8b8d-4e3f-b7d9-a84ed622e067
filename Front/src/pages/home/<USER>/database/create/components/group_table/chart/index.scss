@import "~react-grid-layout/css/styles.css";
@import "~react-resizable/css/styles.css";

.mainChartSpin {
  width: 100%;
  height: calc(100% - 50px);
  display: flex;
  justify-content: center;
  align-items: center;
}

.chatTableWrapper {
  .ant-table-content {
    font-size: 12px;
  }

  .ant-table-cell {
    text-align: center;
  }

  .ant-table-summary {
    background-color: #FAFAFA;
  }

  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    /**/
  }
}

.chatTableWrapper::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  /**/
}

.chatTableWrapper_header {
  width: 100%;
  padding: 16px 0px 16px 0px;
  height: 64px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  & .ant-radio-button-wrapper:first-child {
    border-radius: 6px 0 0 6px;
  }

  & .ant-radio-button-wrapper:last-child {
    border-radius: 0px 6px 6px 0px;
  }

  & .ant-btn {
    border-radius: 6px;
  }
}

.ant-dropdown {
  & .items {
    display: flex;
    align-items: center;
    height: 100%;
    font-size: 14px;
    font-weight: 400;
    font-family: 'PingFang SC';

    img {
      margin-right: 5px;
    }
  }
}