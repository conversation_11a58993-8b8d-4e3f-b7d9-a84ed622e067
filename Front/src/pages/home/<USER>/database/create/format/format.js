import { Checkbox, InputNumber, message, Modal, Radio } from 'antd';
import React, { useContext, useState } from 'react';
import { t } from 'utils/translation';
import { DataContext } from '../context';
import './format.scss';

export default () => {
  const {
    state: { configData, selectedChart, formatValue },
    dispatch
  } = useContext(DataContext);
  const [format, setFormat] = useState(formatValue?.numberConfig?.format ? formatValue.numberConfig.format : 'NUMBER');
  const [decimalNumber, setDecimalNumber] = useState(
    formatValue?.numberConfig?.format === 'NUMBER' ? formatValue.numberConfig.decimalSeparator : 0
  );
  const [decimalPercent, setDecimalPercent] = useState(
    formatValue?.numberConfig?.format === 'PERCENT' ? formatValue.numberConfig.decimalSeparator : undefined
  );
  const [thousandsSeparator, setThousandsSeparator] = useState(
    formatValue?.numberConfig?.format === 'NUMBER' ? formatValue.numberConfig.thousandsSeparator : true
  );

  const handleOk = () => {
    const data = { format };
    if (format === 'NUMBER') {
      if (!decimalNumber && decimalNumber !== 0) {
        message.error(t('analysisCenter-esjKieWfQTQn'));
        return;
      }
      data.decimalSeparator = decimalNumber;
      data.thousandsSeparator = thousandsSeparator;
    } else if (format === 'PERCENT') {
      if (!decimalPercent && decimalPercent !== 0) {
        message.error(t('analysisCenter-1K7ealgL2Qtn'));
        return;
      }
      data.decimalSeparator = decimalPercent;
    } else {
      message.error(t('analysisCenter-NLv35toAGI6u'));
      return;
    }

    const info = configData[selectedChart][formatValue.target].find((n) => n.filterId === formatValue.filterId);
    info.numberConfig = data;
    dispatch({ formatVisible: false });
    message.success(t('analysisCenter-vdZFmDptMn55'));
  };
  return (
    <Modal
      title={t('analysisCenter-aHyS75omz4u3')}
      open
      onOk={handleOk}
      onCancel={() => dispatch({ formatVisible: false })}
      className="formatComponent"
    >
      <Radio.Group value={format} onChange={(e) => setFormat(e.target.value)}>
        <Radio className="radioStyle" value="NUMBER">
          {t('analysisCenter-0wvH2S6Zjqr3')}
          <div className="content">
            <span>{t('analysisCenter-1HzpQRmMWXEq')}</span>
            <InputNumber
              min={0}
              precision={0}
              max={8}
              value={decimalNumber}
              onChange={setDecimalNumber}
              className="inputNumberStyle"
            />
            <Checkbox onChange={(e) => setThousandsSeparator(e.target.checked)} checked={thousandsSeparator}>
              {t('analysisCenter-fNC43QSLHYME')}
            </Checkbox>
          </div>
        </Radio>
        <Radio className="radioStyle" value="PERCENT">
          {t('analysisCenter-2YcILrWQ7Ym0')}
          <div className="content">
            <span>{t('analysisCenter-1HzpQRmMWXEq')}</span>
            <InputNumber
              min={0}
              max={8}
              precision={0}
              value={decimalPercent}
              onChange={setDecimalPercent}
              className="inputNumberStyle"
            />
          </div>
        </Radio>
      </Radio.Group>
    </Modal>
  );
};
