import dataModelService from '@/service/dataModelService';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { CheckCircleOutlined, MoreOutlined } from '@ant-design/icons';
import { Card, Col, Divider, Dropdown, Input, message, Modal, Row, Select, Spin, Tooltip } from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import AnalysisCenterService from 'service/analysisCenterService';
import ScenarioService from 'service/ScenarioService';
import { t } from 'utils/translation';
import '../index.scss';

const { Search } = Input;
const { Option } = Select;

const EditChart = (props) => {
  const {
    visible,
    action,
    scenarioId,
    campaignId,
    boardChart,
    hasWidgets,
    version,
    id,
    createInfo,
    modelId,
    dispatch,
    setPropsInfo
  } = props;
  const [loading, setLoading] = useState(false);
  const [businessTable, setBusinessTable] = useState(1);
  const [searchValue, setSearchValue] = useState('');
  const [entityList, setEntityList] = useState([]);
  const [entityId, setEntityId] = useState(null);
  const [scenarioList, setScenarioList] = useState([]);
  const [businessTableList, setBusinessTableList] = useState([]);
  const [nobusinessTableList, setNobusinessTableList] = useState([]);
  const [scenarioCache, setScenarioCache] = useState(undefined);
  const [campaign_id, setCampaign_id] = useState(undefined);
  const [boardChartData, setBoardChartData] = useState(undefined);
  const [dataModelList, setDataModelList] = useState([]);
  const [dataModelId, setDataModelId] = useState('');
  const [scenarioIdCur, setScenarioIdCur] = useState(null);
  const [tableId, setTableId] = useState(null);
  const [allDataModelList, setAllDataModelList] = useState([]);
  const [businessId, setBusinessId] = useState(null);
  const [tableTitle, setTableTitle] = useState(t('analysisCenter-6ZIpoaJv9Wxn'));
  useEffect(() => {
    (async () => {
      try {
        setBoardChartData(boardChart);
        setScenarioCache(scenarioId);
        setCampaign_id(campaignId);
        setLoading(true);
        if (createInfo) {
          setDataModelId(modelId);
          setBusinessTable(createInfo?.isBusinessTable);
          setTableId(createInfo?.tableId);
          setEntityId(createInfo?.entityId);
        }
        const _scenarioList = await ScenarioService.scenarioList([]);
        const _businessTableList = await AnalysisCenterService.getTableList({
          businessTable: true
        });
        const _nobusinessTableList = await AnalysisCenterService.getTableList({
          businessTable: false
        });

        const res = await dataModelService.getBusinessEntity([
          { operator: 'EQ', propertyName: 'projectId', value: localStorage.getItem('projectId') }
        ]);
        const defaultId = !createInfo?.scenario?.id
          ? scenarioId || _scenarioList.find((item) => item.isDefault)?.id
          : createInfo?.scenario?.id;
        try {
          const dataModelList = await AnalysisCenterService.dataModelList([
            {
              operator: 'EQ',
              propertyName: 'projectId',
              value: localStorage.getItem('projectId')
            }
          ]);
          setDataModelList(filterData(dataModelList, defaultId, _scenarioList));
          setAllDataModelList(filterData(dataModelList, defaultId, _scenarioList, 'all'));
        } catch (error) {
          setDataModelList([]);
        }

        setScenarioIdCur(defaultId);
        setBusinessId(defaultId);
        setEntityList(res);
        setBusinessTableList(_businessTableList);
        setNobusinessTableList(_nobusinessTableList);
        setScenarioList(_scenarioList.sort((a, b) => a.orderNum - b.orderNum));
        setLoading(false);
      } catch (error) {
        setLoading(false);
      }
    })();
  }, []);
  // 过滤数据模型列表
  const filterData = (data, defaultId, list, flag) => {
    const filterDataMadelList = _.map(data, (item) => {
      item.dataModelFactTableInfos =
        item?.dataModelFactTableInfos &&
        _.map(item?.dataModelFactTableInfos, (n) => {
          return {
            existence: n.scenarioId === defaultId,
            ...n,
            name: list.find((item) => item.id === n.scenarioId)?.name || ''
          };
        });
      return { ...item, tooltipTitle: getIdName(item.dataModelFactTableInfos || [], 'onther') };
    });
    const filterData =
      flag !== 'all'
        ? filterDataMadelList.filter(
            (item) => item.dataModelFactTableInfos && item.dataModelFactTableInfos.some((value) => value.existence)
          )
        : filterDataMadelList;
    if (flag === 'all') return filterDataMadelList;
    return filterData;
  };
  const okHandle = () => {
    try {
      const data = {
        isBusinessTable: businessTable,
        name: t('analysisCenter-oGmwKQ7DCicn'),
        extendChartType: campaignId || scenarioId ? 'CREATE_CAMPAIGN_CHART' : undefined,
        dateRange2: [
          { type: 'RELATIVE', timeTerm: 'DAY', isPast: true, times: 30 },
          { type: 'NOW', timeTerm: 'DAY', isPast: true }
        ],
        deptId: window.getDeptId(),
        projectId: localStorage.getItem('projectId'),
        tableTitle
      };
      if (businessTable === 1) {
        if (!businessId) {
          message.error(t('analysisCenter-bKFEeObsOdH1'));
          return;
        }
        data.tables = businessTableList;
        data.scenario = businessId ? scenarioList.filter((n) => n.id === businessId)[0] : { id: scenarioId };
        data.scenario.name = scenarioList.filter((n) => n.id === businessId)[0]?.name;
      } else if (businessTable === 2) {
        if (!entityId) {
          message.error(t('analysisCenter-hIqvwkE5hDGe'));
          return;
        }
        const factTable = _.filter(dataModelList, (item) => item.id === dataModelId)?.[0]?.factTable;
        const childTableList = _.filter(dataModelList, (item) => item.id === dataModelId)?.[0]?.childTableList || [];
        data.tables = [factTable, ...childTableList];
        if (scenarioIdCur) {
          data.scenario = scenarioList.filter((n) => n.id === scenarioIdCur)[0];
          data.scenario.name = scenarioList.filter((n) => n.id === scenarioIdCur)[0]?.name;
        }
        data.entityId = entityId;
      } else {
        data.tables = nobusinessTableList.filter((n) => n.id === tableId);
        data.tableId = tableId;
      }
      setLoading(true);
      localStorage.setItem('currentFlag', 'create');
      localStorage.setItem('createInfo', JSON.stringify(data));
      if (createInfo) {
        action();
        dispatch && dispatch({ info: data, propsDataModelId: dataModelId });
        setPropsInfo(data);
        return;
      }
      props.history.push(`/aimarketer/home/<USER>/database/create`, {
        scenarioId: scenarioCache,
        campaignId: campaign_id,
        boardChart: boardChartData,
        hasWidgets,
        version,
        id,
        currentFlag: 'create',
        createInfo: data,
        dataModelId
      });
      // props.history.push({ pathname: `/aimarketer/home/<USER>/database/edit/${res.id}?fullScreen=${true}`, state: { scenario_id: scenarioCache, campaignId: campaign_id } });
      // props.history.push(`/aimarketer/home/<USER>/database/edit/${res.id}?fullScreen=${true}`);
    } catch (error) {
      setLoading(false);
      message.error(error.message);
    }
  };
  const preView = (id, type) => {
    if (type === 'dataModel') {
      props.history.push(`/aimarketer/home/<USER>/dataModel/detail/${id}`);
    } else {
      props.history.push(`/aimarketer/home/<USER>/subrouter/tablemgr`, {
        id
      });
    }
  };
  const changeModel = (id) => {
    if (!id) {
      setDataModelList(allDataModelList);
    } else {
      const filterData = allDataModelList.filter(
        (item) => item.dataModelFactTableInfos && item.dataModelFactTableInfos.some((value) => value.scenarioId === id)
      );
      setDataModelList(filterData);
    }
    setScenarioIdCur(id);
  };
  const getIdName = (data, type) => {
    let str;
    return (
      data &&
      data.map((item, index) => {
        str = `${item.name}${index !== data.length - 1 ? '、' : ''}`;
        return type === 'show' ? (
          <span>
            {item.name}
            {index !== data.length - 1 ? '、' : ''}
          </span>
        ) : (
          str
        );
      })
    );
  };
  return (
    <Modal
      title={t('analysisCenter-aHyS75omz4u3')}
      open={visible}
      width={1100}
      onOk={okHandle}
      maskClosable={false}
      onCancel={action}
      confirmLoading={loading}
      className="addChart_modal"
    >
      <Row gutter={16} className="search_row">
        <Col span={24}>
          <Search
            className="search"
            onChange={(e) => setSearchValue(e.target.value)}
            value={searchValue}
            placeholder={t('analysisCenter-1HzpQRmMWXEq')}
          />
        </Col>
      </Row>
      <div className="container">
        <Spin spinning={loading}>
          {businessTableList?.length && t('analysisCenter-aHyS75omz4u3').includes(searchValue) ? (
            <Row gutter={16}>
              <Col span={24}>
                <div className="header">
                  <span className="title">{t('analysisCenter-0wvH2S6Zjqr3')}</span>
                  <Select
                    style={{ marginRight: '8px' }}
                    allowClear
                    value={businessId}
                    onChange={(e) => setBusinessId(e)}
                    showSearch
                    optionFilterProp="children"
                    placeholder={t('analysisCenter-9ZqKQr09fYgr')}
                    bordered={false}
                  >
                    {scenarioList.map((item) => (
                      <Option key={item.id} value={item.id}>
                        {item.name}[{item.code}]
                      </Option>
                    ))}
                  </Select>
                </div>
                <div className="item-list">
                  {businessTableList?.length ? (
                    <div className="item">
                      <Card
                        onClick={() => {
                          setBusinessTable(1);
                          setTableTitle(t('analysisCenter-6ZIpoaJv9Wxn'));
                        }}
                        style={{
                          borderColor: businessTable === 1 ? 'var(--ant-primary-color)' : undefined
                        }}
                        hoverable
                      >
                        <div className="card-header">
                          <div className="card-title">{t('analysisCenter-6ZIpoaJv9Wxn')}</div>
                          <CheckCircleOutlined
                            style={{
                              fontSize: 14,
                              color: 'var(--ant-primary-color)',
                              marginRight: 10,
                              visibility: businessTable === 1 ? 'visible' : 'hidden'
                            }}
                          />
                        </div>
                        <Divider />
                        <div className="card-cotent">
                          <span>{t('analysisCenter-O3IKh7U2rZPN')}</span>
                        </div>
                        <Divider />
                        <div className="card-footer">
                          {scenarioList.filter((item) => businessId === item.id)?.[0]?.name || ''}
                        </div>
                      </Card>
                    </div>
                  ) : null}
                </div>
              </Col>
            </Row>
          ) : null}
          {searchValue === '' ||
          dataModelList.filter((w) => w.modelDisplayName.indexOf(searchValue) > -1)?.length > 0 ? (
            <Row gutter={16}>
              <Col span={24}>
                <div className="header">
                  <span className="title">{t('analysisCenter-f97BKMlYHv09')}</span>
                  <div className="rightFilter" style={{ width: 'auto' }}>
                    <Select
                      style={{ marginRight: '8px' }}
                      allowClear
                      value={scenarioIdCur}
                      onChange={changeModel}
                      showSearch
                      optionFilterProp="children"
                      placeholder={t('analysisCenter-9ZqKQr09fYgr')}
                      bordered={false}
                    >
                      <Option key={undefined} value={undefined}>
                        {t('analysisCenter-mYBOmdEr3Dgg')}[all]
                      </Option>
                      {scenarioList.map((item) => (
                        <Option key={item.id} value={item.id}>
                          {item.name}[{item.code}]
                        </Option>
                      ))}
                    </Select>
                    <Select
                      placeholder={t('analysisCenter-RpX3JGeDbUWD')}
                      allowClear
                      showSearch
                      optionFilterProp="children"
                      dropdownStyle={{ borderRadius: 6 }}
                      onChange={(e) => setEntityId(e)}
                      style={{ marginRight: '8px' }}
                      bordered={false}
                      value={createInfo?.entityId}
                    >
                      {entityList.map((item) => (
                        <Option key={item.id} value={item.id}>
                          {`${item.entityName}[${item.entityCode}]`}
                        </Option>
                      ))}
                    </Select>
                  </div>
                </div>
                <div className="item-list item-list-model">
                  {dataModelList
                    .filter((w) => w.modelDisplayName.indexOf(searchValue) > -1)
                    .map((n) => (
                      <div className="item">
                        <Card
                          onClick={(event) => {
                            event.preventDefault();
                            setBusinessTable(2);
                            setDataModelId(n?.id);
                            setTableTitle(n.modelDisplayName);
                          }}
                          style={{
                            borderColor:
                              businessTable === 2 && dataModelId === n?.id ? 'var(--ant-primary-color)' : undefined
                          }}
                          hoverable
                        >
                          <div className="card-header card-header-model">
                            <div className="card-title">
                              <Tooltip title={n?.modelDisplayName || ''}>
                                <span>{n?.modelDisplayName}</span>
                              </Tooltip>
                            </div>
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <CheckCircleOutlined
                                style={{
                                  fontSize: 14,
                                  color: 'var(--ant-primary-color)',
                                  marginRight: 10,
                                  visibility: businessTable === 2 && dataModelId === n?.id ? 'visible' : 'hidden'
                                }}
                              />
                              <Dropdown
                                placement="bottomRight"
                                getPopupContainer={(triggerNode) => triggerNode.parentNode}
                                menu={{
                                  items: [
                                    {
                                      label: (
                                        <span
                                          onClick={(event) => {
                                            event.preventDefault();
                                            preView(n.id, 'dataModel');
                                          }}
                                        >
                                          {t('analysisCenter-LdNMN7dNqjbB')}
                                        </span>
                                      ),
                                      key: 'edit'
                                    }
                                  ]
                                }}
                              >
                                <MoreOutlined />
                              </Dropdown>
                            </div>
                          </div>
                          <Divider />
                          <div className="card-cotent">
                            <Tooltip title={t('analysisCenter-pp1F0UrKqtZw')}>
                              <span>{t('analysisCenter-pp1F0UrKqtZw')}</span>
                            </Tooltip>
                          </div>
                          <Divider />
                          <div className="card-footer">
                            <Tooltip title={n.tooltipTitle}>{getIdName(n.dataModelFactTableInfos, 'show')}</Tooltip>
                          </div>
                        </Card>
                      </div>
                    ))}
                </div>
              </Col>
            </Row>
          ) : null}
          {nobusinessTableList.filter((w) => w.displayName.indexOf(searchValue) > -1)?.length > 0 ? (
            <Row gutter={16}>
              <Col span={24}>
                <div className="header">
                  <span className="title">{t('analysisCenter-rfSUfi91l8H3')}</span>
                </div>
                <div className="item-list item-list-model">
                  {nobusinessTableList
                    .filter((w) => w.displayName.indexOf(searchValue) > -1)
                    .map((n) => (
                      <div className="item">
                        <Card
                          onClick={(event) => {
                            event.preventDefault();
                            setBusinessTable(0);
                            setTableId(n?.id);
                            setTableTitle(n?.displayName);
                          }}
                          style={{
                            borderColor:
                              businessTable === 0 && tableId === n?.id ? 'var(--ant-primary-color)' : undefined
                          }}
                          hoverable
                        >
                          <div className="card-header card-header-model">
                            <div className="card-title">
                              <Tooltip title={n?.displayName}>
                                <span>{n?.displayName}</span>
                              </Tooltip>
                            </div>
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <CheckCircleOutlined
                                style={{
                                  fontSize: 14,
                                  color: 'var(--ant-primary-color)',
                                  marginRight: 10,
                                  visibility: businessTable === 0 && tableId === n?.id ? 'visible' : 'hidden'
                                }}
                              />
                              <Dropdown
                                placement="bottomRight"
                                getPopupContainer={(triggerNode) => triggerNode.parentNode}
                                menu={{
                                  items: [
                                    {
                                      label: (
                                        <span
                                          onClick={(event) => {
                                            event.preventDefault();
                                            preView(n.id, 'dataTable');
                                          }}
                                        >
                                          {t('analysisCenter-LdNMN7dNqjbB')}
                                        </span>
                                      ),
                                      key: 'edit'
                                    }
                                  ]
                                }}
                              >
                                <MoreOutlined />
                              </Dropdown>
                            </div>
                          </div>
                          <Divider />
                          <div className="card-cotent">
                            <Tooltip
                              title={`${t('analysisCenter-pp1F0UrKqtZw')}${n?.memo || t('analysisCenter-pp1F0UrKqtZw-memo')}`}
                            >
                              <span>{`${t('analysisCenter-pp1F0UrKqtZw')}${n?.memo || t('analysisCenter-pp1F0UrKqtZw-memo')}`}</span>
                            </Tooltip>
                          </div>
                        </Card>
                      </div>
                    ))}
                </div>
              </Col>
            </Row>
          ) : null}
        </Spin>
      </div>
    </Modal>
  );
};

export default Form.create()(EditChart);
