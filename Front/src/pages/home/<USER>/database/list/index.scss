// @import 'assets/css/variable.scss';

.databaseList {
  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // background-color: #fff;
    margin: 0 -24px 24px;
    padding: 24px 24px 0;
    // box-shadow: 0 1px 2px 0 rgba(220, 226, 230, 0.8), 0 1px 8px 0 rgba(220, 226, 230, 0.59);

    .ant-btn-default {
      border-color: #fff !important;
    }

    .ant-btn-default:hover {
      border-color: $border_hover !important;
    }

    .ant-btn-default:focus {
      border-color: $border_hover !important;
    }

    h1 {
      font-size: 20px !important;
      font-weight: bold;
      margin: 0;
    }

    .btnGroup {
      button {
        border-radius: 6px;

        &:first-child {
          margin-right: 16px;
        }
      }
    }
  }

  .filterWrap {
    display: flex;
    justify-content: space-between;
    margin-bottom: 28px;

    .left {
      display: flex;

      .filterItem {
        cursor: pointer;
        margin-right: 32px;
        font-size: 18px;
        padding: 0;
        color: rgba(0, 0, 0, 0.65);
      }

      .active {
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }

  .main {
    // background-color: #fff;
    margin-top: 10px;
    min-height: 800px;

    .lgWrap {
      .pieWrap {
        // justify-content: center !important;

        .pieItem {
          min-width: 50% !important;
        }
      }
    }

    .smWrap {
      .pieWrap {
        justify-content: initial !important;

        .pieItem {
          min-width: 100% !important;
        }
      }
    }

    .toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 60px;
      background-color: white;

      .left {
        width: calc(100% - 80px);

        .title {
          font-size: 16px;
          padding-left: 10px;
          font-weight: bold;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .time {
          font-size: 12px;
          padding-left: 10px;
          color: rgba(0, 0, 0, 0.45);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .right {
        display: none;
        padding-right: 10px;
        width: 60px;
      }
    }

    .toolbar:hover {
      .right {
        display: block;
      }
    }

    .colItem:hover {
      box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.2);

      .right {
        display: block;
      }
    }
  }


}

.addChart_modal {
  .ant-modal-content {
    border-radius: 8px !important;
  }

  .ant-modal-footer {
    button {
      border-radius: 6px !important;
    }
  }

  .search_row {
    margin-bottom: 20px;
    padding: 0px 16px;

    .search {
      & .ant-input {
        // border-right: none;
        border-radius: 6px 0 0 6px;
      }

      & .ant-input-group-addon {
        button {
          // border-left: none;
          border-radius: 0 6px 6px 0 !important;
        }
      }
    }
  }

  .container {
    overflow-y: auto;
    padding-right: 12px;
    box-sizing: border-box;
    padding-left: 12px;

    .ant-row {
      margin-bottom: 20px;

      .ant-col {
        .header {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 10px;

          .title {
            font-size: 16px;
            font-weight: 600;
          }
        }


        .item-list> :not(:nth-child(4n)) {
          margin-right: 1.3%;
        }

        .item-list> :nth-child(n+5) {
          margin-top: 20px;
        }

        .item-list {
          display: flex;
          flex-wrap: wrap;

          .item {
            width: 24%;
            display: flex;
            justify-content: flex-start;

            .ant-card {
              border-radius: 6px;
              width: 100%;

              .ant-card-body {
                padding: 0px;

                .card-header-model {
                  padding-right: 8px !important;
                }

                .card-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 12px 20px;

                  .card-title {
                    font-size: 16px;
                    font-weight: 600;
                    max-width: 140px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }
                }

                .ant-divider {
                  margin: 0;
                }

                .card-cotent {
                  padding: 12px 24px;
                  font-size: 14px;
                  font-weight: 400;

                  span {
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    // 样式回退 兼容IE浏览器超两行使用省略号代替
                    line-clamp: 2;
                    box-orient: vertical;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }
                }

                .card-footer {
                  height: 40px;
                  display: flex;
                  align-items: center;
                  padding-left: 24px;
                  font-size: 14px;
                  font-weight: 400;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  color: rgba(0, 0, 0, 0.45);
                }
              }

            }
          }
        }

      }
    }
  }

  .ant-modal-body {
    height: calc(100vh - 260px);
    overflow: auto;
  }
}


.boardChartOverDropdown {
  .boardSubMenuWrap {
    display: flex;

    .menuIconWrap {
      font-size: 21px;
      color: $primary_color;
      margin-right: 10px;
    }

    .menuDesc {
      color: rgba(0, 0, 0, 0.45);
      margin: 4px 0;
      font-size: 12px;
    }
  }

  .ant-dropdown-menu {
    width: 264px;
    border-radius: 6px;
    padding: 8px;

    .ant-dropdown-menu-item {
      border-radius: 6px;

      :hover {
        .title {
          transition: 0.3s all;
          color: $primary_color;
        }
      }
    }

    .ant-dropdown-menu-item:hover {}
  }
}