export default {
  cn: {
  "dataCenter-9riHLUqxjEnV": "表信息或批次信息不完整",
  "dataCenter-JuisJVXovj0Y": "数据表管理",
  "dataCenter-ZhLpRfoyOsAT": "数据导入",
  "dataCenter-P6L5aZwmFDV6": "信息不完整，请重试",
  "dataCenter-3rqkwcCV10Mw": "请先上传文件",
  "dataCenter-CepRluOdWt7c": "请至少选择一个导入字段",
  "dataCenter-DpSGavAtjxEp": "过滤规则不合法",
  "dataCenter-qr4vT37MnT8N": "计算失败，请重试",
  "dataCenter-35LM5MDNMxQ3": "保存成功",
  "dataCenter-OO1nBlRc6M9R": "是否确认取消数据导入，当前步骤数据会丢失哦",
  "dataCenter-L8eOxemILqy4": "否",
  "dataCenter-fHYaFdUbKIoI": "是",
  "dataCenter-YbaFlpg7wxpa": "数据导入向导",
  "dataCenter-8p6hCcZMzNHz": "上传文件",
  "dataCenter-abkFIpm65dOy": "设置规则",
  "dataCenter-yTI5TqHQUqQr": "确认信息",
  "dataCenter-AAMhGIkf6cWL": "基本信息",
  "dataCenter-eBNQ6eyDyLoR": "下载表结构模板",
  "dataCenter-WlbdAI4QXviM": "已选择数据表",
  "dataCenter-DqNWyOBfnBCo": "表业务类型",
  "dataCenter-zgZBesjI7cyd": "主键字段",
  "dataCenter-EnXDeiFfFm4f": "分区字段",
  "dataCenter-WqJ1pY8Klmsz": "时间序列字段",
  "dataCenter-vT6gT1Hrjrye": "源文件数据行数：",
  "dataCenter-4IBejRNEjCF5": "过滤数据行数：",
  "dataCenter-qe7Oa94obZrQ": "过滤后数据行数：",
  "dataCenter-V05TxuOh4okF": "重复数据行数：",
  "dataCenter-6ZVqZd6Bad7R": "有效数据行数：",
  "dataCenter-AqsfqgTbnEdh": "字段匹配",
  "dataCenter-cHLbokmXNSu7": "数据预览",
  "dataCenter-P0nIGEgamzJw": "取消",
  "dataCenter-cEroXNtaLMgc": "上一步",
  "dataCenter-51d62ghmJzRd": "下一步",
  "dataCenter-SqlmYH0wOZZO": "导入数据",
  "dataCenter-glXkBC71k5lb": "是否预设",
  "dataCenter-y9sjHLtBHsk1": "字段名称",
  "dataCenter-ORjlk1H0lJh9": "字段显示名",
  "dataCenter-3NmGA2LHofq7": "数据类型",
  "dataCenter-vX2TNIzG59L4": "上传的文件大于200M",
  "dataCenter-hx1Hti8F7wKX": "上传失败,请重试",
  "dataCenter-YbnOmDDfMfDJ": "上传成功",
  "dataCenter-lgBDCnk2VnIg": "上传文件设置",
  "dataCenter-YXZeSJd5u6a7": "最大支持200M，支持csv文件格式。可以上传CSV格式压缩后的zip文件。",
  "dataCenter-j0qSQy4eH8wa": "本地上传文件：",
  "dataCenter-c3okkCXEdusS": "上传文件",
  "dataCenter-9NRb4axZSvmQ": "选择分隔符",
  "dataCenter-rE7NKNIMWwkS": "请选择分隔符!",
  "dataCenter-D6dAdeaTqxLX": "逗号",
  "dataCenter-Hjv9gTeMtroH": "分号",
  "dataCenter-6azpyQgeWiet": "自定义",
  "dataCenter-OlMEVyyYvo3k": "请填写分隔符!",
  "dataCenter-tOBpuH9kve5C": "最大长度限制为1位字符",
  "dataCenter-VwDlZvDp8RKz": "原始字符集",
  "dataCenter-FTe5I9KS6Urv": "请选择!",
  "dataCenter-iupAiPeJElcd": "导入起始行",
  "dataCenter-5M6JbHWmc6ws": "请输入",
  "dataCenter-ySb3vIk483NB": "如果数据来源为其他时区请调整选择",
  "dataCenter-YDKRaLh1DCsk": "请输入",
  "dataCenter-2LynIVk8s6yp": "新西兰、奥克兰、惠灵顿",
  "dataCenter-8soMjzpEn6OY": "所罗门群岛、库页岛",
  "dataCenter-dApcyrhRmTL9": "澳大利亚、海参崴、关岛",
  "dataCenter-HdmvqacyKDLB": "日本、韩国",
  "dataCenter-nhfy7wsjriTk": "北京，重庆，香港特别行政区，乌鲁木齐",
  "dataCenter-zBuXqJdo1FHJ": "泰国、曼谷、河内、雅加达",
  "dataCenter-HatrdPUlaG79": "新西伯利亚、吉尔吉斯斯坦、孟加拉国、不丹",
  "dataCenter-H5NIYVPl5xTh": "印度、孟买、新德里、班加罗尔、加尔各答",
  "dataCenter-FM40C9zdPX60": "巴基斯坦、土库曼斯坦、马尔代夫",
  "dataCenter-yAvY4wrH06dC": "阿布扎比、第比利斯、迪拜、毛里求斯、路易港",
  "dataCenter-iSqu4V1MT5Ym": "伊朗标准时间、德黑兰、马什哈德",
  "dataCenter-5ff2ePZCy5hN": "非洲东部、俄罗斯、巴格达、科威特、莫斯科、圣彼得堡",
  "dataCenter-BtcoVzjYlsti": "土耳其、以色列、南非、伊斯坦布尔、开罗、雅典",
  "dataCenter-8BoazYzBYsW7": "德国、爱尔兰、法国、意大利、西班牙",
  "dataCenter-CNIDnUrfmgJw": "协调世界时、格林威治时间",
  "dataCenter-ymfuuhi9yZnO": "佛得角、亚速尔群岛",
  "dataCenter-nAvnBdcYoEvI": "南乔治亚岛、费尔南多-迪诺罗尼亚岛",
  "dataCenter-HgJu2qeUltCV": "巴西、乌拉圭、阿根廷",
  "dataCenter-AajAh8udQ8U3": "委内瑞拉、玻利维亚、巴拉圭、智利",
  "dataCenter-1vBhBeJSwgCg": "东部时间(美国和加拿大)",
  "dataCenter-mts8o9feBR46": "美国和加拿大中部时间",
  "dataCenter-YGyfZpQ02pbf": "山地时间",
  "dataCenter-JJcapENbl7lF": "太平洋时间",
  "dataCenter-xyH3Z2eroCA7": "阿拉斯加",
  "dataCenter-WtL5anbNVtUH": "夏威夷",
  "dataCenter-xkuEfPJL9WIy": "萨摩亚、纽埃岛",
  "dataCenter-9SIVJhxc8Sle": "国际日期变更线以西",
  "dataCenter-KzB83fnjYkFG": "匹配上传文件字段",
  "dataCenter-BuUydSgZNnqQ": "不导入",
  "dataCenter-QoFJnRaRO6Lz": "请选择匹配字段!",
  "dataCenter-mY0Dh5O0DULB": "转换识别时间格式",
  "dataCenter-A5korprfeBnI": "请选择转换时间格式!",
  "dataCenter-Q3a2F4RY3M2o": "timestamp（毫秒）",
  "dataCenter-tEvsXSmS9eRr": "timestamp（秒）",
  "dataCenter-xRVHZsjUYeRl": "样例数据",
  "dataCenter-98O8etcgZiXx": "数据时区",
  "dataCenter-ZWwScyBJDcl7": "上传数据 - 跳过已有重复数据",
  "dataCenter-Y7RNiFsSKOBF": "根据数据表主键字段识别重复数据，将对重复数据进行忽略不导入，非重复数据将被导入",
  "dataCenter-kPvmnTECyDIy": "上传数据 - 更新已有重复数据",
  "dataCenter-MMZzq47QzJO5": "根据数据表主键字段识别重复数据，将对重复数据进行覆盖更新导入，非重复数据将被导入",
  "dataCenter-nTauCj6ZfzyR": "更新数据 - 仅更新已有重复数据",
  "dataCenter-e3QbkRignq3E": "根据数据表主键字段识别重复数据，将仅对重复数据进行覆盖更新导入，非重复数据将被忽略",
  "dataCenter-vrTDEpeSqPgx": "替换数据 - 删除历史数据",
  "dataCenter-RqjaDa5cZ69y": "将对历史数据进行删除，上传新数据。请谨慎操作！",
  "dataCenter-vvd90BRQNNmh": "数据上传更新规则",
  "dataCenter-GUa4puIFF4XC": "主键字段",
  "dataCenter-wwL8I21duNYh": "已有数据量超过",
  "dataCenter-O53sEQhJcj1J": "，不支持更新重复数据",
  "dataCenter-KiGye5r3jFzS": "请选择更新规则",
  "dataCenter-FlWHo3Wly8CA": "设置数据上传过滤条件",
  "dataCenter-h7t59SkqRNGB": "符合以上条件的数据上传，最多添加20个条件。",
  "dataCenter-KvVTTg0fKRpX": "设置上传文件分区覆盖规则",
  "dataCenter-3Fpwecqfqks2": "配置制定覆盖分区数据",
  "dataCenter-JYuKo4UWI2Mn": "请输入!",
  "dataCenter-nBp4HaWPCxg6": "请输入",
  "dataCenter-2ZdVZD0F1ugm": "请选择!",
  "dataCenter-GEgt3IM69fAr": "最大长度限制为64位字符",
  "dataCenter-Xb5O8VjTNQUp": "请选择",
  "dataCenter-8OHSc7qhHOQz": "下载分区后表结构模板",
  "dataCenter-c4JTfLIPgTKc": "上传文件数据行数：",
  "dataCenter-jlDih3HTsnMJ": "去重后有效数据行数：",
  "dataCenter-pEtTDD27zhRO": "更新规则",
  },
  en: {
   "dataCenter-9riHLUqxjEnV": "table info or batch info is not complete",
  "dataCenter-JuisJVXovj0Y": "Data Table Management",
  "dataCenter-ZhLpRfoyOsAT": "Data Import",
  "dataCenter-P6L5aZwmFDV6": "Information is incomplete, please try again",
  "dataCenter-3rqkwcCV10Mw": "Please upload the file first",
  "dataCenter-CepRluOdWt7c": "Please select at least one import field",
  "dataCenter-DpSGavAtjxEp": "Filter rules are illegal",
  "dataCenter-qr4vT37MnT8N": "Calculation failed, please try again",
  "dataCenter-35LM5MDNMxQ3": "Save successfully",
  "dataCenter-OO1nBlRc6M9R": "Are you sure to cancel data import, the current step data will be lost",
  "dataCenter-L8eOxemILqy4": "No",
  "dataCenter-fHYaFdUbKIoI": "Yes",
  "dataCenter-YbaFlpg7wxpa": "Data Import Wizard",
  "dataCenter-8p6hCcZMzNHz": "Upload File",
  "dataCenter-abkFIpm65dOy": "Set Rules",
  "dataCenter-yTI5TqHQUqQr": "Confirm Information",
  "dataCenter-AAMhGIkf6cWL": "Basic Information",
  "dataCenter-eBNQ6eyDyLoR": "Download Table Structure Template",
  "dataCenter-WlbdAI4QXviM": "Selected Data Table",
  "dataCenter-DqNWyOBfnBCo": "Table Business Type",
  "dataCenter-zgZBesjI7cyd": "Primary Key Field",
  "dataCenter-EnXDeiFfFm4f": "Partition Field",
  "dataCenter-WqJ1pY8Klmsz": "Time Series Field",
  "dataCenter-vT6gT1Hrjrye": "Source File Data Rows:",
  "dataCenter-4IBejRNEjCF5": "Filtered Data Rows:",
  "dataCenter-qe7Oa94obZrQ": "Filtered Data Rows:",
  "dataCenter-V05TxuOh4okF": "Duplicate Data Rows:",
  "dataCenter-6ZVqZd6Bad7R": "Valid Data Rows:",
  "dataCenter-AqsfqgTbnEdh": "Field Matching",
  "dataCenter-cHLbokmXNSu7": "Data Preview",
  "dataCenter-P0nIGEgamzJw": "Cancel",
  "dataCenter-cEroXNtaLMgc": "Previous Step",
  "dataCenter-51d62ghmJzRd": "Next Step",
  "dataCenter-SqlmYH0wOZZO": "Import Data",
  "dataCenter-glXkBC71k5lb": "Is Pre-set",
  "dataCenter-y9sjHLtBHsk1": "Field Name",
  "dataCenter-ORjlk1H0lJh9": "Field Display Name",
  "dataCenter-3NmGA2LHofq7": "Data Type",
  "dataCenter-vX2TNIzG59L4": "The uploaded file is greater than 200M",
  "dataCenter-hx1Hti8F7wKX": "Upload failed, please try again",
  "dataCenter-YbnOmDDfMfDJ": "Upload Success",
  "dataCenter-lgBDCnk2VnIg": "Upload File Settings",
  "dataCenter-YXZeSJd5u6a7": "The maximum size is 200M, and the supported file format is CSV. You can upload a CSV format compressed zip file.",
  "dataCenter-j0qSQy4eH8wa": "Local Upload File:",
  "dataCenter-c3okkCXEdusS": "Upload File",
  "dataCenter-9NRb4axZSvmQ": "Select Separator",
  "dataCenter-rE7NKNIMWwkS": "Please select separator!",
  "dataCenter-D6dAdeaTqxLX": "Comma",
  "dataCenter-Hjv9gTeMtroH": "Semicolon",
  "dataCenter-6azpyQgeWiet": "Custom",
  "dataCenter-OlMEVyyYvo3k": "Please fill in the separator!",
  "dataCenter-tOBpuH9kve5C": "The maximum length limit is 1 character",
  "dataCenter-VwDlZvDp8RKz": "Original Character Set",
  "dataCenter-FTe5I9KS6Urv": "Please select!",
  "dataCenter-iupAiPeJElcd": "Import Start Line",
  "dataCenter-5M6JbHWmc6ws": "Please enter",
  "dataCenter-ySb3vIk483NB": "If the data source is in another time zone, please adjust the selection",
  "dataCenter-YDKRaLh1DCsk": "Please enter",
  "dataCenter-2LynIVk8s6yp": "New Zealand, Auckland, Wellington",
  "dataCenter-8soMjzpEn6OY": "Solomon Islands, Kuril Islands",
  "dataCenter-dApcyrhRmTL9": "Australia, Vladivostok, Guam",
  "dataCenter-HdmvqacyKDLB": "Japan, Korea",
  "dataCenter-nhfy7wsjriTk": "Beijing, Chongqing, Hong Kong, Urumqi",
  "dataCenter-zBuXqJdo1FHJ": "Thailand, Bangkok, Hanoi, Jakarta",
  "dataCenter-HatrdPUlaG79": "Novosibirsk, Kyrgyzstan, Bangladesh, Bhutan",
  "dataCenter-H5NIYVPl5xTh": "India, Mumbai, New Delhi, Bangalore, Calcutta",
  "dataCenter-FM40C9zdPX60": "Pakistan, Turkmenistan, Maldives",
  "dataCenter-yAvY4wrH06dC": "Abu Dhabi, Tbilisi, Dubai, Mauritius, Port Louis",
  "dataCenter-iSqu4V1MT5Ym": "Iran Standard Time, Tehran, Mashhad",
  "dataCenter-5ff2ePZCy5hN": "Eastern Africa, Russia, Baghdad, Kuwait, Moscow, St. Petersburg",
  "dataCenter-BtcoVzjYlsti": "Turkey, Israel, South Africa, Istanbul, Cairo, Athens",
  "dataCenter-8BoazYzBYsW7": "Germany, Ireland, France, Italy, Spain",
  "dataCenter-CNIDnUrfmgJw": "Coordinated Universal Time, Greenwich Mean Time",
  "dataCenter-ymfuuhi9yZnO": "Cape Verde, Azores",
  "dataCenter-nAvnBdcYoEvI": "South Georgia, Falkland Islands",
  "dataCenter-HgJu2qeUltCV": "Brazil, Uruguay, Argentina",
  "dataCenter-AajAh8udQ8U3": "Venezuela, Bolivia, Paraguay, Chile",
  "dataCenter-1vBhBeJSwgCg": "Eastern Time (USA and Canada)",
  "dataCenter-mts8o9feBR46": "Central Time (USA and Canada)",
  "dataCenter-YGyfZpQ02pbf": "Mountain Time",
  "dataCenter-JJcapENbl7lF": "Pacific Time",
  "dataCenter-xyH3Z2eroCA7": "Alaska",
  "dataCenter-WtL5anbNVtUH": "Hawaii",
  "dataCenter-xkuEfPJL9WIy": "Samoa, Niue",
  "dataCenter-9SIVJhxc8Sle": "International Date Line West",
  "dataCenter-KzB83fnjYkFG": "Match Uploaded File Fields",
  "dataCenter-BuUydSgZNnqQ": "Not Import",
  "dataCenter-QoFJnRaRO6Lz": "Please select matching fields!",
  "dataCenter-mY0Dh5O0DULB": "Convert Recognize Time Format",
  "dataCenter-A5korprfeBnI": "Please select convert time format!",
  "dataCenter-Q3a2F4RY3M2o": "timestamp(milliseconds)",
  "dataCenter-tEvsXSmS9eRr": "timestamp(seconds)",
  "dataCenter-xRVHZsjUYeRl": "Sample Data",
  "dataCenter-98O8etcgZiXx": "Data Time Zone",
  "dataCenter-ZWwScyBJDcl7": "Upload Data - Skip Duplicate Data",
  "dataCenter-Y7RNiFsSKOBF": "According to the primary key field of the data table, duplicate data will be ignored and not imported. Non-duplicate data will be imported.",
  "dataCenter-kPvmnTECyDIy": "Upload Data - Update Duplicate Data",
  "dataCenter-MMZzq47QzJO5": "According to the primary key field of the data table, duplicate data will be covered and updated. Non-duplicate data will be imported.",
  "dataCenter-nTauCj6ZfzyR": "Update Data - Only Update Duplicate Data",
  "dataCenter-e3QbkRignq3E": "According to the primary key field of the data table, duplicate data will be covered and updated. Non-duplicate data will be ignored.",
  "dataCenter-vrTDEpeSqPgx": "Replace Data - Delete Historical Data",
  "dataCenter-RqjaDa5cZ69y": "The historical data will be deleted and new data will be uploaded. Please be careful!",
  "dataCenter-vvd90BRQNNmh": "Data Upload Update Rules",
  "dataCenter-GUa4puIFF4XC": "Primary Key Field",
  "dataCenter-wwL8I21duNYh": "Data Already Exists",
  "dataCenter-O53sEQhJcj1J": ", not support update duplicate data",
  "dataCenter-KiGye5r3jFzS": "Please select update rule",
  "dataCenter-FlWHo3Wly8CA": "Set Data Upload Filter Conditions",
  "dataCenter-h7t59SkqRNGB": "The data that meets the above conditions will be uploaded, with a maximum of 20 conditions.",
  "dataCenter-KvVTTg0fKRpX": "Set Upload File Partition Overwrite Rules",
  "dataCenter-3Fpwecqfqks2": "Configure Overwrite Partition Data",
  "dataCenter-JYuKo4UWI2Mn": "Please enter!",
  "dataCenter-nBp4HaWPCxg6": "Please enter",
  "dataCenter-2ZdVZD0F1ugm": "Please select!",
  "dataCenter-GEgt3IM69fAr": "The maximum length limit is 64 characters",
  "dataCenter-Xb5O8VjTNQUp": "Please select",
  "dataCenter-8OHSc7qhHOQz": "Download Partitioned Table Structure Template",
  "dataCenter-c4JTfLIPgTKc": "Upload File Data Rows:",
  "dataCenter-jlDih3HTsnMJ": "Filtered Data Rows:",
  "dataCenter-pEtTDD27zhRO": "Update Rule",
  }
};
