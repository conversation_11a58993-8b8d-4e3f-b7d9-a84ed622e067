export default {
  cn: {
 'dataCenter-dataModelConfig-olji7vxYmZ': '数据表',
  'dataCenter-dataModelConfig-tab-dataModel': '数据模型',
  'dataCenter-dataModelConfig-tab-publicDimension': '公共维度',
  'dataCenter-dataModelConfig-dataModel-modelName': '模型名称',
  'dataCenter-dataModelConfig-dataModel-factTable': '事实表',
  'dataCenter-dataModelConfig-dataModel-status': '状态',
  'dataCenter-dataModelConfig-dataModel-online': '已上线',
  'dataCenter-dataModelConfig-dataModel-offline': '已下线',
  'dataCenter-dataModelConfig-dataModel-idType': 'ID类型',
  'dataCenter-dataModelConfig-dataModel-createTime': '创建时间',
  'dataCenter-dataModelConfig-dataModel-updateTime': '更新时间',
  'dataCenter-dataModelConfig-dataModel-updateUserName': '更新人',
  'dataCenter-dataModelConfig-dataModel-dimensionCount': '维度数量',
  'dataCenter-dataModelConfig-dataModel-metricCount': '度量数量',
  'dataCenter-dataModelConfig-dataModel-option': '操作',
  'dataCenter-dataModelConfig-dataModel-option-option1': '查看',
  'dataCenter-dataModelConfig-dataModel-option-option2': '更多',
  'dataCenter-dataModelConfig-dataModel-option-option2-item1': '编辑',
  'dataCenter-dataModelConfig-dataModel-option-option2-item2': '删除',
  'dataCenter-dataModelConfig-dataModel-option-option2-item3': '下线',
  'dataCenter-dataModelConfig-dataModel-option-option2-item4': '发布',
  'dataCenter-dataModelConfig-dataModel-expandHelp': '展开帮助',
  'dataCenter-dataModelConfig-dataModel-hideHelp': '收起帮助',
  'dataCenter-dataModelConfig-dataModel-modelList': '模型列表',
  'dataCenter-dataModelConfig-dataModel-moreFilters': '更多过滤',
  'dataCenter-dataModelConfig-dataModel-searchModelName': '搜索名称',
  'dataCenter-dataModelConfig-dataModel-create': '新建',
  'dataCenter-dataModelConfig-dataModel-totalItems': '共 {{count}} 条',
  'dataCenter-dataModelConfig-dataModel-help-create': '创建归因模型',
  'dataCenter-dataModelConfig-dataModel-help-create-step1-title': '1、确定归因的业务指标',
  'dataCenter-dataModelConfig-dataModel-help-create-step1-desc1': '需要对什么业务指标进行策略归因？',
  'dataCenter-dataModelConfig-dataModel-help-create-step1-desc2': '新开户用户？用户存款提款？用户业务订单？',
  'dataCenter-dataModelConfig-dataModel-help-create-step2-title': '2、接入相关数据表',
  'dataCenter-dataModelConfig-dataModel-help-create-step2-desc1': '记录业务指标的数据表是否接入？',
  'dataCenter-dataModelConfig-dataModel-help-create-step2-desc2': '针对业务指标分析的维度表是否接入?',
  'dataCenter-dataModelConfig-dataModel-help-create-step3-title': '3、归因模型设计',
  'dataCenter-dataModelConfig-dataModel-help-create-step3-desc': '通过拖拉拽的方式，将业务表、策略表、以及相关维度表，进行可视化建模，并设计需要归因的度量、维度、归因模型等。',
  'dataCenter-dataModelConfig-dataModel-help-create-step4-title': '4、归因计算',
  'dataCenter-dataModelConfig-dataModel-help-create-step4-desc': '根据数据加载方式，归因模型，每天会定时计算归因宽表，创建完业务指标后，即可进行归因分析。',
  'dataCenter-dataModelConfig-dataModel-help-attributionModelCase': '归因模型案例',
  'dataCenter-dataModelConfig-dataModel-help-attributionModelCase-dataModel': '数据模型',
  'dataCenter-dataModelConfig-dataModel-help-attributionModelCase-attributionCase': '归因案例',

  'dataCenter-dataModelConfig-modalDetail-breadcrumb1': '数据模型',
  'dataCenter-dataModelConfig-modalDetail-breadcrumb2': '数据模型详情',
  'dataCenter-dataModelConfig-modalDetail-tab1': 'ER图',
  'dataCenter-dataModelConfig-modalDetail-tab2': '维度',
  'dataCenter-dataModelConfig-modalDetail-tab3': '度量',
  'dataCenter-dataModelConfig-modalDetail-tab2Title': '维度列表',
  'dataCenter-dataModelConfig-modalDetail-tab3Title': '度量列表',
  'dataCenter-dataModelConfig-modalDetail-tab2Search-placeholder': '搜索维度名称',
  'dataCenter-dataModelConfig-modalDetail-tab3Search-placeholder': '搜索度量名称',
  'dataCenter-dataModelConfig-modalDetail-tab2ColName': '维度名称',
  'dataCenter-dataModelConfig-modalDetail-tab3ColName': '度量名称',
  'dataCenter-dataModelConfig-modalDetail-Col2': '表名',
  'dataCenter-dataModelConfig-modalDetail-Col3': '列',
  'dataCenter-dataModelConfig-modalDetail-Col4': '数据类型',

  'dataCenter-dataModelConfig-dataModel-createStep-saveSuccess': '保存成功',
  'dataCenter-dataModelConfig-dataModel-createStep-next': '下一步',
  'dataCenter-dataModelConfig-dataModel-createStep-saveAsDraft': '保存为草稿',
  'dataCenter-dataModelConfig-dataModel-createStep-previous': '上一步',
  'dataCenter-dataModelConfig-dataModel-createStep-save': '保存',
  'dataCenter-dataModelConfig-dataModel-createStep-cancelCreation': '取消创建',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-baseInfo': '基本信息',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-dataModel': '数据模型',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-modelName': '模型名称',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-factTable': '事实表',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-idType': 'ID类型',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-idField': 'ID类型对应字段',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-remark': '模型备注',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-placeholder-remark': '请输入模型备注，不超过150字',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-placeholder-select': '请选择',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-placeholder-input': '请输入',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModel': '数据模型',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-baseInfo': '基本信息',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-modelConfig': '模型配置',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-left-title': '数据源',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-left-search-placeholder': '搜索',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-right-dimension': 'D维度',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-right-metric': 'M度量',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-right-resetPosition': '重置定位',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-dimensionTitle': 'D 维度',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-metricTitle': 'M 度量',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-add': '添加',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-dimensionName': '维度名称',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-metricName': '度量名称',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-tableName': '数据表',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-columnName': '列名',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-dataType': '数据类型',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-operation': '操作',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-edit': '编辑',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-delete': '删除',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-confirm': '确定',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-cancel': '取消',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-modal-dimension': '维度',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-modal-metric': '度量',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-modal-tableTitle': '数据表',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-modal-ColTitle': '列',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-modal-selectPlaceholder': '请选择',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-modal-placeholder-input': '请输入',

  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-title': '关联关系',
  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-form1': '表的关联关系',
  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-form2': '表关系',
  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-form3': '列的关联关系',
  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-btn': '添加关联关系',

  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-relationOption1': '一对一',
  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-relationOption2': '一对多',
  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-relationOption3': '多对多',

  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-breadcrumb1': '数据模型',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-breadcrumb2': '基本信息',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-breadcrumb3': '归因配置',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-title': '归因配置',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-switch-title': '归因配置',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-switch-desc': '（针对转化类指标，利用不同归因模型，分析某个维度下不同的维度值对指标的贡献度）',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-alertText': '例如分析不同活动对订单转化的贡献度，选择订单表中的订单ID作为归因的度量，选择策略表中的活动ID（名称）作为归因的维度。目前一个归因模型支持一张度量表和一张维度表，如需多个归因模型，请点击“+”新增归因配置。',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-TabsText': '归因配置',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form1-title': '归因的度量',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form1-select-placeholder': '选择表',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form1-select-ruleText': '请选择表',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form1-select2-placeholder': '选择度量',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form1-select2-ruleText': '请选择度量',

  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form2-title': '归因的维度',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form2-select-placeholder': '选择表',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form2-select-ruleText': '请选择表',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form2-select-placeholder-dimension': '选择维度',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form2-select-ruleText-dimension': '请选择维度',

  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-title': '归因模型',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-placeholder': '选择归因模型',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-ruleText': '请选择归因模型',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-option1': '时间衰减归因',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-option2': '首次触点归因',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-option3': '末次触点归因',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-option4': 'U型归因',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-option5': '马尔可夫链归因',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-option6': '平均触点归因',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-modelExplain': '模型说明',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-title': '模型说明',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col1': '模型',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col2': '说明',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col3': '增长策略',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-desc1': '将转化功劳全部归于用户最终触达的渠道',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col2-desc2': '将转化功劳全部归于用户首次触达的渠道',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col2-desc3': '将促成转化的功劳平均分配给转化路径上的所有触达',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col2-desc4': '触达越接近转化发生时间，分配的功劳就越多',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col2-desc5': '向用户首次和最终触达的关键字分别分配 40%的功劳，将剩下的20% 的功劳平均分配给路径中的其他触达',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col2-desc6': '马尔可夫链归因分析‌是一种利用马尔可夫链模型来分析用户行为路径，从而确定不同营销渠道对最终转化贡献的方法。',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col3-desc1': '最保守',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col3-desc2': '最重视增长',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col3-desc3': '适中',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col3-desc4': '保守型',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col3-desc5': '增长型',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col3-desc6': '更客观准确',

  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form4-title': '衰减幅度',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form4-select-placeholder': '选择衰减幅度',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form4-select-ruleText': '请选择衰减幅度',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form4-select-option1': '每 7 天',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form4-select-option2': '每 14 天',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form4-select-option3': '每 30 天',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form4-select-desc': '衰减50%',

  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form5-title': '追溯周期',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form5-input-placeholder': '请输入',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form5-input-ruleText': '请输入追溯周期',

  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form5-select-option1': '天',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form5-select-option2': '周',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form5-select-option3': '月',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form5-select-option4': '小时',

  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-breadcrumb1': '数据模型',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-breadcrumb2': '基本信息',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-breadcrumb3': '归因配置',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-breadcrumb4': '加载配置',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-title': '加载配置',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form1-title': '加载方式',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form-select-placeholder': '请选择',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form1-option1': '增量加载',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form1-option2': '全量加载',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form2-title': '分区表',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form3-title': 'ID类型',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form4-title': '时间分区列',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form5-title': '时间格式',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-advancedSetting': '高级设置',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-advancedSetting-expand': '展开',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-advancedSetting-collapse': '收起',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-advancedSetting-select-placeholder': '请选择',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-advancedSetting-select2-placeholder': '操作符',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-advancedSetting-input-placeholder': '值',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-advancedSetting-add': '添加',

  'dataCenter-dataModelConfig-dataTable-left-title': '数据表主题',
  'dataCenter-dataModelConfig-dataTable-left-search-placeholder': '搜索',
  'dataCenter-dataModelConfig-dataTable-left-option1': '重命名',
  'dataCenter-dataModelConfig-dataTable-left-option2': '移动至',
  'dataCenter-dataModelConfig-dataTable-left-option3': '删除',
  'dataCenter-dataModelConfig-dataTable-left-modal-renameTitle': '重命名主题',
  'dataCenter-dataModelConfig-dataTable-left-modal-moveTitle': '移动主题文件夹',
  'dataCenter-dataModelConfig-dataTable-left-modal-createTitle': '新建主题文件夹',
  'dataCenter-dataModelConfig-dataTable-left-modal-confirm': '确定',
  'dataCenter-dataModelConfig-dataTable-left-modal-cancel': '取消',
  'dataCenter-dataModelConfig-dataTable-left-modal-placeholder-select': '请选择',
  'dataCenter-dataModelConfig-dataTable-left-modal-placeholder-input': '请输入主题名称',
  'dataCenter-dataModelConfig-dataTable-left-modal-create-form1-title': '主题名称',
  'dataCenter-dataModelConfig-dataTable-left-modal-create-form2-title': '所属主题',
  'dataCenter-dataModelConfig-dataTable-left-modal-delete-title': '删除',
  'dataCenter-dataModelConfig-dataTable-left-modal-delete-desc1': '您将删除选项：{{name}}',
  'dataCenter-dataModelConfig-dataTable-left-modal-delete-desc2': '删除该主题后，该主题下的数据表移至【未分类】',
  'dataCenter-dataModelConfig-dataTable-left-modal-delete-confirm': '确定删除',
  'dataCenter-dataModelConfig-dataTable-left-modal-delete-cancel': '取消',

  'dataCenter-dataModelConfig-dataTable-right-filter1-placeholder': 'ID类型',
  'dataCenter-dataModelConfig-dataTable-right-filter2-more-placeholder': '更多过滤',
  'dataCenter-dataModelConfig-dataTable-right-filter3-input-placeholder': '搜索表名称',

  'dataCenter-dataModelConfig-RK0gH-KiNs': '表名称',
  'dataCenter-dataModelConfig-A6byib8XhJ': '更新人',
  'dataCenter-dataModelConfig-OykehdDBLj': 'ID类型',
  'dataCenter-dataModelConfig-V6e4gLrmKX': '更新时间',
  'dataCenter-dataModelConfig-VMuCftYTV6': '维度数量',
  'dataCenter-dataModelConfig-7PhbqJKWgK': '度量数量',
  'dataCenter-dataModelConfig-oyZhGOW5pB': '操作',
  'dataCenter-dataModelConfig-sJZNOWOoSX': '查看',
  'dataCenter-dataModelConfig-_ri-WuxWJf': '更多',
  'dataCenter-dataModelConfig-nJeShZzDV3': '变更主题',
  'dataCenter-dataModelConfig-YRZMWpv2AY': '维度与度量',
  'dataCenter-dataModelCofig-008wxy4o': '数据表',
  'dataCenter-dataModelCofig-2Qq6BQPd': '数据表详情',
  'dataCenter-dataModelCofig-tqd8VAF-B4': '维度',
  'dataCenter-dataModelCofig-oiHYkMRT7o': '度量',
  'dataCenter-dataModelCofig-r8F0iJn1': '数据预览',
  'dataCenter-dataModelCofig-0ztcW7ud': '字段设置',
  'dataCenter-dataModelCofig-_V6pjn12': '基本信息',
  'dataCenter-dataModelCofig-3gQAIodb': '数据表',
  'dataCenter-dataModelCofig-Baerb47w': '表字段',
  'dataCenter-dataModelCofig-0ztcW7udoXTY8X9t': '字段值',
  'dataCenter-dataModelCofig-uCJEjRgS': '数据表',
  'dataCenter-dataModelCofig-84gJ9cXY': '表字段',
  'dataCenter-dataModelCofig-vHwBYJSR': '字段权限',
  'dataCenter-dataModelCofig-8-a-qil8': '脱敏字段',
  'dataCenter-dataModelCofig-4iSOOvUg': '无限制',
  'dataCenter-dataModelCofig-ZZoO7oHX': '数据列表',
  'dataCenter-dataModelCofig-qS6_F6YB': '显示 {{count1}} 条样例数据，共 {{count2}} 条数据',
  'dataCenter-dataModelCofig-LdYPeZ1h': '最近更新时间：{{time}}',
  'dataCenter-dataModelCofig-_24ihQWk': '数据权限',
  'dataCenter-dataModelCofig-jMj56kVJ': '字段列表',
  'dataCenter-dataModelCofig-V4voIr8d': '共{{total}}个字段，{{preset}}个预置字段，{{custom}}个自定义字段',
  'dataCenter-dataModelCofig-eSO3_z9N': '最近更新时间',
  'dataCenter-dataModelCofig-tbqNKAAp': '编辑',
  'dataCenter-dataModelCofig-y_7X2UVb': '保存数据',
  'dataCenter-dataModelCofig-eb_mQDwM': '取消编辑',
  'dataCenter-dataModelCofig-P-4lBfHH': '添加字段',
  'dataCenter-dataModelCofig-LfNBDfI1': '基本属性',
  'dataCenter-dataModelCofig-CqHROtKa': '主键字段',
  'dataCenter-dataModelCofig-6n9IAirN': '物理模型',
  'dataCenter-dataModelCofig-5_XTuWW5': '分区字段',
  'dataCenter-dataModelCofig-8SOMxQGE': '存储格式',
  'dataCenter-dataModelCofig-2yesozL4': '创建信息',
  'dataCenter-dataModelCofig-IwqcGDy5': '最新数据更新时间',
  'dataCenter-dataModelCofig-cDlpQHRJ': '创建者',
  'dataCenter-dataModelCofig-a7s2Gvw8': '创建时间',
  'dataCenter-dataModelCofig-WneI0oL_': '更新者',
  'dataCenter-dataModelCofig-KGkuYhgA': '数据表结构更新时间',
  'dataCenter-dataModelCofig-U2nY00tT': '备注信息',
  'dataCenter-dataModelCofig-lBHpOweY': '保存成功',
  'dataCenter-dataModelCofig-p7z_dxf_': '删除成功',
  'dataCenter-dataModelCofig-bCifD7ev': '根主题文件夹',
  'dataCenter-dataTable-xG-NWBofQV': '维度名称',
  'dataCenter-dataTable-JZlL3FyiJ5': '度量名称',
  'dataCenter-dataTable-pwEn4PwygV': '表名',
  'dataCenter-dataTable-BgJuG_HUlL': '列',
  'dataCenter-dataTable-0FO9Fr6wRj': '数据类型',
  'dataCenter-dataTable-G36cClmnn7': '添加',
  'dataCenter-dataTable-KeC8_yBbwy': '请输入维度名称',
  'dataCenter-dataTable-b-oN-sozvk': '请输入度量名称',
  'dataCenter-dataTable-Y4EBmWBVO6': '添加维度',
  'dataCenter-dataTable-tM2B73M0P4': '添加度量',
  'dataCenter-dataTable-r-EaD8l_8E': '列',
  'dataCenter-dataTable-rUlNLAO8kd': '维度名称',
  'dataCenter-dataTable-QBKQ-8DN_E': '保存成功',
  'dataCenter-dataTable-4nU5z8G4I1': '确定删除',
  'dataCenter-dataTable-hjT9UALI-K': '删除成功',
  'dataCenter-dataTable-p7z_dxf_': '您将删除选项',
  'dataCenter-dataTable-ZdwGhEQgzy': '确认删除吗？',
  'dataCenter-dataTable-cTPlmsZNaA': '未归类',
  'dataCenter-dataTable-M69Dp8iYn2': '主题名称已存在',
  'dataCenter-dataModel-nSQnCnP_ItT': '草稿',
  'dataCenter-dataModel-lE5r_oxVdH': '待上线',
  'dataCenter-dataModel-2Qq6BQPdBLgRriX_5M': '已上线',
  'dataCenter-dataModel-2Qq6BQPdlalR_NLdn5': '已下线',
  'dataCenter-dataModel-yngYi8iK4N': '是否确认取消创建模型，当前步骤数据会丢失哦',
  'dataCenter-dataTable-p65bykBOGu': '最大不能超过20个字符',
  'dataCenter-dataTable-C2zcnjaRf-': '仅支持汉字、英文、数字、—、_',
  'dataCenter-dataTable-HIoDv5BdCO': '最大不能超过100个字符',
  'dataCenter-dataTable-tYxjhj4rqB': '仅支持字母、数字、字符(._-)或者汉字',
  'dataCenter-dataTable-6EskNhiiNO': '事实表不可删除',
  'dataCenter-dataTable-h24ZwhgthG': '搜索表名称',
  'dataCenter-dataTable-IlVcVNvGlo': '表名 {{name}}已存在',
  'dataCenter-dataModel-ZViZwYb9LJ': '管理模型的维度',
  'dataCenter-dataModel-008wxy4oMJon0B-bR3': '管理模型的度量',
  'dataCenter-dataModel-O5D2nyxBPk': '归因模型填写不正确',
  'dataCenter-dataTable-D-T47X1-1w': '是否为时间序列',
  'dataCenter-dataModel-ooJhZJ9yjs': '发布成功',
  'dataCenter-dataModel-A2uR3E_lA5': '下线成功',
  'dataCenter-dataModel-jIw2baY7Wn': '已上线',
  'dataCenter-dataModel-QZlX5SrdCC': '已下线',
  'dataCenter-dataModel-HOY-j2sU1x': '修改事实表会导致模型配置，归因配置的数据丢失',
  'dataCenter-dataModel-B9OPMBIcyO': '归因',
  "dataCenter-rLEJeW0TqW8W": "更新时间",
  "dataCenter-cx3iqF6bW9y8": "ID类型",
  "dataCenter-XJb2oIyaUYWk": "未归类",
  "dataCenter-MCJau86iDqeD": "清空",
  "dataCenter-FiWeHONrDUdp": "查询",
  "dataCenter-ohyY4ai5hO0d": "共",
  "dataCenter-h2CP1ZDFf4Cf": "条",
  "dataCenter-hrjkyg401ETn": "操作成功！",
  "dataCenter-xlS5bOBu6czY": "请输入字段显示名",
  },

  en: {
  'dataCenter-dataModelConfig-olji7vxYmZ': 'Data Table',
  'dataCenter-dataModelConfig-tab-dataModel': 'Data Model',
  'dataCenter-dataModelConfig-tab-publicDimension': 'Public Dimension',
  'dataCenter-dataModelConfig-dataModel-modelName': 'Model Name',
  'dataCenter-dataModelConfig-dataModel-factTable': 'Fact Table',
  'dataCenter-dataModelConfig-dataModel-status': 'Status',
  'dataCenter-dataModelConfig-dataModel-online': 'Online',
  'dataCenter-dataModelConfig-dataModel-offline': 'Offline',
  'dataCenter-dataModelConfig-dataModel-idType': 'ID Type',
  'dataCenter-dataModelConfig-dataModel-createTime': 'Create Time',
  'dataCenter-dataModelConfig-dataModel-updateTime': 'Update Time',
  'dataCenter-dataModelConfig-dataModel-updateUserName': 'Updated By',
  'dataCenter-dataModelConfig-dataModel-dimensionCount': 'Dimension Count',
  'dataCenter-dataModelConfig-dataModel-metricCount': 'Metric Count',
  'dataCenter-dataModelConfig-dataModel-option': 'Operation',
  'dataCenter-dataModelConfig-dataModel-option-option1': 'View',
  'dataCenter-dataModelConfig-dataModel-option-option2': 'More',
  'dataCenter-dataModelConfig-dataModel-option-option2-item1': 'Edit',
  'dataCenter-dataModelConfig-dataModel-option-option2-item2': 'Delete',
  'dataCenter-dataModelConfig-dataModel-option-option2-item3': 'Offline',
  'dataCenter-dataModelConfig-dataModel-option-option2-item4': 'Publish',
  'dataCenter-dataModelConfig-dataModel-expandHelp': 'Expand Help',
  'dataCenter-dataModelConfig-dataModel-hideHelp': 'Hide Help',
  'dataCenter-dataModelConfig-dataModel-modelList': 'Model List',
  'dataCenter-dataModelConfig-dataModel-moreFilters': 'More Filters',
  'dataCenter-dataModelConfig-dataModel-searchModelName': 'Search Name',
  'dataCenter-dataModelConfig-dataModel-create': 'Create',
  'dataCenter-dataModelConfig-dataModel-totalItems': 'Total {{count}} Items',
  'dataCenter-dataModelConfig-dataModel-help-create': 'Create Attribution Model',
  'dataCenter-dataModelConfig-dataModel-help-create-step1-title': '1. Determine the Business Metrics for Attribution',
  'dataCenter-dataModelConfig-dataModel-help-create-step1-desc1': 'What business metrics do you need to attribute strategies to?',
  'dataCenter-dataModelConfig-dataModel-help-create-step1-desc2': 'New accounts? User deposits and withdrawals? User business orders?',
  'dataCenter-dataModelConfig-dataModel-help-create-step2-title': '2. Integrate Relevant Data Tables',
  'dataCenter-dataModelConfig-dataModel-help-create-step2-desc1': 'Are the data tables that record the business metrics integrated?',
  'dataCenter-dataModelConfig-dataModel-help-create-step2-desc2': 'Are the dimension tables that analyze the business metrics integrated?',
  'dataCenter-dataModelConfig-dataModel-help-create-step3-title': '3. Design the Attribution Model',
  'dataCenter-dataModelConfig-dataModel-help-create-step3-desc': 'Using drag-and-drop, integrate the business tables, strategy tables, and related dimension tables, and design the metrics, dimensions, and attribution models needed.',
  'dataCenter-dataModelConfig-dataModel-help-create-step4-title': '4. Calculate Attribution',
  'dataCenter-dataModelConfig-dataModel-help-create-step4-desc': 'Based on the data loading method, the attribution model will calculate the attribution wide table daily, and you can perform attribution analysis after creating the business metrics.',
  'dataCenter-dataModelConfig-dataModel-help-attributionModelCase': 'Attribution Model Case',
  'dataCenter-dataModelConfig-dataModel-help-attributionModelCase-dataModel': 'Data Model',
  'dataCenter-dataModelConfig-dataModel-help-attributionModelCase-attributionCase': 'Attribution Case',

  'dataCenter-dataModelConfig-modalDetail-breadcrumb1': 'Data Model',
  'dataCenter-dataModelConfig-modalDetail-breadcrumb2': 'Data Model Details',
  'dataCenter-dataModelConfig-modalDetail-tab1': 'ER Diagram',
  'dataCenter-dataModelConfig-modalDetail-tab2': 'Dimensions',
  'dataCenter-dataModelConfig-modalDetail-tab3': 'Metrics',
  'dataCenter-dataModelConfig-modalDetail-tab2Title': 'Dimension List',
  'dataCenter-dataModelConfig-modalDetail-tab3Title': 'Metric List',
  'dataCenter-dataModelConfig-modalDetail-tab2Search-placeholder': 'Search Dimension Name',
  'dataCenter-dataModelConfig-modalDetail-tab3Search-placeholder': 'Search Metric Name',
  'dataCenter-dataModelConfig-modalDetail-tab2ColName': 'Dimension Name',
  'dataCenter-dataModelConfig-modalDetail-tab3ColName': 'Metric Name',
  'dataCenter-dataModelConfig-modalDetail-Col2': 'Table Name',
  'dataCenter-dataModelConfig-modalDetail-Col3': 'Column',
  'dataCenter-dataModelConfig-modalDetail-Col4': 'Data Type',

  'dataCenter-dataModelConfig-dataModel-createStep-saveSuccess': 'Save Success',
  'dataCenter-dataModelConfig-dataModel-createStep-next': 'Next Step',
  'dataCenter-dataModelConfig-dataModel-createStep-saveAsDraft': 'Save as Draft',
  'dataCenter-dataModelConfig-dataModel-createStep-previous': 'Previous Step',
  'dataCenter-dataModelConfig-dataModel-createStep-save': 'Save',
  'dataCenter-dataModelConfig-dataModel-createStep-cancelCreation': 'Cancel Creation',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-baseInfo': 'Basic Information',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-dataModel': 'Data Model',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-modelName': 'Model Name',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-factTable': 'Fact Table',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-idType': 'ID Type',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-idField': 'ID Type Corresponding Field',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-remark': 'Model Remark',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-placeholder-remark': 'Please enter a model remark, up to 150 characters',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-placeholder-select': 'Please Select',
  'dataCenter-dataModelConfig-dataModel-createStep-baseInfo-form-placeholder-input': 'Please Enter',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModel': 'Data Model',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-baseInfo': 'Basic Information',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-modelConfig': 'Model Configuration',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-left-title': 'Data Source',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-left-search-placeholder': 'Search',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-right-dimension': 'D Dimension',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-right-metric': 'M Metric',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-right-resetPosition': 'Reset Position',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-dimensionTitle': 'D Dimension',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-metricTitle': 'M Metric',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-add': 'Add',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-dimensionName': 'Dimension Name',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-metricName': 'Metric Name',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-tableName': 'Data Table',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-columnName': 'Column Name',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-dataType': 'Data Type',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-operation': 'Operation',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-edit': 'Edit',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-tableCol-delete': 'Delete',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-confirm': 'Confirm',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-cancel': 'Cancel',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-modal-dimension': 'Dimension',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-modal-metric': 'Metric',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-modal-tableTitle': 'Data Table',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-modal-ColTitle': 'Column',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-modal-selectPlaceholder': 'Please Select',
  'dataCenter-dataModelConfig-dataModel-createStep-modelConfig-dataModelFlow-dimensionAndMetricDrawer-modal-placeholder-input': 'Please Enter',

  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-title': 'Association Relationship',
  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-form1': 'Association Relationship of Tables',
  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-form2': 'Table Relationship',
  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-form3': 'Association Relationship of Columns',
  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-btn': 'Add Association Relationship',

  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-relationOption1': 'One-to-One',
  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-relationOption2': 'One-to-Many',
  'dataCenter-dataModelConfig-dataModelFlow-edgeDrawer-relationOption3': 'Many-to-Many',

  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-breadcrumb1': 'Data Model',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-breadcrumb2': 'Basic Information',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-breadcrumb3': 'Attribution Configuration',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-title': 'Attribution Configuration',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-switch-title': 'Attribution Configuration',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-switch-desc': 'For conversion-based metrics, use different attribution models to analyze the contribution of different dimension values under a specific dimension.',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-alertText':
    'For example, to analyze the contribution of different activities to order conversions, select the order ID from the order table as the metric for attribution, and select the activity ID (name) from the strategy table as the dimension for attribution. Currently, one attribution model supports one metric table and one dimension table. If multiple attribution models are needed, please click "+" to add a new attribution configuration.',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-TabsText': 'Attribution Configuration',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form1-title': 'Attribution Metric',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form1-select-placeholder': 'Select Table',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form1-select-ruleText': 'Please Select Table',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form1-select2-placeholder': 'Select Metric',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form1-select2-ruleText': 'Please Select Metric',

  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form2-title': 'Attribution Dimension',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form2-select-placeholder': 'Select Table',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form2-select-ruleText': 'Please Select Table',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form2-select-placeholder-dimension': 'Select Dimension',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form2-select-ruleText-dimension': 'Please Select Dimension',

  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-title': 'Attribution Model',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-placeholder': 'Select Attribution Model',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-ruleText': 'Please Select Attribution Model',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-option1': 'Time Decay Attribution',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-option2': 'First Touch Attribution',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-option3': 'Last Touch Attribution',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-option4': 'U-Shaped Attribution',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-option5': 'Markov Chain Attribution',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-select-option6': 'Average Touch Attribution',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form3-modelExplain': 'Model Explanation',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-title': 'Model Explanation',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col1': 'Model',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col2': 'Explanation',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col3': 'Growth Strategy',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-desc1': 'All the credit for conversion is attributed to the channel that users ultimately reach',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col2-desc2': 'All the credit for conversion is attributed to the channel that users first reach',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col2-desc3': 'The credit for conversion is equally distributed among all the channels that users reach',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col2-desc4': 'The closer the reach time is to the conversion time, the more credit is attributed',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col2-desc5': '40% of the credit is attributed to the keywords that users first reach and 40% to the keywords that users ultimately reach',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col2-desc6': 'Markov Chain Attribution Analysis is a method to analyze user behavior paths using the Markov Chain model to determine the contribution of different marketing channels to final conversions.',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col3-desc1': 'Most Conservative',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col3-desc2': 'Most Focused on Growth',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col3-desc3': 'Moderate',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col3-desc4': 'Conservative',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col3-desc5': 'Growth-Oriented',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-modelExplain-modal-table-col3-desc6': 'More Objective and Accurate',

  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form4-title': 'Decay Rate',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form4-select-placeholder': 'Select Decay Rate',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form4-select-ruleText': 'Please Select Decay Rate',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form4-select-option1': 'Every 7 Days',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form4-select-option2': 'Every 14 Days',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form4-select-option3': 'Every 30 Days',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form4-select-desc': 'Decay by 50%',

  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form5-title': 'Trace Period',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form5-input-placeholder': 'Please Enter',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form5-input-ruleText': 'Please Enter Trace Period',

  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form5-select-option1': 'Day',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form5-select-option2': 'Week',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form5-select-option3': 'Month',
  'dataCenter-dataModelConfig-dataModel-createStep-attributionConfig-content-form5-select-option4': 'Hour',

  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-breadcrumb1': 'Data Model',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-breadcrumb2': 'Basic Information',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-breadcrumb3': 'Attribution Configuration',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-breadcrumb4': 'Loading Configuration',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-title': 'Loading Configuration',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form1-title': 'Loading Method',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form-select-placeholder': 'Please Select',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form1-option1': 'Incremental Loading',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form1-option2': 'Full Loading',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form2-title': 'Partition Table',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form3-title': 'ID Type',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form4-title': 'Time Partition Column',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-form5-title': 'Time Format',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-advancedSetting': 'Advanced Settings',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-advancedSetting-expand': 'Expand',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-advancedSetting-collapse': 'Collapse',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-advancedSetting-select-placeholder': 'Please Select',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-advancedSetting-select2-placeholder': 'Operator',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-advancedSetting-input-placeholder': 'Value',
  'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-advancedSetting-add': 'Add',

  'dataCenter-dataModelConfig-dataTable-left-title': 'Data Table Theme',
  'dataCenter-dataModelConfig-dataTable-left-search-placeholder': 'Search',
  'dataCenter-dataModelConfig-dataTable-left-option1': 'Rename',
  'dataCenter-dataModelConfig-dataTable-left-option2': 'Move to',
  'dataCenter-dataModelConfig-dataTable-left-option3': 'Delete',
  'dataCenter-dataModelConfig-dataTable-left-modal-renameTitle': 'Rename Theme',
  'dataCenter-dataModelConfig-dataTable-left-modal-moveTitle': 'Move Theme Folder',
  'dataCenter-dataModelConfig-dataTable-left-modal-createTitle': 'Create New Theme Folder',
  'dataCenter-dataModelConfig-dataTable-left-modal-confirm': 'Confirm',
  'dataCenter-dataModelConfig-dataTable-left-modal-cancel': 'Cancel',
  'dataCenter-dataModelConfig-dataTable-left-modal-placeholder-select': 'Please Select',
  'dataCenter-dataModelConfig-dataTable-left-modal-placeholder-input': 'Please Enter Theme Name',
  'dataCenter-dataModelConfig-dataTable-left-modal-create-form1-title': 'Theme Name',
  'dataCenter-dataModelConfig-dataTable-left-modal-create-form2-title': 'Parent Theme',
  'dataCenter-dataModelConfig-dataTable-left-modal-delete-title': 'Delete',
  'dataCenter-dataModelConfig-dataTable-left-modal-delete-desc1': 'You will delete the option: {{name}}',
  'dataCenter-dataModelConfig-dataTable-left-modal-delete-desc2': 'After deleting the theme, the data tables under the theme will be moved to the "Uncategorized" folder',
  'dataCenter-dataModelConfig-dataTable-left-modal-delete-confirm': 'Confirm Delete',
  'dataCenter-dataModelConfig-dataTable-left-modal-delete-cancel': 'Cancel',

  'dataCenter-dataModelConfig-dataTable-right-filter1-placeholder': 'ID Type',
  'dataCenter-dataModelConfig-dataTable-right-filter2-more-placeholder': 'More Filters',
  'dataCenter-dataModelConfig-dataTable-right-filter3-input-placeholder': 'Search Table Name',

  'dataCenter-dataModelConfig-RK0gH-KiNs': 'Table Name',
  'dataCenter-dataModelConfig-A6byib8XhJ': 'Updated By',
  'dataCenter-dataModelConfig-OykehdDBLj': 'ID Type',
  'dataCenter-dataModelConfig-V6e4gLrmKX': 'Update Time',
  'dataCenter-dataModelConfig-VMuCftYTV6': 'Dimension Count',
  'dataCenter-dataModelConfig-7PhbqJKWgK': 'Metric Count',
  'dataCenter-dataModelConfig-oyZhGOW5pB': 'Operation',
  'dataCenter-dataModelConfig-sJZNOWOoSX': 'View',
  'dataCenter-dataModelConfig-_ri-WuxWJf': 'More',
  'dataCenter-dataModelConfig-nJeShZzDV3': 'Change Theme',
  'dataCenter-dataModelConfig-YRZMWpv2AY': 'Dimension and Metric',
  'dataCenter-dataModelCofig-008wxy4o': 'Data Table',
  'dataCenter-dataModelCofig-2Qq6BQPd': 'Data Table Details',
  'dataCenter-dataModelCofig-tqd8VAF-B4': 'Dimension',
  'dataCenter-dataModelCofig-oiHYkMRT7o': 'Metric',
  'dataCenter-dataModelCofig-r8F0iJn1': 'Data Preview',
  'dataCenter-dataModelCofig-0ztcW7ud': 'Field Settings',
  'dataCenter-dataModelCofig-_V6pjn12': 'Basic Information',
  'dataCenter-dataModelCofig-3gQAIodb': 'Data Table',
  'dataCenter-dataModelCofig-Baerb47w': 'Table Fields',
  'dataCenter-dataModelCofig-0ztcW7udoXTY8X9t': 'Field Value',
  'dataCenter-dataModelCofig-uCJEjRgS': 'Data Table',
  'dataCenter-dataModelCofig-84gJ9cXY': 'Table Fields',
  'dataCenter-dataModelCofig-vHwBYJSR': 'Field Permissions',
  'dataCenter-dataModelCofig-8-a-qil8': 'Sensitive Fields',
  'dataCenter-dataModelCofig-4iSOOvUg': 'No Restrictions',
  'dataCenter-dataModelCofig-ZZoO7oHX': 'Data List',
  'dataCenter-dataModelCofig-qS6_F6YB': 'Display {{count1}} Sample Data, Total {{count2}} Data',
  'dataCenter-dataModelCofig-LdYPeZ1h': 'Last Updated: {{time}}',
  'dataCenter-dataModelCofig-_24ihQWk': 'Data Permissions',
  'dataCenter-dataModelCofig-jMj56kVJ': 'Field List',
  'dataCenter-dataModelCofig-V4voIr8d': 'Total {{total}} Fields, {{preset}} Preset Fields, {{custom}} Custom Fields',
  'dataCenter-dataModelCofig-eSO3_z9N': 'Last Updated',
  'dataCenter-dataModelCofig-tbqNKAAp': 'Edit',
  'dataCenter-dataModelCofig-y_7X2UVb': 'Save Data',
  'dataCenter-dataModelCofig-eb_mQDwM': 'Cancel Edit',
  'dataCenter-dataModelCofig-P-4lBfHH': 'Add Field',
  'dataCenter-dataModelCofig-LfNBDfI1': 'Basic Attributes',
  'dataCenter-dataModelCofig-CqHROtKa': 'Primary Key Field',
  'dataCenter-dataModelCofig-6n9IAirN': 'Physical Model',
  'dataCenter-dataModelCofig-5_XTuWW5': 'Partition Fields',
  'dataCenter-dataModelCofig-8SOMxQGE': 'Storage Format',
  'dataCenter-dataModelCofig-2yesozL4': 'Create Information',
  'dataCenter-dataModelCofig-IwqcGDy5': 'Latest Data Update Time',
  'dataCenter-dataModelCofig-cDlpQHRJ': 'Creator',
  'dataCenter-dataModelCofig-a7s2Gvw8': 'Create Time',
  'dataCenter-dataModelCofig-WneI0oL_': 'Updater',
  'dataCenter-dataModelCofig-KGkuYhgA': 'Data Table Structure Update Time',
  'dataCenter-dataModelCofig-U2nY00tT': 'Notes',
  'dataCenter-dataModelCofig-lBHpOweY': 'Save Success',
  'dataCenter-dataModelCofig-p7z_dxf_': 'Delete Success',
  'dataCenter-dataModelCofig-bCifD7ev': 'Root Theme Folder',
  'dataCenter-dataTable-xG-NWBofQV': 'Dimension Name',
  'dataCenter-dataTable-JZlL3FyiJ5': 'Metric Name',
  'dataCenter-dataTable-pwEn4PwygV': 'Table Name',
  'dataCenter-dataTable-BgJuG_HUlL': 'Column',
  'dataCenter-dataTable-0FO9Fr6wRj': 'Data Type',
  'dataCenter-dataTable-G36cClmnn7': 'Add',
  'dataCenter-dataTable-KeC8_yBbwy': 'Please Enter Dimension Name',
  'dataCenter-dataTable-b-oN-sozvk': 'Please Enter Metric Name',
  'dataCenter-dataTable-Y4EBmWBVO6': 'Add Dimension',
  'dataCenter-dataTable-tM2B73M0P4': 'Add Metric',
  'dataCenter-dataTable-r-EaD8l_8E': 'Column',
  'dataCenter-dataTable-rUlNLAO8kd': 'Dimension Name',
  'dataCenter-dataTable-QBKQ-8DN_E': 'Save Success',
  'dataCenter-dataTable-4nU5z8G4I1': 'Confirm Delete',
  'dataCenter-dataTable-hjT9UALI-K': 'Delete Success',
  'dataCenter-dataTable-p7z_dxf_': 'You Will Delete Option',
  'dataCenter-dataTable-ZdwGhEQgzy': 'Are You Sure You Want to Delete?',
  'dataCenter-dataTable-cTPlmsZNaA': 'Uncategorized',
  'dataCenter-dataTable-M69Dp8iYn2': 'Theme Name Already Exists',
  'dataCenter-dataModel-nSQnCnP_ItT': 'Draft',
  'dataCenter-dataModel-lE5r_oxVdH': 'Pending Approval',
  'dataCenter-dataModel-2Qq6BQPdBLgRriX_5M': 'Online',
  'dataCenter-dataModel-2Qq6BQPdlalR_NLdn5': 'Offline',
  'dataCenter-dataModel-yngYi8iK4N': 'Are You Sure You Want to Cancel the Creation of This Model?',
  'dataCenter-dataTable-p65bykBOGu': 'Maximum of 20 Characters',
  'dataCenter-dataTable-C2zcnjaRf-': 'Only Chinese, English, Numbers, -, _ are Supported',
  'dataCenter-dataTable-HIoDv5BdCO': 'Maximum of 100 Characters',
  'dataCenter-dataTable-tYxjhj4rqB': 'Only Letters, Numbers, Characters (._-) or Chinese Characters are Supported',
  'dataCenter-dataTable-6EskNhiiNO': 'Fact Table Cannot Be Deleted',
  'dataCenter-dataTable-IlVcVNvGlo': 'Table Name {{name}} Already Exists',
  'dataCenter-dataTable-h24ZwhgthG': 'Search Table Name',
  'dataCenter-dataModel-ZViZwYb9LJ': 'Dimensions of the Management Model',
  'dataCenter-dataModel-008wxy4oMJon0B-bR3': 'Metrics of the Management Model',
  'dataCenter-dataModel-O5D2nyxBPk': 'Incorrect Attribution Model Format',
  'dataCenter-dataTable-D-T47X1-1w': 'Is This Time Series?',
  'dataCenter-dataModel-ooJhZJ9yjs': 'Publish Success',
  'dataCenter-dataModel-A2uR3E_lA5': 'Offline Success',
  'dataCenter-dataModel-jIw2baY7Wn': 'Online',
  'dataCenter-dataModel-QZlX5SrdCC': 'Offline',
  'dataCenter-dataModel-HOY-j2sU1x': 'Changing the fact table will cause loss of model configuration and attribution configuration data',
  'dataCenter-dataModel-B9OPMBIcyO': 'Attribution',
  "dataCenter-rLEJeW0TqW8W": "Update Time",
  "dataCenter-cx3iqF6bW9y8": "ID Type",
  "dataCenter-XJb2oIyaUYWk": "Uncategorized",
  "dataCenter-MCJau86iDqeD": "Clear",
  "dataCenter-FiWeHONrDUdp": "Query",
  "dataCenter-ohyY4ai5hO0d": "Total",
  "dataCenter-h2CP1ZDFf4Cf": "Items",
  "dataCenter-hrjkyg401ETn": "Operation Success!",
  "dataCenter-xlS5bOBu6czY": "Please Enter Field Display Name",
  },
};
