import dayjs from 'dayjs';
import _ from 'lodash';
import thousands from 'utils/thousands';
import { DataType, ModuleCode, QuarterMapKey } from './types';

export const getQuarter = (): QuarterMapKey => {
  const month = dayjs().month();
  return (Math.floor(month / 3) + 1) as QuarterMapKey;
};

export const getResultByMetricsName = (metricsName: string, moduleCode: ModuleCode, data: DataType): string => {
  if (_.isEmpty(data)) return '0';
  const metric = data.find(
    (item) => item?.businessMetrics?.metricsName === metricsName && item?.businessMetrics?.moduleCode === moduleCode
  );
  if (_.isEmpty(metric)) return '0';
  return ['LONG', 'INT'].includes(metric?.businessMetrics?.metricsType ?? '')
    ? thousands(metric?.result)
    : _.isString(metric?.result)
      ? metric?.result
      : '0';
};
