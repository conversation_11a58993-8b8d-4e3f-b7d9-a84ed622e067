import dayjs, { Dayjs } from 'dayjs';
import { DayMapKey, JumpConfig, QuarterMap } from './types';
import { getQuarter } from './utils';

export const color = {
  warn: '#FAAD14',
  error: '#FF4D4F',
  success: '#52C41A',
  info: '#1677FF',
  disabled: '#D9D9D9'
};

export const descMap = {
  批次总数: '历史流程画布批次的总数量，包括测试批次和正式批次',
  当前任务排队数: '当前时间点流程画布批次任务的排队数量',
  平均任务启动延迟: '近一周流程画布批次任务的平均启动延迟时间',
  平均任务启动耗时: '近一周流程画布批次任务的平均启动耗时时间',
  启用并被流程画布引用: '当前状态为启用中的用户分群被流程画布引用的数量, 曾经引用或正在引用都包含在内',
  流程画布引用率: '启用中并被流程画布引用的分群数量/启用中的分群数量',
  计算任务总数: '历史分群计算任务的总数量',
  今日待计算: '今日待计算的分群任务数量',
  平均计算启动延迟: '近一周用户分群平均计算启动延迟时间',
  平均计算耗时: '近一周用户分群平均计算耗时时间'
};

export const statusColor = {
  草稿: color.disabled,
  测试中: color.info,
  测试完成: color.success,
  启用中: color.success,
  已停用: color.error,
  已结束: color.success,
  运行结束: color.success,
  自动结束: color.success,
  终止: color.warn,
  运行失败: color.error,
  计算成功: color.success,
  计算失败: color.error,
  计算中: color.info
};

export const chartConfig = {
  xField: 'date',
  yField: 'passedCount',
  xAxis: {
    label: {
      autoRotate: false
    }
  },
  maxColumnWidth: 30,
  slider: {
    start: 0,
    end: 1
  }
};

export const chartField = {
  date: {
    alias: '日期'
  },
  passedCount: {
    alias: '覆盖总人次'
  }
};

export const quarterMap: QuarterMap = {
  1: [dayjs().startOf('year'), dayjs().startOf('year').add(2, 'month').endOf('month')],
  2: [dayjs().startOf('year').add(3, 'month'), dayjs().startOf('year').add(5, 'month').endOf('month')],
  3: [dayjs().startOf('year').add(6, 'month'), dayjs().startOf('year').add(8, 'month').endOf('month')],
  4: [dayjs().startOf('year').add(9, 'month'), dayjs().endOf('year')]
} as const;

export const dayMap: Record<DayMapKey, [Dayjs, Dayjs]> = {
  今年以来: [dayjs().startOf('year'), dayjs().subtract(1, 'day')],
  本月以来: [dayjs().startOf('month'), dayjs().subtract(1, 'day')],
  本季度以来: [quarterMap[getQuarter()][0], dayjs().subtract(1, 'day')],
  本年第一季度: quarterMap[1],
  本年第二季度: quarterMap[2],
  本年第三季度: quarterMap[3],
  本年第四季度: quarterMap[4]
} as const;

export const jumpTo: JumpConfig = {
  campaignV2: {
    草稿: {
      propertyName: 'phase',
      operator: 'EQ',
      value: 'DRAFT'
    },
    测试中: {
      propertyName: 'phase',
      operator: 'EQ',
      value: 'TESTING'
    },
    测试完成: {
      propertyName: 'phase',
      operator: 'EQ',
      value: 'TEST_SUC'
    },
    启用中: {
      propertyName: 'phase',
      operator: 'EQ',
      value: 'ENABLE'
    },
    已结束: {
      propertyName: 'phase',
      operator: 'EQ',
      value: 'STOPPED'
    }
  },
  segment: {
    草稿: {
      propertyName: 'status',
      operator: 'EQ',
      value: 'DRAFT'
    },
    启用中: {
      propertyName: 'status',
      operator: 'EQ',
      value: 'NORMAL'
    },
    已停用: {
      propertyName: 'status',
      operator: 'EQ',
      value: 'DISABLE'
    },
    未开始: {
      propertyName: 'calcStatus',
      operator: 'EQ',
      value: 'NOTRUN',
      connector: 'AND'
    },
    计算成功: {
      propertyName: 'calcStatus',
      operator: 'EQ',
      value: 'SUC',
      connector: 'AND'
    },
    计算失败: {
      propertyName: 'calcStatus',
      operator: 'EQ',
      value: 'FAIL',
      connector: 'AND'
    },
    计算中: {
      propertyName: 'calcStatus',
      operator: 'EQ',
      value: 'CALCING',
      connector: 'AND'
    }
  }
};
