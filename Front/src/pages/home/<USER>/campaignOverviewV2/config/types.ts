import { Dayjs } from 'dayjs';

export type ModuleCode = 'CAMPAIGN_V2' | 'CAMPAIGN_V2_CALC_LOG' | 'SEGMENT' | 'SEGMENT_CALC_LOG';

export type ItemType = {
  propertyName: string;
  operator: string;
  value: string;
  connector?: string;
};

export type DataType = {
  result: number;
  businessMetrics: {
    moduleName: string;
    moduleCode: string;
    metricsName: string;
    metricsCode: string;
    metricsType: string;
  };
}[];

export type ChartDataType = {
  avgPassedCount: number;
  totalPassedCount: number;
  passedCountTimeList: {
    date: string;
    passedCount: number;
  }[];
};

export interface ItemContentProps {
  title: TitleType;
  moduleCode: ModuleCode;
  number?: number;
  data?: any;
}

export interface ItemTitleProps {
  title: TitleType;
}

export interface ItemProps {
  title: TitleType;
  isLeft: boolean;
  moduleCode?: ModuleCode;
  number?: number;
  data?: any;
}

export type TitleType =
  | '批次总数'
  | '当前任务排队数'
  | '平均任务启动延迟'
  | '平均任务启动耗时'
  | '启用并被流程画布引用'
  | '流程画布引用率'
  | '计算任务总数'
  | '今日待计算'
  | '平均计算启动延迟'
  | '平均计算耗时'
  | '流程画布总数'
  | '单次执行'
  | '周期执行'
  | '草稿'
  | '测试中'
  | '测试完成'
  | '启用中'
  | '已结束'
  | '待提交审批'
  | '审批中'
  | '审批通过'
  | '审批驳回'
  | '已撤销'
  | '已取消'
  | '运行结束'
  | '自动结束'
  | '终止'
  | '运行失败'
  | '撤回中'
  | '撤回成功'
  | '运行中'
  | '运行中正式批次'
  | '运行中测试批次'
  | '待运行'
  | '待运行正式批次'
  | '待运行测试批次'
  | '待终止'
  | '待终止正式批次'
  | '待终止测试批次'
  | '分群总数'
  | '单次计算分群'
  | '周期计算分群'
  | '已停用'
  | '计算结束'
  | '计算成功'
  | '计算失败'
  | '计算中'
  | '自动计算任务'
  | '手动计算'
  | '累计覆盖总人次'
  | '日平均覆盖人次'
  | '正式批次总数'
  | '测试批次总数';

export type CampaignJumpableKeys = '草稿' | '测试中' | '测试完成' | '启用中' | '已结束';
export type SegmentJumpableKeys = '草稿' | '启用中' | '已停用' | '未开始' | '计算成功' | '计算失败' | '计算中';

export type JumpConfig = {
  campaignV2: Record<CampaignJumpableKeys, ItemType>;
  segment: Record<SegmentJumpableKeys, ItemType>;
};

export type DayMapKey =
  | '今年以来'
  | '本月以来'
  | '本季度以来'
  | '本年第一季度'
  | '本年第二季度'
  | '本年第三季度'
  | '本年第四季度';

export type QuarterMapKey = 1 | 2 | 3 | 4;
export type QuarterMap = Record<QuarterMapKey, [Dayjs, Dayjs]>;
