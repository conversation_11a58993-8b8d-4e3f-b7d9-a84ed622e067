import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';
import CampaignOverviewV2Service from 'service/campaignOverviewV2Service';
import CampaignOverviewChart from './components/chart';
import { ContentSection } from './components/ContentSection';
import { Item } from './components/Item';
import { ChartDataType, dayMap, DayMapKey } from './config';

const commonClasses = {
  item: 'item w-160 h-74 rounded-[6px]',
  itemTitle: 'item-title text-14 leading-[24px] my-text-45',
  itemContent: 'item-content text-24',
  section: 'flex bg-[#fff] rounded-[6px] p-24 gap-24',
  left: 'w-512',
  title: 'title text-16 font-bold mb-16',
  content: 'content flex gap-16 mb-16 flex-wrap w-[inherit]'
};

export const CampaignOverviewV2: React.FC = () => {
  const [data, setData] = useState(undefined);
  const [chatSelete, setChatSelete] = useState<DayMapKey>('本季度以来');
  const [chartData, setChartData] = useState<ChartDataType>({
    avgPassedCount: 0,
    totalPassedCount: 0,
    passedCountTimeList: []
  });

  useEffect(() => {
    (async () => {
      const res = await CampaignOverviewV2Service.listBy([
        {
          propertyName: 'statTime',
          operator: 'DATE_BETWEEN',
          value: `${dayjs().startOf('day').valueOf()},${dayjs().endOf('day').valueOf()}`
        }
      ]);
      setData(res);
    })();
  }, []);

  useEffect(() => {
    (async () => {
      const [begin, end] = dayMap[chatSelete] as [Dayjs, Dayjs];
      const res = await CampaignOverviewV2Service.calcCampaignV2PassedCount({
        beginTime: begin.valueOf(),
        endTime: end.valueOf()
      });
      setChartData(res);
    })();
  }, [chatSelete]);

  return (
    <div className="flex gap-24 flex-col">
      <div className="flex gap-16 mt-16 items-center">
        <div className="text-2xl font-bold">策略概览</div>
        <div className="my-text-45">统计时间 {dayjs().format('YYYY-MM-DD HH:mm:ss')}</div>
      </div>

      <div>
        <div className={commonClasses.section}>
          <div className={commonClasses.left}>
            <div className={commonClasses.title}>流程画布数量</div>
            <ContentSection
              titles={['流程画布总数', '单次执行', '周期执行']}
              moduleCode="CAMPAIGN_V2"
              isLeft
              data={data}
            />
          </div>
          <div className="flex flex-col gap-16">
            <div>
              <div className={commonClasses.title}>流程画布运行</div>
              <ContentSection
                titles={['草稿', '测试中', '测试完成', '启用中', '已结束']}
                moduleCode="CAMPAIGN_V2"
                isLeft={false}
                data={data}
              />
            </div>
            <div>
              <div className={commonClasses.title}>流程画布审批</div>
              <ContentSection
                titles={['待提交审批', '审批中', '审批通过', '审批驳回', '已撤销', '已取消']}
                moduleCode="CAMPAIGN_V2"
                isLeft={false}
                data={data}
              />
            </div>
          </div>
        </div>
      </div>

      <div>
        <div className={commonClasses.section}>
          <div className={commonClasses.left}>
            <div className={commonClasses.title}>流程画布批次</div>
            <ContentSection
              titles={[
                '批次总数',
                '正式批次总数',
                '测试批次总数',
                '当前任务排队数',
                '平均任务启动延迟',
                '平均任务启动耗时'
              ]}
              moduleCode="CAMPAIGN_V2_CALC_LOG"
              isLeft
              data={data}
            />
          </div>
          <div className="flex flex-col gap-16">
            <div>
              <div className={commonClasses.title}>流程画布批次状态</div>
              <ContentSection
                titles={['运行结束', '终止', '运行失败', '撤回中', '撤回成功']}
                moduleCode="CAMPAIGN_V2_CALC_LOG"
                isLeft={false}
                data={data}
              />

              <ContentSection
                titles={['运行中', '运行中正式批次', '运行中测试批次']}
                moduleCode="CAMPAIGN_V2_CALC_LOG"
                isLeft={false}
                data={data}
              />

              <ContentSection
                titles={['待运行', '待运行正式批次', '待运行测试批次']}
                moduleCode="CAMPAIGN_V2_CALC_LOG"
                isLeft={false}
                data={data}
              />

              <ContentSection
                titles={['待终止', '待终止正式批次', '待终止测试批次']}
                moduleCode="CAMPAIGN_V2_CALC_LOG"
                isLeft={false}
                data={data}
              />
            </div>
          </div>
        </div>
      </div>

      <div>
        <div className={commonClasses.section}>
          <div className={commonClasses.left}>
            <div className={commonClasses.title}>用户分群数量</div>
            <ContentSection
              titles={['分群总数', '单次计算分群', '周期计算分群']}
              moduleCode="SEGMENT"
              isLeft
              data={data}
            />
          </div>
          <div className="flex flex-col gap-16">
            <div>
              <div className={commonClasses.title}>用户分群状态</div>
              <ContentSection
                titles={['草稿', '启用中', '已停用', '启用并被流程画布引用', '流程画布引用率']}
                moduleCode="SEGMENT"
                isLeft={false}
                data={data}
              />
            </div>
            <div>
              <div className={commonClasses.title}>用户分群审批</div>
              <ContentSection
                titles={['待提交审批', '审批中', '审批通过', '审批驳回', '已撤销', '已取消']}
                moduleCode="SEGMENT"
                isLeft={false}
                data={data}
              />
            </div>
          </div>
        </div>
      </div>

      <div>
        <div className={commonClasses.section}>
          <div className={commonClasses.left}>
            <div className={commonClasses.title}>用户分群计算</div>
            <ContentSection
              titles={['计算任务总数', '今日待计算', '平均计算启动延迟', '平均计算耗时']}
              moduleCode="SEGMENT_CALC_LOG"
              isLeft
              data={data}
            />
          </div>
          <div className="flex flex-col gap-16">
            <div>
              <div className={commonClasses.title}>用户分群状态</div>
              <ContentSection
                titles={['计算结束', '计算成功', '计算失败', '计算中']}
                moduleCode="SEGMENT_CALC_LOG"
                isLeft={false}
                data={data}
              />
            </div>
          </div>
        </div>
      </div>

      <div>
        <div className={commonClasses.section}>
          <div className="w-200">
            <div className={commonClasses.title}>流程画布覆盖人次</div>
            <div className={commonClasses.content}>
              <Item title="累计覆盖总人次" isLeft={false} number={chartData.totalPassedCount} data={data} />
            </div>
            <div className={commonClasses.content}>
              <Item title="日平均覆盖人次" isLeft={false} number={chartData.avgPassedCount} data={data} />
            </div>
          </div>
          <div className="w-[calc(98%-200px)] flex flex-col gap-16">
            <CampaignOverviewChart
              chartData={chartData}
              chatSelete={chatSelete}
              setChatSelete={setChatSelete}
              dayMap={dayMap}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
