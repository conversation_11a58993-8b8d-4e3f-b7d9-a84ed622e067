import classNames from 'classnames';
import React from 'react';
import { useHistory } from 'react-router-dom';
import {
  descMap,
  getResultByMetricsName,
  ItemContentProps,
  ItemProps,
  ItemTitleProps,
  ItemType,
  JumpConfig,
  jumpTo,
  ModuleCode,
  statusColor
} from '../config/index';

import thousands from 'utils/thousands';

import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';

const commonClasses = {
  item: 'item w-160 h-74 rounded-[6px]',
  itemTitle: 'item-title text-14 leading-[24px] my-text-45',
  itemContent: 'item-content text-24'
};

const ItemTitle: React.FC<ItemTitleProps> = ({ title }) => {
  const desc = descMap[title as keyof typeof descMap];

  return (
    <div className={commonClasses.itemTitle}>
      {statusColor[title as keyof typeof statusColor] && (
        <span
          style={{
            display: 'inline-block',
            width: '6px',
            height: '6px',
            backgroundColor: statusColor[title as keyof typeof statusColor],
            marginRight: '8px',
            borderRadius: '50%'
          }}
        />
      )}

      {title}
      {desc && (
        <Tooltip title={desc}>
          <ExclamationCircleOutlined className="ml-4" />
        </Tooltip>
      )}
    </div>
  );
};

const ItemContent: React.FC<ItemContentProps> = ({ title, moduleCode, number, data }) => {
  return (
    <div className={commonClasses.itemContent}>
      {number ? thousands(number) : getResultByMetricsName(title, moduleCode, data)}
    </div>
  );
};

export const Item: React.FC<ItemProps> = ({ title, isLeft, moduleCode, number, data }) => {
  const history = useHistory();
  const type = (
    moduleCode === 'CAMPAIGN_V2' || moduleCode === 'CAMPAIGN_V2_CALC_LOG' ? 'campaignV2' : 'segment'
  ) as keyof JumpConfig;
  const jumpToMap = jumpTo[type][title as keyof JumpConfig[typeof type]];

  const clickJumpTo = (jumpToMap: ItemType, moduleCode: ModuleCode): void => {
    const query = [jumpToMap];
    const type = moduleCode === 'CAMPAIGN_V2' || moduleCode === 'CAMPAIGN_V2_CALC_LOG' ? 'campaignV2' : 'segment';
    sessionStorage.setItem(type === 'campaignV2' ? 'campaignQuery' : 'segmentQuery', JSON.stringify(query));
    history.push(type === 'campaignV2' ? `/aimarketer/home/<USER>'/aimarketer/home/<USER>/userGroup');
  };

  return (
    <div
      className={classNames(isLeft ? 'bg-[var(--primary-hover-bg)] p-8' : 'py-8', {
        [commonClasses.item]: true,
        'cursor-pointer': !!jumpToMap
      })}
      onClick={() => {
        jumpToMap && clickJumpTo(jumpToMap, moduleCode!);
      }}
    >
      <ItemTitle title={title} />
      <ItemContent title={title} moduleCode={moduleCode!} number={number} data={data} />
    </div>
  );
};
