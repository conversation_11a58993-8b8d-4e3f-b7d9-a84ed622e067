import React from 'react';
import { ModuleCode, TitleType } from '../config/index';
import { Item } from './Item';

interface ContentSectionProps {
  titles: TitleType[];
  moduleCode: ModuleCode;
  isLeft?: boolean;
  data: any;
}

export const ContentSection: React.FC<ContentSectionProps> = ({ titles, moduleCode, isLeft = false, data }) => (
  <div className="content flex gap-16 mb-16 flex-wrap w-[inherit]">
    {titles.map((item: TitleType, index: React.Key) => (
      <Item key={index} title={item} isLeft={isLeft} moduleCode={moduleCode} data={data} />
    ))}
  </div>
);
