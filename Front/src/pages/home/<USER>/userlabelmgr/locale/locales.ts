export default {
  cn: {
  "dataCenter-E9mSxx3NooeK": "计算成功",
  "dataCenter-hwiqdyCFpUkX": "计算失败",
  "dataCenter-qRX10zGtZLhQ": "已失效",
  "dataCenter-0UlRHnVkwmzH": "计算中",
  "dataCenter-tExZXq1iPbiG": "未开始",
  "dataCenter-tQRg8YYZQ1vV": "正常运行",
  "dataCenter-uWJFiezYyxZb": "停用",
  "dataCenter-WrHuks98NtF2": "未运行",
  "dataCenter-DhOgNplE3vp8": "SQL创建",
  "dataCenter-sMhyrRoeWO38": "外部导入",
  "dataCenter-LlqMATYPibs8": "条件创建",
  "dataCenter-mYbSZSXLRlRQ": "模型创建",
  "dataCenter-d2fEh5HzoRXD": "外部订阅",
  "dataCenter-qkLVS9tvPPCK": "自定义条件",
  "dataCenter-HBh3hsU09Vvf": "自定义SQL",
  "dataCenter-jfomVJne3gFy": "模型创建",
  "dataCenter-RJLf1LUCIVuH": "外部订阅",
  "dataCenter-hvuIrdvzEHR5": "每月更新",
  "dataCenter-6YubFJ8UV0fS": "每周更新",
  "dataCenter-CbBr9M9z8ZYA": "每天更新",
  "dataCenter-wOdmdqEHvG5O": "一次更新",
  "dataCenter-rZYC30KYct2o": "周期更新",
  "dataCenter-EUSS33E5insY": "手动更新",
  "dataCenter-qbDPkJPaVFIc": "不更新",
  "dataCenter-uXbNvoOqMnQX": "永久",
  "dataCenter-0XQokW2CO3aV": "时间范围",
  "dataCenter-8M7Gtpoq8w7V": "周一",
  "dataCenter-9JYmbgWhAPKb": "周二",
  "dataCenter-tArWaWqJ2LAc": "周三",
  "dataCenter-ky7zvpvWvm1w": "周四",
  "dataCenter-Kw9W9cSX6CvK": "周五",
  "dataCenter-LhgxtxA4I5tv": "周六",
  "dataCenter-mQP8CO3L4tAr": "周日",
  "dataCenter-mRDKSRiNNAF8": "正常",
  "dataCenter-kSDOtX4u1ZGJ": "禁用",
  "dataCenter-RxAz3k3W3SZx": "标签标识",
  "dataCenter-eRqvtTsZT4sI": "标签显示名",
  "dataCenter-2gCXOXuqrlzK": "标签状态",
  "dataCenter-BkUSrVBRayUV": "计算状态",
  "dataCenter-TMEA25ZWSlys": "ID类型",
  "dataCenter-k2E5BEZecASv": "标签类型",
  "dataCenter-GY5Fv3TjP0Lw": "最近计算时间",
  "dataCenter-fK28ngw51EqO": "创建者",
  "dataCenter-v1LoX1H6svOQ": "创建时间",
  "dataCenter-n55g2eqrGc9b": "标签分类",
  "dataCenter-lKw7sv79oBfy": "更新者",
  "dataCenter-0d7EJE1ovO4I": "更新时间",
  "dataCenter-0SjYtHfzcXa3": "标签值是否枚举",
  "dataCenter-R4KujRw3GW1F": "是",
  "dataCenter-1qOb5L5irpKa": "否",
  "dataCenter-TXSgVex4YSFm": "使用次数",
  "dataCenter-kXzz4dQtWqaP": "共",
  "dataCenter-fJVzTwK3XYW1": "条",
  "dataCenter-nfmPaneKt8VE": "确认要",
  "dataCenter-okDE2MTmSbSn": "启用",
  "dataCenter-JB3EOwZjkXIB": "禁用",
  "dataCenter-FxYLqaLnUcaH": "该标签吗？",
  "dataCenter-WvYmI9dICYNg": "你将",
  "dataCenter-Wr11ptBUsovU": "标签：",
  "dataCenter-SlsZxrvIkSRv": "停用标签将停止计算任务，影响后续所有业务应用，请确认后操作。",
  "dataCenter-zeRlkmtsPv8n": "取消",
  "dataCenter-r2hSvb4lTVoS": "成功",
  "dataCenter-NnuSVSHxqLds": "下载失败",
  "dataCenter-NBZaEM2h3M1E": "确认要刷新标签值，使用最新的标签值？",
  "dataCenter-bsIwT5my9Oc7": "确定",
   "dataCenter-ezj2WTaA72Oj": "取消",
  "dataCenter-J4V0U0mNGt5a": "刷新标签值成功",
  "dataCenter-u6cENmgmWANJ": "ID类型",
  "dataCenter-nZwRTC4aVZ80": "标签标识",
  "dataCenter-bH7Cd5XH1TBf": "标签显示名",
  "dataCenter-Fi9zuCLhbXIV": "标签分类",
  "dataCenter-OJBCuxf27aP0": "标签类型(一级)",
  "dataCenter-MQOGDoVHpznA": "标签类型(二级)",
  "dataCenter-zMcmxK9Z09f2": "有效时间",
  "dataCenter-GOn4TyHHLMQb": "更新方式",
  "dataCenter-dbiraRAsrpcc": "使用次数",
  "dataCenter-L9IqwoHXlvvr": "最近计算状态",
  "dataCenter-m05KHZ2NraMI": "最近计算时间",
  "dataCenter-vuOm49EGslUy": "数据类型",
  "dataCenter-61yWCfT4rIvW": "标签状态",
  "dataCenter-n2DafY4rG1Tq": "创建者",
  "dataCenter-allWabFPS9LO": "创建时间",
  "dataCenter-bWc5EoSoXpiJ": "更新者",
  "dataCenter-Shkt7CIXoicq": "标签值是否枚举",
  "dataCenter-bGlDeikTFODh": "是",
  "dataCenter-EzFKrgUkbbDE": "否",
  "dataCenter-rp4010qtgMng": "操作",
  "dataCenter-kyq18Rml0HJ4": "查看",
  "dataCenter-6gSuUfeOt088": "编辑",
  "dataCenter-4bVCc8KGBhfL": "刷新标签值",
  "dataCenter-GpSjoYgHuDFC": "更多",
  "dataCenter-K7YeZaaY4f6c": "筛选",
  "dataCenter-IAQWRK0D2WS9": "标签值查询",
  "dataCenter-MiTJEUVGiEgn": "下载标签列表",
  "dataCenter-R98qpIRCuMWt": "标签值管理",
  "dataCenter-Gyts9nV3YhVl": "批量分类管理",
  "dataCenter-Spx3sBjJrAjM": "新建标签",
  "dataCenter-CTvYu15DACFV": "标签列表",
  "dataCenter-31cuVzV3wc5d": "刷新后,历史的标签枚举值将无法被使用",
  "dataCenter-yIKNKOFo4hrL": "标签标识",
  "dataCenter-xh28WlXgWlet": "标签值",
  "dataCenter-gqXEIEHikeA2": "业务映射名",
  "dataCenter-Tk5aDc7FMmVw": "上传的文件大于200M",
  "dataCenter-uATswz8pIeRl": "请选择ID类型",
  "dataCenter-L7L72XhHdxc7": "上传失败,请重试",
  "dataCenter-m46F89GZ8RHX": "上传成功",
  "dataCenter-HHGsiZgzkg48": "该ID类型下无标签数据，请检查",
  "dataCenter-7KXghQ33rN3w": "取消保存标签值？",
  "dataCenter-2ucEdWoPlZ6I": "是否取消保存标签值，取消后当前数据会丢失。",
  "dataCenter-liav5R5MULx2": "是",
  "dataCenter-QYKSr6VUl3Fm": "否",
  "dataCenter-Nf6ctfvhZt20": "操作成功",
  "dataCenter-zTuFcroBKKsx": "标签值管理",
  "dataCenter-xXAF4iPbJuHK": "展示规则",
  "dataCenter-NC5Xdr18OnlN": "下载标签值映射数据表",
  "dataCenter-6d8QzqA4OM1t": "请选择ID类型",
  "dataCenter-m3Th4m1Kwi4Z": "优先展示",
  "dataCenter-37KAhqOE01SP": "在标签同时具有标签值和标签值显示名数据时，优先按照以下勾选形式展示；如标签值没有显示名数据，则只会显示标签值",
  "dataCenter-Wu2dhD3S0wYK": "标签值",
  "dataCenter-9MlwODkRM8sk": "标签值显示名",
    "dataCenter-WMjlKs7RDgkL": "上传文件",
  "dataCenter-XcFPAfJhdljb": "下载数据模板",
  "dataCenter-GID8n563oVZw": "上传数据文件",
  "dataCenter-9wiHdjeqzJEo": "点击或拖动文件到这里",
  "dataCenter-Bj1R3X6B4PeQ": "支持扩展名：",
  "dataCenter-7Cwdm8BY8Hv7": "成功匹配",
  "dataCenter-XBlVDFdV66N8": "条记录",
  "dataCenter-PDH2M6UfQ9iB": "查看",
  "dataCenter-ySxNWKcpWaU9": "未匹配到",
  "dataCenter-gK1QOcbQdro9": "导出",
  "dataCenter-u92wrsS9432v": "查看成功匹配记录",
  "dataCenter-KrE1vX3rRtkw": "请输入标签值进行查询",
  "dataCenter-9r2x7dTqfd4O": "取消",
  "dataCenter-UINhcXc3eCKs": "保存",
  "dataCenter-yNOBm75awiW3": "ID类型",
    "dataCenter-g6ssWO07ymJ7": "SQL创建",
  "dataCenter-9sZgRUgv9LVY": "自定义SQL",
  "dataCenter-PBZGLyWf3ngN": "通过自定义SQL输出标签数据",
  "dataCenter-no0dCPibx5OR": "选择创建标签方式",
   "dataCenter-6eSrpdX7O5Wa": "根目录",
  "dataCenter-11qmJ0rCXyDf": "编辑",
  "dataCenter-7uWR7OM9UKYK": "添加子分类",
  "dataCenter-YGC1YnF3yTj8": "所属分类",
  "dataCenter-B8QHOANEPdDY": "请选择",
  "dataCenter-tcxsrokkFuwB": "名称",
  "dataCenter-l32R04oFKLvW": "请输入",
  "dataCenter-eyJhBPJarD1i": "最多不能超过64个字",
  "dataCenter-xwv1m4SH8Dkj": "备注",
  "dataCenter-fHYjxGQeVJPX": "最多不能超过150个字",
  "dataCenter-72vz0NE2c8gm": "转移中...",
  "dataCenter-0NhipKTiu1rE": "转移成功!",
  "dataCenter-Y4iUhtWJvlrM": "转移失败!",
  "dataCenter-nvFRn2wKwWtB": "删除标签分类",
  "dataCenter-Ojpzb2mlTjYd": "您将删除标签一级分类：",
  "dataCenter-RtaSIyIwSzVY": "该分类下所有标签都将变为未分类，请谨慎操作。",
  "dataCenter-KUQshJiK0qge": "确定",
  "dataCenter-3ffx4ykN2xaz": "取消",
  "dataCenter-li4qW18MeTCX": "删除中...",
  "dataCenter-AG5JnX2C6ObD": "删除成功!",
  "dataCenter-A7akm30Mklzi": "标签列表",
  "dataCenter-ZKD7M3sTMuu2": "批量分类管理",
  "dataCenter-PfUNU23yKKGG": "标签标识",
  "dataCenter-kFnBXqVl36Vk": "标签显示名",
  "dataCenter-VYA1nbHBbGb9": "描述备注",
  "dataCenter-QiOSLF8jpPei": "标签分类",
  "dataCenter-4QOCsE9ZPHaz": "新建子分类",
  "dataCenter-beNpr9jjuNX2": "编辑",
  "dataCenter-gbLxzM1EBDpF": "删除",
  "dataCenter-bzOMPRonL4wj": "操作",
  "dataCenter-aqQ50rUMlaFy": "未分类标签",
  "dataCenter-HiZr0wruxj8b": "更新时间",
  },
  en: {
  "dataCenter-E9mSxx3NooeK": "Success",
  "dataCenter-hwiqdyCFpUkX": "Failed",
  "dataCenter-qRX10zGtZLhQ": "Expired",
  "dataCenter-0UlRHnVkwmzH": "Calculating",
  "dataCenter-tExZXq1iPbiG": "Not started",
  "dataCenter-tQRg8YYZQ1vV": "Normal",
  "dataCenter-uWJFiezYyxZb": "Disabled",
  "dataCenter-WrHuks98NtF2": "Not running",
  "dataCenter-DhOgNplE3vp8": "SQL created",
  "dataCenter-sMhyrRoeWO38": "External import",
  "dataCenter-LlqMATYPibs8": "Condition created",
  "dataCenter-mYbSZSXLRlRQ": "Model created",
  "dataCenter-d2fEh5HzoRXD": "External subscription",
  "dataCenter-qkLVS9tvPPCK": "Custom condition",
  "dataCenter-HBh3hsU09Vvf": "Custom SQL",
  "dataCenter-jfomVJne3gFy": "Model created",
  "dataCenter-RJLf1LUCIVuH": "External subscription",
  "dataCenter-hvuIrdvzEHR5": "Monthly update",
  "dataCenter-6YubFJ8UV0fS": "Weekly update",
  "dataCenter-CbBr9M9z8ZYA": "Daily update",
  "dataCenter-wOdmdqEHvG5O": "One-time update",
  "dataCenter-rZYC30KYct2o": "Periodic update",
  "dataCenter-EUSS33E5insY": "Manual update",
  "dataCenter-qbDPkJPaVFIc": "No update",
  "dataCenter-uXbNvoOqMnQX": "Permanent",
  "dataCenter-0XQokW2CO3aV": "Time range",
  "dataCenter-8M7Gtpoq8w7V": "Monday",
  "dataCenter-9JYmbgWhAPKb": "Tuesday",
  "dataCenter-tArWaWqJ2LAc": "Wednesday",
  "dataCenter-ky7zvpvWvm1w": "Thursday",
  "dataCenter-Kw9W9cSX6CvK": "Friday",
  "dataCenter-LhgxtxA4I5tv": "Saturday",
  "dataCenter-mQP8CO3L4tAr": "Sunday",
  "dataCenter-mRDKSRiNNAF8": "Normal",
  "dataCenter-kSDOtX4u1ZGJ": "Disabled",
  "dataCenter-RxAz3k3W3SZx": "Label identifier",
  "dataCenter-eRqvtTsZT4sI": "Label display name",
  "dataCenter-2gCXOXuqrlzK": "Label status",
  "dataCenter-BkUSrVBRayUV": "Calculation status",
  "dataCenter-TMEA25ZWSlys": "ID type",
  "dataCenter-k2E5BEZecASv": "Label type",
  "dataCenter-GY5Fv3TjP0Lw": "Last calculation time",
  "dataCenter-fK28ngw51EqO": "Creator",
  "dataCenter-v1LoX1H6svOQ": "Creation time",
  "dataCenter-n55g2eqrGc9b": "Label category",
  "dataCenter-lKw7sv79oBfy": "Updater",
  "dataCenter-0d7EJE1ovO4I": "Update time",
  "dataCenter-0SjYtHfzcXa3": "Is label value an enumeration",
  "dataCenter-R4KujRw3GW1F": "Yes",
  "dataCenter-1qOb5L5irpKa": "No",
  "dataCenter-TXSgVex4YSFm": "Usage count",
  "dataCenter-kXzz4dQtWqaP": "Total",
  "dataCenter-fJVzTwK3XYW1": "items",
  "dataCenter-nfmPaneKt8VE": "Are you sure you want to ",
  "dataCenter-okDE2MTmSbSn": "enable",
  "dataCenter-JB3EOwZjkXIB": "disable",
  "dataCenter-FxYLqaLnUcaH": "this label?",
  "dataCenter-WvYmI9dICYNg": " You will ",
  "dataCenter-Wr11ptBUsovU": "label:",
  "dataCenter-SlsZxrvIkSRv": "Disable the label will stop the calculation task, affecting subsequent business applications, please confirm the operation.",
  "dataCenter-zeRlkmtsPv8n": "Cancel",
  "dataCenter-r2hSvb4lTVoS": "Success",
  "dataCenter-NnuSVSHxqLds": "Download failed",
  "dataCenter-NBZaEM2h3M1E": "Are you sure you want to refresh the label value, using the latest label value?",
  "dataCenter-bsIwT5my9Oc7": "Confirm",
  "dataCenter-ezj2WTaA72Oj": "Cancel",
  "dataCenter-J4V0U0mNGt5a": "Refresh label value successfully",
  "dataCenter-u6cENmgmWANJ": "ID type",
  "dataCenter-nZwRTC4aVZ80": "Label identifier",
  "dataCenter-bH7Cd5XH1TBf": "Label display name",
  "dataCenter-Fi9zuCLhbXIV": "Label category",
  "dataCenter-OJBCuxf27aP0": "Label type(first)",
  "dataCenter-MQOGDoVHpznA": "Label type(second)",
  "dataCenter-zMcmxK9Z09f2": "Valid time",
  "dataCenter-GOn4TyHHLMQb": "Update mode",
  "dataCenter-dbiraRAsrpcc": "Usage count",
  "dataCenter-L9IqwoHXlvvr": "Last calculation status",
  "dataCenter-m05KHZ2NraMI": "Last calculation time",
  "dataCenter-vuOm49EGslUy": "Data type",
  "dataCenter-61yWCfT4rIvW": "Label status",
   "dataCenter-n2DafY4rG1Tq": "Creator",
  "dataCenter-allWabFPS9LO": "Creation time",
  "dataCenter-bWc5EoSoXpiJ": "Updater",
  "dataCenter-Shkt7CIXoicq": "Is label value an enumeration",
  "dataCenter-bGlDeikTFODh": "Yes",
  "dataCenter-EzFKrgUkbbDE": "No",
  "dataCenter-rp4010qtgMng": "Operation",
  "dataCenter-kyq18Rml0HJ4": "View",
  "dataCenter-6gSuUfeOt088": "Edit",
  "dataCenter-4bVCc8KGBhfL": "Refresh label value",
  "dataCenter-GpSjoYgHuDFC": "More",
  "dataCenter-K7YeZaaY4f6c": "Filter",
  "dataCenter-IAQWRK0D2WS9": "Label value query",
  "dataCenter-MiTJEUVGiEgn": "Download label list",
  "dataCenter-R98qpIRCuMWt": "Label value management",
  "dataCenter-Gyts9nV3YhVl": "Batch classification management",
  "dataCenter-Spx3sBjJrAjM": "New label",
  "dataCenter-CTvYu15DACFV": "Label list",
  "dataCenter-31cuVzV3wc5d": "After refreshing, the historical label enumeration values will no longer be used",
  "dataCenter-yIKNKOFo4hrL": "Label identifier",
    "dataCenter-xh28WlXgWlet": "Label value",
  "dataCenter-gqXEIEHikeA2": "Business mapping name",
  "dataCenter-Tk5aDc7FMmVw": "The uploaded file is greater than 200M",
  "dataCenter-uATswz8pIeRl": "Please select ID type",
  "dataCenter-L7L72XhHdxc7": "Upload failed, please try again",
  "dataCenter-m46F89GZ8RHX": "Upload successfully",
  "dataCenter-HHGsiZgzkg48": "There is no label data under this ID type, please check",
  "dataCenter-7KXghQ33rN3w": "Cancel save label value?",
  "dataCenter-2ucEdWoPlZ6I": "Are you sure you want to cancel saving the label value, canceling will lose the current data.",
  "dataCenter-liav5R5MULx2": "Yes",
  "dataCenter-QYKSr6VUl3Fm": "No",
  "dataCenter-Nf6ctfvhZt20": "Operation successful",
  "dataCenter-zTuFcroBKKsx": "Label value management",
  "dataCenter-xXAF4iPbJuHK": "Display rule",
  "dataCenter-NC5Xdr18OnlN": "Download label value mapping data table",
  "dataCenter-6d8QzqA4OM1t": "Please select ID type",
  "dataCenter-m3Th4m1Kwi4Z": "Priority display",
  "dataCenter-37KAhqOE01SP": "When the label has both label value and label value display name data, the following selection form is displayed in priority; if the label value does not have display name data, only the label value will be displayed",
  "dataCenter-Wu2dhD3S0wYK": "Label value",
  "dataCenter-9MlwODkRM8sk": "Label value display name",
    "dataCenter-WMjlKs7RDgkL": "Upload file",
  "dataCenter-XcFPAfJhdljb": "Download data template",
  "dataCenter-GID8n563oVZw": "Upload data file",
  "dataCenter-9wiHdjeqzJEo": "Click or drag the file here",
  "dataCenter-Bj1R3X6B4PeQ": "Supported extensions:",
  "dataCenter-7Cwdm8BY8Hv7": "Successfully matched",
  "dataCenter-XBlVDFdV66N8": "records",
  "dataCenter-PDH2M6UfQ9iB": "View",
  "dataCenter-ySxNWKcpWaU9": "Not matched",
  "dataCenter-gK1QOcbQdro9": "Export",
  "dataCenter-u92wrsS9432v": "View successfully matched records",
  "dataCenter-KrE1vX3rRtkw": "Please enter the label value to query",
  "dataCenter-9r2x7dTqfd4O": "Cancel",
  "dataCenter-UINhcXc3eCKs": "Save",
  "dataCenter-yNOBm75awiW3": "ID type",
    "dataCenter-g6ssWO07ymJ7": "SQL created",
  "dataCenter-9sZgRUgv9LVY": "Custom SQL",
  "dataCenter-PBZGLyWf3ngN": "Output label data through custom SQL",
  "dataCenter-no0dCPibx5OR": "Select label creation method",
   "dataCenter-6eSrpdX7O5Wa": "Root directory",
  "dataCenter-11qmJ0rCXyDf": "Edit",
  "dataCenter-7uWR7OM9UKYK": "Add sub-category",
  "dataCenter-YGC1YnF3yTj8": "Belonging category",
  "dataCenter-B8QHOANEPdDY": "Please select",
  "dataCenter-tcxsrokkFuwB": "Name",
  "dataCenter-l32R04oFKLvW": "Please enter",
  "dataCenter-eyJhBPJarD1i": "Cannot exceed 64 characters",
  "dataCenter-xwv1m4SH8Dkj": "Remark",
  "dataCenter-fHYjxGQeVJPX": "Cannot exceed 150 characters",
  "dataCenter-72vz0NE2c8gm": "Transferring...",
  "dataCenter-0NhipKTiu1rE": "Transfer successfully!",
  "dataCenter-Y4iUhtWJvlrM": "Transfer failed!",
  "dataCenter-nvFRn2wKwWtB": "Delete label category",
  "dataCenter-Ojpzb2mlTjYd": "You will delete the label first category:",
  "dataCenter-RtaSIyIwSzVY": "All labels under this category will become unclassified, please be careful.",
  "dataCenter-KUQshJiK0qge": "Confirm",
  "dataCenter-3ffx4ykN2xaz": "Cancel",
  "dataCenter-li4qW18MeTCX": "Deleting...",
  "dataCenter-AG5JnX2C6ObD": "Delete successfully!",
  "dataCenter-A7akm30Mklzi": "Label list",
  "dataCenter-ZKD7M3sTMuu2": "Batch classification management",
  "dataCenter-PfUNU23yKKGG": "Label identifier",
  "dataCenter-kFnBXqVl36Vk": "Label display name",
  "dataCenter-VYA1nbHBbGb9": "Description note",
  "dataCenter-QiOSLF8jpPei": "Label category",
  "dataCenter-4QOCsE9ZPHaz": "New sub-category",
  "dataCenter-beNpr9jjuNX2": "Edit",
  "dataCenter-gbLxzM1EBDpF": "Delete",
  "dataCenter-bzOMPRonL4wj": "Operation",
  "dataCenter-aqQ50rUMlaFy": "Unclassified label",
  "dataCenter-HiZr0wruxj8b": "Update time",
  }
};
