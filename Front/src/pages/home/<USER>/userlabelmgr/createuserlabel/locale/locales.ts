export default {
  cn: {
  "dataCenter-Wkcchzqi54lL": "条件创建",
  "dataCenter-IN3MzyTnAOwZ": "SQL创建",
  "dataCenter-HNCOep1OkU4W": "模型创建",
  "dataCenter-cHukTucwBOZ4": "外部导入",
  "dataCenter-0drMO0SCPUEH": "外部订阅",
  "dataCenter-NOYTTI1dH2b4": "标签类型",
  "dataCenter-kkdBtDOvXMQE": "ID类型",
  "dataCenter-yh8Xu7abqg03": "ID类型必选",
  "dataCenter-UZgO8eV1jPgL": "请选择ID类型",
  "dataCenter-W002I2wP0ruo": "标签标识",
  "dataCenter-54qSzLz8SPlO": "必须输入标签标识",
  "dataCenter-YBUglLLcuCMv": "标签标识请不要超出60个字符",
  "dataCenter-7g4ooUvlRVBI": "请输入标签标识",
  "dataCenter-jSC8rsFvs4mz": "标签显示名",
  "dataCenter-pZ2H2U0tpupy": "必须输入标签显示名",
  "dataCenter-xP3tQIoJ18BV": "标签显示名请不要超出60个字符",
  "dataCenter-PqN6cG2tzpne": "请输入中文、英文、数字、下划线、常见符号",
  "dataCenter-K5hCD4B7ExaT": "请输入标签显示名",
  "dataCenter-nMc3xxZTwNWb": "数据类型",
  "dataCenter-oZQSBs2aLPxW": "数据类型必选",
  "dataCenter-w4S95OkvP4P8": "请选择数据类型",
  "dataCenter-ZAfa4X4WG8ec": "标签分类",
  "dataCenter-887Aaj2j4sib": "请选择标签分类",

  "dataCenter-y662mF2tlWNK": "标签标识已经存在！",
  "dataCenter-Z2dd9oChSyVG": "有效时间必选！",
  "dataCenter-HSgPi1PeeXjL": "更新方式必选！",
  "dataCenter-DFU9lWJP8ZjA": "保存成功",
  "dataCenter-u6U2J5OVbGeT": "永久有效",
  "dataCenter-zjZ7PTY2Zttt": "编辑",
  "dataCenter-2SK03woX2Og8": "手动更新",
  "dataCenter-BjT5loqBU8IN": "每天更新",
  "dataCenter-uqmIsAGrt7Iq": "每",
   "dataCenter-TY8DZTN5NH6L": "更新",
  "dataCenter-zH89usKoaaHo": "每月",
  "dataCenter-N9vQ7k6a74tA": "号更新",
  "dataCenter-b0DrTuZ3mTVm": "标签值为非枚举值？",
  "dataCenter-Ay1qw84p5uVZ": "标签值为枚举值？",
  "dataCenter-rV24YD4hUNl1": "标签拥有的标签值数量",
  "dataCenter-9knu7kk39kdD": "大于",
  "dataCenter-iO2MYC0ihOI4": "小于",
  "dataCenter-tca04nLJOjpD": "50个，建议将参数定义为",
  "dataCenter-lpxlSNB1ojy1": "非枚举",
  "dataCenter-xinw51HlHwve": "枚举",
  "dataCenter-g4LWNIi56wqj": "确认修改",
  "dataCenter-72aLQz7AKRpN": "取消",
  "dataCenter-o5FcFhSuFkIb": "标签列表",
  "dataCenter-q54RkANwbfjg": "标签详情",
  "dataCenter-Lx0pclzIIUtd": "编辑标签",
  "dataCenter-7Rx4QdK6bwio": "创建标签",
  "dataCenter-xDsFTj3GLFuf": "基本信息",
  "dataCenter-WGEH4CQnFUW5": "标签值是否枚举 :",
  "dataCenter-5mLtOCDS7eGZ": "是",
  "dataCenter-TVoMSwq5HxKm": "否",
  "dataCenter-Eo0oqkvR1vcn": "有效时间:",
  "dataCenter-iZsO104j1qvH": "更新方式:",
  "dataCenter-LZZQ7JBo1bPT": "备注不能超过200个字符",
  "dataCenter-IlK7GFvEszH5": "备注 :",
  "dataCenter-XmZPL9AGLVL0": "取消",
  "dataCenter-ybZTl7Hq2auz": "保存",
  "dataCenter-aolatgYeiG81": "周一",
  "dataCenter-eMO7A2WHhnvH": "周二",
  "dataCenter-mewrT7DtYvrs": "周三",
  "dataCenter-CEOnRubynyaK": "周四",
  "dataCenter-9BKTLmrr7Pam": "周五",
  "dataCenter-FG5Yai4gxioX": "周六",
  "dataCenter-a554Pa0bpP4x": "周日",
  "dataCenter-CXL0hT9HVg7j": "已失效",
  "dataCenter-VqyoBWZGhIHy": "计算不出下次周期",
  "dataCenter-3A0Adr20CcSH": "无",
  "dataCenter-uNSAaCCC9xdk": "计算时间：",
  "dataCenter-CfIDmEDvtuE7": "更新方式",
  "dataCenter-xGq3AgR0GvYd": "手动更新",
  "dataCenter-0aKMEWgndloB": "在创建完成后手动发起计算任务",
  "dataCenter-OcPc2rMOYdkY": "定期更新",
  "dataCenter-Lrn6FycUO2KZ": "周期更新",
  "dataCenter-3Vx5QuU11bjg": "根据设定的规则，定期自动计算（每月更新设定日期晚于当前月最后一天，则最后一天更新）",
  "dataCenter-w9EV51kZ1Jhn": "计算频率：",
  "dataCenter-SRAIekBeph3y": "每天",
  "dataCenter-lLGvgIUtg00b": "每周",
  "dataCenter-ATdE3WOzAdtf": "每月",
  "dataCenter-CrEOmA4r4oSX": "下次计算时间：",
  "dataCenter-FbtntkV99l5z": "规则设置",
  "dataCenter-wM4uKpPenqdv": "sql示例：select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10",
  "dataCenter-drOHxVriv0n4": "验证公式",
  "dataCenter-KrEh28WOi0DA": "函数",
  "dataCenter-Wdbxu2jansBC": "搜索",
  "dataCenter-fdW7icRqaXaw": "用法：",
  "dataCenter-iPUKHExWyjix": "说明：",
  "dataCenter-v3yEWCVJ180l": "示例：",
  "dataCenter-4vL4qE9QgaTz": "数据表",
  "dataCenter-t2zfNMyS4jsM": "有效时间",
  "dataCenter-dwJYoJ31K6Px": "永久有效",
  "dataCenter-tLHXAj74BkT2": "限制时间范围",
  "dataCenter-wDdajRAmve8G": "标签值",
  "dataCenter-AuHhiT1ceThk": "业务映射名",
  "dataCenter-6FrDqvsMtNeW": "操作成功",
  "dataCenter-500J9KydGis0": "编辑标签值",
  "dataCenter-EDExUr0D9wJL": "保存",
  "dataCenter-pbDjShCZWPCo": "请输入标签值进行查询",
  },
  en: {
  "dataCenter-Wkcchzqi54lL": "Condition Creation",
  "dataCenter-IN3MzyTnAOwZ": "SQL Creation",
  "dataCenter-HNCOep1OkU4W": "Model Creation",
  "dataCenter-cHukTucwBOZ4": "External Import",
  "dataCenter-0drMO0SCPUEH": "External Subscribe",
  "dataCenter-NOYTTI1dH2b4": "Label Type",
  "dataCenter-kkdBtDOvXMQE": "ID Type",
  "dataCenter-yh8Xu7abqg03": "ID Type Required",
  "dataCenter-UZgO8eV1jPgL": "Please Select ID Type",
  "dataCenter-W002I2wP0ruo": "Label Identifier",
  "dataCenter-54qSzLz8SPlO": "Label Identifier Required",
  "dataCenter-YBUglLLcuCMv": "Label Identifier Please Do Not Exceed 60 Characters",
  "dataCenter-7g4ooUvlRVBI": "Please Enter Label Identifier",
  "dataCenter-jSC8rsFvs4mz": "Label Display Name",
  "dataCenter-pZ2H2U0tpupy": "Label Display Name Required",
  "dataCenter-xP3tQIoJ18BV": "Label Display Name Please Do Not Exceed 60 Characters",
  "dataCenter-PqN6cG2tzpne": "Please Enter Chinese, English, Numbers, Underlines, and Common Symbols",
  "dataCenter-K5hCD4B7ExaT": "Please Enter Label Display Name",
  "dataCenter-nMc3xxZTwNWb": "Data Type",
  "dataCenter-oZQSBs2aLPxW": "Data Type Required",
  "dataCenter-w4S95OkvP4P8": "Please Select Data Type",
  "dataCenter-ZAfa4X4WG8ec": "Label Category",
  "dataCenter-887Aaj2j4sib": "Please Select Label Category",
  "dataCenter-y662mF2tlWNK": "Label Identifier Already Exists!",
  "dataCenter-Z2dd9oChSyVG": "Effective Time Required!",
  "dataCenter-HSgPi1PeeXjL": "Update Method Required!",
  "dataCenter-DFU9lWJP8ZjA": "Save Success",
  "dataCenter-u6U2J5OVbGeT": "Permanent Validity",
  "dataCenter-zjZ7PTY2Zttt": "Edit",
  "dataCenter-2SK03woX2Og8": "Manual Update",
  "dataCenter-BjT5loqBU8IN": "Daily Update",
  "dataCenter-uqmIsAGrt7Iq": "Every",
   "dataCenter-TY8DZTN5NH6L": "Update",
  "dataCenter-zH89usKoaaHo": "Monthly",
  "dataCenter-N9vQ7k6a74tA": "th Update",
  "dataCenter-b0DrTuZ3mTVm": "Is Non-Enum Value?",
  "dataCenter-Ay1qw84p5uVZ": "Is Enum Value?",
  "dataCenter-rV24YD4hUNl1": "Number of Label Values",
  "dataCenter-9knu7kk39kdD": "Greater Than",
  "dataCenter-iO2MYC0ihOI4": "Less Than",
  "dataCenter-tca04nLJOjpD": "50, Suggest Defining Parameters As",
  "dataCenter-lpxlSNB1ojy1": "Non-Enum",
  "dataCenter-xinw51HlHwve": "Enum",
  "dataCenter-g4LWNIi56wqj": "Confirm Modification",
  "dataCenter-72aLQz7AKRpN": "Cancel",
  "dataCenter-o5FcFhSuFkIb": "Label List",
  "dataCenter-q54RkANwbfjg": "Label Details",
  "dataCenter-Lx0pclzIIUtd": "Edit Label",
  "dataCenter-7Rx4QdK6bwio": "Create Label",
  "dataCenter-xDsFTj3GLFuf": "Basic Information",
  "dataCenter-WGEH4CQnFUW5": "Is Enum Value :",
  "dataCenter-5mLtOCDS7eGZ": "Yes",
  "dataCenter-TVoMSwq5HxKm": "No",
  "dataCenter-Eo0oqkvR1vcn": "Effective Time:",
  "dataCenter-iZsO104j1qvH": "Update Method:",
  "dataCenter-LZZQ7JBo1bPT": "Note Cannot Exceed 200 Characters",
  "dataCenter-IlK7GFvEszH5": "Note :",
  "dataCenter-XmZPL9AGLVL0": "Cancel",
  "dataCenter-ybZTl7Hq2auz": "Save",
  "dataCenter-aolatgYeiG81": "Monday",
  "dataCenter-eMO7A2WHhnvH": "Tuesday",
  "dataCenter-mewrT7DtYvrs": "Wednesday",
  "dataCenter-CEOnRubynyaK": "Thursday",
  "dataCenter-9BKTLmrr7Pam": "Friday",
  "dataCenter-FG5Yai4gxioX": "Saturday",
  "dataCenter-a554Pa0bpP4x": "Sunday",
  "dataCenter-CXL0hT9HVg7j": "Expired",
  "dataCenter-VqyoBWZGhIHy": "Cannot Calculate Next Cycle",
  "dataCenter-3A0Adr20CcSH": "None",
  "dataCenter-uNSAaCCC9xdk": "Calculation Time:",
  "dataCenter-CfIDmEDvtuE7": "Update Method",
  "dataCenter-xGq3AgR0GvYd": "Manual Update",
  "dataCenter-0aKMEWgndloB": "Manual Update After Creation",
  "dataCenter-OcPc2rMOYdkY": "Periodic Update",
  "dataCenter-Lrn6FycUO2KZ": "Periodic Update",
  "dataCenter-3Vx5QuU11bjg": "Periodic Update According to the Set Rules (If the Update Date Set in the Monthly Update Is Later Than the Last Day of the Current Month, the Last Day Will Be Updated)",
  "dataCenter-w9EV51kZ1Jhn": "Calculation Frequency:",
  "dataCenter-SRAIekBeph3y": "Daily",
  "dataCenter-lLGvgIUtg00b": "Weekly",
  "dataCenter-ATdE3WOzAdtf": "Monthly",
  "dataCenter-CrEOmA4r4oSX": "Next Calculation Time:",
  "dataCenter-FbtntkV99l5z": "Rule Setting",
  "dataCenter-wM4uKpPenqdv": "sql example: select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10",
  "dataCenter-drOHxVriv0n4": "Verify Formula",
  "dataCenter-KrEh28WOi0DA": "Function",
  "dataCenter-Wdbxu2jansBC": "Search",
  "dataCenter-fdW7icRqaXaw": "Usage:",
  "dataCenter-iPUKHExWyjix": "Description:",
  "dataCenter-v3yEWCVJ180l": "Example:",
  "dataCenter-4vL4qE9QgaTz": "Data Table",
  "dataCenter-t2zfNMyS4jsM": "Effective Time",
  "dataCenter-dwJYoJ31K6Px": "Permanent Validity",
  "dataCenter-tLHXAj74BkT2": "Limit Time Range",
  "dataCenter-wDdajRAmve8G": "Label Value",
  "dataCenter-AuHhiT1ceThk": "Business Mapping Name",
  "dataCenter-6FrDqvsMtNeW": "Operation Success",
  "dataCenter-500J9KydGis0": "Edit Label Value",
  "dataCenter-EDExUr0D9wJL": "Save",
  "dataCenter-pbDjShCZWPCo": "Please Enter Label Value to Query",
  }
};
