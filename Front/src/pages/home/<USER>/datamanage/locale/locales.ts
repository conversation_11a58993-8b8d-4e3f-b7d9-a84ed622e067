export default {
  cn: {
  "dataCenter-ioj9WQjH4ii2": "操作成功！",
  "dataCenter-5b0UmzL039bH": "表名：",
  "dataCenter-Uzpg809CXc3w": "导入数据",
  "dataCenter-iRFueUIHV7Ok": "主键字段：",
  "dataCenter-oxPeYNM3KZdy": "设置主键",
  "dataCenter-4nsEiRJEx7gK": "主键设置仅支持一次提交到生产环境操作",
  "dataCenter-QBRletHEHf48": "基本属性",
  "dataCenter-a6JFrjO4ImtR": "创建者：",
  "dataCenter-0ii2BPJ3fmzj": "创建时间：",
  "dataCenter-YUydYQWO7lEu": "更新者：",
  "dataCenter-4omb40g6xxle": "更新时间：",
  "dataCenter-lwwhy4YzcL6J": "表显示名",
  "dataCenter-l0Gc8L4OzcOp": "表显示名必须填写",
  "dataCenter-eoOgvf0JAywd": "最大长度限制为64位字符",
  "dataCenter-GYmz0c3WStjv": "仅支持中文、英文、数字、下划线",
  "dataCenter-dgb3OEMD9ZVS": "表显示名已存在",
  "dataCenter-qSiKhe3moyrE": "请求错误",
  "dataCenter-B3f1dvYgZbBB": "备注",
  "dataCenter-lj0yh5Zs7RkY": "最大长度限制为128位字符",
  "dataCenter-UxV5bkZ85bUK": "请输入中文、英文、数字、下划线",
  "dataCenter-70ayzdSbXD0g": "请输入备注",
  "dataCenter-YEddZgu9xL05": "物理模型",
  "dataCenter-xKtvNJthqPAt": "分区字段：",
  "dataCenter-HfKC9WzaZvUM": "设置分区字段",
  "dataCenter-FIw9S6kHg1RH": "分区字段设置仅支持一次提交到生产环境操作",
  "dataCenter-U4900juROtdD": "存储格式：",
  "dataCenter-34dhArILjw2g": "表结构设计",
  "dataCenter-S08g6xbC7YdY": "添加字段",
  "dataCenter-W52JYmnO2Dqo": "提交到草稿",
  "dataCenter-xp0CXeslxglP": "确认要提交到生产环境吗?",
  "dataCenter-tHGyRUDOzEiK": "提交到生产环境",
  "dataCenter-Rv5ljt6reo9n": "数据表",
  "dataCenter-qxs3IA80nfNW": "搜索",
  "dataCenter-VUvJbp7yJ16Z": "新建数据表",
  "dataCenter-GaheTri3KIEf": "请选择",
  "dataCenter-M4rM9CVIxqNY": "请输入字段名称!",
  "dataCenter-MzWfJrIbJmUt": "最大长度限制为64位字符",
  "dataCenter-fOzPu23NMY4X": "请输入英文,数字,下划线,$,仅限英文开头",
  "dataCenter-PZmVH42VkO5L": "字段名已存在",
  "dataCenter-EIjg7HXOE4kH": "请求错误",
  "dataCenter-c6Cx4qmGZsP2": "请输入字段显示名!",
  "dataCenter-d1Gyy1tz5gof": "请输入中,英文,数字,下划线",
  "dataCenter-EZwSo5ezxExQ": "字段显示名已存在",
  "dataCenter-FC50m52Phj8Z": "请选择数据类型!",
  "dataCenter-ANJ4IWy3QgSg": "字段名称",
  "dataCenter-4YI9My0PuY0C": "字段显示名",
  "dataCenter-bRu0Hka89D5l": "数据类型",
  "dataCenter-UIDevFCgWMYl": "备注",
  "dataCenter-0Q4Anbe0WV3A": "操作",
  "dataCenter-MehBGSY3VKDn": "保存",
  "dataCenter-7RtBMv61UFnz": "确认要删除吗?",
  "dataCenter-jZ76m9RDVCmv": "删除",
  "dataCenter-beQgi4uLXWg6": "确认要取消吗?",
  "dataCenter-DF7jk2ImUELd": "取消",
  "dataCenter-UDDEYB01LIp3": "编辑",
  "dataCenter-b4PfbadElZe6": "设置主键",
  "dataCenter-AEdgKW57a9og": "表名：",
  "dataCenter-r3Lwg18bPSZv": "设置主键",
  "dataCenter-ZhLG1JLRoA3z": "请选择主键",
  "dataCenter-MBpaPZKFc1Pw": "最多选择五个主键",
  "dataCenter-jXW88EWgQrox": "设置失败",
  "dataCenter-2QLvNBeSG5Ox": "主键设置仅支持一次提交到生产环境操作；可以设置多个字段作为联合主键。",
  "dataCenter-HZqeq9SAHTzg": "请选择分区字段",
  "dataCenter-Cc6UKnSeVajT": "请选择",
  "dataCenter-QjXIbDcvcRey": "请选择分区字段",
  "dataCenter-To3pA9HcuD3m": "最多32个字符",
  "dataCenter-VHpTNy68FyAz": "设置分区字段",
  "dataCenter-ViJhphPqyA3j": "分区字段",
  "dataCenter-o62M57Tv0Ve1": "分区字段设置仅支持一次提交到生产环境操作；可以设置多个字段作为分区字段，请注意分区字段层级关系；",
  "dataCenter-IoAVvdG52eg0": "最大支持设置5个字段。重要：分区字段的值建议在32个字符以内。",
  "dataCenter-xfx4mCgVuGHa": "新建数据表",
  "dataCenter-Pez7kDH2Rl0O": "项目ID：",
  "dataCenter-vuK1Hw8MCcrS": "表名称",
  "dataCenter-OQKJulVaGWHB": "请输入表名称",
  "dataCenter-t3zL2SCCMtTP": "请输入英文、数字、下划线、仅限英文开头",
  "dataCenter-ggOAaSCyws7D": "表名已存在",
  "dataCenter-oZbx8rETpxb1": "请求错误",
  "dataCenter-JHjl3u8zfBU5": "表显示名",
  "dataCenter-xkQMYx6y2UMm": "表显示名必须填写",
  "dataCenter-8msScbXhUsJE": "仅支持中文、英文、数字、下划线",
 
  },
  en: {
  "dataCenter-ioj9WQjH4ii2": "Success",
  "dataCenter-5b0UmzL039bH": "Table Name:",
  "dataCenter-Uzpg809CXc3w": "Import Data",
  "dataCenter-iRFueUIHV7Ok": "Primary Key Field:",
  "dataCenter-oxPeYNM3KZdy": "Set Primary Key",
  "dataCenter-4nsEiRJEx7gK": "Primary key setting only supports one submission to the production environment",
  "dataCenter-QBRletHEHf48": "Basic Properties",
  "dataCenter-a6JFrjO4ImtR": "Creator:",
  "dataCenter-0ii2BPJ3fmzj": "Create Time:",
  "dataCenter-YUydYQWO7lEu": "Updater:",
  "dataCenter-4omb40g6xxle": "Update Time:",
  "dataCenter-lwwhy4YzcL6J": "Table Display Name",
  "dataCenter-l0Gc8L4OzcOp": "Table Display Name is required",
  "dataCenter-eoOgvf0JAywd": "Maximum length is 64 characters",
  "dataCenter-GYmz0c3WStjv": "Only supports Chinese, English, numbers, and underscores",
  "dataCenter-dgb3OEMD9ZVS": "Table Display Name already exists",
  "dataCenter-qSiKhe3moyrE": "Request Error",
  "dataCenter-B3f1dvYgZbBB": "Remark",
  "dataCenter-lj0yh5Zs7RkY": "Maximum length is 128 characters",
  "dataCenter-UxV5bkZ85bUK": "Please enter Chinese, English, numbers, and underscores",
  "dataCenter-70ayzdSbXD0g": "Please enter remarks",
  "dataCenter-YEddZgu9xL05": "Physical Model",
  "dataCenter-xKtvNJthqPAt": "Partition Field:",
  "dataCenter-HfKC9WzaZvUM": "Set Partition Field",
  "dataCenter-FIw9S6kHg1RH": "Partition field setting only supports one submission to the production environment",
  "dataCenter-U4900juROtdD": "Storage Format:",
  "dataCenter-34dhArILjw2g": "Table Structure Design",
  "dataCenter-S08g6xbC7YdY": "Add Field",
  "dataCenter-W52JYmnO2Dqo": "Submit to Draft",
  "dataCenter-xp0CXeslxglP": "Are you sure you want to submit to the production environment?",
  "dataCenter-tHGyRUDOzEiK": "Submit to Production Environment",
  "dataCenter-Rv5ljt6reo9n": "Data Table",
  "dataCenter-qxs3IA80nfNW": "Search",
  "dataCenter-VUvJbp7yJ16Z": "New Data Table",
  "dataCenter-GaheTri3KIEf": "Please Select",
  "dataCenter-M4rM9CVIxqNY": "Please Enter Field Name!",
  "dataCenter-MzWfJrIbJmUt": "Maximum length is 64 characters",
  "dataCenter-fOzPu23NMY4X": "Please enter English, numbers, underscores, $, only English at the beginning",
  "dataCenter-PZmVH42VkO5L": "Field Name already exists",
  "dataCenter-EIjg7HXOE4kH": "Request Error",
  "dataCenter-c6Cx4qmGZsP2": "Please Enter Field Display Name!",
  "dataCenter-d1Gyy1tz5gof": "Please enter Chinese, English, numbers, underscores",
  "dataCenter-EZwSo5ezxExQ": "Field Display Name already exists",
  "dataCenter-FC50m52Phj8Z": "Please Select Data Type!",
  "dataCenter-ANJ4IWy3QgSg": "Field Name",
  "dataCenter-4YI9My0PuY0C": "Field Display Name",
  "dataCenter-bRu0Hka89D5l": "Data Type",
  "dataCenter-UIDevFCgWMYl": "Remark",
  "dataCenter-0Q4Anbe0WV3A": "Operation",
  "dataCenter-MehBGSY3VKDn": "Save",
  "dataCenter-7RtBMv61UFnz": "Are you sure you want to delete it?",
  "dataCenter-jZ76m9RDVCmv": "Delete",
  "dataCenter-beQgi4uLXWg6": "Are you sure you want to cancel it?",
  "dataCenter-DF7jk2ImUELd": "Cancel",
  "dataCenter-UDDEYB01LIp3": "Edit",
  "dataCenter-b4PfbadElZe6": "Set Primary Key",
  "dataCenter-AEdgKW57a9og": "Table Name:",
  "dataCenter-r3Lwg18bPSZv": "Set Primary Key",
  "dataCenter-ZhLG1JLRoA3z": "Please Select Primary Key",
  "dataCenter-MBpaPZKFc1Pw": "Maximum of five primary keys",
  "dataCenter-jXW88EWgQrox": "Set Failed",
  "dataCenter-2QLvNBeSG5Ox": "Primary key setting only supports one submission to the production environment operation; multiple fields can be set as a joint primary key.",
  "dataCenter-HZqeq9SAHTzg": "Please Select Partition Field",
  "dataCenter-Cc6UKnSeVajT": "Please Select",
  "dataCenter-QjXIbDcvcRey": "Please Select Partition Field",
  "dataCenter-To3pA9HcuD3m": "Maximum of 32 characters",
  "dataCenter-VHpTNy68FyAz": "Set Partition Field",
  "dataCenter-ViJhphPqyA3j": "Partition Field",
  "dataCenter-o62M57Tv0Ve1": "Partition field setting only supports one submission to the production environment operation; multiple fields can be set as a partition field, please pay attention to the hierarchical relationship of the partition field;",
  "dataCenter-IoAVvdG52eg0": "Maximum of five fields. Important: The value of the partition field is recommended to be within 32 characters.",
  "dataCenter-xfx4mCgVuGHa": "New Data Table",
  "dataCenter-Pez7kDH2Rl0O": "Project ID:",
  "dataCenter-vuK1Hw8MCcrS": "Table Name",
  "dataCenter-OQKJulVaGWHB": "Please Enter Table Name",
  "dataCenter-t3zL2SCCMtTP": "Please Enter English, Numbers, Underscores, Only English at the Beginning",
  "dataCenter-ggOAaSCyws7D": "Table Name already exists",
  "dataCenter-oZbx8rETpxb1": "Request Error",
  "dataCenter-JHjl3u8zfBU5": "Table Display Name",
  "dataCenter-xkQMYx6y2UMm": "Table Display Name is required",
  "dataCenter-8msScbXhUsJE": "Only supports Chinese, English, numbers, and underscores",
  }
};
