import { Scene } from '@antv/l7';
import { DrillDownLayer, setDataConfig } from '@antv/l7-district';
import { Mapbox } from '@antv/l7-maps';
import { Breadcrumb, Select, Spin, Table } from 'antd';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { t } from 'utils/translation';

import UserGroupPortraitService from 'service/userGroupPortraitService';

const { Option } = Select;

const userGroupPortraitService = new UserGroupPortraitService();

const default_cn = [
  {
    columnName: 'COUNTRY',
    operator: 'EQ',
    value: t('portraitCenter-sVgTIpIIbxw6')
  }
];

const default_cn_column = 'PROVINCE';

const langMapEnum = {
  zh_CN: 'NAME_CHN',
  en_US: 'NAME_EN'
};

const TGIColumns = [
  {
    key: 'order',
    dataIndex: 'order',
    title: t('portraitCenter-2xicdnTSEncu')
  },
  {
    key: 'name',
    dataIndex: 'name',
    title: t('portraitCenter-vzHO5LQ4PjjG')
  },
  {
    key: 'ratio',
    dataIndex: 'ratio',
    title: t('portraitCenter-d8SmuskC3RxO'),
    render: (text) => <span>{`${parseFloat((text * 100).toFixed(2))}%`}</span>
  },
  {
    key: 'tgi',
    dataIndex: 'tgi',
    title: 'TGI',
    render: (text) => <span>{`${parseFloat((text * 100).toFixed(2))}%`}</span>
  }
];

const columns = [
  {
    key: 'order',
    dataIndex: 'order',
    title: t('portraitCenter-2xicdnTSEncu')
  },
  {
    key: 'name',
    dataIndex: 'name',
    title: t('portraitCenter-vzHO5LQ4PjjG')
  },
  {
    key: 'ratio',
    dataIndex: 'ratio',
    title: t('portraitCenter-d8SmuskC3RxO'),
    render: (text) => <span>{`${parseFloat((text * 100).toFixed(2))}%`}</span>
  }
];

let drillLayer;
let scene;
let provinceData = [];
let cityDatas = [];

export default function MapCanvas(props) {
  const { scenarioData, state, segmentId, mapId, showTgi, segmentCount, force } = props;
  const { userGroupValue, TGIChecked, scenarioList } = state;

  const [data, setData] = useState([]);
  const [selectValue, setSelectValue] = useState(7);
  const [loading, setLoading] = useState(false);
  const [searchList, setSearchList] = useState(default_cn);
  const [column, setColumn] = useState(default_cn_column);
  const [city, setCity] = useState(null);
  const [reflash, setReflash] = useState(false);
  const [TGIData, setTGIData] = useState([]);
  const [ratioTotal, setRatioTotal] = useState(null);

  const mountNodeRef = useRef(null);
  const timer = useRef(null);

  const url = location.origin.includes('localhost') ? location.origin : `${location.origin}/aimarketer`;
  setDataConfig({
    country: {
      CHN: {
        1: {
          // 设置省级地图
          fill: {
            type: 'pbf', // 支持pbf 和 geojson pbf 进行了数据压缩，减少数据量
            url: `${url}/font/privince.bin`
            // url: '/map/privince.geoJson',
          },
          provinceLine: {
            type: 'pbf',
            url: `${url}/font/privinceLine.bin`
            // url: '/map/privinceLine.geoJson'
          },
          label: {
            type: 'json',
            url: `${url}/font/label.json`
          }
        },
        2: {
          fill: {
            type: 'pbf',
            url: `${url}/font/city.bin`
          }
        }
      }
    }
  });

  useEffect(() => {
    // provinceData = [];
    scene = new Scene({
      id: mapId,
      logoVisible: false,
      map: new Mapbox({
        pitch: 0,
        style: 'blank',
        center: [107.054293, 35.246265],
        zoom: 4.056
      })
    });

    scene.on('loaded', () => {
      drillLayer = new DrillDownLayer(scene, {
        provinceData,
        cityData: cityDatas,
        joinBy: ['NAME_CHN', 'name'],
        viewStart: 'Country',
        viewEnd: 'Province',
        label: false,
        province: {
          provinceStroke: '#8C8C8C',
          provinceStrokeWidth: 0.3,
          chinaNationalStroke: '#8C8C8C',
          chinaNationalWidth: 0.4
        },
        fill: {
          color: {
            field: 'ratio',
            values: (value) => {
              const maxRatio = _.maxBy(provinceData, (o) => {
                return o.ratio;
              })?.ratio;
              const minRatio = _.minBy(provinceData, (o) => {
                return o.ratio;
              })?.ratio;

              const level1 = value >= minRatio && value < minRatio + (maxRatio - minRatio) / 4;
              const level2 =
                value >= minRatio + (maxRatio - minRatio) / 4 &&
                value < minRatio + (maxRatio - minRatio) / 4 + (maxRatio - minRatio) / 4;
              const level3 =
                value >= minRatio + (maxRatio - minRatio) / 4 + (maxRatio - minRatio) / 4 &&
                value < minRatio + (maxRatio - minRatio) / 4 + (maxRatio - minRatio) / 4 + (maxRatio - minRatio) / 4;
              const level4 =
                value >= minRatio + (maxRatio - minRatio) / 4 + (maxRatio - minRatio) / 4 + (maxRatio - minRatio) / 4 &&
                value <= maxRatio;
              if (level1) return '#D9E9FF';
              if (level2) return '#B0D0FF';
              if (level3) return '#87B3FF';
              if (level4) return '#5B8FF9';
              else return '#D9E9FF';
            }
          }
        },
        drillDownEvent: (properties) => {
          setColumn('CITY');
          const _searchList = _.cloneDeep(searchList);
          _searchList.push({
            columnName: 'PROVINCE',
            operator: 'EQ',
            value: properties[langMapEnum[localStorage.getItem('lang')]]
          });
          // setCity(properties.NAME_CHN);
          setCity(properties[langMapEnum[localStorage.getItem('lang')]]);
          setSearchList(_searchList);
        },
        popup: {
          enable: true,
          Html: (props) => {
            if (props.total > 0) {
              return `<div style="maxWidth:300"><span style="font-Weight:600">${
                props.NAME_CHN
              }</span><br/><span>${props.total}人</span><br><span>${(props.ratio * 100).toFixed(2)}%</span></div>`;
            } else {
              return `<div><span style="font-Weight:600">${props.NAME_CHN}</span><br/><span>暂无数据</span></div>`;
            }
          }
        }
      });
    });
  }, [reflash]);

  const doRequest = async (data) => {
    try {
      const res = await userGroupPortraitService.calcSegmentChart2(data);
      if (!res) {
        // dispatch({ loading: false });
        setLoading(false);
      } else if (res.header.code === 0) {
        if (!_.isEmpty(res.body.chartData)) {
          const { dataList = [], total } = res.body.chartData;
          const _dataList = _.cloneDeep(dataList);
          const orderDataList = _dataList.map((item, index) => {
            return {
              order: index + 1,
              ...item
            };
          });
          setTGIData(orderDataList.slice(0, selectValue));
          // if (!ratioTotal) {
          //   setRatioTotal(total);
          // }
          setRatioTotal(total);
          setData(orderDataList);
          provinceData = _dataList;
          cityDatas = _dataList;
          setLoading(false);
          try {
            drillLayer.updateData('province', provinceData);
            drillLayer.updateData('city', cityDatas);
          } catch (err) {
            console.error(err);
          }
        }
        // dispatch({ loading: false });
      } else if (res.header.code === 210) {
        // dispatch({loading: false});
        timer.current = setTimeout(async () => {
          data.force = false;
          await doRequest(data);
        }, 3000);
      }
    } catch (error) {
      // responseData[selectedChart] = {rowList: [], columnList: []};
      // dispatch({ loading: false });
      setLoading(false);
    }
  };

  useEffect(() => {
    if (drillLayer) {
      const getChartsData = async () => {
        setLoading(true);
        if (scenarioData.length) {
          const params = {
            projectId: localStorage.getItem('projectId'),
            chartType: 'MAP',
            queryTable: 'USER',
            segmentId,
            queryType: 'SEGMENT_PORTRAIT',
            columnName: column,
            scenario: scenarioList,
            searchList,
            force: force?.status
          };
          doRequest(params);
          // const charstData = await userGroupPortraitService.calcSegmentChart({
          //   projectId: localStorage.getItem('projectId'),
          //   chartType: 'MAP',
          //   queryTable: 'USER',
          //   segmentId,
          //   column,
          //   scenario: scenarioList,
          //   searchList
          // });

          // const { dataList = [], total } = charstData.chartData;
          // let _dataList = _.cloneDeep(dataList);
          // let orderDataList = _dataList.map((item, index) => {
          //   return {
          //     order: index + 1,
          //     ...item
          //   };
          // });
          // setTGIData(orderDataList.slice(0, selectValue));
          // setRatioTotal(total);
          // setData(orderDataList);
          // provinceData = _dataList;
          // cityDatas = _dataList;

          // setLoading(false);
          // if (_dataList.length) {
          //   try {
          //     drillLayer.updateData('province', provinceData);
          //     drillLayer.updateData('city', cityDatas);
          //   } catch {}
          // }
        }
      };
      getChartsData();
      return () => {
        timer.current && clearTimeout(timer.current);
      };
    }
  }, [scenarioData, userGroupValue, scenarioList, searchList, force]);

  const reset = () => {
    setSearchList(default_cn);
    setCity(null);
    drillLayer.destroy();
    scene.destroy();
    setReflash(!reflash);
    setColumn(default_cn_column);
  };

  const onSelectChange = (e) => {
    setTGIData(data.slice(0, e));
    setSelectValue(e);
  };

  // useEffect(() => {
  //   console.log(state);
  //   console.log(segmentCount);
  // }, [ratioTotal, segmentCount, state]);

  return (
    <div className="chartWrap">
      {data.length ? (
        <Select value={selectValue} className="countSwitch" onChange={onSelectChange}>
          <Option value={7}>{t('portraitCenter-y5ISQcSRrnEO')}</Option>
          <Option value={15}>{t('portraitCenter-aEmS5ygwIHuh')}</Option>
          <Option value={20}>{t('portraitCenter-XpRVt3WjR8qG')}</Option>
          <Option value={25}>{t('portraitCenter-OuzCF7BPQp4G')}</Option>
          <Option value={30}>{t('portraitCenter-VqTadgzVULav')}</Option>
        </Select>
      ) : null}
      {/* {ratioTotal && segmentCount ? <span style={{ position: 'absolute', top: '35px', color: 'rgba(0, 0, 0, 0.45)' }}>有效人数占比 {(ratioTotal / segmentCount * 100).toFixed(0)}%</span> : null} */}
      {ratioTotal && segmentCount && !loading ? (
        <span
          style={{
            position: 'absolute',
            top: '35px',
            color: 'rgba(0, 0, 0, 0.45)'
          }}
        >
          {t('portraitCenter-3QwNQd8QMDW8')} {parseFloat(((ratioTotal / segmentCount) * 100).toFixed(2))}%
        </span>
      ) : null}
      <Spin spinning={loading}>
        <div className="mapWrap">
          <div className="mapItem">
            {city ? (
              <Breadcrumb>
                <Breadcrumb.Item onClick={reset}>{t('portraitCenter-8A0kJ5s2S1dT')}</Breadcrumb.Item>
                <Breadcrumb.Item>{city}</Breadcrumb.Item>
              </Breadcrumb>
            ) : null}
            <div />
            <div id={mapId} style={{ height: '100%', position: 'relative' }} ref={mountNodeRef} />
            <div className="legend">
              <span
                style={{
                  display: 'block',
                  marginBottom: '8px',
                  color: 'rgba(0, 0, 0, 0.45)',
                  fontSize: '12px'
                }}
              >
                {t('portraitCenter-5SjWQgs9wdNb')}
              </span>
              <span
                style={{
                  display: 'flex',
                  width: '200px',
                  alignItems: 'center'
                }}
              >
                <span
                  style={{
                    marginRight: '8px',
                    color: 'rgba(0, 0, 0, 0.45)',
                    fontSize: '12px'
                  }}
                >
                  {t('portraitCenter-Blm4r95jMxHa')}
                </span>
                <span
                  style={{
                    display: 'block',
                    background: 'linear-gradient(90deg, #D9E9FF 0%, #87B3FF 49.29%, #5B8FF9 100%)',
                    height: '8px',
                    width: '100px'
                  }}
                />
                <span
                  style={{
                    marginLeft: '8px',
                    color: 'rgba(0, 0, 0, 0.45)',
                    fontSize: '12px'
                  }}
                >
                  {t('portraitCenter-BP8UVHVIXRRy')}
                </span>
              </span>
            </div>
          </div>
          <div className="tgiItem">
            <Table
              dataSource={TGIData}
              columns={TGIChecked && showTgi ? TGIColumns : columns}
              pagination={false}
              scroll={{ y: 386 }}
              rowKey="order"
              size="middle"
            />
          </div>
        </div>
      </Spin>
    </div>
  );
}
