import { DualAxes } from '@ant-design/charts';
import { Empty, Select, Spin } from 'antd';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import UserGroupPortraitService from 'service/userGroupPortraitService';
import { t } from 'utils/translation';

const { Option } = Select;

const userGroupPortraitService = new UserGroupPortraitService();

export default function DemoDualAxes(props) {
  const {
    scenarioData,
    state,
    thema,
    column,
    rangeList,
    segmentId,
    segmentIdCheck,
    type,
    showTgi,
    segmentCount,
    force
  } = props;
  const { userGroupValue, userGroupCheck, TGIChecked, scenarioList, tgiStatus } = state;

  const [data, setData] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [selectValue, setSelectValue] = useState(7);
  const [loading, setLoading] = useState(false);
  const [ratioTotal, setRatioTotal] = useState(null);

  const timer = useRef(null);

  const doRequest = async (data) => {
    try {
      const res = await userGroupPortraitService.calcSegmentChart2(data);
      if (!res) {
        // dispatch({ loading: false });
        setLoading(false);
      } else if (res.header.code === 0) {
        if (!_.isEmpty(res.body.chartData)) {
          const { dataList = [], total } = res.body.chartData;
          setData(dataList.slice(0, selectValue));
          setRatioTotal(total);
          setDataSource(dataList);
          setLoading(false);
        }
        // dispatch({ loading: false });
      } else if (res.header.code === 210) {
        // dispatch({loading: false});
        timer.current = setTimeout(async () => {
          data.force = false;
          await doRequest(data);
        }, 3000);
      }
    } catch (error) {
      // responseData[selectedChart] = {rowList: [], columnList: []};
      // dispatch({ loading: false });
      setLoading(false);
    }
  };

  useEffect(() => {
    const getChartsData = async () => {
      setLoading(true);
      if (scenarioData.length) {
        const params = {
          projectId: localStorage.getItem('projectId'),
          chartType: 'COLUMN_CHART',
          queryTable: 'USER',
          queryType: 'SEGMENT_PORTRAIT',
          columnName: column,
          force: force?.status,
          segmentId: type === 'default' ? segmentId : segmentIdCheck,
          scenario: scenarioList,
          rangeList: rangeList || undefined
        };
        // const charstData = await userGroupPortraitService.calcSegmentChart({
        //   projectId: localStorage.getItem('projectId'),
        //   chartType: 'COLUMN_CHART',
        //   queryTable: 'USER',
        //   column,
        //   segmentId: type === 'default' ? segmentId : segmentIdCheck,
        //   scenario: scenarioList,
        //   rangeList: rangeList || undefined
        // });
        doRequest(params);
        // const { dataList, total } = charstData.chartData;

        // setData(dataList.slice(0, selectValue));
        // setRatioTotal(total);
        // setDataSource(dataList);
        // setLoading(false);
      }
    };
    getChartsData();
    return () => {
      timer.current && clearTimeout(timer.current);
    };
  }, [userGroupCheck, scenarioData, userGroupValue, scenarioList, tgiStatus, force]);

  const onSelectChange = (e) => {
    setData(dataSource.slice(0, e));
    setSelectValue(e);
  };

  const config = {
    data: [data, TGIChecked && showTgi ? data : ''],
    xField: 'name',
    yField: ['ratio', 'tgi'],
    legend: {
      layout: 'horizontal',
      position: 'bottom',
      radio: false
    },
    tooltip: {
      showTitle: true,
      title: 'name',
      fields: ['name', 'tgi', 'total', 'ratio'],
      customContent: (title, data) => {
        if (TGIChecked && showTgi) {
          return `<div style="padding: 12px 4px;list-style:none;">${
            data[0]?.data.name
          }<br><span style='display: block'><br><span style="background-color:#FFC53D;" class="g2-tooltip-marker"></span>${t(
            'portraitCenter-alpA8ZZP22jR',
            { tgi: parseFloat((data[1]?.data.tgi * 100).toFixed(2)) }
          )}</span><span style="display: block"><br><span style="background-color:${
            thema === 'content' ? '#5B8FF9' : '#5AD8A6'
          };" class="g2-tooltip-marker"></span>${t('portraitCenter-44A4jyFvanSh', {
            percent: parseFloat((data[3]?.data.ratio * 100).toFixed(2))
          })}</span><span style="display: block"><br><span class="g2-tooltip-marker"></span>${t(
            'portraitCenter-e17s9CshW0oD',
            { count: data[2]?.data.total }
          )}</span></div>`;
        } else {
          return `<div style="padding: 12px 4px;list-style:none;">${
            data[0]?.data.name
          }<br><span style='display: none'><br>TGI：${
            data[1]?.data.tgi
          }</span><span style="display: block"><br><span style="background-color:${
            thema === 'content' ? '#5B8FF9' : '#5AD8A6'
          };" class="g2-tooltip-marker"></span>${t('portraitCenter-44A4jyFvanSh', {
            percent: parseFloat((data[3]?.data.ratio * 100).toFixed(2))
          })}</span><span style="display: block"><br><span class="g2-tooltip-marker"></span>${t(
            'portraitCenter-e17s9CshW0oD',
            { count: data[2]?.data.total }
          )}</span></div>`;
        }
      }
    },
    slider: {},
    meta: {
      name: {
        sync: false // 开启之后 slider 无法重绘,
      },
      ratio: {
        alias: '占比',
        formatter: (v) => {
          return `${parseFloat((v * 100).toFixed(2))}%`;
        }
      },
      tgi: {
        alias: 'TGI',
        formatter: (v) => {
          return `${parseFloat((v * 100).toFixed(2))}%`;
        }
      },
      total: {
        alias: '人数',
        seriesField: 'total'
      }
    },
    geometryOptions: [
      {
        geometry: 'column',
        color: thema === 'content' ? '#5B8FF9' : '#5AD8A6',
        maxColumnWidth: 30,
        lineStyle: {
          lineWidth: 2
        }
      },
      {
        geometry: 'line',
        color: '#FFC53D',
        lineStyle: {
          lineWidth: 2
        }
      }
    ]
  };

  return (
    <div className="chartWrap">
      {data.length ? (
        <Select value={selectValue} className="countSwitch" onChange={onSelectChange}>
          <Option value={7}>{t('portraitCenter-y5ISQcSRrnEO')}</Option>
          <Option value={15}>{t('portraitCenter-aEmS5ygwIHuh')}</Option>
          <Option value={20}>{t('portraitCenter-XpRVt3WjR8qG')}</Option>
          <Option value={25}>{t('portraitCenter-OuzCF7BPQp4G')}</Option>
          <Option value={30}>{t('portraitCenter-VqTadgzVULav')}</Option>
        </Select>
      ) : null}
      {/* {ratioTotal && scenarioTotalCount ? <span style={{ position: 'absolute', top: '35px', color: 'rgba(0, 0, 0, 0.45)' }}>有效人数占比 {(ratioTotal / scenarioTotalCount * 100).toFixed(0)}%</span> : null} */}
      {ratioTotal && segmentCount && !loading ? (
        <span
          style={{
            position: 'absolute',
            top: '35px',
            color: 'rgba(0, 0, 0, 0.45)'
          }}
        >
          {t('portraitCenter-3QwNQd8QMDW8', { percent: parseFloat(((ratioTotal / segmentCount) * 100).toFixed(2)) })}
        </span>
      ) : null}
      <Spin spinning={loading}>
        {data.length ? (
          <DualAxes {...config} />
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{ position: 'relative', top: '100px' }} />
        )}
      </Spin>
    </div>
  );
}
