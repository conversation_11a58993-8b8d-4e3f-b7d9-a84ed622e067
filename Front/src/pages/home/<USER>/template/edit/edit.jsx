import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Modal, Popconfirm, Row, Select, Spin, message } from 'antd';
import html2canvas from 'html2canvas';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import CampaignV2Service from 'service/CampaignV2Service';
import CampaignV2Template from 'service/CampaignV2Template';
import scenarioService from 'service/ScenarioService';
import FunnelAnalysis from 'service/funnelAnalysis';
import { t } from 'utils/translation';
import { FlowEditor } from 'wolf-static-cpnt';
import NodeEditor from '../../create/nodeEditor/nodeEditor';
import './edit.scss';

const campaignV2Service = new CampaignV2Service();
const campaignV2Template = new CampaignV2Template();
const { Option } = Select;

const Create = (props) => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const [scenarioList, setScenarioList] = useState([]);
  const [tagList, setTagList] = useState([]);
  const [scenarioId, setScenarioId] = useState(null);
  const [isCollpase, setIsCollpase] = useState(true);
  const [state, setState] = useState({
    isDatatistId: true,
    isUtmRule: true,
    // 准备编辑的节点
    editingNode: {
      nodeId: null,
      busiType: '',
      detail: {},
      canvas: null,
      editorTip: '',
      editorTitle: ''
    },
    flows: [],
    loading: false,
    scenarioList: []
  });
  const [flowNodeMap, setFlowNodeMap] = useState(null);

  useEffect(() => {
    const init = async () => {
      const flowNodes = await campaignV2Service.listFlowNodesBox();
      const flowNodeMap = flowNodes
        .flatMap((v) => v.children)
        .reduce((a, b) => {
          a[b.id] = b;
          return a;
        }, {});
      setFlowNodeMap(flowNodeMap);
      const scenarioList = await scenarioService.scenarioList([]);
      setScenarioList(scenarioList.sort((a, b) => a.orderNum - b.orderNum));

      const tagList = await campaignV2Template.listTag([]);
      setTagList(tagList);

      if (!_.isNil(id)) {
        const data = await campaignV2Template.get(id);
        const { name, scenarioId, tagList, remark, campaignFlows } = data;
        setScenarioId(scenarioId);
        form.setFieldsValue({
          name,
          scenarioId,
          tagList,
          remark
        });

        setState({
          ...state,
          flows: campaignFlows
        });
      } else {
        setScenarioId(scenarioList.find((item) => item.isDefault)?.id);
        form.setFieldsValue({
          scenarioId: scenarioList.find((item) => item.isDefault)?.id
        });
      }
    };
    init();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onValueChange = (flows) => {
    setState({
      ...state,
      flows
    });
  };

  const onEditNode = (node) => {
    if (!node) return;
    const boxNode = flowNodeMap[node.id];
    // eslint-disable-next-line no-unused-vars
    const { editorTip, editorTitle, editorWidth, iframeUrl } = boxNode || {};
    let _iframeUrl = iframeUrl;
    const findScenarioCode = scenarioList.find((i) => i.id === scenarioId)?.code;
    // 如果是IFrameNode，需要把活动id，批次id，nodeId拼接到url后边
    if (!_.isNil(_iframeUrl)) {
      if (_.includes(_iframeUrl, '?')) {
        _iframeUrl = `${_iframeUrl}&campaignId=${props.id}&nodeId=${node.nodeId}&scenarioCode=${findScenarioCode}`;
      } else {
        _iframeUrl = `${_iframeUrl}?campaignId=${props.id}&nodeId=${node.nodeId}&scenarioCode=${findScenarioCode}`;
      }
    }
    setState({
      ...state,
      editingNode: {
        nodeId: node.nodeId,
        busiType: node.busiType,
        detail: node.detail,
        editorTip,
        editorTitle,
        editorWidth,
        iframeUrl: _iframeUrl
      }
    });
  };

  const dataProvider = {
    getFlowBoxNodes: () => {
      return campaignV2Service.listFlowNodesBox();
    },
    getAtTimeNodesData: (flows) => {
      return campaignV2Service.getNextTimeInAtTimeNodes(flows);
    },
    getEventCountLogsByProjectId: async () =>
      FunnelAnalysis.getEventCountLogsByProjectId({
        projectId: localStorage.getItem('projectId')
      })
  };

  const onNodeSubmit = (detail) => {
    const { flows, editingNode } = state;
    // 处理作假测试
    if (editingNode.busiType === 'FakeCampaignNode') {
      // this.props.fakeExecute(detail);
    } else {
      // 处理业务节点
      flows.filter((v) => v.nodeId === editingNode.nodeId).map((v) => (v.detail = detail));

      setState({
        ...state,
        editingNode: {},
        flows: [...flows]
      });
    }
  };

  const setEditingNode = (editingNode) => {
    setState({
      ...state,
      editingNode
    });
  };

  const onCloseNodeEditor = () => {
    setEditingNode({});
  };

  const checkAndFixFlowData = () => {
    // debugger;
    const { flows } = state;
    const nodeErrorMsgs = [];
    if (_.isEmpty(flows)) {
      nodeErrorMsgs.push(t('operationCenter-Y43NIk2SuQ3u'));
      return _.join(nodeErrorMsgs, ',  ');
    }
    flows.forEach((item) => {
      switch (item.busiType) {
        case 'JoinNode': // 合并节点
        case 'JoinHelperNode': // 合并节点辅助
        case 'ExitHelperNode': // 退出节点
        case 'AppPushNode': // app推送节点
        case 'WxPushNode': // 微信推送节点
        case 'SMSPushNode': // 短信推送节点;
          item.detail = { busiType: item.busiType };
          return;
        default:
          break;
      }

      if (!item.detail || Object.getOwnPropertyNames(item.detail) === 0 || !item.detail.isInit) {
        nodeErrorMsgs.push(`${t('operationCenter-aQAbpRyhqQYJ')}[${item.name}]`);
      }
      if (item.detail && !item.detail.isValid) {
        nodeErrorMsgs.push(`[${item.name}]${t('operationCenter-B7YSwPmaWjn4')}(${item.detail.errMsg ?? ''})`);
      }
    });
    if (nodeErrorMsgs.length > 0) {
      return _.join(nodeErrorMsgs, ',  ');
    }
  };

  // 首先校验数据
  const showFakeDrawer = async () => {
    setState({
      ...state,
      loading: true
    });
    try {
      const formValue = await form.validateFields();
      const errMsg = checkAndFixFlowData();
      if (errMsg) {
        message.error(errMsg);
        setState({
          ...state,
          loading: false
        });
        return;
      }

      // 校验通过后 设置样式 然后截图

      const outerContainer = document.querySelector('.outerContainer');
      const fakeDrawer = outerContainer.getElementsByClassName('id');
      _.forEach(fakeDrawer, (item, index) => (fakeDrawer[index].style.display = 'none'));
      const flowNodeEditor = document.querySelector('.FlowNodeEditor'); // 选中框
      const droppable = document.querySelector('.droppable'); // 小蓝圈
      // 获取outerContainer的高度
      const outerContainerHeight = outerContainer.offsetHeight;
      flowNodeEditor.style.display = 'none';
      droppable.style.border = 'none';
      outerContainer.style.height = `${outerContainerHeight - 200}px`;

      const canvas = await html2canvas(document.querySelector('.outerContainer'));
      const thumbnail = canvas.toDataURL('image/png');
      const data = {
        ...formValue,
        campaignFlows: state.flows,
        thumbnail
      };
      data.campaignFlows.map((item) => {
        if (!_.isEmpty(item.detail.blackSegmentList)) {
          item.detail.blackSegmentList = _.filter(item.detail.blackSegmentList, (v) => !_.isEmpty(v));
        }
        if (!_.isEmpty(item.detail.whiteSegmentList)) {
          item.detail.whiteSegmentList = _.filter(item.detail.whiteSegmentList, (v) => !_.isEmpty(v));
        }
        return item;
      });
      if (!_.isNil(id)) {
        data.id = parseInt(id);
      }
      await campaignV2Template.save(data);
      message.success(t('operationCenter-ekNxuPnoDnqo'));
      setState({
        ...state,
        loading: false
      });
      setTimeout(() => {
        props.history.go(-1);
      }, 500);
    } catch (err) {
      const outerContainer = document.querySelector('.outerContainer');

      // 重置outerContainer的高度 防止高度塌陷
      const outerContainerHeight = outerContainer.offsetHeight;
      outerContainer.style.height = `${outerContainerHeight + 200}px`;

      const fakeDrawer = outerContainer.getElementsByClassName('id');
      _.forEach(fakeDrawer, (item, index) => (fakeDrawer[index].style.display = 'block'));
      const flowNodeEditor = document.querySelector('.FlowNodeEditor');
      const droppable = document.querySelector('.droppable'); // 小蓝圈
      droppable.style.border = '1px dashed #1890ff';
      flowNodeEditor.style.display = 'block';
      setState({
        ...state,
        loading: false
      });
    }
  };

  const goBack = async () => {
    props.history.go(-1);
  };

  // const addImg = async () => {
  //   // 找到class为FlowNodeEditor的div 给一个display:none
  //   const flowNodeEditor = document.querySelector('.FlowNodeEditor');
  //   const droppable = document.querySelector('.droppable');
  //   flowNodeEditor.hidden = true;
  //   // flowNodeEditor.style.display = 'none';
  //   droppable.style.border = 'none';
  //   setTimeout(async () => {
  //     let canvas = await html2canvas(document.querySelector('.outerContainer'));
  //     // 转换成png
  //     let img = canvas.toDataURL('image/png');
  //     // 下载图片
  //     let a = document.createElement('a');
  //     a.href = img;
  //     a.download = '测试.png';
  //     a.click();
  //     setState({
  //       ...state,
  //       canvas: img
  //     });
  //   }, 1000);
  // };

  const formChange = (changedValues) => {
    if (changedValues?.tagList) {
      const { tagList } = changedValues;
      const lastTagList = tagList[tagList.length - 1];
      if (!/^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g.test(lastTagList)) {
        message.error(t('operationCenter-PD7ht1DFRCWa'), 0.5);
        const newTagList = tagList.slice(0, tagList.length - 1);
        form.setFieldsValue({
          tagList: newTagList
        });
      } else if (tagList.length > 0 && tagList.length > 5) {
        message.error(t('operationCenter-W04YkUIuWGwA'), 0.5);
        const newTagList = tagList.slice(0, 5);
        form.setFieldsValue({
          tagList: newTagList
        });
      } else if (tagList.length > 0 && lastTagList.length > 20) {
        message.error(t('operationCenter-Vixio3xD2TJW'), 0.5);
        const newTagList = tagList.slice(0, tagList.length - 1);
        form.setFieldsValue({
          tagList: newTagList
        });
      }
    }
  };

  const changeScenario = async (id) => {
    if (!_.isEmpty(state.flows)) {
      form.setFieldsValue({
        scenarioId
      });
      Modal.confirm({
        title: t('operationCenter-OFnsKGPsFwCm'),
        icon: <ExclamationCircleOutlined />,
        content: `${t('operationCenter-YVahBygUWE93')}${id ? t('operationCenter-To83W6yun12s') : t('operationCenter-fKMxlBlYYs9M')}t('operationCenter-RR19rfoCDQFx')`,
        okText: t('operationCenter-To83W6yun12s'),
        cancelText: t('operationCenter-bE7przWN8RP2'),
        onCancel: () => {
          onCancel();
        },
        onOk: () => {
          tip(id);
        }
      });
    } else {
      tip(id);
    }
  };

  const tip = (id) => {
    const flowNodeEditor = document.querySelector('.FlowNodeEditor');
    if (flowNodeEditor) {
      flowNodeEditor.style.display = 'none';
    }
    form.setFieldsValue({
      scenarioId: id
    });
    setScenarioId(id);
    setState({
      ...state,
      flows: []
    });
  };

  const onCancel = () => {
    // setShowModal(false);
  };

  return (
    <div className="templateEdit">
      <Spin tip="Loading..." spinning={state.loading}>
        <div>
          <header>
            <div className="Breadcrumbs">
              <Link to="/aimarketer/home/<USER>/template" style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
                {t('operationCenter-dOgMnbSoNXcl')}
              </Link>
              <span> / </span>
              <span>
                {id ? t('operationCenter-nyIapgKpBF1M') : t('operationCenter-QZGgSV3BQJt9')}
                {t('operationCenter-kcCqgqJbdpdk')}
              </span>
            </div>

            <div className="flex justify-between">
              <h2>
                {id ? t('operationCenter-nyIapgKpBF1M') : t('operationCenter-QZGgSV3BQJt9')}
                {t('operationCenter-050yBF79c7gs')}
              </h2>
              <Button onClick={() => setIsCollpase(!isCollpase)}>
                {isCollpase ? t('operationCenter-Xfi2PfwJC5Xs') : t('operationCenter-g6wI7C8xobFj')}
              </Button>
            </div>
          </header>
        </div>
        <div className="Board" style={isCollpase ? { display: 'block' } : { display: 'none' }}>
          <Form form={form} onValuesChange={formChange} layout="vertical">
            <Row>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label={t('operationCenter-ZqvjXJIeFOGy')}
                  colon={false}
                  labelAlign="left"
                  rules={[
                    { required: true, message: t('operationCenter-15QWawKFetHO') },
                    { max: 64, message: t('operationCenter-mkLftAQBlsEk') },
                    {
                      pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
                      message: t('operationCenter-Y2h8MQjlOkuw')
                    }
                  ]}
                >
                  <Input showCount maxLength={64} placeholder={t('operationCenter-H3CWFkNbZJUJ')} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="scenarioId"
                  label={t('operationCenter-RR19rfoCDQFx')}
                  colon={false}
                  labelAlign="left"
                  rules={[
                    { required: true, message: t('operationCenter-f6eYXwuyjH5K') }
                    // { pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g, message: '仅支持中文、英文、数字、下划线' }
                  ]}
                >
                  <Select
                    onChange={(id) => changeScenario(id)}
                    placeholder={t('operationCenter-f6eYXwuyjH5K')}
                    allowClear
                    showSearch
                    disabled={id}
                    optionFilterProp="children"
                  >
                    {_.map(scenarioList, (item) => {
                      return (
                        <Option key={item.id} value={item.id}>
                          {item.name}[{item.code}]
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="tagList" label={t('operationCenter-kJNzyvFTxCuj')} colon={false} labelAlign="left">
                  <Select placeholder={t('operationCenter-He3rN8rQhmGb')} mode="tags">
                    {tagList.map((item) => {
                      return (
                        <Option key={item} value={item}>
                          {item}
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="remark"
                  label={t('operationCenter-52tLtZLf9Ph0')}
                  colon={false}
                  labelAlign="left"
                  rules={[{ max: 150, message: t('operationCenter-KxcXZdUyWAeq') }]}
                >
                  <Input maxLength={150} showCount placeholder={t('operationCenter-2kcL0JckmnGf')} />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
        {scenarioId && (
          <div className={isCollpase ? 'content hasForm' : 'content hideForm'} style={{ background: '#fff' }}>
            <FlowEditor
              value={state.flows || []}
              dataProvider={dataProvider}
              onChange={(v) => onValueChange(v)}
              onClickNode={onEditNode}
              onEditNode={onEditNode}
              debug={false}
              mode="edit"
            />
            <NodeEditor
              detail={state.editingNode.detail}
              tip={state.editingNode.editorTip}
              title={state.editingNode.editorTitle}
              width={state.editingNode.editorWidth}
              busiType={state.editingNode.busiType}
              iframeUrl={state.editingNode.iframeUrl}
              onSubmit={onNodeSubmit}
              onClose={onCloseNodeEditor}
              scenario={scenarioList.find((it) => it.id === scenarioId)}
            />
          </div>
        )}
        {scenarioId && (
          <div className="footerButton">
            {/* <Button onClick={addImg} style={{ marginRight: '8px' }}>生成截图</Button> */}
            <Popconfirm title={t('operationCenter-sOaQicbkCx4y')} onConfirm={goBack}>
              <Button>
                {t('operationCenter-1UAYiaEbCnIB')}
                {id ? t('operationCenter-nyIapgKpBF1M') : t('operationCenter-QZGgSV3BQJt9')}
              </Button>
            </Popconfirm>
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginLeft: '10px' }}
              loading={state.loading}
              onClick={showFakeDrawer}
            >
              {t('operationCenter-R967zgN0AuCa')}
              {id ? t('operationCenter-nyIapgKpBF1M') : t('operationCenter-QZGgSV3BQJt9')}
            </Button>
          </div>
        )}
      </Spin>
    </div>
  );
};

export default Create;
