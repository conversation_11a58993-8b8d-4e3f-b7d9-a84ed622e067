import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React from 'react';
import { Link } from 'react-router-dom';
import { t } from 'utils/translation';

function calc(day1, day2) {
  const StartTime = Date.parse(_.replace(day1, /-/g, '/'));
  const EndTime = Date.parse(_.replace(day2, /-/g, '/'));
  const oneDay = 1000 * 60 * 60 * 24;
  const d1 = new Date(StartTime).getTime();
  const d2 = new Date(EndTime).getTime() + 86400000;
  const day = {
    remainTime: parseInt((d2 - d1) / oneDay),
    nowTime: new Date().getTime(),
    endTime: d2
  };

  if (day.nowTime > day.endTime) {
    return t('operationCenter-BuEH3WrXuaYW');
  }
  return `${parseInt((d2 - d1) / oneDay)}${t('operationCenter-qUiGDZdvyRm3')}`;
}

export default {
  // query表单配置
  elements: {
    name: {
      type: 'input',
      label: t('operationCenter-2z9LzIKTthvK'),
      operator: 'LIKE',
      width: 10,
      componentOptions: {
        allowClear: true,
        placeholder: t('operationCenter-2z9LzIKTthvK')
      }
    },
    tagList: {
      type: 'select',
      label: t('operationCenter-kJNzyvFTxCuj'),
      operator: 'IN',
      width: 10,
      componentOptions: {
        mode: 'multiple',
        allowClear: true,
        placeholder: t('operationCenter-f6eYXwuyjH5K'),
        // filterOption: (input, option) => {
        //   return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        // },
        options: []
      }
    },
    createUserId: {
      type: 'select',
      label: t('operationCenter-jmnlUA1U28B1'),
      operator: 'EQ',
      width: 10,
      componentOptions: {
        allowClear: true,
        showSearch: true,
        placeholder: t('operationCenter-f6eYXwuyjH5K'),
        filterOption: (input, option) => {
          return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        options: []
      }
    },
    scenarioId: {
      type: 'select',
      label: t('operationCenter-RR19rfoCDQFx'),
      operator: 'EQ',
      width: 10,
      componentOptions: {
        allowClear: true,
        showSearch: true,
        placeholder: t('operationCenter-f6eYXwuyjH5K'),
        filterOption: (input, option) => {
          return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        options: []
      }
    },
    useCount: {
      type: 'moreScreen',
      label: t('operationCenter-vVLPByGG1vrN'),
      width: 10,
      operator: 'EQ',
      componentOptions: {
        allowClear: true
      }
    }
  },
  // 表单数据
  formData: {},
  columns: [
    {
      title: t('operationCenter-R4phepUOO5tG'),
      dataIndex: 'id',
      width: 120
    },
    {
      title: t('operationCenter-MR87FyokSsX9'),
      dataIndex: 'taskName',
      width: 280,
      ellipsis: {
        showTitle: false
      },
      render: (text, val) => (
        <Link to={`/aimarketer/home/<USER>/detailURL/${val.id}`}>
          <Tooltip placement="topLeft" title={text}>
            {text}
          </Tooltip>
        </Link>
      )
    },
    {
      title: t('operationCenter-FjAagHOwB6Xv'),
      dataIndex: 'endTime',
      width: 320,
      render: (text, val) =>
        `${val?.effectiveStartTime} ~ ${val?.effectiveEndTime} ${t('operationCenter-Mv4jCvGGN4ot')}${calc(
          val.effectiveStartTime,
          val.effectiveEndTime
        )}`
    },
    {
      title: t('operationCenter-b6tA5STW5rl2'),
      dataIndex: 'status',
      sorter: true,
      width: 120,
      render: (text) => (
        <div className="status">
          <span className={getStatus(text)} />
          {statusMap[text]}
        </div>
      )
    },
    {
      title: t('operationCenter-qrCC7CBEaAVK'),
      dataIndex: 'createUserName',
      width: 120
    },
    {
      title: t('operationCenter-09FBwbbWjrYf'),
      dataIndex: 'createTime',
      width: 200,
      sorter: (a, b) => a.createTime - b.createTime,
      // defaultSortOrder: 'descend',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: t('operationCenter-iONBMVIJ6zVn'),
      dataIndex: 'updateUserName',
      width: 120
    },
    {
      title: t('operationCenter-QK8hMQaap5mS'),
      dataIndex: 'updateTime',
      width: 200,
      sorter: (a, b) => a.updateTime - b.updateTime,
      defaultSortOrder: 'descend',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    }
  ]
};
const statusMap = {
  ENABLE: t('operationCenter-3OteYV7RcpsA'),
  DISABLE: t('operationCenter-splfHhnsi3RR')
};

const getStatus = (text) => {
  if (!text) return '';
  if (text === 'ENABLE') return 'circle enable';
  if (text === 'DISABLE') return 'circle disable';
};
