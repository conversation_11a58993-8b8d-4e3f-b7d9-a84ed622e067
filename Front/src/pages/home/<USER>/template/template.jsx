import heatStatistics from '@/service/heatStatistics';
import CheckAuth from '@/utils/checkAuth';
import { FilterOutlined, MoreOutlined } from '@ant-design/icons';
import { Button, Col, Dropdown, Empty, message, Modal, Pagination, Row, Spin, Typography } from 'antd';
import QueryForList from 'components/bussinesscoms/queryforlist/index';
import Heat from 'components/heat';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import CampaignV2Template from 'service/CampaignV2Template';
import scenarioService from 'service/ScenarioService';
import UserService from 'service/UserService';
import { t } from 'utils/translation';
import config from './config';
import './template.scss';

const campaignV2Template = new CampaignV2Template();
const userService = new UserService();
const { Text } = Typography;

export default function Template() {
  const history = useHistory();
  const [isFold, setIsFold] = useState(false);
  const [search, setSearch] = useState([]);
  const [elements, setElements] = useState(config.elements);
  const [showModal, setShowModal] = useState(false);
  const [editTemplateId, setEditTemplateId] = useState('');
  const [loading, setLoading] = useState(false);
  const [templateList, setTemplateList] = useState([]);
  const [pageConfig, setPageConfig] = useState({
    page: 1,
    size: 10
  });
  const [headList, setHeadList] = useState([]);
  // const queryData = useCallback((data) => setSearch([...data]), []);
  const queryData = (data) => {
    const _filters = _.map(data, (item) => {
      if (item.propertyName === 'USE') {
        const valueArr = item.value.split(',');
        return {
          connector: 'AND',
          propertyName: item.propertyName,
          operator: valueArr[0] === '' ? 'EQ' : valueArr[0],
          value: valueArr.splice(1).toString()
        };
      }
      if (item.propertyName === 'useCount') {
        const valueArr = item.value.split(',');
        return {
          connector: 'AND',
          propertyName: item.propertyName,
          operator: valueArr[0] === '' ? 'EQ' : valueArr[0],
          value: valueArr.splice(1).toString()
        };
      }
      return {
        connector: 'AND',
        propertyName: item.propertyName,
        operator: item.operator,
        value: item.propertyName === 'tagList' ? `${item.value}` : item.value
      };
    });
    setPageConfig((prevConfig) => ({
      ...prevConfig,
      page: 1
    }));
    setSearch(_filters);
  };

  useEffect(() => {
    init();
  }, [pageConfig, search]);

  const init = async () => {
    setLoading(true);
    try {
      const redata = await userService.listBy([]);
      const scenarioList = await scenarioService.scenarioList([]);
      const templateList = await campaignV2Template.query({
        ...pageConfig,
        search,
        sorts: [
          {
            direction: 'desc',
            propertyName: 'id'
          }
        ]
      });
      const tagList = await campaignV2Template.listTag([]);
      const _elements = _.cloneDeep(elements);
      _elements.tagList.componentOptions.options = _.map(tagList, (item) => ({
        key: item,
        value: item,
        text: item
      }));
      _elements.createUserId.componentOptions.options = _.map(redata, (item) => ({
        key: item.id,
        value: item.id,
        text: item.name
      }));
      _elements.scenarioId.componentOptions.options = _.map(scenarioList, (item) => ({
        key: item.id,
        value: item.id,
        text: item.name
      }));
      setElements(_elements);
      setTemplateList(templateList);
      const ids = templateList?.content.map((item) => item.id).join();
      const params = [
        {
          operator: 'EQ',
          propertyName: 'projectId',
          value: localStorage.getItem('projectId')
        },
        {
          operator: 'EQ',
          propertyName: 'dataType',
          value: 'CAMPAIGN_TEMPLATE'
        },
        {
          operator: 'IN',
          propertyName: 'operateType',
          value: 'FAVORITE,COPY,USE'
        },
        !_.isEmpty(ids) && {
          operator: 'IN',
          propertyName: 'dataId',
          value: ids
        }
      ].filter((item) => item);
      const _headList = await heatStatistics.listBy(params);
      setHeadList(_headList);
      setLoading(false);
    } catch (err) {
      console.error('🚀 ~ file: template.jsx:119 ~ init ~ err:', err);
      setLoading(false);
    }
  };

  const smartUrlCreate = () => {
    history.push('/aimarketer/home/<USER>/template/edit');
  };

  const del = async () => {
    await campaignV2Template.delById(editTemplateId);
    message.success(t('operationCenter-M8EaSHLU8nD7'));

    const totalPages = Math.ceil((templateList.totalElements - 1) / pageConfig.size);
    const newPage = pageConfig.page > totalPages ? totalPages : pageConfig.page;

    setPageConfig((prevConfig) => ({
      ...prevConfig,
      page: newPage
    }));

    onCancel();
  };

  const jump = (id) => {
    const findValue = templateList.content.find((item) => item.id === id);
    history.push('/aimarketer/home/<USER>/create?type=TEMPLATE', {
      templateValue: findValue
    });
  };

  const onCancel = () => {
    setShowModal(false);
    setEditTemplateId('');
  };

  const changePage = async (page, size) => {
    setPageConfig({
      page,
      size
    });
  };

  const goDetail = (id) => {
    history.push(`/aimarketer/home/<USER>/template/detail/${id}`);
  };

  return (
    <div className="campaignV2Template">
      <header>
        <div className="title">
          <h2>{t('operationCenter-UnbzV0RGcSUe')}</h2>
        </div>
        <div className="rightSide">
          <Button onClick={() => setIsFold(!isFold)} icon={<FilterOutlined />}>
            {t('operationCenter-HRO8FVSJYH6k')}
          </Button>
          {CheckAuth.checkAuth('aim_campaignV2_template_edit') && (
            <Button onClick={smartUrlCreate} style={{ marginLeft: '8px' }} type="primary">
              {t('operationCenter-cSWYhr2TqkOl')}
            </Button>
          )}
        </div>
      </header>
      <QueryForList elements={elements} onQuery={queryData} show={isFold} />
      <div className="main">
        <Spin spinning={loading}>
          <Row gutter={16}>
            {templateList?.content?.map((item) => {
              const findValue = headList.find((i) => i.dataId === item.id);
              return (
                <Col
                  key={item.id}
                  style={{
                    margin: '10px 0',
                    paddingLeft: 12,
                    paddingRight: 12
                  }}
                  xxl={5}
                  xl={6}
                  lg={8}
                  md={8}
                  sm={8}
                  xs={8}
                >
                  <div
                    className="colItem"
                    style={{
                      border: '1px solid #fff',
                      minHeight: '416px',
                      width: '100%',
                      backgroundColor: '#fff'
                    }}
                  >
                    <div className="process">
                      <div className="gradient" />
                      <div className="mask">
                        <CheckAuth code="aim_campaignV2_template_edit">
                          <span className="use" onClick={() => jump(item.id)}>
                            {t('operationCenter-RcMedMH8LIm7')}
                          </span>
                        </CheckAuth>
                        {CheckAuth.checkAuth('aim_campaignV2_template_edit') ||
                        CheckAuth.checkAuth('aim_campaignV2_delete_template') ? (
                          <Dropdown
                            placement="bottomRight"
                            getPopupContainer={(triggerNode) => triggerNode.parentNode}
                            menu={{
                              items: [
                                CheckAuth.checkAuth('aim_campaignV2_template_edit') && {
                                  label: t('operationCenter-nyIapgKpBF1M'),
                                  onClick: () => {
                                    history.push(`/aimarketer/home/<USER>/template/edit/${item.id}`);
                                  },
                                  key: 'edit'
                                },
                                CheckAuth.checkAuth('aim_campaignV2_delete_template') && {
                                  label: t('operationCenter-ttBX2JpE713M'),
                                  onClick: () => {
                                    setEditTemplateId(item.id);
                                    setShowModal(true);
                                  },
                                  key: 'del'
                                }
                              ]
                            }}
                          >
                            <Button type="default" icon={<MoreOutlined />} size="small" shape="circle" />
                          </Dropdown>
                        ) : null}
                      </div>
                      <img className="img" src={item.thumbnail} alt="" style={{ width: '100%', height: '100%' }} />
                    </div>
                    <div className="info" onClick={() => goDetail(item.id)}>
                      <div className="title flex">
                        <Text ellipsis={{ tooltip: item.name }} style={{ width: '90%' }}>
                          {' '}
                          {item.name}
                        </Text>

                        <Heat dataId={item.id} dataType="CAMPAIGN_TEMPLATE" notSearch findValue={findValue} />
                      </div>
                      <div className="templateInformation">
                        <div>
                          {t('operationCenter-lTIkFOlePiqJ')}
                          {item.id}
                        </div>
                        <div>
                          {t('operationCenter-NVqnLNLfnMzt')}
                          {item.createUserName}
                        </div>
                        <div className="tags">
                          {item?.tagList &&
                            item?.tagList.map((item, index) => {
                              return (
                                <span key={index} className="tag">
                                  <Text style={{ width: '50px' }} ellipsis={{ tooltip: item }}>
                                    {item}
                                  </Text>
                                </span>
                              );
                            })}
                        </div>
                      </div>
                    </div>
                  </div>
                </Col>
              );
            })}
          </Row>
          {templateList?.totalElements > 0 ? (
            <Pagination
              showTotal={(total) =>
                `${t('operationCenter-EomZHolpW4m5')} ${total} ${t('operationCenter-G711hVg2dzD9')}`
              }
              current={pageConfig.page}
              // defaultCurrent={1}
              total={templateList?.totalElements}
              showQuickJumper
              showSizeChanger
              showLessItems
              pageSize={pageConfig.size}
              pageSizeOptions={['10', '12', '16', '20']}
              onChange={changePage}
            />
          ) : (
            <Empty
              className="empty"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={t('operationCenter-Y7zGAZmkKzGz')}
            />
          )}
        </Spin>
        <Modal
          title={t('operationCenter-n8fCO9uIgKD1')}
          open={showModal}
          onOk={del}
          onCancel={onCancel}
          okText={t('operationCenter-UTgC6ysIckdl')}
          cancelText={t('operationCenter-bE7przWN8RP2')}
        >
          <p>{t('operationCenter-xdad9HH2fRZB')}</p>
        </Modal>
      </div>
    </div>
  );
}
