import CheckAuth from '@/utils/checkAuth';
import { Button, Descriptions, message, Modal, Spin, Typography } from 'antd';
import Heat from 'components/heat';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { Link, useHistory, useParams } from 'react-router-dom';
import CampaignV2Service from 'service/CampaignV2Service';
import CampaignV2Template from 'service/CampaignV2Template';
import FunnelAnalysis from 'service/funnelAnalysis';
import { FlowCanvas } from 'wolf-static-cpnt';

import { t } from 'utils/translation';
import NodeEditor from '../../create/nodeEditor/nodeEditor';
import './details.scss';

const querystring = require('querystring');

const { Text } = Typography;
const campaignV2Template = new CampaignV2Template();
const campaignV2Service = new CampaignV2Service();
const tagStyle = {
  marginRight: '8px',
  textAlign: 'center',
  border: '1px solid #D9D9D9',
  background: '#FAFAFA'
};

const TemplateDetail = () => {
  const { id } = useParams();
  const [data, setData] = useState(null);
  const history = useHistory();
  const [flowNodeMap, setFlowNodeMap] = useState(null);
  const [showEdit, setShowEdit] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [state, setState] = useState({
    buttonLoading: false,
    showCreate: false,
    tagList: [],
    isShowAnalysis: false, // 是否显示活动分析
    visible: false,
    tabsKey: 1,
    load: true,
    tableParams: {
      page: 1,
      // search: [{ propertyName: 'campaignId', operator: 'EQ', value: params.id }],
      size: 5,
      sorts: [
        {
          direction: 'desc',
          propertyName: 'id'
        }
      ]
    },
    // 准备编辑的节点
    editingNode: {
      nodeId: null,
      busiType: '',
      detail: {},
      editorTip: '',
      editorTitle: ''
    },
    loading: false,
    mode: 'detail',
    fakeLoading: false,
    timeDetail: {},
    executionDate: {},
    reEditLoading: false,
    totalCount: 0,
    canvasType: null,
    showUpdateRules: false
  });

  useEffect(() => {
    const init = async () => {
      const data = await campaignV2Template.get(parseInt(id));
      setData(data);
      const flowNodes = await campaignV2Service.listFlowNodesBox();
      const flowNodeMap = flowNodes
        .flatMap((v) => v.children)
        .reduce((a, b) => {
          a[b.id] = b;
          return a;
        }, {});
      setFlowNodeMap(flowNodeMap);
    };
    init();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const dataProvider = {
    getAtTimeNodesData: (flows) => {
      return campaignV2Service.getNextTimeInAtTimeNodes(flows);
    },
    getEventCountLogsByProjectId: async () =>
      FunnelAnalysis.getEventCountLogsByProjectId({
        projectId: localStorage.getItem('projectId')
      })
  };

  const onNodeSubmit = (detail) => {
    const { editingNode } = state;
    // 处理作假测试
    if (editingNode.busiType === 'FakeCampaignNode') {
      fakeExecute(detail);
      setState({
        ...state,
        mode: 'detail'
      });
    }
  };

  const setEditingNode = (editingNode) => {
    setState({
      ...state,
      editingNode
    });
  };

  const onNodeEditorClose = () => {
    setShowEdit(false);
    setEditingNode({});
    setState({
      ...state,
      mode: 'detail'
    });
  };

  const fakeExecute = async (fakeBusi) => {
    // 作假测试
    const { id } = state;
    setState({
      ...state,
      fakeLoading: true
    });
    message.success(t('operationCenter-Vp6deDTdIWW8'), 1);
    try {
      await campaignV2Service.fakeExecute({
        campaignId: id,
        ...fakeBusi
      });
      setState({
        ...state,
        editingNode: {}
      });
      // await getDetail();
      setState({
        ...state,
        fakeLoading: false
      });
    } catch (error) {
      setState({
        ...state,
        fakeLoading: false
      });
    }
  };

  const getNodeEditor = () => {
    // 获取右侧详情
    const { editingNode } = state;
    return (
      <NodeEditor
        loading={state.fakeLoading}
        mode={state.mode}
        detail={editingNode.detail}
        tip={editingNode.editorTip}
        title={editingNode.editorTitle}
        width={editingNode.editorWidth}
        busiType={editingNode.busiType}
        iframeUrl={editingNode.iframeUrl}
        onSubmit={onNodeSubmit}
        onClose={onNodeEditorClose}
        scenarioId={data?.scenarioId}
        scenario={data?.scenario}
      />
    );
  };

  const onFlowCanvasClick = (node) => {
    setShowEdit(true);
    if (!node) return;
    const boxNode = flowNodeMap[node.id];
    const { editorTip, editorTitle, editorWidth, iframeUrl } = boxNode || {};
    let _iframeUrl = iframeUrl;
    // 如果是IFrameNode，需要把活动id，批次id，nodeId拼接到url后边
    if (!_.isNil(_iframeUrl)) {
      const params = querystring.parse(window.location.search.substr(1));
      if (_.includes(_iframeUrl, '?')) {
        _iframeUrl = `${_iframeUrl}&campaignId=${params.id}&batchId=${params.calcLogId}&nodeId=${node.nodeId}`;
      } else {
        _iframeUrl = `${_iframeUrl}?campaignId=${params.id}&batchId=${params.calcLogId}&nodeId=${node.nodeId}`;
      }
    }
    setState({
      ...state,
      editingNode: {
        nodeId: node.nodeId,
        busiType: node.busiType,
        detail: node.detail,
        editorTip,
        editorTitle,
        editorWidth,
        iframeUrl: _iframeUrl
      }
    });
  };

  const jump = () => {
    // 跳转到/aimarketer/home/<USER>/create?type=TEMPLATE 并且携带state
    history.push('/aimarketer/home/<USER>/create?type=TEMPLATE', {
      templateValue: data
    });
  };

  const delTemplate = async () => {
    await campaignV2Template.delById(data.id);
    message.success(t('operationCenter-M8EaSHLU8nD7'));
    history.push('/aimarketer/home/<USER>/template');
  };

  const onCancel = () => {
    setShowModal(false);
  };

  return (
    <>
      <Spin tip="Loading..." spinning={false}>
        <div className="datails">
          <div className="templateDetails">
            <div className="Breadcrumbs">
              <Link to="/aimarketer/home/<USER>/template" style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
                {t('operationCenter-klOrOFtWyd6n')}
              </Link>
              <span> / </span>
              <span>{t('operationCenter-KNNkRivkWXLG')}</span>
            </div>
            <div className="title">
              <div className="my-flex-ja">
                <h2>{data?.name}</h2>
                <Heat dataId={id} dataType="CAMPAIGN_TEMPLATE" />
              </div>
              <div className="buttons">
                <CheckAuth code="aim_campaignV2_template_edit">
                  <Button type="primary" style={{ marginRight: '8px' }} onClick={jump}>
                    {t('operationCenter-RcMedMH8LIm7')}
                  </Button>
                </CheckAuth>
                <CheckAuth code="aim_campaignV2_delete_template">
                  <Button onClick={() => setShowModal(true)}>{t('operationCenter-PjpoCD0cIPA7')}</Button>
                </CheckAuth>
              </div>
            </div>
            <Descriptions column={3} className="details">
              <Descriptions.Item label={t('operationCenter-hJ3KoOqprdUV')}>{data?.id}</Descriptions.Item>
              <Descriptions.Item label={t('operationCenter-3SFaubxC8tkA')}>{data?.createUserName}</Descriptions.Item>
              <Descriptions.Item label={t('operationCenter-kJNzyvFTxCuj')}>
                <div className="tags">
                  {data?.tagList &&
                    data?.tagList.map((item, index) => {
                      return (
                        <span key={index} className="tag" style={tagStyle}>
                          <Text style={{ width: '50px' }} ellipsis={{ tooltip: item }}>
                            {item}
                          </Text>
                        </span>
                      );
                    })}
                </div>
              </Descriptions.Item>
              <Descriptions.Item label={t('operationCenter-TE71maDHCWym')}>{data?.scenario?.name}</Descriptions.Item>
              <Descriptions.Item label={t('operationCenter-w1SSeAzgaeZJ')}>
                {dayjs(data?.updateTime).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label={t('operationCenter-52tLtZLf9Ph0')}>
                <Text style={{ width: 150 }} ellipsis={{ tooltip: data?.remark }}>
                  {data?.remark}
                </Text>
              </Descriptions.Item>
            </Descriptions>
          </div>
          <section>
            <div style={{ fontWeight: '600' }}>{t('operationCenter-NJTPLrmweq2s')}</div>
            <FlowCanvas
              dataProvider={dataProvider}
              value={data?.campaignFlows}
              onClickNode={onFlowCanvasClick}
              mode="detail"
            />
            {/* {tabKey === 1 ? <BasicContent data={data} count={count} /> : <SmarkDetails smarkData={data} />} */}
          </section>
        </div>
        {showEdit && getNodeEditor()}
        <Modal
          title={t('operationCenter-n8fCO9uIgKD1')}
          open={showModal}
          onOk={delTemplate}
          onCancel={onCancel}
          okText={t('operationCenter-UTgC6ysIckdl')}
          cancelText={t('operationCenter-bE7przWN8RP2')}
        >
          <p>{t('operationCenter-Unjf0MTxcEXT')}</p>
        </Modal>
      </Spin>
    </>
  );
};

export default TemplateDetail;
