import { FilterOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/hooks';
import { Button, Modal, Table, message } from 'antd';
import _ from 'lodash';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';
import CheckAuth from 'utils/checkAuth';

import Log from 'utils/log';
import getMenuTitle from 'utils/menuTitle';

import QueryForList from 'components/bussinesscoms/queryforlist/index';
import ColumnActionCom from 'components/featurecoms/tableactioncom/index';
import indexManagementService from 'service/indexManagementService';
// import UserService from 'service/userService';
import UserService from 'service/UserService';

import AddChart from '../addChart/index';
// import Edit from '../edit/index';
import { t } from 'utils/translation';
import config from './config';
import './indexManagement.scss';

const userService = new UserService();

const log = Log.getLogger('UserManage');

const stateToProps = (state) => {
  return {
    meunData: state.meunData.menus.main
  };
};

async function getTableList(params) {
  const data = await indexManagementService.pageQuery(params);
  return {
    total: data.totalElements,
    list: data.content
  };
}

const featureData = {
  edit: {
    text: t('analysisCenter-xFdVYQUXVb1d'),
    code: 'aim_metrics_edit'
  },
  Divider: { key: 'Divider' },
  dropdownData: {
    delete: {
      text: t('analysisCenter-CgsrPYpBsXsI'),
      code: 'aim_metrics_edit'
    }
  }
};

const IndexManagement = (props) => {
  const [visible, setVisible] = useState(false);
  const [search, setSearch] = useState([]);
  const [elements, setElements] = useState(config.elements);

  useEffect(() => {
    init();
    log.debug('init', elements);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  async function init() {
    const redata = await userService.listBy([]);
    const _elements = _.cloneDeep(elements);
    _elements.createUserId.componentOptions.options = _.map(redata, (item) => ({
      key: item.id,
      value: item.id,
      text: item.name
    }));
    _elements.updateUserId.componentOptions.options = _.map(redata, (item) => ({
      key: item.id,
      value: item.id,
      text: item.name
    }));
    setElements(_elements);
  }

  // eslint-disable-next-line no-unused-vars
  const { tableProps, params, refresh } = useRequest(
    ({ current, pageSize, sorter: s, filters: f }) => {
      const p = { page: current, size: pageSize };
      if (s?.field && s?.order) {
        p.sorts = [
          {
            direction: _.includes(s?.order, 'desc') ? 'desc' : 'asc',
            propertyName: s?.field
          }
        ];
      } else if (!s) {
        p.sorts = [
          {
            direction: 'desc',
            propertyName: 'updateTime'
          }
        ];
      }
      if (f) {
        Object.entries(f).forEach(([filed, value]) => {
          p[filed] = value;
        });
      }
      return getTableList({ ...p, search });
    },
    {
      paginated: true,
      defaultPageSize: 10,
      refreshDeps: [search]
    }
  );

  const searchRef = useRef(search);

  searchRef.current = search;

  const onColumnActionClick = (key, feature, record) => {
    if (key === 'edit') {
      props.history.push(`/aimarketer/home/<USER>/indexManagement/detail/${record.id}`);
    } else if (key === 'delete') {
      Modal.confirm({
        title: t('analysisCenter-JOtLzp4IEdLx'),
        content: <p className="confirmDelete">{t('analysisCenter-bz7dIuLS7Lt8')}</p>,
        okText: t('analysisCenter-99yXbg0uLjSx'),
        okType: 'danger',
        cancelText: t('analysisCenter-Um9398LCxql9'),
        async onOk() {
          await indexManagementService.delById(record.id);
          message.success(t('analysisCenter-wpapmhXGcmED'));
          setSearch([...searchRef.current]);
        },
        onCancel() {}
      });
    }
  };

  const [isFold, setIsFold] = useState(false);

  const actionColumn = {
    title: t('analysisCenter-kWRv5sWtBal1'),
    className: 'td-set',
    width: 150,
    fixed: 'right',
    render: (text, record) => {
      return <ColumnActionCom closeDropdown featureData={featureData} record={record} onClick={onColumnActionClick} />;
    }
  };

  const [columns, setColumns] = useState(config.columns);
  // you can read sorter and filters from params[0]
  // const { sorter = {}, filters = {} } = params[0];

  const queryData = useCallback((data) => setSearch([...data]), []);

  // 增加操作列
  useEffect(() => {
    const _columns = _.cloneDeep(columns);
    _columns.push(actionColumn);
    setColumns(_columns);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 改变弹出框可见状态
  const changeVisible = (visible) => {
    setVisible(visible);
  };

  return (
    <div className="dataForm">
      <header>
        <h1>{getMenuTitle(props.meunData, props.history.location.pathname)}</h1>
        <div className="rightSide">
          <Button
            onClick={() => setIsFold(!isFold)}
            style={{
              cursor: 'pointer'
            }}
          >
            <FilterOutlined style={{ fontSize: '16px' }} />
            {t('analysisCenter-1sG94GZ2p3ct')}
          </Button>
          <CheckAuth code="aim_metrics_edit">
            <Button
              className="DTButton"
              type="primary"
              onClick={() => {
                changeVisible(true);
              }}
            >
              {t('analysisCenter-gMzUwKYqZQrp')}
            </Button>
          </CheckAuth>
        </div>
      </header>
      <QueryForList show={isFold} elements={elements} onQuery={queryData} />
      <Table
        columns={columns}
        className="table1"
        rowKey="id"
        {...tableProps}
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showLessItems: true,
          pageSizeOptions: ['10', '20', '50'],
          showTotal: (e) => `${t('analysisCenter-7xvThY2VvLNR')} ${e} ${t('analysisCenter-x1syjbwgePFe')}`,
          showQuickJumper: true
        }}
        scroll={{ x: 1300 }}
      />
      {visible && <AddChart visible={visible} history={props.history} action={() => setVisible(false)} />}
    </div>
  );
};

export default connect(stateToProps)(IndexManagement);
