// @import 'assets/css/variable.scss';

.dataForm {
  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    margin: 0 -24px 24px;
    padding: 14px 24px;
    box-shadow: 0 1px 2px 0 rgba(220, 226, 230, 0.8),
      0 1px 8px 0 rgba(220, 226, 230, 0.59);

    h1 {
      font-size: 24px;
      margin: 0;
    }

    .rightSide {
      button {
        border-radius: 6px;
        &:first-child {
          margin-right: 16px;
        }
      }
    }
  }

  .query {
    margin: 0;
    padding: 24px;
    box-shadow: 0 1px 2px 0 rgba(220, 226, 230, 0.8),
      0 1px 8px 0 rgba(220, 226, 230, 0.59);
    margin-bottom: 6px;

    .ant-row .left .ant-row > .ant-col {
      & > span:first-child {
        min-width: 120px;
      }

      label.ant-checkbox-wrapper {
        padding-left: 120px;
      }

      .ant-select-dropdown {
        z-index: 1;
      }
    }

    .ant-row .right {
      padding: 0 12px !important;
    }
  }

  .table1 {
    padding: 24px;
    background-color: #fff;

    td {
      .status {
        display: flex;
        align-items: center;

        .circle {
          display: inline-block;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          margin-right: 5px;
          background-color: rgba(0, 0, 0, 0.25);

          &.green {
            background-color: #52c41a;
          }
        }
      }

      &.name {
        div {
          display: flex;

          span {
            word-break: break-all;
          }
        }
      }
    }

    td.td-set {
      span {
        cursor: pointer;

        span {
          display: inline-block;
          padding: 0 10px;
          color: $primary_color;
        }
      }
    }
  }
}
