import dayjs from 'dayjs';
import { t } from 'utils/translation';

export default {
  // query表单配置
  elements: {
    name: {
      type: 'input',
      label: t('analysisCenter-3f8V5cUWkRh0'),
      operator: 'LIKE',
      width: 8,
      componentOptions: {
        allowClear: true,
        placeholder: t('analysisCenter-3f8V5cUWkRh0')
      }
    },
    createUserId: {
      type: 'select',
      label: t('analysisCenter-x9Cn1aSOQu7J'),
      operator: 'EQ',
      width: 8,
      componentOptions: {
        allowClear: true,
        showSearch: true,
        placeholder: t('analysisCenter-6lwwV201ikQ7'),
        filterOption: (input, option) => {
          return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        options: []
      }
    },
    createTime: {
      type: 'dateRange',
      label: t('analysisCenter-1HcTJjUd3cSI'),
      width: 8,
      operator: 'DATE_BETWEEN',
      componentOptions: {
        allowClear: true
      }
    },
    updateUserId: {
      type: 'select',
      label: t('analysisCenter-2obZyogGDJ0d'),
      operator: 'EQ',
      width: 8,
      componentOptions: {
        allowClear: true,
        showSearch: true,
        placeholder: t('analysisCenter-6lwwV201ikQ7'),
        filterOption: (input, option) => {
          return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        options: []
      }
    },
    updateTime: {
      type: 'dateRange',
      label: t('analysisCenter-0anpfwjJIA22'),
      width: 8,
      operator: 'DATE_BETWEEN',
      componentOptions: {
        allowClear: true
      }
    }
  },
  // 表单数据
  formData: {},
  columns: [
    {
      title: t('analysisCenter-3f8V5cUWkRh0'),
      dataIndex: 'name',
      width: 200
    },
    {
      title: t('analysisCenter-0859fFIHpBRe'),
      dataIndex: 'dataType',
      width: 100
    },
    {
      title: t('analysisCenter-IK4HnywyYJcj'),
      dataIndex: 'businessTable',
      width: 120,
      render: (text) => {
        if (text) {
          return t('analysisCenter-askVeGq2zIPc');
        } else {
          return t('analysisCenter-crayVkffXI5X');
        }
      }
    },
    {
      title: t('analysisCenter-EdLJgffEjRY6'),
      dataIndex: 'remark',
      width: 250,
      ellipsis: true
    },
    {
      title: t('analysisCenter-x9Cn1aSOQu7J'),
      dataIndex: 'createUserName',
      width: 100
    },
    {
      title: t('analysisCenter-PdJK9hz5P1Ty'),
      dataIndex: 'createTime',
      width: 200,
      // defaultSortOrder: 'descend',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: t('analysisCenter-2obZyogGDJ0d'),
      dataIndex: 'updateUserName',
      width: 100
    },
    {
      title: t('analysisCenter-0anpfwjJIA22'),
      dataIndex: 'updateTime',
      width: 200,
      sorter: (a, b) => a.updateTime - b.updateTime,
      defaultSortOrder: 'descend',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    }
  ]
};
