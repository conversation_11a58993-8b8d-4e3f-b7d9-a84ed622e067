import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { CheckOutlined } from '@ant-design/icons';
import { Card, Checkbox, Col, Input, Modal, Radio, Row, message } from 'antd';
import React, { useEffect, useState } from 'react';
import AnalysisCenterService from 'service/analysisCenterService';

import ScenarioService from 'service/ScenarioService';
import { t } from 'utils/translation';

const { Search } = Input;

const EditChart = (props) => {
  const {
    form: { getFieldDecorator, validateFields },
    visible,
    action
  } = props;
  const [loading, setLoading] = useState(false);
  const [businessTable, setBusinessTable] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const [businessTableList, setBusinessTableList] = useState([]);
  const [nobusinessTableList, setNobusinessTableList] = useState([]);

  useEffect(() => {
    (async () => {
      try {
        setLoading(true);
        await ScenarioService.scenarioList([]);
        const _businessTableList = await AnalysisCenterService.getTableList({
          businessTable: true
        });
        const _nobusinessTableList = await AnalysisCenterService.getTableList({
          businessTable: false
        });
        setBusinessTableList(_businessTableList);
        setNobusinessTableList(_nobusinessTableList);
        setLoading(false);
      } catch (error) {
        setLoading(false);
      }
    })();
  }, []);

  const okHandle = () => {
    validateFields(async (err, fields) => {
      if (err) return;
      try {
        const data = {
          businessTable,
          name: ''
        };
        if (businessTable) {
          data.tables = businessTableList;
        } else {
          if (!fields.nobusinessTable) {
            message.error(t('analysisCenter-71Iq1P60B9J1'));
            return;
          }
          data.tables = nobusinessTableList.filter((n) => n.id === fields.nobusinessTable);
        }
        setLoading(true);
        // const res = await indexManagementService.save(data);
        props.history.push(`/aimarketer/home/<USER>/indexManagement/detail/add?add`, {
          dataTable: data.tables,
          businessTable
        });
      } catch {
        setLoading(false);
      }
    });
  };

  return (
    <Modal
      title={t('analysisCenter-gMzUwKYqZQrp')}
      open={visible}
      width={800}
      onOk={okHandle}
      maskClosable={false}
      onCancel={action}
      confirmLoading={loading}
    >
      <Form>
        <Row gutter={16}>
          <Col span={12}>
            <Card
              onClick={() => setBusinessTable(true)}
              style={{ borderColor: businessTable ? 'red' : undefined }}
              hoverable
            >
              <CheckOutlined
                style={{
                  fontSize: 20,
                  color: '#ff7517',
                  marginRight: 10,
                  visibility: businessTable ? 'visible' : 'hidden'
                }}
              />
              <span>{t('analysisCenter-wIcBiM6bkmgr')}</span>
            </Card>
            <div
              style={{
                marginTop: 12,
                padding: '0px 8px',
                height: 300,
                overflow: 'auto',
                display: businessTable ? '' : 'none'
              }}
            >
              {/* <Form.Item label="ID类型">
                {getFieldDecorator('scenario.id', {
                  // rules: [
                  //   { required: businessTable, message: '请选择' }
                  // ]
                })(<Select showSearch optionFilterProp="children" style={{ width: '100%' }} placeholder="请选择">
                  {scenarioList.map(n => <Option key={n.id} value={n.id}>{n.name}[{n.code}]</Option>)}
                </Select>)}
              </Form.Item> */}
              {businessTableList.map((n) => (
                <p key={n.id}>
                  <Checkbox disabled checked>
                    {n.displayName}
                  </Checkbox>
                </p>
              ))}
            </div>
          </Col>
          <Col span={12}>
            <Card
              onClick={() => setBusinessTable(false)}
              style={{ borderColor: !businessTable ? 'red' : undefined }}
              hoverable
            >
              <CheckOutlined
                style={{
                  fontSize: 20,
                  color: '#ff7517',
                  marginRight: 10,
                  visibility: !businessTable ? 'visible' : 'hidden'
                }}
              />
              <span>{t('analysisCenter-icDkAev5o5wp')}</span>
            </Card>
            <div
              style={{
                marginTop: 12,
                padding: '0px 8px',
                height: 300,
                overflow: 'auto',
                display: !businessTable ? '' : 'none'
              }}
            >
              <div style={{ marginBottom: 10, marginTop: 4 }}>
                <Search
                  onChange={(e) => setSearchValue(e.target.value)}
                  value={searchValue}
                  placeholder={t('analysisCenter-M2AR1s9Wmcjb')}
                />
              </div>
              <Form.Item>
                {getFieldDecorator('nobusinessTable', {
                  // rules: [
                  //   { required: !businessTable, message: '请选择' }
                  // ]
                })(
                  <Radio.Group>
                    {nobusinessTableList
                      .filter((w) => w.displayName.indexOf(searchValue) > -1)
                      .map((n) => (
                        <p key={n.id}>
                          <Radio value={n.id} style={{ marginBottom: 12 }}>
                            {n.displayName}
                          </Radio>
                        </p>
                      ))}
                  </Radio.Group>
                )}
              </Form.Item>
            </div>
          </Col>
        </Row>
        <div
          style={{
            marginTop: 20,
            fontSize: 14,
            color: 'rgba(0, 0, 0, 0.447058823529412)'
          }}
        >
          {t('analysisCenter-AnLegm5breml')}
        </div>
      </Form>
    </Modal>
  );
};

export default Form.create()(EditChart);
