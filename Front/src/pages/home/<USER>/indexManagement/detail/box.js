import React from 'react';
import _ from 'lodash';

const style = {
  // border: '1px dashed gray',
  // backgroundColor: 'white',
  // margin: 5,
  // padding: '0 5px',
  // width: 120,
  // borderRadius: 3,
  cursor: 'point'
};

const tableDataTypeConfig = {
  NUMBER: ['LONG', 'INTEGER', 'INT', 'DOUBLE'],
  DATE: ['DATE', 'DATETIME', 'TIMESTAMP', 'HIVE_DATE', 'HIVE_TIMESTAMP'],
  STRING: ['STRING']
};

export default ({ name, dataType, dataSource, onClick }) => {
  const renderTableIcon = () => {
    if (dataType === 'TABLE_SCHEMA') {
      // 根据dataSource的dataType显示不同图标
      if (_.includes(tableDataTypeConfig.STRING, dataSource.dataType)) {
        return (
          <svg
            version="1.1"
            id="图层_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
            x="0px"
            y="0px"
            viewBox="0 0 1024 1024"
            width="16px"
            height="16px"
            xmlSpace="preserve"
            style={{ marginRight: 5 }}
            fill="rgba(0,0,0, .65)"
          >
            <path d="M75.4,768H0l117.5-512h85.1L320,768h-78.5l-23.8-115.2h-118L75.4,768z M120,556.1h78l-38.5-187.7L120,556.1z" />
            <path d="M588,256c47.1,0,79.6,41.2,79.6,101v72.5c0,30.6-9.5,57.6-35.3,76.8c28,17.1,39.8,46.2,39.8,76.8V667c0,59.7-32.5,101-79.6,101H384V256H588z M567.8,460.8c14.6,0,19.1-4.3,19.1-24.2v-61.2c0-15.6-6.7-24.2-19.1-24.2H464.7v109.5H567.8z M572.3,672.7c12.3,0,19.1-8.5,19.1-24.2v-74c0-21.3-10.1-22.8-25.8-22.8H464.7v120.9H572.3z" />
            <path d="M943,375.6c0-15.5-6.8-23.9-19.1-23.9h-87.7c-12.4,0-19.1,8.4-19.1,23.9v272.9c0,15.5,6.8,23.9,19.1,23.9h87.7c12.4,0,19.1-8.4,19.1-23.9v-61.9h81v81.6c0,59.1-32.6,99.9-79.9,99.9H815.9c-47.2,0-79.9-40.8-79.9-99.9V355.9c0-59.1,32.6-99.9,79.9-99.9h128.3c47.2,0,79.9,40.8,79.9,99.9v81.6h-81V375.6z" />
          </svg>
        );
      } else if (_.includes(tableDataTypeConfig.NUMBER, dataSource.dataType)) {
        return (
          <svg
            version="1.1"
            id="图层_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
            x="0px"
            y="0px"
            width="16px"
            height="16px"
            viewBox="0 0 1024 1024"
            xmlSpace="preserve"
            style={{ marginRight: 5 }}
            fill="rgba(0,0,0, .65)"
          >
            <path d="M106,362.7L0,395.4V310l124-54h68v512h-86V362.7z" />
            <path d="M414.3,413h-89.2v-55.2c0-56.6,40.8-101.8,91.8-101.8h130c51,0,91.8,45.3,91.8,101.8v89.1c0,56.6-43.3,83.4-91.8,101.8l-109.6,42.4c-16.6,6.4-25.5,9.9-25.5,28.3v52.3H640V768H320V596.9c0-56.6,43.3-83.4,91.8-101.8l109.6-42.4c16.6-6.4,25.5-9.9,25.5-28.3v-43.8c0-18.4-8.9-28.3-25.5-28.3h-81.6c-16.6,0-25.5,9.9-25.5,28.3V413z" />
            <path d="M796.4,645.6c0,18.3,8.9,28.1,25.4,28.1H907c16.5,0,25.4-9.8,25.4-28.1v-66.1c0-21.1-12.7-28.1-30.5-28.1h-87.1v-90h87.1c17.8,0,25.4-5.6,25.4-28.1v-54.9c0-18.3-8.9-28.1-25.4-28.1h-75c-16.5,0-31.4,9.8-31.4,28.1v32.4H704v-53.5C704,301,750.7,256,801.5,256h125.9c50.9,0,91.5,45,91.5,101.3v73.1c0,30.2-10.8,57-40,76c31.8,16.9,45.1,45.7,45.1,76v84.4c0,56.3-40.7,101.3-91.5,101.3h-136c-50.9,0-91.5-45-91.5-101.3v-53.5h91.5V645.6z" />
          </svg>
        );
      } else if (_.includes(tableDataTypeConfig.DATE, dataSource.dataType)) {
        return (
          <svg
            version="1.1"
            id="图层_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
            x="0px"
            y="0px"
            width="16px"
            height="16px"
            viewBox="0 0 1024 1024"
            xmlSpace="preserve"
            style={{ marginRight: 5 }}
            fill="rgba(0,0,0, .65)"
          >
            <g>
              <path d="M816,128H704V64h-96v64H416V64h-96v64H208c-79.4,0-144,64.6-144,144v480c0,79.4,64.6,144,144,144h608c79.4,0,144-64.6,144-144V272C960,192.6,895.4,128,816,128z M864,752c0,26.5-21.5,48-48,48H208c-26.5,0-48-21.5-48-48V272c0-26.5,21.5-48,48-48h112v64h96v-64h192v64h96v-64h112c26.5,0,48,21.5,48,48V752z" />
              <rect x="256" y="384" width="192" height="96" />
              <rect x="256" y="576" width="192" height="96" />
              <rect x="576" y="384" width="192" height="96" />
              <rect x="576" y="576" width="192" height="96" />
            </g>
          </svg>
        );
      }
    }
  };

  return (
    <span onClick={() => onClick && onClick(dataSource)} style={{ ...style, display: 'flex', alignItems: 'center' }}>
      {renderTableIcon()}
      <span title={name} className="boxName">
        {name}
      </span>
    </span>
  );
};
