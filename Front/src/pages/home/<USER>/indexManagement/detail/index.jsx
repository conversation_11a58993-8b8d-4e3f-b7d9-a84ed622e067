import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { DownOutlined } from '@ant-design/icons';
import { Breadcrumb, Button, Col, Input, Popover, Row, Select, Tree, message } from 'antd';
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/material.css';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { Controlled as CodeMirror } from 'react-codemirror2';
import { Link, useParams } from 'react-router-dom';

import AnalysisCenterService from 'service/analysisCenterService';
import indexManagementService from 'service/indexManagementService';
import { t } from 'utils/translation';
import Box from './box';
import './index.scss';
import { myfunction, words } from './myFunction';

require('codemirror/mode/javascript/javascript');

const { Search, TextArea } = Input;
const { Option } = Select;

const Detail = (props) => {
  // 表字段过滤值
  const [fieldsSearchValue, setFieldsSearchValue] = useState('');
  // 表字段列表
  const [tableSchemaList, setTableSchemaList] = useState([]);
  // 指标文本
  const [value, setValue] = useState('');
  // 函数列表
  const [functionList, setFunctionList] = useState([]);
  // 表字段过滤值
  const [functionSearchValue, setFunctionSearchValue] = useState('');

  const [loading, setLoading] = useState(false);

  const [info, setInfo] = useState({});

  const ref = useRef(null);

  const [def, setDef] = useState({});

  const [checkLoading, setCheckLoading] = useState(false);

  const [checkResultData, setCheckResultData] = useState({});

  const { id } = useParams();

  // const def = {
  //   keywords: words(keywords)
  //   // types: cTypes,
  //   // blockKeywords: words('case catch class else for foreach if switch try while'),
  //   // defKeywords: words('function local class'),
  //   // typeFirstDefinitions: true,
  //   // atoms: words('true false null'),
  //   // hooks: { '#': cppHook },
  //   // modeProps: { fold: ['brace', 'include'] }
  // };

  useEffect(() => {
    const init = async () => {
      const preKeywords = 'AS';
      const redata = await indexManagementService.getFunctionsList();
      setFunctionList(redata);

      let needAddKeywords = ' ';
      _.forEach(redata, (item) => {
        // 找到(的位置截断
        const targetIndex = item.createSql.indexOf('(');
        const targetWord = item.createSql.substring(0, targetIndex);
        needAddKeywords += `${targetWord} `;
      });
      let info = {};
      if (id !== 'add') {
        info = await indexManagementService.get(id);
        setInfo(info);
        setValue(info?.formulaText);
      }

      const data = await AnalysisCenterService.getTableSchemaList([
        {
          operator: 'IN',
          propertyName: 'table.id',
          value:
            id !== 'add'
              ? info?.tables.map((n) => n.id).join()
              : props?.location?.state?.dataTable?.map((n) => n.id).join()
        }
      ]);

      const tableSchemaList = _.groupBy(data, 'table.id');
      // 根据data去生成另一种keywords
      let variableKeywords = '';
      _.forEach(data, (item) => {
        variableKeywords += `[${item.table.displayName}.${item.displayName}#${item.id}] `;
      });
      setDef({
        keywords: words(`${_.trim(preKeywords)} ${_.trim(needAddKeywords)}`),
        // types: cTypes,
        blockKeywords: words('case catch else for foreach if switch try while'),
        defKeywords: words('function local class'),
        variableKeywords: words(_.trim(variableKeywords))
        // typeFirstDefinitions: true,
        // atoms: words('true false null'),
        // hooks: { '#': cppHook },
        // modeProps: { fold: ['brace', 'include'] }
      });
      setTableSchemaList(tableSchemaList);
    };
    init();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { getFieldDecorator, validateFields } = props.form;

  const options = {
    mode: 'customModel',
    lineWrapping: true,
    // addModeClass: true,
    spellcheck: true
  };

  const filterFunctionList = _.filter(functionList, (item) => {
    return item.name.indexOf(functionSearchValue) > -1 || _.toLower(item.name).indexOf(functionSearchValue) > -1;
  });

  const onClickField = (data) => {
    const Doc = ref.current.editor.doc;
    // 首先让编辑器获得焦点
    Doc.cm.focus();
    // 将光标置为原来的位置
    const cursorPos = Doc.getCursor();

    if (
      data.dataType === 'DATE' ||
      data.dataType === 'DATETIME' ||
      data.dataType === 'TIMESTAMP' ||
      data.dataType === 'HIVE_DATE' ||
      data.dataType === 'HIVE_TIMESTAMP'
    ) {
      Doc.replaceRange(
        `FROM_UNIXTIME([${data.table.displayName}.${data.displayName}#${data.id}]/1000)`,
        cursorPos,
        cursorPos
      );
    } else {
      Doc.replaceRange(`[${data.table.displayName}.${data.displayName}#${data.id}]`, cursorPos, cursorPos);
    }
  };

  const onClickFunction = (data) => {
    const Doc = ref.current.editor.doc;
    // 首先让编辑器获得焦点
    Doc.cm.focus();
    // 获取当前光标位置
    const cursorPos = Doc.getCursor();
    // 把函数插入到光标位置
    Doc.replaceRange(`${data.createSql}`, cursorPos, cursorPos);
    // 把光标设置到括号里的位置
    setTimeout(() => {
      const cursorPosNext = Doc.getCursor();
      // 计算括号里有多少字符
      const targetIndex = data.createSql.indexOf('(');
      const countString = data.createSql.substring(targetIndex);

      Doc.setCursor({
        ...cursorPosNext,
        ch: cursorPosNext.ch - (countString.length - 1)
      });
    }, 4);
  };

  // 处理提交
  const handleSubmit = () => {
    validateFields(async (err, fields) => {
      if (err) return;
      try {
        setLoading(true);
        const data = {
          ...info,
          ...fields,
          formulaText: value,
          projectId: localStorage.getItem('projectId'),
          tables: id === 'add' ? props?.location?.state?.dataTable : info.tables,
          businessTable: id === 'add' ? props?.location?.state?.businessTable : info.businessTable
        };
        await indexManagementService.save(data);
        setLoading(false);
        message.success(t('analysisCenter-JZmEVqvXhENQ'), 1);
        props.history.go(-1);
      } catch {
        setLoading(false);
      }
    });
  };

  const check = () => {
    validateFields(async (err, fields) => {
      if (err) return;
      try {
        setCheckLoading(true);
        const data = {
          ...info,
          ...fields,
          formulaText: value,
          projectId: localStorage.getItem('projectId'),
          businessTable: id === 'add' ? props?.location?.state?.businessTable : info.businessTable
        };
        const redata = await indexManagementService.checkFormulaText(data);
        setCheckResultData(redata);
        setCheckLoading(false);
      } catch {
        setCheckLoading(false);
      }
    });
  };

  const treeData = Object.entries(tableSchemaList).reduce((data1, item) => {
    const childrenList = item[1].filter((w) => w.displayName.indexOf(fieldsSearchValue) > -1);
    if (childrenList.length > 0) {
      const info = {
        title: item[1][0].table.displayName,
        key: `table_${item[0]}`,
        children: childrenList.map((n) => ({
          title: (
            <Box
              name={n.displayName}
              onClick={onClickField}
              keyValue={`${n.table.name}.${n.name}`}
              dataType="TABLE_SCHEMA"
              dataSource={n}
            />
          ),
          key: n.id
        }))
      };
      data1.push(info);
    }
    return data1;
  }, []);

  return (
    <div className="indexManagementDetail">
      <header>
        <Breadcrumb>
          <Breadcrumb.Item>
            <Link to="/aimarketer/home/<USER>/indexManagement">{t('analysisCenter-ghBXtIrJ4skE')}</Link>
          </Breadcrumb.Item>
          {/* <Breadcrumb.Item>{`${_.isNil(props.match.params.id) ? '创建' : '编辑'}指标`}</Breadcrumb.Item> */}
          <Breadcrumb.Item>{`${props.location.search !== '' ? t('analysisCenter-ShF8bJN9qas6') : t('analysisCenter-xFdVYQUXVb1d')}t('analysisCenter-WpvBxPhwWFWq')`}</Breadcrumb.Item>
        </Breadcrumb>
      </header>
      <section className="mainContent">
        <div className="left">
          <div className="description">{t('analysisCenter-DOrQENiB1QYz')}</div>
          <Form className="form-integral">
            <Row className="formMainContent" gutter={24}>
              <Col span={16}>
                <Form.Item label={t('analysisCenter-z6uD9F1L33lY')}>
                  {getFieldDecorator('name', {
                    rules: [
                      { required: true, message: t('analysisCenter-zM0aYp7O6uEk') },
                      { max: 64, message: t('analysisCenter-sz3uyUIajow6') },
                      {
                        pattern: /^[A-Za-z0-9_\-\u4e00-\u9fa5]+$/g,
                        message: t('analysisCenter-WJPBYGM60zbi')
                      }
                    ],
                    initialValue: info?.name ?? ''
                  })(<Input placeholder={t('analysisCenter-zM0aYp7O6uEk')} />)}
                </Form.Item>
              </Col>
            </Row>
            <Row className="formMainContent" gutter={24}>
              <Col span={16}>
                <Form.Item label={t('analysisCenter-hA1jJu5PYAYz')}>
                  {getFieldDecorator('dataType', {
                    rules: [{ required: true, message: t('analysisCenter-eLe8XdMBMbPJ') }],
                    initialValue: info?.dataType ?? ''
                  })(
                    <Select
                      placeholder={t('analysisCenter-xC97U4A2aimp')}
                      getPopupContainer={(triggerNode) => triggerNode.parentNode}
                    >
                      <Option value="LONG">LONG</Option>
                      <Option value="INT">INT</Option>
                      <Option value="DOUBLE">DOUBLE</Option>
                      {/* <Option value="DATE">DATE</Option>
                    <Option value="DATETIME">DATETIME</Option>
                    <Option value="TIMESTAMP">TIMESTAMP</Option>
                    <Option value="STRING">STRING</Option> */}
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={16}>
                <Form.Item label={t('analysisCenter-W8fPMUDkDizz')}>
                  {getFieldDecorator('remark', {
                    rules: [{ max: 150, message: t('analysisCenter-cRcrn9P0Vp6k') }],
                    initialValue: info?.remark ?? ''
                  })(<TextArea placeholder={t('analysisCenter-AyA56RNhIZzz')} autoSize={{ minRows: 3, maxRows: 5 }} />)}
                </Form.Item>
              </Col>
            </Row>
          </Form>
          <div className="codeMirrorWrapper">
            {def.keywords ? (
              <CodeMirror
                value={value}
                options={options}
                ref={ref}
                defineMode={{
                  name: 'customModel',
                  fn: (config) => myfunction(config, def)
                }}
                onBeforeChange={(editor, data, value) => {
                  setValue(value);
                }}
                pasteLinesPerSelection

                // onChange={(editor, data, value) => {
                //   // console.log(value, 1111);
                // }}
              />
            ) : (
              <div className="placeDiv" />
            )}
          </div>
          <div className="checkArea">
            <Button className="checkButton" loading={checkLoading} onClick={check}>
              {t('analysisCenter-OII2vl3zcEfO')}
            </Button>
            <p className="checkResult">
              {_.toPairs(checkResultData)[0] && _.toPairs(checkResultData)[0][0]
                ? checkResultData[_.toPairs(checkResultData)[0][0]]
                : ''}
            </p>
          </div>
        </div>
        <div className="center">
          <div className="centerTitle">{t('analysisCenter-VitrgNJW1zrn')}</div>
          <div className="centerContent">
            <Search
              onChange={(e) => setFunctionSearchValue(e.target.value)}
              size="small"
              placeholder={t('analysisCenter-6ky3FZ84KNSH')}
            />
            <ul className="functionWrapper">
              {_.map(filterFunctionList, (item) => {
                const content = (
                  <div className="functionPopoverContent">
                    <p>
                      {t('analysisCenter-nPyKW7vlbW2W')}:{item.usage}
                    </p>
                    <p>
                      {t('analysisCenter-V59k4XnKXzMp')}:{item.description}
                    </p>
                    <p>
                      {t('analysisCenter-6o4VrumbgJPp')}:{item.sample}
                    </p>
                  </div>
                );
                return (
                  <Popover
                    key={item.name}
                    overlayClassName="functionPopover"
                    placement="left"
                    title={item.name}
                    content={content}
                    trigger="hover"
                  >
                    <div key={item.name} className="functionItem" onClick={() => onClickFunction(item)}>
                      {item.name}
                    </div>
                  </Popover>
                );
              })}
            </ul>
          </div>
        </div>
        <div className="right">
          <div className="rightTitle">{t('analysisCenter-ZzfpNwDoOXtM')}</div>
          <div className="rightContent">
            <div style={{ marginTop: 10 }}>
              <Search
                onChange={(e) => setFieldsSearchValue(e.target.value)}
                size="small"
                placeholder={t('analysisCenter-z8ZYz3DIXIfc')}
              />
              <div className="treeWrapper">
                <Tree
                  selectable={false}
                  className="tableSchemaTree"
                  treeData={treeData}
                  showIcon
                  switcherIcon={<DownOutlined />}
                  defaultExpandAll
                >
                  {/* {Object.entries(tableSchemaList).map(item => {
                    const childrenList = item[1].filter(w => w.displayName.indexOf(fieldsSearchValue) > -1);
                    if (childrenList.length === 0) return null;
                    return (<TreeNode title={item[1][0].table.displayName} key={item[0]}>
                      {childrenList.map(n => <TreeNode title={<Box name={n.displayName} onClick={onClickField} keyValue={`${n.table.name}.${n.name}`} dataType="TABLE_SCHEMA" dataSource={n} />} key={n.id} />)}
                    </TreeNode>);
                  })} */}
                </Tree>
              </div>
            </div>
          </div>
        </div>
      </section>
      <div className="footer">
        <Button onClick={() => props.history.go(-1)}>{t('analysisCenter-Um9398LCxql9')}</Button>
        <Button loading={loading} type="primary" onClick={() => handleSubmit()}>
          {t('analysisCenter-Fo4khyURVArE')}
        </Button>
      </div>
    </div>
  );
};

export default Form.create({ name: 'integral' })(Detail);
