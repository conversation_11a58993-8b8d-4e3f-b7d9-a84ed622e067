.indexManagementDetail {
  flex: 1 1;
  display: flex;
  flex-direction: column;
  ::-webkit-scrollbar{
    width:4px;
    height:4px;
    /**/
  }
  header {
    background: #fff;
    margin: 0 -24px;
    padding: 0 24px;

    .ant-breadcrumb {
      margin: 12px 0 8px 0;
      color: rgba(0, 0, 0, .45);
      font-size: 14px;
    }
    h1{
      font-size: 24px;
    }
  }
  .footer {
    background: #fff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    padding: 0 24px;
    box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.12);
  }

  .mainContent {
    background-color: #fff;
    min-width: 1280px;
    margin-top: 24px;
    padding: 20px;
    height: calc(100vh - 130px);
    display: flex;
    .center{
      width: 220px;
      border-left: 1px solid #D7D7D7;
      padding: 0 10px;
      .centerContent{
        margin-top: 10px;
        .functionWrapper{
          max-height: calc(100vh - 270px);
          overflow-y: auto;
          overflow-x: hidden;
          margin-top: 5px;
          .functionItem{
            height: 30px;
            line-height: 30px;
            cursor: pointer;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .functionItem:hover{
            background-color: rgba(0,0,0,.06);
          }
        }
      }
    }
    .right{
      width: 220px;
      border-left: 1px solid #D7D7D7;
      padding: 0 10px;
      .treeWrapper{
        height: calc(100vh - 270px);
        overflow-y: auto;
        overflow-x: hidden;
        .boxName{
          max-width: 110px;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .left{
      flex: 1;
      height: calc(100vh - 190px);
      overflow-y: auto;
      padding: 0 10px;
      .description{
        margin-bottom: 20px;
      }
      .ant-form-item{
        display: flex;
        .ant-form-item-label{
          width: 80px;
        }
        .ant-form-item-control-wrapper{
          width: calc(100% - 80px);
        }
      }
      .codeMirrorWrapper{
        margin-left: 80px;
        .CodeMirror{
          width: 700px;
          height: 120px;
          border-radius: 4px;
          border: 1px solid #d9d9d9;
        }
        .placeDiv{
          width: 700px;
          height: 120px;
          border-radius: 4px;
          border: 1px solid #d9d9d9;
        }
        .CodeMirror:hover{
          border: 1px solid $border_hover;
        }
      }

      .checkArea{
        padding-left: 80px;
        .checkButton{
          margin: 10px 0;

        }
      }

    }

  }
  .ant-legacy-form-item-label{
    width: 80px;
  }
}

.functionPopover{
  .functionPopoverContent{
    p{
      display: flex;
      max-width: 600px;
    }
  }
}