import { Form, Input, message, Modal, Select, Space, Table, Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import MyToDoListService from 'service/myToDoListService';
import UserService from 'service/UserService';
import { elements, initHistoryParam, initParam } from '../config';

import TableSearch from '@/components/bussinesscoms/tableSearch';
import '../index.scss';

const userService = new UserService();

const { TextArea } = Input;

const hisToryPagination = {
  showTotal: (totals) => `共 ${totals} 条`,
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
};

const statusList = {
  RUNNING: '审批中',
  PASS: '审批通过',
  REJECT: '驳回',
  BACKOUT: '已撤销',
  CANCEL: '已取消'
};

const detailUrlList = {
  process_canvas: '/aimarketer/home/<USER>',
  user_segment: '/aimarketer/home/<USER>/userGroup'
};

export default function MyToDoListRunning({ props, tabKey, dictTypeList, userList, reflash, setReflash }) {
  const {
    location: { state },
    dispatch,
    messageInfo
  } = props;

  const [form] = Form.useForm();

  const [dataSource, setDataSource] = useState([]);
  const [historyTableSource, setHistoryTableSource] = useState([]);
  const [userId, setUserId] = useState(undefined);
  const [loading, setLoading] = useState(false);
  const [param, setParam] = useState(_.cloneDeep(state?.paramRunning || initParam));
  const [historyParam, setHistoryParam] = useState(initHistoryParam);
  const [columnsKey, setColumnsKey] = useState('');

  const [open, setOpen] = useState(false);
  const [logStatus, setLogStatus] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [historyLoading, setHistoryLoading] = useState(false);

  const renderOptions = (record) => (
    <Space size={16}>
      <a onClick={() => onColumnActionClick('finish', record)} type="link">
        通过
      </a>
      <a onClick={() => onColumnActionClick('reject', record)} type="link">
        驳回
      </a>
      <a onClick={() => onColumnActionClick('log', record)} type="link">
        审批详情
      </a>
    </Space>
  );

  const columnsHistory = [
    {
      title: '提交人',
      key: 'promoterName',
      width: 150,
      dataIndex: 'promoterName',
      render: (text, record) => (
        <div>{(record.createTime && record.status === 'RUNNING') || record.status === 'BACKOUT' ? text : '-'}</div>
      )
    },
    {
      title: '申请时间',
      width: 200,
      dataIndex: 'createTime',
      sorter: true,
      key: 'createTime',
      render: (text, record) =>
        (record.createTime && record.status === 'RUNNING') || record.status === 'BACKOUT'
          ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss')
          : '-'
    },
    {
      title: '审批状态',
      width: 100,
      key: 'status',
      render: (text) => <span>{statusList[text.status]}</span>
    },
    {
      title: '审批人',
      key: 'approverName',
      width: 100,
      render: (record) => <span>{record.approverName ? record.approverName : '-'}</span>
    },
    {
      title: '审批意见',
      key: 'opinion',
      width: 240,
      render: (record) => (
        <Tooltip title={record.opinion ? record.opinion : ''}>
          <span className="optionWrap">{record.opinion ? record.opinion : '-'}</span>
        </Tooltip>
      )
    },
    {
      title: '审批时间',
      width: 200,
      dataIndex: 'approvalTime',
      sorter: true,
      key: 'approvalTime',
      render: (text, record) => (record.approvalTime ? dayjs(record.approvalTime).format('YYYY-MM-DD HH:mm:ss') : '-')
    }
  ];

  const columnsRunning = [
    {
      title: '审批任务编号',
      key: 'taskId',
      dataIndex: 'taskId',
      sorter: true,
      width: 100
    },
    {
      title: '审批事项名称',
      key: 'contentName',
      width: 200,
      ellipsis: true,
      dataIndex: 'contentName',
      render: (text, record) => (
        <Tooltip title={text}>
          <a onClick={() => onColumnActionClick('detail', record)}>{record.contentName}</a>
        </Tooltip>
      )
    },
    {
      title: '审批对象',
      key: 'pcodeName',
      width: 100,
      dataIndex: 'pcodeName',
      render: (text, render) => <div>{render.pcodeName}</div>
    },
    {
      title: '审批子对象',
      key: 'typeCodeName',
      width: 100,
      dataIndex: 'typeCodeName',
      render: (text, render) => <div>{render.typeCodeName}</div>
    },
    {
      title: '提交人',
      key: 'approvalUserName',
      width: 150,
      dataIndex: 'approvalUserName',
      render: (text, render) => <div>{render.approvalUserName}</div>
    },
    {
      title: '申请时间',
      width: 150,
      dataIndex: 'startTime',
      key: 'startTime',
      sorter: (a, b) => a.startTime - b.startTime,
      render: (text, record) => (record.startTime ? dayjs(record.startTime).format('YYYY-MM-DD HH:mm:ss') : '-')
    },
    {
      title: '操作',
      width: 120,
      fixed: 'right',
      render: (text, record) => <Space>{renderOptions(record)}</Space>
    }
  ];

  useEffect(() => {
    const init = async () => {
      const { id } = await userService.getCurrentUser();

      setUserId(id);
    };
    init();
  }, []);

  const handleHistoryTableChange = (lastpagination, filtersArg, sorter) => {
    historyParam.page = lastpagination.current;
    historyParam.size = lastpagination.pageSize;
    if (sorter.field) {
      historyParam.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setHistoryParam({ ...historyParam });
  };

  useEffect(() => {
    const getTableData = async () => {
      setLoading(true);
      if (userId) {
        const result = await MyToDoListService.acitvityQuery({
          companyId: Number(localStorage.getItem('organizationId')),
          isSuperAdmin: !!JSON.parse(localStorage.getItem('superAdmin'))
        });

        setReflash(!reflash);
        const search = _.cloneDeep(param.search);
        let newResult = [];

        newResult = result.dataList.map((item) => {
          return {
            ...item,
            contentName: item.variables.contentName,
            approvalTypePcode: item.variables.approvalTypePcode,
            approvalTypeCode: item.variables.approvalTypeCode,
            pcodeName: item.variables.pcodeName,
            starterId: Number(item.starter),
            typeCodeName: item.variables.typeCodeName,
            approvalUserName: item.variables.approvalUserName
          };
        });

        dispatch({
          type: 'messageInfo',
          messageInfo: { toDoList: messageInfo.toDoList, toToListV2: result.dataList.length }
        });

        let res = [];
        res = filterDataList(search, newResult);

        props.history.replace({
          state: { ...state, paramRunning: { ...param, search }, tabKey }
        });

        setDataSource(
          res.map((item) => {
            return {
              ...item,
              startTime: dayjs(item.startTime).valueOf()
            };
          })
        );
      }
      setLoading(false);
    };
    tabKey === '2' && getTableData();
  }, [tabKey, userId, param]);

  useEffect(() => {
    const getHistoryTableData = async () => {
      setHistoryLoading(true);
      const finalParam = _.cloneDeep(historyParam);
      const result = await MyToDoListService.processHistory(finalParam);
      hisToryPagination.total = result.totalElements;
      hisToryPagination.current = historyParam.page;
      hisToryPagination.pageSize = historyParam.size;
      setHistoryTableSource(result.content.filter((item) => item.status !== 'PENDING'));
      setLoading(false);
      setHistoryLoading(false);
    };
    getHistoryTableData();
  }, [historyParam]);

  const filterDataList = (search, dataList) => {
    return dataList.filter((item) => {
      return search.every((filter) => {
        const { operator, propertyName, value } = filter;
        const itemValue = item[propertyName];

        if (operator === 'LIKE') {
          return itemValue && itemValue.includes(value);
        }

        if (operator === 'EQ') {
          return itemValue === value;
        }

        if (operator === 'DATE_BETWEEN') {
          const [start, end] = value.split(',').map(Number);
          const itemTime = new Date(itemValue).getTime();
          return itemTime >= start && itemTime <= end;
        }

        return false;
      });
    });
  };

  const showModal = () => {
    setOpen(true);
  };

  const handleCancel = () => {
    setOpen(false);
  };

  const hideLogModal = () => {
    setLogStatus(false);
  };

  const onSubmit = async () => {
    setConfirmLoading(true);
    const { opinion, status, contentType, taskId, approvalNo, createTime } = form.getFieldValue();

    try {
      await form.validateFields();
    } catch (error) {
      setConfirmLoading(false);
      return;
    }

    try {
      await MyToDoListService.saveProcessLabelInstance({
        approvalNo,
        taskId,
        nodeCreateTime: createTime,
        contentType,
        approverId: userId,
        status: status === 'FINISH' ? 'PASS' : 'REJECT',
        opinion,
        projectId: localStorage.getItem('projectId')
      });

      message.success(status === 'FINISH' ? '审批成功' : '驳回成功');
      setConfirmLoading(false);
      handleCancel();
      setParam({ ...param });
    } catch (error) {
      console.error(error);
    } finally {
      setConfirmLoading(false);
    }
  };

  const onColumnActionClick = async (key, record) => {
    setColumnsKey(key);
    const { id, createUserId, createTime, contentId, mark, status, starter, businessKey, variables, taskId } = record;

    const { approvalTypePcode, contentName, lsApprovalNo, contentType, contentUrl } = variables;

    if (key === 'detail') {
      if (approvalTypePcode === 'marketing_material') {
        const result = await MyToDoListService.getAuthorization({
          loginId: userId,
          projectId: localStorage.getItem('projectId')
        });

        const newUrl =
          contentUrl.indexOf('?') >= 0
            ? `${contentUrl}&Authorization=${result}`
            : `${contentUrl}?Authorization=${result}`;
        window.open(newUrl, '_blank');
      } else if (approvalTypePcode === 'user_segment' || approvalTypePcode === 'process_canvas') {
        props.history.push(
          `${detailUrlList[approvalTypePcode]}/detail?id=${businessKey}&definition=${true}&status=${true}&promoterId=${starter}&nodeCreateTime=${dayjs(createTime).valueOf()}&taskId=${taskId}&approvalType=ACTIVITI`
        );
      } else {
        const newContentUrl = contentUrl.includes('?') ? `${contentUrl}&isTag=true` : `${contentUrl}?isTag=true`;
        const pathName = `/aimarketer/home/<USER>
        props.history.push(pathName);
      }
    } else if (key === 'finish') {
      form.setFieldsValue({
        name: contentName,
        approvalNo: lsApprovalNo,
        taskId,
        id,
        createUserId,
        definitionId: record.definitionId,
        status: 'FINISH',
        contentUrl,
        opinion: null,
        contentType,
        createTime: dayjs(createTime).valueOf()
      });
      showModal();
    } else if (key === 'reject') {
      form.setFieldsValue({
        name: contentName,
        approvalNo: lsApprovalNo,
        taskId,
        createUserId,
        definitionId: record.definitionId,
        id,
        status: 'REJECT',
        contentUrl,
        opinion: null,
        contentType,
        createTime: dayjs(createTime).valueOf()
      });
      showModal();
    } else if (key === 'log') {
      // showLogModal(id);
      props.history.push(`/aimarketer/home/<USER>
        contentType,
        taskId,
        approvalNo: lsApprovalNo,
        contentName,
        approvalStatus: status,
        createUserId,
        contentId,
        mark,
        contentUrl,
        approvalType: 'ACTIVITI',
        createTime: dayjs(createTime).valueOf()
      });
    }
  };
  return (
    <div className="todoList">
      <div className="search">
        <TableSearch
          elements={elements(2, userList, dictTypeList)}
          onChange={(data) => setParam({ ...param, ...data })}
          span={8}
          initialValues={state?.paramRunning?.search}
        />
      </div>
      <div className="content">
        <div className="tableWrap">
          <Table
            dataSource={dataSource}
            columns={columnsRunning}
            loading={loading}
            rowKey="taskId"
            scroll={{ x: 1600 }}
          />
        </div>

        <Modal
          title="审批详情"
          className="processHistoryModalWrap"
          open={logStatus}
          destroyOnClose
          // okText="确认"
          // onOk={hideLogModal}
          confirmLoading={historyLoading}
          onCancel={hideLogModal}
          footer={null}
        >
          <div>
            <Table
              dataSource={historyTableSource}
              pagination={hisToryPagination}
              columns={columnsHistory}
              loading={historyLoading}
              onChange={handleHistoryTableChange}
              scroll={{ x: 400 }}
              rowKey="id"
            />
          </div>
        </Modal>

        <Modal
          title="审批流程"
          className="processModalWrap"
          open={open}
          destroyOnClose
          okText={`确认${columnsKey === 'finish' ? '通过' : '驳回'}`}
          onOk={() => onSubmit()}
          confirmLoading={confirmLoading}
          onCancel={handleCancel}
        >
          <div className="processForm">
            <Form
              name="basic"
              layout="vertical"
              form={form}
              colon={false}
              confirmLoading={confirmLoading}
              initialValues={{
                name: null,
                id: null,
                opinion: null,
                status: null
              }}
            >
              <Form.Item label="审批事项名称" name="name">
                <Select disabled />
              </Form.Item>

              <Form.Item label="审批意见" name="opinion" rules={[{ max: 100, message: '最大长度限制为100位字符' }]}>
                <TextArea
                  placeholder="请输入审批意见"
                  autoSize={{
                    minRows: 2,
                    maxRows: 6
                  }}
                />
              </Form.Item>
            </Form>
          </div>
        </Modal>
      </div>
    </div>
  );
}
