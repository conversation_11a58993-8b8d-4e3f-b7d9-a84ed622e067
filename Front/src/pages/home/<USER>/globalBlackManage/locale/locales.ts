export default {
  cn: {
  "dataCenter-eJrtvmp2Yec1": "ID类型名称",
  "dataCenter-u3fuTee2SOiC": "ID类型编码",
  "dataCenter-FwGxmp5vLBqH": "更新者",
  "dataCenter-NObTcv2jmnnU": "更新时间",
  "dataCenter-2jjIs0lheOiD": "用户",
  "dataCenter-lhPEprfuG4OZ": "员工",
  "dataCenter-pReio18wMnhK": "产品",
  "dataCenter-TBsI07sjXqdD": "权益",
  "dataCenter-VZ1CM76ICD0w": "内容",
  "dataCenter-HdmVQ1jKCeOe": "设备",
  "dataCenter-LAjGNFHeykhv": "分支机构",
  "dataCenter-DERQs4C7Md0m": "逗号",
  "dataCenter-KFMO6RgmBXNf": "分号",
  "dataCenter-0Ko9q6ARixPJ": "自定义",
  "dataCenter-tLwTg12jNyW0": "删除",
  "dataCenter-QFSOX9mnyvv2": "更多过滤",
  "dataCenter-RTotGcDhIqwA": "导入记录ID",
  "dataCenter-ngrfQFZOpApH": "用户ID",
  "dataCenter-lP6hni8TJkpY": "用户ID类型",
  "dataCenter-myQkN8SrRSWz": "更新时间",
  "dataCenter-vp343TK7Clxv": "操作",
  "dataCenter-jd0gTSBGSpr5": "删除成功",
  "dataCenter-KjJoGZU7FUXV": "请先选择要删除的黑名单数据",
  "dataCenter-YtSf7UO7yGlF": "导入成功",
  "dataCenter-42XDepgv4b5K": "用户 ID 类型",
  "dataCenter-XGrVth7VQhnD": "人数",
  "dataCenter-JYWutHr4nekM": "请输入",
  "dataCenter-aV8s5G5jNaC4": "导入记录",
  "dataCenter-A895Bsgcb3Gy": "导入",
  "dataCenter-gEj3i6nRgIcd": "黑名单列表",
  "dataCenter-fO889jeSl6Us": "批量删除",
  "dataCenter-OichoXwiZCmv": "共",
  "dataCenter-teVQb1k2p8Z2": "条",
  "dataCenter-vmfrzPwglPeF": "删除",
  "dataCenter-6wTQZJcJTOLb": "该操作无法撤销，删除后的黑名单数据将不再做全局过滤。",
  "dataCenter-NT6a4d30oYxG": "移除黑名单用户",
  "dataCenter-gKE8wYlXrekU": "用户ID类型",
  "dataCenter-EZon5W2gWwEG": "导入方式",
  "dataCenter-Vx23zPFXhxX4": "分群导入",
  "dataCenter-1f3Y1gITq3hP": "文件导入",
  "dataCenter-vMuNA5b0HFUX": "更多过滤",
  "dataCenter-jCF3QQQyjCIm": "操作人",
  "dataCenter-dC9El7YuzS29": "更新规则",
  "dataCenter-KygeqrNRjzfT": "备注",
  "dataCenter-4tTBgV9ab27V": "搜索导入记录ID",
  "dataCenter-3F0oCCULidq0": "导入记录ID",
  "dataCenter-Z7bgGJtyAQG3": "人数",
  "dataCenter-5V8C1OLbrE7K": "单次更新",
  "dataCenter-u85xv1v2XFUi": "定期更新",
  "dataCenter-0TDSQkTx0wy3": "导入时间",
  "dataCenter-SSKUxlQ6XjQV": "更新时间",
  "dataCenter-l50GPFMsCxGp": "操作人",
  "dataCenter-gRY8rlthPxlg": "操作",
  "dataCenter-w6hBxMqR3Lkw": "导入成功",
  "dataCenter-hjI7lR1NUjw5": "ID只能为纯数字",
  "dataCenter-EPWwTq6vB1Sw": "删除成功",
   "dataCenter-zCX0I1HXpV2E": "请选择更新时间",
  "dataCenter-TS4HI7WORzA0": "保存成功",
  "dataCenter-PWved6hyO9JE": "每天",
  "dataCenter-6gumeAXFIUh7": "取最新的分群批次数据更新定期分群导入记录",
  "dataCenter-paE6C2Tiyav3": "取消",
  "dataCenter-isEfyvcHkG4k": "确认",
  "dataCenter-HFssNsCJnD4Q": "全局黑名单",
  "dataCenter-mksNtJRaVR9i": "导入记录",
  "dataCenter-GxHzD44FTPva": "分群导入-更新时间配置",
  "dataCenter-Wy4PcV6BLsXq": "导入",
  "dataCenter-OvRwhTTlfZCL": "导入记录列表",
  "dataCenter-SEZ1uU70kJu5": "删除",
  "dataCenter-vU3fmpDMHZRl": "该操作无法撤销，删除后的黑名单数据将不再做全局过滤。",
  "dataCenter-Q6AWM5xRtzTA": "请选择",
  "dataCenter-7qjRqNBjMdhX": "请输入",
  "dataCenter-sI8R2Is8TRsB": "清空",
  "dataCenter-zDT4HdzzL020": "查询",
  "dataCenter-qB1EFzhUfdFa": "数据导入中，请勿退出该页面",
  "dataCenter-1xqHtkLMySMM": "上传的文件大于200M",
  "dataCenter-oUC1y4VZfnAq": "上传失败,请重试",
  "dataCenter-Q7istB8uPwjJ": "上传成功",
  "dataCenter-f1cO59gov9WX": "黑名单导入",
  "dataCenter-n6OWXo2TBHOW": "总数",
  "dataCenter-gKhu0iBWY1yp": "条，成功添加",
  "dataCenter-Qb31V0okRiSg": "条，重复数据",
  "dataCenter-WMWMzsz6J5W0": "条。",
  "dataCenter-Kskoz7nAAjEN": "分群规则",
  "dataCenter-wq3NAvSxnrTK": "请输入黑名单分群",
  "dataCenter-TwYIJrPfaEmj": "人",
  "dataCenter-tmTXa8I4Jj8T": "上传文件",
  "dataCenter-jcMBarDGQwSW": "最大支持200M，支持csv格式。请上传单列、字符串类型的用户id，多列将忽略首列外数据。",
   "dataCenter-yeGh0dQrQIS9": "请上传文件",
  "dataCenter-sKnVEdrUAfyI": "导入起始行",
  "dataCenter-ZOBPEYdZFUlz": "更新规则",
  "dataCenter-7zgQ9gjsxFXB": "请选择更新规则",
  "dataCenter-5o7Hs15oxBtx": "备注不要超出150个字符",
  "dataCenter-z4A1O7X4Urfy": "取消",
  "dataCenter-1s5XccYT3xy4": "导入中",
  "dataCenter-uKOTRfx4FEIi": "导入",
    "dataCenter-kKUMDohXLdjZ": "黑名单分群",

  },
  en: {
 "dataCenter-eJrtvmp2Yec1": "ID Type Name",
  "dataCenter-u3fuTee2SOiC": "ID Type Code",
  "dataCenter-FwGxmp5vLBqH": "Updater",
  "dataCenter-NObTcv2jmnnU": "Update Time",
  "dataCenter-2jjIs0lheOiD": "User",
  "dataCenter-lhPEprfuG4OZ": "Employee",
  "dataCenter-pReio18wMnhK": "Product",
  "dataCenter-TBsI07sjXqdD": "Equity",
  "dataCenter-VZ1CM76ICD0w": "Content",
  "dataCenter-HdmVQ1jKCeOe": "Device",
  "dataCenter-LAjGNFHeykhv": "Branch",
  "dataCenter-DERQs4C7Md0m": "Comma",
  "dataCenter-KFMO6RgmBXNf": "Semicolon",
  "dataCenter-0Ko9q6ARixPJ": "Custom",
  "dataCenter-tLwTg12jNyW0": "Delete",
  "dataCenter-QFSOX9mnyvv2": "More Filters",
  "dataCenter-RTotGcDhIqwA": "Import Record ID",
  "dataCenter-ngrfQFZOpApH": "User ID",
  "dataCenter-lP6hni8TJkpY": "User ID Type",
  "dataCenter-myQkN8SrRSWz": "Update Time",
  "dataCenter-vp343TK7Clxv": "Operation",
  "dataCenter-jd0gTSBGSpr5": "Delete Success",
  "dataCenter-KjJoGZU7FUXV": "Please select the blacklisted data to be deleted",
  "dataCenter-YtSf7UO7yGlF": "Import Success",
  "dataCenter-42XDepgv4b5K": "User ID Type",
  "dataCenter-XGrVth7VQhnD": "Number of People",
  "dataCenter-JYWutHr4nekM": "Please enter",
  "dataCenter-aV8s5G5jNaC4": "Import Record",
  "dataCenter-A895Bsgcb3Gy": "Import",
  "dataCenter-gEj3i6nRgIcd": "Blacklist List",
  "dataCenter-fO889jeSl6Us": "Batch Delete",
  "dataCenter-OichoXwiZCmv": "Total",
  "dataCenter-teVQb1k2p8Z2": "Items",
  "dataCenter-vmfrzPwglPeF": "Delete",
  "dataCenter-6wTQZJcJTOLb": "This operation cannot be undone. After deletion, the blacklisted data will no longer be globally filtered.",
  "dataCenter-NT6a4d30oYxG": "Remove Blacklisted User",
  "dataCenter-gKE8wYlXrekU": "User ID Type",
  "dataCenter-EZon5W2gWwEG": "Import Method",
  "dataCenter-Vx23zPFXhxX4": "Group Import",
  "dataCenter-1f3Y1gITq3hP": "File Import",
  "dataCenter-vMuNA5b0HFUX": "More Filters",
  "dataCenter-jCF3QQQyjCIm": "Operator",
  "dataCenter-dC9El7YuzS29": "Update Rule",
  "dataCenter-KygeqrNRjzfT": "Remark",
  "dataCenter-4tTBgV9ab27V": "Search Import Record ID",
  "dataCenter-3F0oCCULidq0": "Import Record ID",
  "dataCenter-Z7bgGJtyAQG3": "Number of People",
  "dataCenter-5V8C1OLbrE7K": "Single Update",
  "dataCenter-u85xv1v2XFUi": "Regular Update",
  "dataCenter-0TDSQkTx0wy3": "Import Time",
  "dataCenter-SSKUxlQ6XjQV": "Update Time",
  "dataCenter-l50GPFMsCxGp": "Operator",
  "dataCenter-gRY8rlthPxlg": "Operation",
  "dataCenter-w6hBxMqR3Lkw": "Import Success",
  "dataCenter-hjI7lR1NUjw5": "ID can only be pure numbers",
  "dataCenter-EPWwTq6vB1Sw": "Delete Success",
   "dataCenter-zCX0I1HXpV2E": "Please select the update time",
  "dataCenter-TS4HI7WORzA0": "Save Success",
  "dataCenter-PWved6hyO9JE": "Every Day",
  "dataCenter-6gumeAXFIUh7": "Take the latest batch data of the group to update the regular group import record",
  "dataCenter-paE6C2Tiyav3": "Cancel",
  "dataCenter-isEfyvcHkG4k": "Confirm",
  "dataCenter-HFssNsCJnD4Q": "Global Blacklist",
  "dataCenter-mksNtJRaVR9i": "Import Record",
  "dataCenter-GxHzD44FTPva": "Group Import-Update Time Configuration",
  "dataCenter-Wy4PcV6BLsXq": "Import",
  "dataCenter-OvRwhTTlfZCL": "Import Record List",
  "dataCenter-SEZ1uU70kJu5": "Delete",
  "dataCenter-vU3fmpDMHZRl": "This operation cannot be undone. After deletion, the blacklisted data will no longer be globally filtered.",
  "dataCenter-Q6AWM5xRtzTA": "Please select",
  "dataCenter-7qjRqNBjMdhX": "Please enter",
  "dataCenter-sI8R2Is8TRsB": "Clear",
  "dataCenter-zDT4HdzzL020": "Search",
  "dataCenter-qB1EFzhUfdFa": "Data is being imported, please do not exit the page",
  "dataCenter-1xqHtkLMySMM": "The uploaded file is greater than 200M",
  "dataCenter-oUC1y4VZfnAq": "Upload failed, please try again",
  "dataCenter-Q7istB8uPwjJ": "Upload Success",
  "dataCenter-f1cO59gov9WX": "Blacklist Import",
  "dataCenter-n6OWXo2TBHOW": "Total",
  "dataCenter-gKhu0iBWY1yp": "Items, successfully added",
  "dataCenter-Qb31V0okRiSg": "Items, duplicate data",
  "dataCenter-WMWMzsz6J5W0": "Items.",
  "dataCenter-Kskoz7nAAjEN": "Group Rule",
  "dataCenter-wq3NAvSxnrTK": "Please enter the blacklisted group",
  "dataCenter-TwYIJrPfaEmj": "Person",
  "dataCenter-tmTXa8I4Jj8T": "Upload File",
  "dataCenter-jcMBarDGQwSW": "Maximum support 200M, supports csv format. Please upload a single column and string type user id, multi-column data will be ignored except the first column.",
   "dataCenter-yeGh0dQrQIS9": "Please upload file",
  "dataCenter-sKnVEdrUAfyI": "Import Start Line",
  "dataCenter-ZOBPEYdZFUlz": "Update Rule",
  "dataCenter-7zgQ9gjsxFXB": "Please select the update rule",
  "dataCenter-5o7Hs15oxBtx": "Remark should not exceed 150 characters",
  "dataCenter-z4A1O7X4Urfy": "Cancel",
  "dataCenter-1s5XccYT3xy4": "Importing",
  "dataCenter-uKOTRfx4FEIi": "Import",
  "dataCenter-kKUMDohXLdjZ": "Blacklist Group"
  }
};
