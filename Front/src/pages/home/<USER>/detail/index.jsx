import { Badge, Button, Calendar, Space, Typography } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import CalendarService from 'service/calendarService';
import { t } from 'utils/translation';
import './index.scss';

import dayOfYear from 'dayjs/plugin/dayOfYear';
import isBetween from 'dayjs/plugin/isBetween';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import weekday from 'dayjs/plugin/weekday';

const { Text } = Typography;

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);
dayjs.extend(isBetween);
dayjs.extend(weekday);
dayjs.extend(dayOfYear);

const typeMap = {
  DAILY: 'day',
  WEEKLY: 'week',
  MONTHLY: 'month',
  YEARLY: 'year'
};

const colors = ['#FF6800', '#1677FF', '#FAAD14', '#52C41A', '#FF4D4F'];

const getListData = (value, summaryList) => {
  const date = dayjs(value); // 无需在这里将时间戳转为 dayjs 对象，直接传入 dayjs 构造函数即可
  let listData = [];

  summaryList &&
    summaryList.forEach((item) => {
      if (item?.recur) {
        // 代表是周期
        const {
          summary,
          headDate: start,
          recur: { interval: initInterval, until, frequency, dayList, count, monthDayList, monthList }
        } = item;
        const interval = initInterval === -1 ? 1 : initInterval;
        const isInterval = date.isSameOrAfter(start, typeMap[frequency]);
        // todo 年修改为day，用day来算具体日期
        const isUntil =
          !until ||
          date.isSameOrBefore(dayjs(until).format('YYYY-MM-DD'), frequency === 'YEARLY' ? 'day' : typeMap[frequency]);
        const isInRange = isInterval && isUntil;
        // isUntil会自动判断是否在日期之内， 所以接下来不用判断多少次结束之后的日期
        if (frequency && isInRange) {
          switch (frequency) {
            case 'SECONDLY':
              window.console.log('SECONDLY');
              break;
            case 'MINUTELY':
              window.console.log('MINUTELY');
              break;
            case 'HOURLY':
              window.console.log('HOURLY');
              break;
            case 'DAILY':
              if (count === -1) {
                if (date.diff(start, typeMap[frequency]) % interval === 0) {
                  // 判断日期间隔
                  listData = [{ type: 'warning', content: summary }];
                }
              } else {
                const currentDate = dayjs(start);
                const eventTimes = []; // 用于存储日期的数组
                for (let i = 0; i < count; i++) {
                  const date = currentDate.add(i * interval, 'day'); // 将 dayjs 对象加上指定天数
                  eventTimes.push(date.format('YYYY-MM-DD')); // 将日期格式化并 push 进数组中
                }
                if (eventTimes.includes(dayjs(value).format('YYYY-MM-DD'))) {
                  listData = [{ type: 'warning', content: summary }];
                }
              }
              break;
            case 'WEEKLY':
              if (count === -1 && _.isEmpty(dayList)) {
                // 计算开始时间 当前时间是周几
                const offset = dayjs(start).day();
                const currentTime = dayjs(value.valueOf()).day();
                if (offset === currentTime) {
                  listData = [{ type: 'warning', content: summary }];
                }
              } else {
                // 非无限循环也有两种情况 一种是有list 一种是查外面周几
                // 周末有两种情况 一种是无限循环，另一种是重复几周
                // 要考虑count的情况 interval 间隔几周
                const map = { SU: 0, MO: 1, TU: 2, WE: 3, TH: 4, FR: 5, SA: 6 };
                const days = !_.isEmpty(dayList) ? dayList.map((item) => map[item.day]) : [dayjs(start).day()];
                const currentDate = dayjs(start);
                // 找开始时间 从开始时间往后推
                const isCurrentDate = dayjs(value.format('YYYY-MM-DD')).isSameOrAfter(
                  currentDate.format('YYYY-MM-DD'),
                  'day'
                );
                if (!_.isEmpty(days) && count === -1 && isCurrentDate && days.includes(dayjs(value).day())) {
                  listData = [{ type: 'warning', content: summary }];
                } else if (!_.isEmpty(days)) {
                  let currentDate = dayjs(start);
                  let currentWeekday = currentDate.day();

                  // 创建一个空数组用于存放结果
                  const eventTimes = [];

                  // 当结果数组长度小于 count 时，进行循环遍历
                  while (eventTimes.length < count) {
                    // 判断当前日期是否为 days 数组中的某一个星期数
                    if (days.includes(currentWeekday)) {
                      eventTimes.push(currentDate.format('YYYY-MM-DD'));
                    }

                    // 将 currentDate 对象加上一天的时间，并更新 currentWeekday 变量
                    // todo 5.30 改为1*interval
                    currentDate = currentDate.add(1 * interval, 'day');
                    currentWeekday = currentDate.day();

                    // 如果 currentWeekday 为 0（即周日），则将 currentDate 对象加上一天的时间，
                    // 并更新 currentWeekday 变量为 1（即周一）
                    if (currentWeekday === 0) {
                      currentDate = currentDate.add(1, 'day');
                      currentWeekday = 1;
                    }
                  }
                  if (eventTimes.includes(dayjs(value).format('YYYY-MM-DD'))) {
                    listData = [{ type: 'warning', content: summary }];
                  }
                }
              }
              break;
            case 'MONTHLY':
              if (count === -1) {
                // 获取每个月的第几号
                const offset = dayjs(start).date();
                if (interval === 1) {
                  if (date.date() === offset) {
                    listData = [{ type: 'warning', content: summary }];
                  }
                } else if (_.isEmpty(monthDayList)) {
                  if (date.format('MM-DD') === dayjs(start).format('MM-DD')) {
                    listData = [{ type: 'warning', content: summary }];
                  }
                } else {
                  // 无限循环，生成所有日期
                  const nowYear = dayjs().year();
                  const offset = dayjs(start).month() + 1;
                  const list = [];
                  monthDayList.forEach((item) => {
                    const moment = `${nowYear}-${offset}-${item}`;
                    const formatMoment = dayjs(dayjs(moment).valueOf()).format('YYYY-MM-DD');
                    for (let i = 0; i < 100; i++) {
                      const nextMoment = dayjs(formatMoment).add(i * interval, 'month');
                      list.push(nextMoment.format('YYYY-MM-DD'));
                    }
                  });

                  // todo 在生成的日期中查找符合条件的日期 此处用filter代替for循环提高性能
                  const filteredList = list.filter((item) => item === dayjs(value).format('YYYY-MM-DD'));
                  if (!_.isEmpty(filteredList)) {
                    listData = [{ type: 'warning', content: summary }];
                  }
                }
              } else {
                // 循环monthDayList每个月的item，一共执行count次
                let allCount = 0;
                let currentDate = dayjs(start);
                // 定义事件时间数组
                const eventTimes = [];
                while (allCount < count) {
                  // 计算本次符合条件的日期列表
                  const nextDays = [];
                  for (let i = 0; i < monthDayList.length; i++) {
                    // todo 5.30 改为1*interval
                    let nextDay = currentDate.add(i * interval, 'month');
                    while (nextDay.date() !== monthDayList[i]) {
                      nextDay = nextDay.add(1, 'day');
                    }
                    nextDays.push(nextDay);
                  }
                  // 累加符合条件日期的个数
                  allCount += nextDays.length;
                  // 如果累计的个数超过recordCount，则只取指定个数的事件
                  if (allCount > count) {
                    const diff = allCount - count;
                    nextDays.splice(-diff, diff);
                  }
                  // 添加本次记录的事件时间
                  eventTimes.push(...nextDays.map((day) => day.format('YYYY-MM-DD')));
                  // 跳过指定的月数
                  currentDate = currentDate.add(interval, 'month');
                }
                if (eventTimes.includes(dayjs(value).format('YYYY-MM-DD'))) {
                  listData = [{ type: 'warning', content: summary }];
                }
              }
              break;
            case 'YEARLY':
              if (count === -1) {
                const nowYear = dayjs().year();
                const offset = dayjs(start).date();
                if (!_.isEmpty(monthList)) {
                  monthList.forEach((item) => {
                    const moment = `${nowYear}-${item}-${offset}`;
                    // 格式化日期
                    const formatMoment = dayjs(dayjs(moment).valueOf()).format('MM-DD');
                    if (dayjs(value).format('MM-DD') === formatMoment) {
                      listData = [{ type: 'warning', content: summary }];
                    }
                  });
                } else {
                  const nowYear = dayjs().year();
                  const nowMonth = dayjs(start).month() + 1;
                  const offset = dayjs(start).date();
                  const moment = `${nowYear}-${nowMonth}-${offset}`;
                  const formatMoment = dayjs(dayjs(moment).valueOf()).format('MM-DD');
                  if (dayjs(value).format('MM-DD') === formatMoment) {
                    listData = [{ type: 'warning', content: summary }];
                  }
                }
              } else {
                const offset = dayjs(start).date(); // 获取几号
                // 循环monthList每个月的offset号，一共执行count次
                let allCount = 0;
                let currentDate = dayjs(start);
                // 定义事件时间数组
                const eventTimes = [];
                if (!_.isEmpty(monthList)) {
                  while (allCount < count) {
                    // 计算本次符合条件的日期列表
                    const nextDays = [];
                    for (let i = 0; i < monthList.length; i++) {
                      let nextDay = currentDate.add(i, 'year');
                      while (nextDay.date() !== offset) {
                        nextDay = nextDay.add(1, 'year');
                      }
                      nextDays.push(nextDay);
                    }
                    // 累加符合条件日期的个数
                    allCount += nextDays.length;
                    // 如果累计的个数超过recordCount，则只取指定个数的事件
                    if (allCount > count) {
                      const diff = allCount - count;
                      nextDays.splice(-diff, diff);
                    }
                    // 添加本次记录的事件时间
                    eventTimes.push(...nextDays.map((day) => day.format('YYYY-MM-DD')));
                    // 跳过指定的月 计算月份
                    currentDate = currentDate.add(interval, 'year');
                  }
                } else {
                  const nowYear = dayjs().year();
                  const nowMonth = dayjs(start).month() + 1;
                  const offset = dayjs(start).date();
                  for (let i = 0; i < count; i++) {
                    const year = nowYear + i;
                    const moment = `${year}-${nowMonth}-${offset}`;
                    const formatMoment = dayjs(dayjs(moment).valueOf()).format('YYYY-MM-DD');
                    eventTimes.push(formatMoment);
                  }
                }
                if (eventTimes.includes(dayjs(value).format('YYYY-MM-DD'))) {
                  listData = [{ type: 'warning', content: summary }];
                }
              }
              break;
            default:
              break;
          }
        }
        if (!frequency) {
          // 全天只取当天
          const startOf = dayjs(start).format('YYYY-MM-DD');
          if (startOf === date.format('YYYY-MM-DD')) {
            listData = [{ type: 'warning', content: summary }];
          }
        }
      }
    });
  return listData;
};

// eslint-disable-next-line no-unused-vars
export default (props) => {
  const { record } = props;
  const [date, setDate] = useState(dayjs());
  const [calendarValue, setCalendarValue] = useState(null);

  useEffect(() => {
    const init = async () => {
      const value = await CalendarService.get(record.id);
      setCalendarValue(value);
    };
    init();
  }, []);

  const dateCellRender = (value) => {
    const listData = getListData(value, calendarValue && calendarValue.summaryList);
    return (
      <ul className="events">
        {listData.map((item) => {
          const index = calendarValue.summaryList.findIndex((value) => value.summary === item.content);
          return (
            <li key={item.content}>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <span
                  style={{
                    background: colors[index || 0],
                    display: 'inline-block',
                    width: 6,
                    height: 6,
                    borderRadius: '50%'
                  }}
                />
                <Text ellipsis={{ tooltip: item.content }} style={{ width: '90%' }}>
                  {item.content}
                </Text>
              </div>
            </li>
          );
        })}
      </ul>
      // <ul className="events">
      //   {!_.isEmpty(listData) ? listData.map((item) => (
      //     <li key={item.content}>
      //       <div
      //         style={{
      //           display: 'flex',
      //           justifyContent: 'space-between',
      //           alignItems: 'center'
      //         }}
      //       >
      //         <span style={{
      //           background: colors[item?.index || 0],
      //           display: 'inline-block',
      //           width: 6,
      //           height: 6,
      //           borderRadius: '50%'
      //         }}
      //         />
      //         <Text ellipsis={{ tooltip: item.content }} style={{ width: '90%' }}>
      //           {item.content}
      //         </Text>
      //       </div>
      //     </li>
      //   )) : <Badge
      //     text="其他"
      //     color="#d9d9d9"
      //   />}
      // </ul>
    );
  };

  return (
    <div className="calendarDetail">
      <Space direction="vertical">
        <div>{t('dataCenter-xwrxOPzg5NB6')}{calendarValue?.name}</div>
        <div>
          {t('dataCenter-noL5QaIaHV3b')}
          <Badge status={calendarValue?.status === 'NORMAL' ? 'success' : 'error'} style={{ marginRight: 8 }} />
          {calendarValue?.status === 'NORMAL' ? t('dataCenter-eynyiqiRJnkE') : t('dataCenter-OnUCORxcIuVO')}
        </div>
        <div>
          {t('dataCenter-X9ez72oAMZcR')}
          <Text ellipsis={{ tooltip: calendarValue?.memo || '-' }} style={{ width: '800px' }}>
            {calendarValue?.memo || '-'}
          </Text>
        </div>
      </Space>
      <div style={{ position: 'relative', marginTop: 12 }}>
        <div
          style={{
            position: 'absolute',
            top: 12,
            display: 'flex',
            justifyContent: 'flex-end'
          }}
        >
          <Button onClick={() => setDate(dayjs())}>{t('dataCenter-oT8EUBDuMK93')}</Button>
        </div>
        <Calendar value={date} onChange={(e) => setDate(e)} dateCellRender={(value) => dateCellRender(value)} />
      </div>
    </div>
  );
};
