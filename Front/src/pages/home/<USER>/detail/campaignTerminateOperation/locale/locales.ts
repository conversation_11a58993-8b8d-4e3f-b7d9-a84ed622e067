export default {
  cn: {
    // List.jsx
    'operationCenter-KH2JRA2SfJCw': '开始',
    'operationCenter-JHWtwZvzKOpp': '暂停',
    'operationCenter-rX7uKn3D2Gal': '恢复',
    'operationCenter-2QLt0Vdx7oby': '结束',
    'operationCenter-h5eC3UdLdOvY': '终止',
    'operationCenter-Ugu3ZXJGYRUH': '强制终止',
    'operationCenter-fbDcjM5bCUMt': '回执',
    'operationCenter-15TAR6HIY5cq': '更新',
    'operationCenter-cXxyBDV3kR6N': '删除',
    'operationCenter-7K8YCIQOyn4L': '召回',
    'operationCenter-FX2toIOUMf4m': '批次ID',
    'operationCenter-S4RZIxt3fiQt': '是否是测试运行',
    'operationCenter-0Lmpv4IV56fh': '操作时间',
    'operationCenter-pYohORRJVsi4': '描述',
    'operationCenter-FEe66t8XTA2e': '操作人',
    'operationCenter-X8wid7MTG79V': '操作时间',
    'operationCenter-ieJrsYWV52ea': '流程开启人：',
    'operationCenter-PjpWsNpIfKB4': '开启时间：',
    'operationCenter-VNK2YclPVxU3': '流程终止人：',
    'operationCenter-jWE9yCvs6g6y': '终止时间：',
    'operationCenter-V63Yckr1XvRt': '操作动作',
    'operationCenter-LMHcZAynmEIp': '是',
    'operationCenter-kzU6e3a2gck2': '否',
    'operationCenter-SFEwOJcjjqOz': '数据加载中',
    'operationCenter-68Gf9hNQmaDh': '流程画布',
    'operationCenter-SCHm3W23hInV': '查看流程',
    'operationCenter-YWWUIKCfaBqi': '流程详情'
  },
  en: {
    'operationCenter-KH2JRA2SfJCw': 'Start',
    'operationCenter-JHWtwZvzKOpp': 'Pause',
    'operationCenter-rX7uKn3D2Gal': 'Resume',
    'operationCenter-2QLt0Vdx7oby': 'End',
    'operationCenter-h5eC3UdLdOvY': 'Terminate',
    'operationCenter-Ugu3ZXJGYRUH': 'Force Terminate',
    'operationCenter-fbDcjM5bCUMt': 'Receipt',
    'operationCenter-15TAR6HIY5cq': 'Update',
    'operationCenter-cXxyBDV3kR6N': 'Delete',
    'operationCenter-7K8YCIQOyn4L': 'Recall',
    'operationCenter-FX2toIOUMf4m': 'Batch ID',
    'operationCenter-S4RZIxt3fiQt': 'Is Test Run',
    'operationCenter-0Lmpv4IV56fh': 'Operation Time',
    'operationCenter-pYohORRJVsi4': 'Description',
    'operationCenter-FEe66t8XTA2e': 'Operator',
    'operationCenter-X8wid7MTG79V': 'Operation Time',
    'operationCenter-ieJrsYWV52ea': 'Process Start Person:',
    'operationCenter-PjpWsNpIfKB4': 'Start Time:',
    'operationCenter-VNK2YclPVxU3': 'Process Terminate Person:',
    'operationCenter-jWE9yCvs6g6y': 'Terminate Time:',
    'operationCenter-V63Yckr1XvRt': 'Operation Action',
    'operationCenter-LMHcZAynmEIp': 'Yes',
    'operationCenter-kzU6e3a2gck2': 'No',
    'operationCenter-SFEwOJcjjqOz': 'Data Loading',
    'operationCenter-68Gf9hNQmaDh': 'Process Canvas',
    'operationCenter-SCHm3W23hInV': 'View Process',
    'operationCenter-YWWUIKCfaBqi': 'Process Details'
  }
};
