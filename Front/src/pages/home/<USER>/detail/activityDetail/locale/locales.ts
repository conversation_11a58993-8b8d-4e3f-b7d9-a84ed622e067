export default {
  cn: {
    // List.jsx
    'operationCenter-n1LmJCVzLK5J': '详情',
    'operationCenter-HzrMDSX0h2Y4': '查看推送/撤回结果',
    'operationCenter-kX1foierILlb': '至少保留一个展示字段',
    'operationCenter-00hnB5g5cJjm': '保存成功',
    'operationCenter-K1AAACn8Qgwe': '当前节点无详情',
    'operationCenter-HoxK10LLgbgg': '操作',
    'operationCenter-COa32sclnGEG': '请先筛选执行记录，再导出报表',
    'operationCenter-xNcmVUYLIBPn': '活动流程',
    'operationCenter-JMXkLc4GUkXd': '流程画布',
    'operationCenter-ySB0QrGlIIjD': '流程详情',
    'operationCenter-tTKtrT1DsTsc': '执行详情',
    'operationCenter-WUBeIZCbEtpV': '报表导出',
    'operationCenter-Rq5eWHlTI7qF': '列设置',
    'operationCenter-U7reH2EjNsVF': '推送/撤回结果',
    'operationCenter-I7gT3vD0shNn': '确定',
    'operationCenter-AGK9HqawlEsd': '报表导出',
    'operationCenter-0OVPS6uEPyVs': '导出',
    'operationCenter-VUwwgpL5vduv': '条',
    'operationCenter-hFiaBPFasDBl': '请输入导出的日志数据量,最大值10000',

    // list/config.js
    'operationCenter-e5DAteGWlp4m': '输入搜索内容',

    // editcolumns/config.js
    'operationCenter-6EacRHUrh1Uz': '字段名称',
    'operationCenter-bYMaoYPEBcMr': '顺序',
    'operationCenter-pcPhzInktJIY': '输入排序值',

    // editcolumns/index.jsx
    'operationCenter-HRZhfOkEQh1h': '最大长度限制为8位字符',
    'operationCenter-rXrw8lrRDgse': '只能输入0和大于0的整数',
    'operationCenter-BotOgAnXNnFL': '说明：勾选所需显示字段后，输入数值进行排序，数字越小排序越靠前',
    'operationCenter-rnAEVg3ufj6V': '至少保留一个字段',
    'operationCenter-9mSFJwQyllOi': '列显示设置',
    'operationCenter-LgEM5lP69cMN': '取消',
    'operationCenter-syeSh8y2ia5y': '确定',

    // activityDetailV2/list/saveGroup.tsx
    'operationCenter-V7oQishnlG4c': '分群已保存，是否提交审批?',
    'operationCenter-dHHjWxr5JlKe': '确认提交',
    'operationCenter-EgC59Xf1khy4': '稍后提交',
    'operationCenter-0Aw0UZaVQ8Rm': '提交审批成功',
    'operationCenter-oPn20qasZetr': '请选择失效时间',
    'operationCenter-r8IZx1B7Ufg1': '存为用户分群',
    'operationCenter-RsvsgX4VEhLk': '归属部门：',
    'operationCenter-zkhz7gpwp2im': '分群类型：日志分群',
    'operationCenter-Ml7oOCgB9TN4': 'ID类型：',
    'operationCenter-OHp76B7BiMdn': '分群名称',
    'operationCenter-eNL2XuS5spDz': '请输入分群名称',
    'operationCenter-r4xSCTqmpqTz': '分群名称不要超出60个字符',
    'operationCenter-Ff0IygKujXKo': '请输入字母、数字、字符(._-)或者汉字',
    'operationCenter-uhEMePzrRRWx': '分群名称不能为空',
    'operationCenter-pxjYZoCz6FMw': '分群名称已存在',
    'operationCenter-S9ca9amyQQqX': '备注描述',
    'operationCenter-LLIQd8t60tzJ': '分群备注不要超出150个字符',
    'operationCenter-lJ1uv4JCI7Lj': '有效时间：',
    'operationCenter-05DamuXNPsAE': '永久有效',
    'operationCenter-JH3ILyZFlyp8': '失效时间',

    // activityDetailV2/list/config.js
    'operationCenter-3gkdfUKTMeeX': '成功',
    'operationCenter-6bBfQYTROBVz': '失败',
    'operationCenter-vv4aMmhtB6Yc': '防打扰过滤',
    'operationCenter-lsrLElbS26Pv': '日志类型',
    'operationCenter-OnzOw2DA57jJ': '调度日志',
    'operationCenter-ARoTAHpCtDqv': '触达日志',
    'operationCenter-QHV9tXeb9zsi': '撤回日志',
    'operationCenter-A96U7dazgTMb': '回调日志',
    'operationCenter-KJB7YIltNyE0': '推送日志',
    'operationCenter-NBt1Nmjs7ZHG': '召回日志',
    'operationCenter-x6BRj3f4Kb52': '召回结果日志',
    'operationCenter-8Y3DsaBAi8bm': '节点ID',
    'operationCenter-AQXEhCxf7uvF': '运营ID',
    'operationCenter-dXrgmzwRZeMo': '消息Id',
    'operationCenter-qv8YNKDDcfni': '日志时间',
    'operationCenter-kOxKaTMBgD9g': '渠道返回状态',
    'operationCenter-k7BAjD7IxMTC': '渠道返回体',
    'operationCenter-ptmIDNHBaFvJ': 'ID类型',
    'operationCenter-uKD2B9eIBd0g': '批次ID',
    'operationCenter-VsgPp6oHM4l7': '消息ID',
    'operationCenter-6TVqgBWq6gqy': '最后动作时间',
    'operationCenter-xhpQ7ipgD7Sq': '流程名称',
    'operationCenter-IiHhZRVI8wgI': '流程开始时间',
    'operationCenter-uLFrjBUTYPcL': '流程结束时间',
    'operationCenter-jjUMKIymKZM1': '节点名称',
    'operationCenter-PTJhA7H6Qjxn': '节点显示名称',
    'operationCenter-JVVHfWWeXU9Y': '节点类型',

    // activityDetailV2/download/index.tsx
    'operationCenter-SZ5vL2vfHkDR': '下载数据',
    'operationCenter-mN7sZRkYrjG2': '设置导出字段',
    'operationCenter-vkqKdiBKBp7p': '字段',
    'operationCenter-t8FdQEakot6h': '字段显示名',

    // activityDetailV2/list/ListV2.jsx
    'operationCenter-AeQd1iPWU40C': '查看结果',
    'operationCenter-myGIcMpbEm4A': '批次ID',
    'operationCenter-1qnZe4YiuHFz': '流程ID',
    'operationCenter-52Yqy2I8m5SX': '至少保留一个展示字段',
    'operationCenter-DjeDUcMcgpko': '当前节点无详情',
    'operationCenter-WYgfJZUif9DZ': '可用',
    'operationCenter-Vc1ZpJvOnf4i': '不存在',
    'operationCenter-4gwnJqg8CzY2': '已停用',
    'operationCenter-7XaKq0ZClNsm': 'iframe节点数据',
    'operationCenter-vxQx1CaJYmVC': '渠道ID',
    'operationCenter-foerXvEcLKpF': '重试次数',
    'operationCenter-J2GFWa6qvRkE': '渠道状态',
    'operationCenter-QSfF14vw2uEO': '关联推送消息ID',
    'operationCenter-Y0JMmfGoQ06X': '渠道返回状态',
    'operationCenter-fHhlkW52nOVn': '成功',
    'operationCenter-4IihKQF5GdQk': '失败',
    'operationCenter-W9z3C5RldFBN': '渠道返回体',
    'operationCenter-kssLl1CmjdK6': '渠道返回消息',
    'operationCenter-SokjGGb9Ebrx': '用户信息',
    'operationCenter-nl465wUXzIvk': '操作',
    'operationCenter-ITpDLLXbWWW9': '执行详情',
    'operationCenter-lSOLPVzzmoYw': '流程画布',
    'operationCenter-XRRGM992KRJl': '流程详情',
    'operationCenter-3uAapj85bq5r': '所有日志类型均支持保存分群',
    'operationCenter-kZ67MrpDSQYN': '流程画布批次运行中、终止、结束状态均支持保存分群',
    'operationCenter-jfOB4iM0WpVC': '所选条件下查询无结果，不支持保存分群',
    'operationCenter-GS5OZyw4wGF7': '保存分群',
    'operationCenter-N1Sfjbb9r4KD': '运行中的活动保存分群时数据会存在差异，且可能保存失败，如分群计算失败，可在分群列表中重新计算，是否确认保存分群？',
    'operationCenter-LDCzdkyTnceS': '确定保存分群',
    'operationCenter-vnz00lncr3v6': '取消',
    'operationCenter-IXWy8rFV0x9A': '保存分群',
    'operationCenter-9cGeC9jF9FCk': '下载数据仅支持1万条',
    'operationCenter-ncwAPqbjeS9a': '流程画布运行中、终止、结束状态均支持下载',
    'operationCenter-IB6lqJ5yLWnv': '下载数据',
    'operationCenter-Xx3FOBJU56ke': '列设置',
    'operationCenter-4emXn4GsNWgh': '查看结果',
    'operationCenter-blDQX56bQ0ku': '确定',

    // activityDetailV2/editcolumns/index.jsx
    'operationCenter-XyEFajdJxELO': '最大长度限制为8位字符',
    'operationCenter-KZwvkGjgSUR9': '只能输入0和大于0的整数',
    'operationCenter-GY4CfXkHaIPt': '说明：勾选所需显示字段后，输入数值进行排序，数字越小排序越靠前',
    'operationCenter-cSrDNxCZB6gY': '至少保留一个字段',
    'operationCenter-8GarAxC1mBfK': '列显示设置',
    'operationCenter-PwUHtnsT48Wb': '取消',
    'operationCenter-8UTBYSIHzARf': '确定',

    // activityDetailV2/editcolumns/config.js
    'operationCenter-1wrhsYzGWFoI': '字段名称',
    'operationCenter-nmtSYDa5jU4c': '顺序',
    'operationCenter-qgXDtKSHFbbj': '输入排序值',

    // activityDetailV2/download/config.ts
    'operationCenter-ERgJcpxaE0zH': '流程ID',
    'operationCenter-By3RkRQnnNhb': '日志类型',
    'operationCenter-TbHMvPbiGLuF': '日志时间',
    'operationCenter-1gBvvr9jZxPr': '渠道返回状态',
    'operationCenter-HNKde0QmpOM9': '渠道返回体',
    'operationCenter-NabdoEsFGPI1': 'ID类型',
    'operationCenter-xhUUOryVabaG': '批次ID',
    'operationCenter-3uSThULuH4ZV': '运营ID',
    'operationCenter-gfzB0WsXy2Xa': '节点ID',
    'operationCenter-qUP5lAU8CVWH': '消息ID',
    'operationCenter-aUmBMbewu8Qk': '最后动作时间',
    'operationCenter-Hyjy31VbSOL7': '流程名称',
    'operationCenter-H8TFPaLwxVYo': '流程开始时间',
    'operationCenter-GtCeQPNW0OHU': '流程结束时间',
    'operationCenter-KcbRi0qUGDci': '节点名称',
    'operationCenter-E1z5ngNaRfwI': '节点显示名称',
    'operationCenter-CjCirOLFnLJr': '节点类型'
  },
  en: {
    // List.jsx
    'operationCenter-n1LmJCVzLK5J': 'Details',
    'operationCenter-HzrMDSX0h2Y4': 'View Push/Recall Results',
    'operationCenter-kX1foierILlb': 'At least one display field must be retained',
    'operationCenter-00hnB5g5cJjm': 'Save successful',
    'operationCenter-K1AAACn8Qgwe': 'Current node has no details',
    'operationCenter-HoxK10LLgbgg': 'Actions',
    'operationCenter-COa32sclnGEG': 'Please filter execution records first, then export report',
    'operationCenter-xNcmVUYLIBPn': 'Activity Process',
    'operationCenter-JMXkLc4GUkXd': 'Process Canvas',
    'operationCenter-ySB0QrGlIIjD': 'Process Details',
    'operationCenter-tTKtrT1DsTsc': 'Execution Details',
    'operationCenter-WUBeIZCbEtpV': 'Report Export',
    'operationCenter-Rq5eWHlTI7qF': 'Column Settings',
    'operationCenter-U7reH2EjNsVF': 'Push/Recall Results',
    'operationCenter-I7gT3vD0shNn': 'Confirm',
    'operationCenter-AGK9HqawlEsd': 'Report Export',
    'operationCenter-0OVPS6uEPyVs': 'Export',
    'operationCenter-VUwwgpL5vduv': 'records',
    'operationCenter-hFiaBPFasDBl': 'Please enter the amount of log data to export, maximum value is 10000',

    // list/config.js
    'operationCenter-e5DAteGWlp4m': 'Enter search content',

    // editcolumns/config.js
    'operationCenter-6EacRHUrh1Uz': 'Field Name',
    'operationCenter-bYMaoYPEBcMr': 'Order',
    'operationCenter-pcPhzInktJIY': 'Enter sort value',

    // editcolumns/index.jsx
    'operationCenter-HRZhfOkEQh1h': 'Maximum length limit is 8 characters',
    'operationCenter-rXrw8lrRDgse': 'Only 0 and positive integers are allowed',
    'operationCenter-BotOgAnXNnFL': 'Note: Check the required display fields, then enter values for sorting. Smaller numbers rank higher',
    'operationCenter-rnAEVg3ufj6V': 'At least one field must be retained',
    'operationCenter-9mSFJwQyllOi': 'Column Display Settings',
    'operationCenter-LgEM5lP69cMN': 'Cancel',
    'operationCenter-syeSh8y2ia5y': 'Confirm',

    // activityDetailV2/list/saveGroup.tsx
    'operationCenter-V7oQishnlG4c': 'Segment saved, submit for approval?',
    'operationCenter-dHHjWxr5JlKe': 'Confirm Submit',
    'operationCenter-EgC59Xf1khy4': 'Submit Later',
    'operationCenter-0Aw0UZaVQ8Rm': 'Approval submitted successfully',
    'operationCenter-oPn20qasZetr': 'Please select expiration time',
    'operationCenter-r8IZx1B7Ufg1': 'Save as User Segment',
    'operationCenter-RsvsgX4VEhLk': 'Department: ',
    'operationCenter-zkhz7gpwp2im': 'Segment Type: Log Segment',
    'operationCenter-Ml7oOCgB9TN4': 'ID Type: ',
    'operationCenter-OHp76B7BiMdn': 'Segment Name',
    'operationCenter-eNL2XuS5spDz': 'Please enter segment name',
    'operationCenter-r4xSCTqmpqTz': 'Segment name should not exceed 60 characters',
    'operationCenter-Ff0IygKujXKo': 'Please enter letters, numbers, characters (._-) or Chinese characters',
    'operationCenter-uhEMePzrRRWx': 'Segment name cannot be empty',
    'operationCenter-pxjYZoCz6FMw': 'Segment name already exists',
    'operationCenter-S9ca9amyQQqX': 'Description',
    'operationCenter-LLIQd8t60tzJ': 'Segment description should not exceed 150 characters',
    'operationCenter-lJ1uv4JCI7Lj': 'Valid Time: ',
    'operationCenter-05DamuXNPsAE': 'Permanent',
    'operationCenter-JH3ILyZFlyp8': 'Expiration Time',

    // activityDetailV2/list/config.js
    'operationCenter-3gkdfUKTMeeX': 'Success',
    'operationCenter-6bBfQYTROBVz': 'Failed',
    'operationCenter-vv4aMmhtB6Yc': 'Do Not Disturb Filter',
    'operationCenter-lsrLElbS26Pv': 'Log Type',
    'operationCenter-OnzOw2DA57jJ': 'Schedule Log',
    'operationCenter-ARoTAHpCtDqv': 'Reach Log',
    'operationCenter-QHV9tXeb9zsi': 'Recall Log',
    'operationCenter-A96U7dazgTMb': 'Callback Log',
    'operationCenter-KJB7YIltNyE0': 'Push Log',
    'operationCenter-NBt1Nmjs7ZHG': 'Recall Log',
    'operationCenter-x6BRj3f4Kb52': 'Recall Result Log',
    'operationCenter-8Y3DsaBAi8bm': 'Node ID',
    'operationCenter-AQXEhCxf7uvF': 'Operation ID',
    'operationCenter-dXrgmzwRZeMo': 'Message ID',
    'operationCenter-qv8YNKDDcfni': 'Log Time',
    'operationCenter-kOxKaTMBgD9g': 'Channel Return Status',
    'operationCenter-k7BAjD7IxMTC': 'Channel Return Body',
    'operationCenter-ptmIDNHBaFvJ': 'ID Type',
    'operationCenter-uKD2B9eIBd0g': 'Batch ID',
    'operationCenter-VsgPp6oHM4l7': 'Message ID',
    'operationCenter-6TVqgBWq6gqy': 'Last Action Time',
    'operationCenter-xhpQ7ipgD7Sq': 'Process Name',
    'operationCenter-IiHhZRVI8wgI': 'Process Start Time',
    'operationCenter-uLFrjBUTYPcL': 'Process End Time',
    'operationCenter-jjUMKIymKZM1': 'Node Name',
    'operationCenter-PTJhA7H6Qjxn': 'Node Display Name',
    'operationCenter-JVVHfWWeXU9Y': 'Node Type',

    // activityDetailV2/download/index.tsx
    'operationCenter-SZ5vL2vfHkDR': 'Download Data',
    'operationCenter-mN7sZRkYrjG2': 'Set Export Fields',
    'operationCenter-vkqKdiBKBp7p': 'Field',
    'operationCenter-t8FdQEakot6h': 'Field Display Name',

    // activityDetailV2/list/ListV2.jsx
    'operationCenter-AeQd1iPWU40C': 'View Results',
    'operationCenter-myGIcMpbEm4A': 'Batch ID',
    'operationCenter-1qnZe4YiuHFz': 'Process ID',
    'operationCenter-52Yqy2I8m5SX': 'At least one display field must be retained',
    'operationCenter-DjeDUcMcgpko': 'Current node has no details',
    'operationCenter-WYgfJZUif9DZ': 'Available',
    'operationCenter-Vc1ZpJvOnf4i': 'Not Exist',
    'operationCenter-4gwnJqg8CzY2': 'Stopped',
    'operationCenter-7XaKq0ZClNsm': 'Iframe Node Data',
    'operationCenter-vxQx1CaJYmVC': 'Channel ID',
    'operationCenter-foerXvEcLKpF': 'Retry Times',
    'operationCenter-J2GFWa6qvRkE': 'Channel Status',
    'operationCenter-QSfF14vw2uEO': 'Associated Push Message ID',
    'operationCenter-Y0JMmfGoQ06X': 'Channel Return Status',
    'operationCenter-fHhlkW52nOVn': 'Success',
    'operationCenter-4IihKQF5GdQk': 'Failed',
    'operationCenter-W9z3C5RldFBN': 'Channel Return Body',
    'operationCenter-kssLl1CmjdK6': 'Channel Return Message',
    'operationCenter-SokjGGb9Ebrx': 'User Information',
    'operationCenter-nl465wUXzIvk': 'Actions',
    'operationCenter-ITpDLLXbWWW9': 'Execution Details',
    'operationCenter-lSOLPVzzmoYw': 'Process Canvas',
    'operationCenter-XRRGM992KRJl': 'Process Details',
    'operationCenter-3uAapj85bq5r': 'All log types support saving segments',
    'operationCenter-kZ67MrpDSQYN': 'Process canvas batch running, terminated, and ended states all support saving segments',
    'operationCenter-jfOB4iM0WpVC': 'No results found under selected conditions, segment saving not supported',
    'operationCenter-GS5OZyw4wGF7': 'Save Segment',
    'operationCenter-N1Sfjbb9r4KD': 'Running activities may have data differences when saving segments and may fail. If segment calculation fails, you can recalculate in the segment list. Are you sure to save the segment?',
    'operationCenter-LDCzdkyTnceS': 'Confirm Save Segment',
    'operationCenter-vnz00lncr3v6': 'Cancel',
    'operationCenter-IXWy8rFV0x9A': 'Save Segment',
    'operationCenter-9cGeC9jF9FCk': 'Download data supports up to 10,000 records',
    'operationCenter-ncwAPqbjeS9a': 'Process canvas running, terminated, and ended states all support download',
    'operationCenter-IB6lqJ5yLWnv': 'Download Data',
    'operationCenter-Xx3FOBJU56ke': 'Column Settings',
    'operationCenter-4emXn4GsNWgh': 'View Results',
    'operationCenter-blDQX56bQ0ku': 'Confirm',

    // activityDetailV2/editcolumns/index.jsx
    'operationCenter-XyEFajdJxELO': 'Maximum length limit is 8 characters',
    'operationCenter-KZwvkGjgSUR9': 'Only 0 and positive integers are allowed',
    'operationCenter-GY4CfXkHaIPt': 'Note: Check the required display fields, then enter values for sorting. Smaller numbers rank higher',
    'operationCenter-cSrDNxCZB6gY': 'At least one field must be retained',
    'operationCenter-8GarAxC1mBfK': 'Column Display Settings',
    'operationCenter-PwUHtnsT48Wb': 'Cancel',
    'operationCenter-8UTBYSIHzARf': 'Confirm',

    // activityDetailV2/editcolumns/config.js
    'operationCenter-1wrhsYzGWFoI': 'Field Name',
    'operationCenter-nmtSYDa5jU4c': 'Order',
    'operationCenter-qgXDtKSHFbbj': 'Enter sort value',

    // activityDetailV2/download/config.ts
    'operationCenter-ERgJcpxaE0zH': 'Process ID',
    'operationCenter-By3RkRQnnNhb': 'Log Type',
    'operationCenter-TbHMvPbiGLuF': 'Log Time',
    'operationCenter-1gBvvr9jZxPr': 'Channel Return Status',
    'operationCenter-HNKde0QmpOM9': 'Channel Return Body',
    'operationCenter-NabdoEsFGPI1': 'ID Type',
    'operationCenter-xhUUOryVabaG': 'Batch ID',
    'operationCenter-3uSThULuH4ZV': 'Operation ID',
    'operationCenter-gfzB0WsXy2Xa': 'Node ID',
    'operationCenter-qUP5lAU8CVWH': 'Message ID',
    'operationCenter-aUmBMbewu8Qk': 'Last Action Time',
    'operationCenter-Hyjy31VbSOL7': 'Process Name',
    'operationCenter-H8TFPaLwxVYo': 'Process Start Time',
    'operationCenter-GtCeQPNW0OHU': 'Process End Time',
    'operationCenter-KcbRi0qUGDci': 'Node Name',
    'operationCenter-E1z5ngNaRfwI': 'Node Display Name',
    'operationCenter-CjCirOLFnLJr': 'Node Type'
  }
};
