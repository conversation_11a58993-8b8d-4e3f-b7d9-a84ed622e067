export const elements = (tab<PERSON>ey, userList, dictTypeList) => {
  if (tabKey === 2) {
    return [
      {
        type: 'input',
        name: 'taskId',
        label: '审批编号',
        operator: 'LIKE'
      },
      {
        type: 'input',
        name: 'contentName',
        label: '审批事项名称',
        operator: 'LIKE'
      },
      {
        type: 'dateRange',
        name: 'startTime',
        label: '申请时间',
        operator: 'DATE_BETWEEN'
      },
      {
        name: 'starterId',
        type: 'select',
        label: '提交人',
        operator: 'EQ',
        options: userList
          ? userList.map((item) => {
              return { key: item.id, value: item.id, label: item.name };
            })
          : []
      },
      {
        name: 'approvalTypePcode',
        type: 'select',
        label: '审批对象',
        operator: 'EQ',
        options: dictTypeList.map((item) => {
          return { key: item?.code, value: item?.code, label: item?.name };
        })
      },
      {
        name: 'typeCodeName',
        type: 'input',
        label: '审批子对象',
        operator: 'LIKE'
      }
    ];
  } else if (tabKey === 3) {
    return [
      {
        type: 'input',
        name: 'approvalNo',
        label: '审批编号',
        operator: 'LIKE'
      },
      {
        type: 'input',
        name: 'contentName',
        label: '审批事项名称',
        operator: 'LIKE'
      },
      {
        type: 'dateRange',
        name: 'createTime',
        label: '申请时间',
        operator: 'DATE_BETWEEN'
      },
      {
        name: 'status',
        type: 'select',
        label: '审批状态',
        operator: 'EQ',
        options: [
          { key: 'REJECT', value: 'REJECT', label: '驳回' },
          { key: 'PASS', value: 'PASS', label: '通过' }
        ]
      },
      {
        name: 'contentType',
        type: 'select',
        label: '类型',
        operator: 'EQ',
        options: dictTypeList.map((item) => {
          return { key: item.value, value: item.value, label: item.label };
        })
      },
      {
        name: 'approverId',
        type: 'select',
        label: '审批人',
        operator: 'EQ',
        options: userList
          ? userList.map((item) => {
              return { key: item.id, value: item.id, label: item.name };
            })
          : []
      },
      {
        name: 'createUserId',
        type: 'select',
        label: '提交人',
        operator: 'EQ',
        options: userList
          ? userList.map((item) => {
              return { key: item.id, value: item.id, label: item.name };
            })
          : []
      }
    ];
  }
  return [
    {
      type: 'input',
      name: 'approvalNo',
      label: '审批编号',
      operator: 'LIKE'
      // componentConfig: {
      //   placeholder: '请输入图标名称'
      // },
      // options:[]
    },
    {
      type: 'input',
      name: 'contentName',
      label: '审批事项名称',
      operator: 'LIKE'
    },
    {
      type: 'dateRange',
      name: 'createTime',
      label: '申请时间',
      operator: 'DATE_BETWEEN'
    },
    {
      name: 'status',
      type: 'select',
      label: '审批状态',
      operator: 'EQ',
      options: [
        { key: 'RUNNING', value: 'RUNNING', label: '审批中' },
        { key: 'REJECT', value: 'REJECT', label: '驳回' },
        { key: 'PASS', value: 'PASS', label: '通过' },
        { key: 'BACKOUT', value: 'BACKOUT', label: '已撤销' },
        { key: 'CANCEL', value: 'CANCEL', label: '已取消' }
      ]
    },
    {
      name: 'contentType',
      type: 'select',
      label: '类型',
      operator: 'EQ',
      options: dictTypeList.map((item) => {
        return { key: item.value, value: item.value, label: item.label };
      })
    },
    {
      name: 'createUserId',
      type: 'select',
      label: '提交人',
      operator: 'EQ',
      options: userList
        ? userList.map((item) => {
            return { key: item.id, value: item.id, label: item.name };
          })
        : []
    }
  ];
};

export const initParam = {
  page: 1,
  search: [],
  size: 10,
  sorts: [{ propertyName: 'createTime', direction: 'desc' }]
};

export const initHistoryParam = {
  page: 1,
  search: [],
  size: 10,
  sorts: [{ propertyName: 'createTime', direction: 'asc' }]
};
