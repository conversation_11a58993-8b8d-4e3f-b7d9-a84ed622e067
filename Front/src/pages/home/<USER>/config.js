import CampaignV2Service from 'service/CampaignV2Service';
import { t } from 'utils/translation';

const campaignV2Service = new CampaignV2Service();

// 计算规则
export const ruleList = [
  {
    name: t('operationCenter-yNni0ZRvLi0r'),
    text: t('operationCenter-yNni0ZRvLi0r'),
    value: 'ONCE',
    key: 'ONCE'
  },
  {
    name: t('operationCenter-YN0No5zI1xTS'),
    text: t('operationCenter-YN0No5zI1xTS'),
    value: 'SCHEDULE',
    key: 'SCHEDULE'
  }
];

// 更新频率
export const scheduleRate = {
  DAY: t('operationCenter-dCBcBH9f25Lq'),
  WEEK: t('operationCenter-vsqf1SBA20dp'),
  MONTH: t('operationCenter-4QTYr0JdxkaS')
};

export const proessStatusList = {
  PENDING: t('operationCenter-b6TGv7R6QMlA'),
  RUNNING: t('operationCenter-UiY9oRI17wEv'),
  PASS: t('operationCenter-uzQK3EeWWUse'),
  REJECT: t('operationCenter-X5OMcGM4KFFc'),
  BACKOUT: t('operationCenter-AoOkJPCYPiCi'),
  CANCEL: '-',
  NULL: '-'
};

// 状态
export const statusList = [
  {
    name: t('operationCenter-HNksP2SdfOoM'),
    text: t('operationCenter-HNksP2SdfOoM'),
    value: 'NOTRUN',
    key: 'NOTRUN',
    color: '#D9D9D9'
  },
  {
    name: t('operationCenter-WazQ2eRmTntF'),
    text: t('operationCenter-WazQ2eRmTntF'),
    value: 'RUNNING',
    key: 'RUNNING',
    color: '#1890FF'
  },
  {
    name: t('operationCenter-oqzenaNG2DeD'),
    text: t('operationCenter-oqzenaNG2DeD'),
    value: 'STOPPING',
    key: 'STOPPING',
    color: '#FFE58F'
  },
  {
    name: t('operationCenter-0FisWLjipmub'),
    text: t('operationCenter-0FisWLjipmub'),
    value: 'STOPPED',
    key: 'STOPPED',
    color: '#52C41A'
  },
  {
    name: t('operationCenter-xlikClRoEAvK'),
    text: t('operationCenter-xlikClRoEAvK'),
    value: 'TERMINATED',
    key: 'TERMINATED',
    color: '#FAAD14'
  },
  {
    name: t('operationCenter-6PoOxTnlXJZB'),
    text: t('operationCenter-6PoOxTnlXJZB'),
    value: 'FAIL',
    key: 'FAIL',
    color: '#FF4D4F'
  },
  {
    name: t('operationCenter-ObrQ3ZSVK3rN'),
    text: t('operationCenter-ObrQ3ZSVK3rN'),
    value: 'COMPLETE_FAIL',
    key: 'COMPLETE_FAIL',
    color: '#FF4D4F'
  },
  {
    name: t('operationCenter-fP20hjHwSu7H'),
    text: t('operationCenter-fP20hjHwSu7H'),
    value: 'RECALLING',
    key: 'RECALLING',
    color: '#FFADD2'
  },
  {
    name: t('operationCenter-uwck4sEBEbww'),
    text: t('operationCenter-uwck4sEBEbww'),
    value: 'RECALL_SUC',
    key: 'RECALL_SUC',
    color: '#F759AB'
  }
];

export const phaseList = [
  {
    name: t('operationCenter-mHJIRNRr5T3L'),
    text: t('operationCenter-mHJIRNRr5T3L'),
    value: 'DRAFT',
    key: 'DRAFT',
    color: 'rgba(0, 0, 0, 0)'
  },
  {
    name: t('operationCenter-QXswS4LEYZEK'),
    text: t('operationCenter-QXswS4LEYZEK'),
    value: 'TESTING',
    key: 'TESTING',
    color: '#52c41a'
  },
  {
    name: t('operationCenter-EvzfawJ5n0iP'),
    text: t('operationCenter-EvzfawJ5n0iP'),
    value: 'TEST_SUC',
    key: 'TEST_SUC',
    color: '#1890ff'
  },
  {
    name: t('operationCenter-UbrGf8iQ79EL'),
    text: t('operationCenter-UbrGf8iQ79EL'),
    value: 'ENABLE',
    key: 'ENABLE',
    color: '#f5222d'
  },
  {
    name: t('operationCenter-vkfGxnyunsVD'),
    text: t('operationCenter-vkfGxnyunsVD'),
    value: 'STOPPED',
    key: 'STOPPED',
    color: '#faad14'
  }
];

// 审批状态
export const approveStatusList = [
  {
    name: t('operationCenter-b6TGv7R6QMlA'),
    text: t('operationCenter-b6TGv7R6QMlA'),
    value: 'PENDING',
    key: 'PENDING'
  },
  {
    name: t('operationCenter-uzQK3EeWWUse'),
    text: t('operationCenter-uzQK3EeWWUse'),
    value: 'PASS',
    key: 'PASS'
  },
  {
    name: t('operationCenter-X5OMcGM4KFFc'),
    text: t('operationCenter-X5OMcGM4KFFc'),
    value: 'REJECT',
    key: 'REJECT'
  },
  {
    name: t('operationCenter-UiY9oRI17wEv'),
    text: t('operationCenter-UiY9oRI17wEv'),
    value: 'RUNNING',
    key: 'RUNNING'
  },
  {
    name: t('operationCenter-AoOkJPCYPiCi'),
    text: t('operationCenter-AoOkJPCYPiCi'),
    value: 'BACKOUT',
    key: 'BACKOUT'
  },
  {
    name: t('operationCenter-ID54D8jc6UEm'),
    text: t('operationCenter-ID54D8jc6UEm'),
    value: 'CANCEL',
    key: 'CANCEL'
  }
];

export const lastCalcStatus = [
  {
    name: t('operationCenter-HNksP2SdfOoM'),
    text: t('operationCenter-HNksP2SdfOoM'),
    value: 'NOTRUN',
    key: 'NOTRUN',
    color: '#D9D9D9'
  },
  {
    name: t('operationCenter-WazQ2eRmTntF'),
    text: t('operationCenter-WazQ2eRmTntF'),
    value: 'RUNNING',
    key: 'RUNNING',
    color: '#1890FF'
  },
  {
    name: t('operationCenter-oqzenaNG2DeD'),
    text: t('operationCenter-oqzenaNG2DeD'),
    value: 'STOPPING',
    key: 'STOPPING',
    color: '#FFE58F'
  },
  {
    name: t('operationCenter-0FisWLjipmub'),
    text: t('operationCenter-0FisWLjipmub'),
    value: 'STOPPED',
    key: 'STOPPED',
    color: '#52C41A'
  },
  {
    name: t('operationCenter-xlikClRoEAvK'),
    text: t('operationCenter-xlikClRoEAvK'),
    value: 'TERMINATED',
    key: 'TERMINATED',
    color: '#FAAD14'
  },
  {
    name: t('operationCenter-6PoOxTnlXJZB'),
    text: t('operationCenter-6PoOxTnlXJZB'),
    value: 'FAIL',
    key: 'FAIL',
    color: '#FF4D4F'
  },
  {
    name: t('operationCenter-ObrQ3ZSVK3rN'),
    text: t('operationCenter-ObrQ3ZSVK3rN'),
    value: 'COMPLETE_FAIL',
    key: 'COMPLETE_FAIL',
    color: '#FF4D4F'
  },
  {
    name: t('operationCenter-fP20hjHwSu7H'),
    text: t('operationCenter-fP20hjHwSu7H'),
    value: 'RECALLING',
    key: 'RECALLING',
    color: '#FFADD2'
  },
  {
    name: t('operationCenter-uwck4sEBEbww'),
    text: t('operationCenter-uwck4sEBEbww'),
    value: 'RECALL_SUC',
    key: 'RECALL_SUC',
    color: '#F759AB'
  }
];

// 活动类型
export const typeList = [
  {
    name: t('operationCenter-FEC3gnFiQTck'),
    text: t('operationCenter-FEC3gnFiQTck'),
    value: 'CUSTOM',
    key: 'CUSTOM'
  },
  {
    name: t('operationCenter-klOrOFtWyd6n'),
    text: t('operationCenter-klOrOFtWyd6n'),
    value: 'TEMPLATE',
    key: 'TEMPLATE'
  }
];

export const queryConfig = [
  {
    name: t('operationCenter-YM8YGxy7zX2s'),
    propertyName: 'name',
    nodeType: 'input',
    operator: 'LIKE'
  },
  {
    name: t('operationCenter-IOGJslDz6P3w'),
    propertyName: 'campaignType',
    nodeType: 'select',
    options: typeList,
    operator: 'EQ'
  },
  {
    name: t('operationCenter-oL2uEkjObqec'),
    propertyName: 'phase',
    nodeType: 'select',
    options: phaseList,
    operator: 'EQ'
  },
  {
    name: t('operationCenter-g5Wt1ET6WzCd'),
    propertyName: 'phase',
    nodeType: 'select',
    options: phaseList,
    operator: 'EQ'
  },
  {
    name: t('operationCenter-hEABj1eu6rI9'),
    propertyName: 'campaignTarget',
    nodeType: 'input',
    operator: 'LIKE'
  },
  {
    name: t('operationCenter-qtHd5rczU4ou'),
    propertyName: 'beginTime',
    nodeType: 'dateRange',
    operator: 'DATE_BETWEEN'
  },
  {
    name: t('operationCenter-3SFaubxC8tkA'),
    propertyName: 'user',
    nodeType: 'select',
    options: [],
    operator: 'EQ',
    disabledNode: 'createUserId'
  },
  // {
  //   name: '执行规则',
  //   propertyName: 'calcRule',
  //   nodeType: 'select',
  //   options: ruleList,
  //   operator: 'EQ'
  // },
  {
    name: t('operationCenter-rRrkTkoENA1N'),
    propertyName: 'createUserId',
    nodeType: 'checkbox',
    operator: 'EQ',
    disabledNode: 'user'
  }
];

export const tableConfig = {
  srcPath: '',
  serviceName: campaignV2Service,
  handles: [
    [
      {
        name: t('operationCenter-vlPXIrZCzYj5'),
        content: '/aimarketer/home/<USER>/create?id=',
        type: 'DRAFT'
      },
      {
        name: t('operationCenter-pq9SLQPtquKU'),
        content: t('operationCenter-Ss7cS9NMcSyZ'),
        type: 'RUNNING'
      }
    ],
    [
      {
        name: t('operationCenter-Vu7jz3gRPFsw'),
        content: t('operationCenter-1SJguuc5a6yW'),
        type: 'DELETE'
      },
      {
        name: t('operationCenter-c6oUGMEobwXT'),
        content: '',
        type: 'COPY'
      }
    ]
  ],
  method: 'query'
};

// 历史记录
export const historyTableConfig = {
  srcPath: '',
  serviceName: campaignV2Service,
  handles: [
    [
      {
        name: t('operationCenter-qN4Uv5SMyd4x'),
        content: '/aimarketer/home/<USER>/detail/flows?id=',
        type: 'DETAIL'
      }
    ]
  ],
  method: 'listCalcLogs'
};

export const breadcrumbConfig = {
  level1Name: {
    CUSTOM: t('operationCenter-dEyHpLRb5cS6'),
    TEMPLATE: t('operationCenter-F2YVYplTxJmH')
  },
  link: {
    CUSTOM: '/aimarketer/home/<USER>',
    TEMPLATE: '/aimarketer/home/<USER>/template'
  },
  level2List: typeList
};

export const formConfig = [
  {
    name: t('operationCenter-YM8YGxy7zX2s'),
    propertyName: 'name',
    nodeType: 'Input',
    placeholder: t('operationCenter-R9Paetqg2KUl'),
    span: 12,
    rules: [
      {
        required: true,
        message: t('operationCenter-95IHrdFLopXm')
      },
      {
        max: 20,
        message: t('operationCenter-Hc1mXPb5QvEJ')
      },
      {
        pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
        message: t('operationCenter-zHxD11NIh0ZM')
      },
      {
        message: t('operationCenter-a08xXIj2zDVi')
      }
    ]
  },
  {
    name: t('operationCenter-hEABj1eu6rI9'),
    propertyName: 'campaignTarget',
    nodeType: 'Input',
    placeholder: t('operationCenter-A6FBz3NTN69X'),
    span: 12,
    rules: [
      {
        required: true,
        message: t('operationCenter-X8MhWV5G01MB')
      },
      {
        max: 60,
        message: t('operationCenter-9S2bwdaK8jcY')
      }
    ]
  },
  {
    name: t('operationCenter-bwEluTmqSyK7'),
    propertyName: 'time',
    nodeType: 'CustomRangePicker',
    span: 12,
    rules: [
      {
        required: true,
        message: t('operationCenter-XgzCBtucvsDk')
      }
    ],
    showTime: {
      format: 'HH:mm'
      // defaultValue: [dayjs(), dayjs().add(5, 'minutes')]
    }
    // disabledDate: function disabledDate(current) {
    //   return current && current < dayjs().startOf('day');
    // },
    // disabledTime: function disabledTime(date, type) {
    //   if (type === 'start') {
    //     return {
    //       disabledHours: () => {
    //         if (dayjs.isdayjs(date)) {
    //           return [];
    //         } else if (date[0].format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD')) {
    //           return range(0, 24).splice(0, dayjs().hour());
    //         }
    //       },
    //       disabledMinutes: () => {
    //         if (dayjs.isdayjs(date)) {
    //           return [];
    //         } else if (date[0].format('YYYY-MM-DD HH') === dayjs().format('YYYY-MM-DD HH')) {
    //           return range(0, 60).splice(0, dayjs().minute());
    //         }
    //       }
    //     };
    //   }
    //   return {
    //     disabledHours: () => {
    //       return [];
    //       // // console.log('date', date);
    //       // if (dayjs.isdayjs(date) || _.isEmpty(date)) {
    //       //   return [];
    //       // } else if (date[0].format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD')) {
    //       // //   if (dayjs().diff(date[0], 'minute') >= 5) {
    //       // //   //   console.log('date', date);
    //       // //   //   return range(0, 24).splice(0, dayjs().hour());
    //       // //   } else {
    //       // //   //   range(0, 24).splice(0, dayjs().hour() + 1);
    //       // //   }
    //       // // } else {

    //       //   // }
    //       //   if (dayjs().endOf('hour').diff(date[0], 'minute') >= 5) {
    //       //     return [];
    //       //   } else {
    //       //     return range(0, 24).splice(0, dayjs().hour() + 1);
    //       //   }
    //       // }
    //     },
    //     disabledMinutes: () => {
    //       return [];
    //       // if (dayjs.isdayjs(date) || _.isEmpty(date)) {
    //       //   return [];
    //       // } else {
    //       //   let targetMinute = date[0].minute() + 5;
    //       //   targetMinute = targetMinute >= 59 ? targetMinute - 59 : targetMinute;
    //       //   return range(0, 60).splice(0, targetMinute);
    //       // }
    //     }
    //   };
    // }
  },
  {
    name: t('operationCenter-tvci7KA7bSso'),
    propertyName: 'remark',
    nodeType: 'TextArea',
    placeholder: t('operationCenter-uCEcRBP0cMOA'),
    span: 24,
    rules: [
      {
        required: false
      },
      {
        max: 150,
        message: t('operationCenter-K9TCWlEKoYA7')
      }
    ]
    // }, {
    //   name: '活动类型',
    //   propertyName: 'campaignType',
    //   nodeType: 'Select',
    //   placeholder: '请选择活动类型',
    //   span: 12,
    //   options: typeList,
    //   rules: [{
    //     required: true,
    //     message: '请选择活动类型!'
    //   }]
  }
];

export const elements = {
  id: {
    type: 'select',
    label: 'ID',
    width: 12,
    operator: 'IN',
    componentOptions: {
      mode: 'tags',
      // autoClearSearchValue: 'tags',
      allowClear: true,
      placeholder: t('operationCenter-lMTJevDOvP6t')
    }
  },
  name: {
    type: 'input',
    label: t('operationCenter-B0MIaOtYT5wf'),
    width: 12,
    operator: 'LIKE',
    componentOptions: {
      allowClear: true,
      placeholder: t('operationCenter-C7M2eVz4436w')
    }
  },
  campaignType: {
    type: 'select',
    label: t('operationCenter-IOGJslDz6P3w'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true,
      showSearch: true,
      options: [
        {
          name: t('operationCenter-FEC3gnFiQTck'),
          text: t('operationCenter-FEC3gnFiQTck'),
          value: 'CUSTOM',
          key: 'CUSTOM'
        },
        {
          name: t('operationCenter-F2YVYplTxJmH'),
          text: t('operationCenter-F2YVYplTxJmH'),
          value: 'TEMPLATE',
          key: 'TEMPLATE'
        }
      ],
      filterOption: (input, option) => {
        return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      placeholder: t('operationCenter-lMTJevDOvP6t')
    }
  },
  phase: {
    type: 'select',
    label: t('operationCenter-oL2uEkjObqec'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true,
      showSearch: true,
      options: phaseList,
      filterOption: (input, option) => {
        return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      placeholder: t('operationCenter-lMTJevDOvP6t')
    }
  },
  approvalStatus: {
    type: 'select',
    label: t('operationCenter-g5Wt1ET6WzCd'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true,
      showSearch: true,
      options: approveStatusList,
      filterOption: (input, option) => {
        return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      placeholder: t('operationCenter-lMTJevDOvP6t')
    }
  },
  // lastCalcStatus: {
  //   type: 'select',
  //   label: '最后批次运行状态',
  //   width: 12,
  //   operator: 'EQ',
  //   componentOptions: {
  //     allowClear: true,
  //     showSearch: true,
  //     options: lastCalcStatus,
  //     filterOption: (input, option) => {
  //       return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  //     },
  //     placeholder: '请选择最后批次运行状态'
  //   }
  // },
  campaignTarget: {
    type: 'input',
    label: t('operationCenter-hEABj1eu6rI9'),
    width: 12,
    operator: 'LIKE',
    componentOptions: {
      allowClear: true,
      placeholder: t('operationCenter-C7M2eVz4436w')
    }
  },
  // 'scenario.id': {
  //   type: 'select',
  //   label: 'ID类型',
  //   width: 12,
  //   operator: 'EQ',
  //   componentOptions: {
  //     allowClear: true,
  //     showSearch: true,
  //     filterOption: (input, option) => {
  //       return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  //     },
  //     placeholder: '请选择ID类型'
  //   }
  // },
  'scenario.code': {
    type: 'select',
    label: t('operationCenter-M3GUYWcHt703'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true,
      showSearch: true,
      filterOption: (input, option) => {
        return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      placeholder: t('operationCenter-lMTJevDOvP6t')
    }
  },
  beginTime: {
    type: 'dateRange',
    label: t('operationCenter-DDacZCwAhwmB'),
    width: 12,
    operator: 'DATE_BETWEEN',
    componentOptions: {
      allowClear: true
    }
  },
  // createUserId: {
  //   type: 'select',
  //   label: '创建账号',
  //   width: 12,
  //   operator: 'EQ',
  //   componentOptions: {
  //     allowClear: true,
  //     showSearch: true,
  //     options: typeList,
  //     filterOption: (input, option) => {
  //       return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  //     },
  //     placeholder: '请选择创建账号'
  //   }
  // },
  'createUser.id': {
    type: 'select',
    label: t('operationCenter-0Y2qZNzHxnWj'),
    width: 12,
    operator: 'IN',
    componentOptions: {
      allowClear: true,
      showSearch: true,
      mode: 'multiple',
      options: typeList,
      // filterOption: (input, option) => {
      //   return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      // },
      filterOption: false,
      placeholder: t('operationCenter-lMTJevDOvP6t')
    }
  },
  lastEnableUser: {
    type: 'select',
    label: t('operationCenter-I1zpxZOIYlWp'),
    width: 12,
    operator: 'IN',
    componentOptions: {
      allowClear: true,
      showSearch: true,
      mode: 'multiple',
      options: typeList,
      // filterOption: (input, option) => {
      //   return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      // },
      filterOption: false,
      placeholder: t('operationCenter-lMTJevDOvP6t')
    }
  },
  calcRule: {
    type: 'select',
    label: t('operationCenter-PG7tqyTZk4oR'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true,
      showSearch: true,
      options: ruleList,
      filterOption: (input, option) => {
        return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      placeholder: t('operationCenter-lMTJevDOvP6t')
    }
  },
  flows: {
    type: 'input',
    label: t('operationCenter-V3j4yK3YcHeN'),
    width: 12,
    operator: 'LIKE',
    componentOptions: {
      allowClear: true,
      placeholder: t('operationCenter-C7M2eVz4436w')
    }
  },
  passedCount: {
    type: 'moreScreen',
    label: t('operationCenter-EOLMkV5hd9l2'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true
    }
  },
  joinCount: {
    type: 'moreScreen',
    label: t('operationCenter-PUDMoFD9vyOu'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true
    }
  },
  favoriteCount: {
    type: 'moreScreen',
    label: t('operationCenter-egVl6W5YNPTb'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true
    }
  },
  copyCount: {
    type: 'moreScreen',
    label: t('operationCenter-AxjdNmKN2aRp'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true
    }
  }
};

export const elementsWithoutApprove = {
  id: {
    type: 'select',
    label: 'ID',
    width: 12,
    operator: 'IN',
    componentOptions: {
      mode: 'tags',
      // autoClearSearchValue: 'tags',
      allowClear: true,
      placeholder: t('operationCenter-lMTJevDOvP6t')
    }
  },
  name: {
    type: 'input',
    label: t('operationCenter-YM8YGxy7zX2s'),
    width: 12,
    operator: 'LIKE',
    componentOptions: {
      allowClear: true,
      placeholder: t('operationCenter-C7M2eVz4436w')
    }
  },
  campaignType: {
    type: 'select',
    label: t('operationCenter-IOGJslDz6P3w'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true,
      showSearch: true,
      options: [
        {
          name: t('operationCenter-FEC3gnFiQTck'),
          text: t('operationCenter-FEC3gnFiQTck'),
          value: 'CUSTOM',
          key: 'CUSTOM'
        },
        {
          name: t('operationCenter-F2YVYplTxJmH'),
          text: t('operationCenter-F2YVYplTxJmH'),
          value: 'TEMPLATE',
          key: 'TEMPLATE'
        }
      ],
      filterOption: (input, option) => {
        return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      placeholder: t('operationCenter-lMTJevDOvP6t')
    }
  },
  phase: {
    type: 'select',
    label: t('operationCenter-oL2uEkjObqec'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true,
      showSearch: true,
      options: phaseList,
      filterOption: (input, option) => {
        return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      placeholder: t('operationCenter-lMTJevDOvP6t')
    }
  },
  // campaignTarget: {
  //   type: 'input',
  //   label: '流程目标',
  //   width: 12,
  //   operator: 'LIKE',
  //   componentOptions: {
  //     allowClear: true,
  //     placeholder: '请输入'
  //   }
  // },
  'scenario.code': {
    type: 'select',
    label: t('operationCenter-M3GUYWcHt703'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true,
      showSearch: true,
      filterOption: (input, option) => {
        return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      placeholder: t('operationCenter-lMTJevDOvP6t')
    }
  },
  beginTime: {
    type: 'dateRange',
    label: t('operationCenter-DDacZCwAhwmB'),
    width: 12,
    operator: 'DATE_BETWEEN',
    componentOptions: {
      allowClear: true
    }
  },
  'createUser.id': {
    type: 'select',
    label: t('operationCenter-0Y2qZNzHxnWj'),
    width: 12,
    operator: 'IN',
    componentOptions: {
      allowClear: true,
      showSearch: true,
      mode: 'multiple',
      options: typeList,
      filterOption: false,
      placeholder: t('operationCenter-lMTJevDOvP6t')
    }
  },
  lastEnableUser: {
    type: 'select',
    label: t('operationCenter-I1zpxZOIYlWp'),
    width: 12,
    operator: 'IN',
    componentOptions: {
      allowClear: true,
      showSearch: true,
      mode: 'multiple',
      options: typeList,
      filterOption: false,
      placeholder: t('operationCenter-lMTJevDOvP6t')
    }
  },
  calcRule: {
    type: 'select',
    label: t('operationCenter-PG7tqyTZk4oR'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true,
      showSearch: true,
      options: ruleList,
      filterOption: (input, option) => {
        return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      placeholder: t('operationCenter-lMTJevDOvP6t')
    }
  },
  flows: {
    type: 'input',
    label: t('operationCenter-V3j4yK3YcHeN'),
    width: 12,
    operator: 'LIKE',
    componentOptions: {
      allowClear: true,
      placeholder: t('operationCenter-C7M2eVz4436w')
    }
  },
  passedCount: {
    type: 'moreScreen',
    label: t('operationCenter-EOLMkV5hd9l2'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true
    }
  },
  joinCount: {
    type: 'moreScreen',
    label: t('operationCenter-PUDMoFD9vyOu'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true
    }
  },
  favoriteCount: {
    type: 'moreScreen',
    label: t('operationCenter-egVl6W5YNPTb'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true
    }
  },
  copyCount: {
    type: 'moreScreen',
    label: t('operationCenter-AxjdNmKN2aRp'),
    width: 12,
    operator: 'EQ',
    componentOptions: {
      allowClear: true
    }
  }
};
