import { Button, Modal, Space, Table, Tooltip, message } from 'antd';
import TableSearch from 'components/bussinesscoms/tableSearch/index';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import UserService from 'service/UserService';
import MyToDoListService from 'service/myToDoListService';
import { elements, initHistoryParam, initParam } from '../config';

import myToDoListService from '../../../../service/myToDoListService';
import '../index.scss';

const userService = new UserService();

const pagination = {
  showTotal: (totals) => `共 ${totals} 条`,
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
};

const hisToryPagination = {
  showTotal: (totals) => `共 ${totals} 条`,
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
};

const statusList = {
  RUNNING: '审批中',
  PASS: '审批通过',
  REJECT: '驳回',
  BACKOUT: '已撤销',
  CANCEL: '已取消'
};

const detailUrlList = {
  CAMPAIGN: '/aimarketer/home/<USER>',
  SEGMENT: '/aimarketer/home/<USER>/userGroup'
};

export default function MyToDoListStart({ props, tabKey, dictTypeList, userList, reflash, setReflash }) {
  const {
    location: { state }
  } = props;

  const [dataSource, setDataSource] = useState([]);
  const [historyTableSource, setHistoryTableSource] = useState([]);
  const [userId, setUserId] = useState(undefined);
  const [loading, setLoading] = useState(false);
  const [param, setParam] = useState(_.cloneDeep(state?.paramStart || initParam));
  const [historyParam, setHistoryParam] = useState(initHistoryParam);

  const [logStatus, setLogStatus] = useState(false);
  const [historyLoading, setHistoryLoading] = useState(false);

  const renderOptions = (record) => (
    <Space size={16}>
      <Button
        onClick={() => onColumnActionClick('back', record)}
        type="link"
        style={{ padding: 0 }}
        disabled={record.status !== 'RUNNING' || record.type === 'LABEL'}
      >
        撤回
      </Button>
      <Button onClick={() => onColumnActionClick('log', record)} type="link" style={{ padding: 0 }}>
        审批详情
      </Button>
    </Space>
  );
  const columns = [
    {
      title: '审批提交编号',
      key: 'approvalNo',
      dataIndex: 'approvalNo',
      sorter: true,
      width: 200
    },
    {
      title: '审批事项名称',
      key: 'contentName',
      width: 200,
      dataIndex: 'contentName',
      ellipsis: true,
      render: (text, record) => (
        <Tooltip title={text}>
          <a onClick={() => onColumnActionClick('detail', record)}>{text}</a>
        </Tooltip>
      )
    },
    {
      title: '类型',
      key: 'contentType',
      width: 150,
      dataIndex: 'contentType',
      render: (text) => <span>{dictTypeList.find((item) => item.value === text)?.label}</span>
    },
    {
      title: '提交人',
      key: 'promoterName',
      width: 150,
      dataIndex: 'promoterName'
    },
    {
      title: '申请时间',
      width: 200,
      dataIndex: 'createTime',
      sorter: true,
      key: 'createTime',
      render: (text, record) => (record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : '-')
      //   render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '审批状态',
      width: 100,
      key: 'status',
      render: (text) => <span>{statusList[text.status]}</span>
    },
    // {
    //   title: '审批意见',
    //   key: 'opinion',
    //   width: 200,
    //   render: (record) => (
    //     <Tooltip title={record.opinion ? record.opinion : ''}>
    //       <span className="optionWrap">{record.opinion ? record.opinion : '-'}</span>
    //     </Tooltip>
    //   )
    // },
    {
      title: '审批人',
      key: 'approverName',
      width: 100,
      render: (record) => <span>{record.approverName ? record.approverName : '-'}</span>
    },
    {
      title: '审批时间',
      width: 200,
      dataIndex: 'approvalTime',
      sorter: true,
      key: 'approvalTime',
      render: (text, record) => (record.approvalTime ? dayjs(record.approvalTime).format('YYYY-MM-DD HH:mm:ss') : '-')
      //   render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      width: 150,
      fixed: 'right',
      render: (text, record) => <Space>{renderOptions(record)}</Space>
    }
  ];

  const columnsHistory = [
    {
      title: '提交人',
      key: 'promoterName',
      width: 150,
      dataIndex: 'promoterName',
      render: (text, record) => (
        <div>{(record.createTime && record.status === 'RUNNING') || record.status === 'BACKOUT' ? text : '-'}</div>
      )
    },
    {
      title: '申请时间',
      width: 200,
      dataIndex: 'createTime',
      sorter: true,
      key: 'createTime',
      render: (text, record) =>
        (record.createTime && record.status === 'RUNNING') || record.status === 'BACKOUT'
          ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss')
          : '-'
    },
    {
      title: '审批状态',
      width: 100,
      key: 'status',
      render: (text) => <span>{statusList[text.status]}</span>
    },
    {
      title: '审批人',
      key: 'approverName',
      width: 100,
      render: (record) => <span>{record.approverName ? record.approverName : '-'}</span>
    },
    {
      title: '审批意见',
      key: 'opinion',
      width: 200,
      render: (record) => (
        <Tooltip title={record.opinion ? record.opinion : ''}>
          <span className="optionWrap">{record.opinion ? record.opinion : '-'}</span>
        </Tooltip>
      )
    },
    {
      title: '审批时间',
      width: 200,
      dataIndex: 'approvalTime',
      sorter: true,
      key: 'approvalTime',
      render: (text, record) => (record.approvalTime ? dayjs(record.approvalTime).format('YYYY-MM-DD HH:mm:ss') : '-')
    }
  ];

  useEffect(() => {
    const init = async () => {
      const { id } = await userService.getCurrentUser();
      setUserId(id);
    };
    init();
  }, []);

  const handleTableChange = (lastpagination, filtersArg, sorter) => {
    param.page = lastpagination.current;
    param.size = lastpagination.pageSize;
    if (sorter.field) {
      param.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setParam({ ...param });
  };

  const handleHistoryTableChange = (lastpagination, filtersArg, sorter) => {
    historyParam.page = lastpagination.current;
    historyParam.size = lastpagination.pageSize;
    if (sorter.field) {
      historyParam.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setHistoryParam({ ...historyParam });
  };

  useEffect(() => {
    const getTableData = async () => {
      setLoading(true);
      if (userId) {
        const finalParam = _.cloneDeep(param);
        finalParam.search = [
          ...finalParam.search,
          {
            operator: 'EQ',
            propertyName: 'projectId',
            value: localStorage.getItem('projectId')
          },
          { operator: 'EQ', propertyName: 'promoterId', value: userId },
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: window.getDeptId()
          },
          {
            operator: 'EQ',
            propertyName: 'approvalType',
            value: 'ACTIVITI'
          }
          // { operator: 'EQ', propertyName: 'createUserId', value: userId }
        ];

        const result = await MyToDoListService.query2(finalParam);
        pagination.total = result.totalElements;
        pagination.current = param.page;
        pagination.pageSize = param.size;
        props.history.replace({
          state: { ...state, paramStart: param, tabKey }
        });

        setReflash(!reflash);
        setDataSource(result.content);
        setLoading(false);
      }
    };
    tabKey === '1' && getTableData();
  }, [param, tabKey, userId]);

  useEffect(() => {
    const getHistoryTableData = async () => {
      setHistoryLoading(true);
      const finalParam = _.cloneDeep(historyParam);
      const result = await MyToDoListService.processHistory(finalParam);
      hisToryPagination.total = result.totalElements;
      hisToryPagination.current = historyParam.page;
      hisToryPagination.pageSize = historyParam.size;
      setHistoryTableSource(result.content.filter((item) => item.status !== 'PENDING'));
      setLoading(false);
      setHistoryLoading(false);
    };
    getHistoryTableData();
  }, [historyParam]);

  const hideLogModal = () => {
    setLogStatus(false);
  };

  const onColumnActionClick = async (key, record) => {
    const {
      approvalNo,
      contentType,
      contentUrl,
      contentId,
      contentName,
      mark,
      status,
      createUserId,
      promoterId,
      taskId,
      createTime
    } = record;
    if (key === 'back') {
      Modal.confirm({
        title: '撤回',
        className: 'backWrap',
        content: (
          <p className="backDesc">
            您将撤回审批流程：{contentName}
            ，撤销后，您可再次编辑后重新提交审批。
          </p>
        ),
        okText: '确认撤回',
        okType: 'primary',
        cancelText: '取消',
        async onOk() {
          await myToDoListService.saveProcessLabelInstance({
            approvalNo,
            contentType,
            approverId: userId,
            status: 'BACKOUT',
            taskId,
            projectId: localStorage.getItem('projectId')
          });
          message.success('撤回成功');
          setParam({ ...param });
        },
        onCancel() {}
      });
    } else if (key === 'detail') {
      if (contentType === 'MARKET_WORKS') {
        const result = await myToDoListService.getAuthorization({
          loginId: userId,
          projectId: localStorage.getItem('projectId')
        });

        const newUrl =
          contentUrl.indexOf('?') >= 0
            ? `${contentUrl}&Authorization=${result}`
            : `${contentUrl}?Authorization=${result}`;
        window.open(newUrl, '_blank');
      } else if (mark === 'INSIDE') {
        props.history.push(
          `${detailUrlList[contentType]}/detail?id=${contentId}&definition=${true}&status=${true}&promoterId=${promoterId}&approvalType=ACTIVITI`
        );
      } else {
        const newContentUrl = contentUrl.includes('?') ? `${contentUrl}&isTag=true` : `${contentUrl}?isTag=true`;
        const pathName = `/aimarketer/home/<USER>
        props.history.push(pathName);
      }
    } else if (key === 'log') {
      // showLogModal(id);
      props.history.push(`/aimarketer/home/<USER>
        contentType,
        approvalNo,
        contentName,
        contentUrl,
        approvalStatus: status,
        createUserId,
        contentId,
        mark,
        createTime,
        approvalType: 'ACTIVITI'
      });
    }
  };

  return (
    <div className="todoList">
      <div className="search">
        <TableSearch
          elements={elements(1, userList, dictTypeList)}
          onChange={(data) => setParam({ ...param, ...data })}
          span={8}
          initialValues={state?.paramStart?.search}
        />
      </div>
      <div className="content">
        <div className="tableWrap">
          <Table
            dataSource={dataSource}
            columns={columns}
            loading={loading}
            onChange={handleTableChange}
            rowKey="id"
            pagination={pagination}
            scroll={{ x: 1300 }}
          />
        </div>

        <Modal
          title="审批历史"
          className="processHistoryModalWrap"
          open={logStatus}
          destroyOnClose
          // okText="确认"
          // onOk={hideLogModal}
          confirmLoading={historyLoading}
          onCancel={hideLogModal}
          footer={null}
        >
          <div>
            <Table
              dataSource={historyTableSource}
              pagination={hisToryPagination}
              columns={columnsHistory}
              loading={historyLoading}
              onChange={handleHistoryTableChange}
              scroll={{ x: 400 }}
              rowKey="id"
            />
          </div>
        </Modal>
      </div>
    </div>
  );
}
