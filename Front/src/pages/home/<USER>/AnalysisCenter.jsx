import React, { Component } from 'react';
import Loadable from 'react-loadable';
import { Redirect, Route, Switch } from 'react-router-dom';

// import NotFind from 'components/notFind/NotFind';
// import DatabaseCreate from './database/create/index';
// import DatabaseList from './database/list/index';
// import DataDashboardList from './dataDashboard/list/index';
// import ChartDetail from './database/detail/index';
// import DashboardFullscreen from './dataDashboard/fullscreen/index';
// import IndexManagement from './indexManagement/list/indexManagement';
// import IndexManagementDetail from './indexManagement/detail/index';
// import FunnelAnalysis from './funnelAnalysis/index';
// import RetentionAnalysis from './retentionAnalysis/index';
// import EventAnalysis from './eventAnalysis/index';

const NotFind = Loadable({
  loader: () => import('components/notFind/NotFind'),
  loading: () => null,
  delay: 200
});

const DatabaseEdit = Loadable({
  loader: () => import('./database/create/index'),
  loading: () => null,
  delay: 200
});
const DatabaseList = Loadable({
  loader: () => import('./database/list/index'),
  loading: () => null,
  delay: 200
});
const DataDashboardList = Loadable({
  loader: () => import('./dataDashboard/list/index'),
  loading: () => null,
  delay: 200
});
const ChartDetail = Loadable({
  loader: () => import('./database/detail/index'),
  loading: () => null,
  delay: 200
});
const DashboardFullscreen = Loadable({
  loader: () => import('./dataDashboard/fullscreen/index'),
  loading: () => null,
  delay: 200
});
const IndexManagement = Loadable({
  loader: () => import('./indexManagement/list/indexManagement'),
  loading: () => null,
  delay: 200
});
const IndexManagementDetail = Loadable({
  loader: () => import('./indexManagement/detail/index'),
  loading: () => null,
  delay: 200
});
const FunnelAnalysis = Loadable({
  loader: () => import('./funnelAnalysis/index'),
  loading: () => null,
  delay: 200
});
const RetentionAnalysis = Loadable({
  loader: () => import('./retentionAnalysis/index'),
  loading: () => null,
  delay: 200
});
const EventAnalysis = Loadable({
  loader: () => import('./eventAnalysis/index'),
  loading: () => null,
  delay: 200
});
class AnalysisCenter extends Component {
  render() {
    return (
      <Switch>
        <Route exact path="/aimarketer/home/<USER>/analysisCenter/dataDashboard" component={DataDashboardList} />
        <Route
          exact
          path="/aimarketer/home/<USER>/analysisCenter/dataDashboard/:id"
          component={DataDashboardList}
        />
        <Route
          exact
          path="/aimarketer/home/<USER>/analysisCenter/dataDashboard/fullscreen/:id"
          component={DashboardFullscreen}
        />
        <Route exact path="/aimarketer/home/<USER>/analysisCenter/database" component={DatabaseList} />
        <Route exact path="/aimarketer/home/<USER>/analysisCenter/database/detail/:id" component={ChartDetail} />
        <Route exact path="/aimarketer/home/<USER>/analysisCenter/database/edit/:id" component={DatabaseEdit} />
        <Route exact path="/aimarketer/home/<USER>/analysisCenter/database/create" component={DatabaseEdit} />

        <Route exact path="/aimarketer/home/<USER>/dataCenter/indexManagement" component={IndexManagement} />
        <Route
          exact
          path="/aimarketer/home/<USER>/dataCenter/indexManagement/detail/:id"
          component={IndexManagementDetail}
        />
        <Route
          exact
          path="/aimarketer/home/<USER>/analysisCenter/database/funnelAnalysis"
          component={FunnelAnalysis}
        />
        <Route
          exact
          path="/aimarketer/home/<USER>/analysisCenter/database/funnelAnalysis/:id"
          component={FunnelAnalysis}
        />
        <Route
          exact
          path="/aimarketer/home/<USER>/analysisCenter/database/retentionAnalysis"
          component={RetentionAnalysis}
        />
        <Route
          exact
          path="/aimarketer/home/<USER>/analysisCenter/database/retentionAnalysis/:id"
          component={RetentionAnalysis}
        />
        <Route
          exact
          path="/aimarketer/home/<USER>/analysisCenter/database/eventAnalysis"
          component={EventAnalysis}
        />
        <Route
          exact
          path="/aimarketer/home/<USER>/analysisCenter/database/eventAnalysis/:id"
          component={EventAnalysis}
        />
        <Redirect
          exact
          from="/aimarketer/home/<USER>/analysisCenter"
          to="/aimarketer/home/<USER>/analysisCenter/database"
        />
        <Route component={NotFind} />
      </Switch>
    );
  }
}

export default AnalysisCenter;
