import React, { Component } from 'react';
import { Button, Table, Drawer, message, Modal, Alert } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import PermissionService from 'service/permissionService';
import CheckAuth from 'utils/checkAuth';
import { t } from 'utils/translation';
import TransferSuperAdmin from '../components/transferSuperAdmin';
import { getPermissionListColumns } from '../config';
import './index.scss';

const service = new PermissionService();

const { confirm } = Modal;
class PermissionList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      alertVisible: false,
      tableData: [],
      isSuperAdmin: false,
      transferSuperAdminVisible: false // 转让超级管理员弹窗
    };
  }

  componentDidMount() {
    this.getPermissionData();
    this.checkIsSuperAdmin();
  }

  checkIsSuperAdmin = async () => {
    try {
      const response = await service.reqIsSuperAdmin();
      this.setState({
        isSuperAdmin: response || false
      });
    } catch (error) {
      this.setState({ loading: false });
    }
  };

  getPermissionData = async () => {
    this.setState({ loading: true });
    try {
      const response = await service.reqGetTableData();
      this.setState({ loading: false });
      this.setState({ tableData: response, loading: false });
    } catch (error) {
      this.setState({ loading: false });
    }
  };

  // 新建权限
  handleAddClick = () => {
    this.props.history.push('/aimarketer/home/<USER>/permission/add');
  };

  // 权限人员
  handlePersionClick = (record) => {
    this.props.history.push(`/aimarketer/home/<USER>/permission/user/${record.id}`);
  };

  // 权限详情
  handleDetails = (record) => {
    this.props.history.push(`/aimarketer/home/<USER>/permission/details/${record.id}`);
  };

  // 转让权限开始
  handleTransferSuperAdminClick = () => {
    this.setState({
      transferSuperAdminVisible: true
    });
  };

  // 转让取消
  handleTransferSuperAdminCancel = () => {
    this.setState({
      transferSuperAdminVisible: false
    });
  };

  // 转让完成
  handleTransferSuperAdminOk = async (data) => {
    const params = {
      roleId: data.roleId,
      userIds: [data.userId]
    };
    try {
      await service.reqTransferSuperAdmin(params);
      message.success(t('setting-9JJatwo9n9Zx'));
      this.setState({
        isSuperAdmin: false,
        transferSuperAdminVisible: false
      });
      this.getPermissionData();
    } catch (error) {
      // error
    }
  };

  handleAlertClose = () => {
    this.setState({ alertVisible: false });
  };

  // 删除权限 需要当前权限下人员为 0
  handleDeletePermissionClick = async (record) => {
    const that = this;
    confirm({
      title: t('setting-82ut8dY8DqTS'),
      icon: <ExclamationCircleOutlined />,
      content: t('setting-ytEgdKQpbb6d'),
      okText: t('setting-Z2kJOouTsL16'),
      okType: 'primary',
      async onOk() {
        const { memberCount, id } = record;
        if (!id) return false;
        if (memberCount > 0) {
          return that.setState({ alertVisible: true }, () => {
            message.error(t('setting-odZyNU13cMdK'));
            setTimeout(() => {
              that.handleAlertClose();
            }, 1000);
          });
        }
        try {
          await service.reqDelPermissionById(id);
          that.getPermissionData();
          message.success(t('setting-GLvdHzEgtEaE'));
        } catch (error) {
          console.error(error);
        }
      },
      onCancel() {
        window.console.log('Cancel');
      }
    });
  };

  render() {
    const { tableData, loading, isSuperAdmin, transferSuperAdminVisible, alertVisible } = this.state;
    return (
      <div className="permissionList">
        <header>
          <h1>{t('setting-xUVjsbpZgeNd')}</h1>
          <div className="btnGroup">
            {isSuperAdmin ? (
              <Button onClick={this.handleTransferSuperAdminClick}>{t('setting-41PVRmlLfIYr')}</Button>
            ) : null}
            <CheckAuth code="aim_role_manage_edit">
              <Button type="primary" onClick={this.handleAddClick}>
                {t('setting-gcF00RYuFQdn')}
              </Button>
            </CheckAuth>
          </div>
        </header>
        <div className="table1">
          {alertVisible ? (
            <Alert
              style={{ marginBottom: 10 }}
              message={t('setting-7pUdgtaUv6HH')}
              type="error"
              showIcon
              closable
              onClose={this.handleAlertClose}
            />
          ) : null}
          <Table
            rowKey="id"
            columns={getPermissionListColumns(this)}
            dataSource={tableData}
            bordered={false}
            loading={loading}
            handlePersionClick={this.handlePersionClick}
            handleDetails={this.handleDetails}
            pagination={false}
          />
        </div>
        <Drawer
          title={t('setting-cL9yGx7ZsP1M')}
          width={600}
          open={transferSuperAdminVisible}
          destroyOnClose
          maskClosable={false}
          onClose={this.handleTransferSuperAdminCancel}
        >
          <TransferSuperAdmin
            handleCancel={this.handleTransferSuperAdminCancel}
            handleOk={this.handleTransferSuperAdminOk}
          />
        </Drawer>
      </div>
    );
  }
}

export default PermissionList;
