// 权限管理模块翻译文件
// Files: list/index.jsx, add/index.jsx, details/index.jsx, user/index.jsx, config.js, components/*

export default {
  cn: {
    // list/index.jsx - 权限列表页面
    'setting-9JJatwo9n9Zx': '转让完成',
    'setting-82ut8dY8DqTS': '删除',
    'setting-ytEgdKQpbb6d': '删除后无法恢复，确认删除？',
    'setting-Z2kJOouTsL16': '确定删除',
    'setting-odZyNU13cMdK': '当前权限有成员列表 无法删除',
    'setting-GLvdHzEgtEaE': '删除成功',
    'setting-xUVjsbpZgeNd': '权限管理',
    'setting-41PVRmlLfIYr': '转让超级管理员',
    'setting-gcF00RYuFQdn': '新增权限',
    'setting-7pUdgtaUv6HH': '若要删除角色权限，成员数必须为0。请先转移该角色下所有成员',
    'setting-cL9yGx7ZsP1M': '转让超级管理员权限',

    // add/index.jsx - 新增权限页面
    'setting-ZwTmfS0tC9N3': '权限新建成功',
    'setting-vftC42fNjMvD': '创建权限角色',
    'setting-SvRbk7Bv0sB4': '退出不会保留该权限{{step}}的内容，确认退出？',
    'setting-4MqFIwel1KQT': '确认退出',
    'setting-B49k1OaCx5Cr': '取消',
    'setting-G7Db8ld1hYGq': '退出创建',
    'setting-AaEa2EAfF2QM': '上一步',
    'setting-6zJatGVgz7B4': '下一步',
    'setting-iO6KZdvqLlt3': '完成',

    // details/index.jsx - 权限详情页面
    'setting-PyEOF3ISwlk3': '权限详情',
    'setting-2CWxnrCc09ku': '数据权限针对已经添加的字段或者值进行限制',
    'setting-BTDojge8VuCz': '取消',
    'setting-9hUqAs1QhqEd': '保存',
    'setting-6hjF3likSXNH': '更改',
    'setting-mIJjAdQz7pwM': '保存成功',

    // user/index.jsx - 权限用户页面
    'setting-YVDtvhrpmDXX': '成员列表',
    'setting-SlX2c9wx6asO': '成员数量',
    'setting-sksPHrxqwhvu': '创建人',
    'setting-ZW74Folnzdyz': '创建时间',
    'setting-xVy9vLpjLesU': '类型',
    'setting-CzLbrMnKsz4y': '自定义',
    'setting-aZArbb3hg6n7': '预设',
    'setting-BJD27TIijqte': '备注',
    'setting-LEMJAdfVbWBW': '权限详情',
    'setting-Fkl9wXHcux4q': '数据存在错误',
    'setting-mniJ84a7LqPn': '搜索企业邮箱',
    'setting-x5K4hKVPHnyT': '批量转移成员',
    'setting-xhIK8kquzU6m': '移入成员',
    'setting-ObYqIOBAxDXx': '您还没有选择成员',
    'setting-fPIy7bOu1FbT': '暂无数据',
    'setting-EA70ExiQG5jw': '{{transferAll}}转移成员',

    // config.js - 配置文件 (表格列)
    'setting-tc4ZIEsBmGCC': '名称',
    'setting-RNBnD8YIgxFp': '成员数量',
    'setting-cFgmZpFXPvRG': '类型',
    'setting-nOPac3SlkV2Z': '创建时间',
    'setting-TTz6rxUIwN4T': '创建账号',
    'setting-R8WDPnqx0y19': '操作',
    'setting-BaYuMMGVPszX': '成员列表',
    'setting-7fRKQWl0xiBA': '权限详情',
    'setting-Y9gLF8n26OE1': '更多',
    'setting-2I6vpRTNIgP3': '删除',

    // config.js - 用户表格列
    'setting-kfl4D1URd9Of': '姓名',
    'setting-Lxbbb02vpz6O': '员工号',
    'setting-snu3uYWdZ3Jm': '企业邮箱',
    'setting-dOFJBEYRTztL': '手机号',
    'setting-yYvMi9ys4FnR': '最近登录时间',
    'setting-lf26vCRjmEY9': '账号状态',
    'setting-EbAIaVuSK9NE': '转移该成员',

    // components/baseMsg.jsx - 基本信息组件
    'setting-gVivCWiwR8oa': '角色名称',
    'setting-ip8uIsCN9zBG': '请输入角色名称',
    'setting-21cnGR12dXmi': '最大长度限制为16位字符',
    'setting-3X3NDD65GKkG': '该角色名称已存在',
    'setting-MTqXnbUtPBPq': '必填，名称不可重复',
    'setting-PdqmkVEcbpQu': '角色描述',
    'setting-4WRD8wNqd8ij': '角色描述过长',
    'setting-32A2jPsPe63x': '请输入角色描述，不超过150字',

    // components/transferSuperAdmin/index.jsx - 转让超级管理员组件
    'setting-8fxCsyCHMyyu': '将权限转让至',
    'setting-XDOhY1t8am5A': '必须选择一个角色',
    'setting-6TONWuN2laQ7': '请选择',
    'setting-ObjCi5hxksdZ': '将我的权限设为',
    'setting-AhfB1zWGt4oF': '必须选择一种权限',
    'setting-u5rjwtvxZ54Z': '{{count}}人',
    'setting-zaTr3s1AgW1X': '取消',
    'setting-SZZ7Ad83i10p': '确认转让',

    // components/addUser/index.jsx - 添加用户组件
    'setting-f5UnV0rHXwzr': '企业邮箱',
    'setting-L8CO3FwmFgc4': '姓名',
    'setting-CSc59jkPa8IB': '权限角色',
    'setting-ZCIxnfINWszw': '搜索企业邮箱',
    'setting-gqnt2xArruRS': '请选择',
    'setting-f1NU2dOW6BNJ': '已经选择{{count}}项',

    // config.js - 步骤配置
    'setting-2TkKEVGPLmKE': '基本信息',
    'setting-gMWCpFEn68t1': '数据权限',
    'setting-TS1o3IP3i74S': '功能权限',
    'setting-MbKOc7xAtqNz': '审批权限',

    // components/addComponents/functionPermission.jsx - 功能权限组件
    'setting-PGsnGw60GVwF': '一级分类',
    'setting-VMsGZxTuBzWB': '二级分类',
    'setting-JUDlaskuG9nu': '操作描述',
    'setting-tVA9btMpkIKA': '请先勾选功能权限',

    // components/addComponents/dataPermission.jsx - 数据权限组件
    'setting-RQlDyP4K49Fu': '数据分类',
    'setting-ausZKKNGG4P4': '权限',
    'setting-t0KtxHIgJBON': '无限制',
    'setting-KbuOkPmU2Bcn': '添加限制',
    'setting-vxqNlusZek6Z': '清空限制',
    'setting-snF9YxbK0DmT': '脱敏数据',
    'setting-FAmqnPsHdGD8': '请选择数据权限',

    // components/transferUser/index.jsx - 转移用户组件
    'setting-RFSVJIU4fC1U': '转移至权限角色',
    'setting-uMPKgpf11EXR': '必须选择一个权限角色',
    'setting-lc9PrF68eu5i': '确认',

    // components/addComponents/addTags.jsx - 添加标签组件
    'setting-eSUtfowf3ZwI': '数据权限',
    'setting-q074AThJhK9k': '至少选择一个数据权限',
    'setting-wWAQ43CeyGCC': '最多选择50项',

    // components/addComponents/definition.jsx - 审批定义组件
    'setting-NqAuc3ZhmCWE': '审批分类'
  },
  en: {
    // list/index.jsx - Permission List Page
    'setting-9JJatwo9n9Zx': 'Transfer completed',
    'setting-82ut8dY8DqTS': 'Delete',
    'setting-ytEgdKQpbb6d': 'Cannot be recovered after deletion, confirm deletion?',
    'setting-Z2kJOouTsL16': 'Confirm Delete',
    'setting-odZyNU13cMdK': 'Current permission has member list, cannot delete',
    'setting-GLvdHzEgtEaE': 'Delete successful',
    'setting-xUVjsbpZgeNd': 'Permission Management',
    'setting-41PVRmlLfIYr': 'Transfer Super Admin',
    'setting-gcF00RYuFQdn': 'Add Permission',
    'setting-7pUdgtaUv6HH': 'To delete role permissions, member count must be 0. Please transfer all members under this role first',
    'setting-cL9yGx7ZsP1M': 'Transfer Super Admin Permission',

    // add/index.jsx - Add Permission Page
    'setting-ZwTmfS0tC9N3': 'Permission created successfully',
    'setting-vftC42fNjMvD': 'Create Permission Role',
    'setting-SvRbk7Bv0sB4': 'Exiting will not save the {{step}} content, confirm exit?',
    'setting-4MqFIwel1KQT': 'Confirm Exit',
    'setting-B49k1OaCx5Cr': 'Cancel',
    'setting-G7Db8ld1hYGq': 'Exit Creation',
    'setting-AaEa2EAfF2QM': 'Previous',
    'setting-6zJatGVgz7B4': 'Next',
    'setting-iO6KZdvqLlt3': 'Complete',

    // details/index.jsx - Permission Details Page
    'setting-PyEOF3ISwlk3': 'Permission Details',
    'setting-2CWxnrCc09ku': 'Data permissions restrict already added fields or values',
    'setting-BTDojge8VuCz': 'Cancel',
    'setting-9hUqAs1QhqEd': 'Save',
    'setting-6hjF3likSXNH': 'Edit',
    'setting-mIJjAdQz7pwM': 'Save successful',

    // user/index.jsx - Permission User Page
    'setting-YVDtvhrpmDXX': 'Member List',
    'setting-SlX2c9wx6asO': 'Member Count',
    'setting-sksPHrxqwhvu': 'Creator',
    'setting-ZW74Folnzdyz': 'Create Time',
    'setting-xVy9vLpjLesU': 'Type',
    'setting-CzLbrMnKsz4y': 'Custom',
    'setting-aZArbb3hg6n7': 'Preset',
    'setting-BJD27TIijqte': 'Remarks',
    'setting-LEMJAdfVbWBW': 'Permission Details',
    'setting-Fkl9wXHcux4q': 'Data error exists',
    'setting-mniJ84a7LqPn': 'Search enterprise email',
    'setting-x5K4hKVPHnyT': 'Batch Transfer Members',
    'setting-xhIK8kquzU6m': 'Add Members',
    'setting-ObYqIOBAxDXx': 'You have not selected any members',
    'setting-fPIy7bOu1FbT': 'No data',
    'setting-EA70ExiQG5jw': '{{transferAll}}Transfer Members',

    // config.js - Configuration File (Table Columns)
    'setting-tc4ZIEsBmGCC': 'Name',
    'setting-RNBnD8YIgxFp': 'Member Count',
    'setting-cFgmZpFXPvRG': 'Type',
    'setting-nOPac3SlkV2Z': 'Create Time',
    'setting-TTz6rxUIwN4T': 'Creator Account',
    'setting-R8WDPnqx0y19': 'Actions',
    'setting-BaYuMMGVPszX': 'Member List',
    'setting-7fRKQWl0xiBA': 'Permission Details',
    'setting-Y9gLF8n26OE1': 'More',
    'setting-2I6vpRTNIgP3': 'Delete',

    // config.js - User Table Columns
    'setting-kfl4D1URd9Of': 'Name',
    'setting-Lxbbb02vpz6O': 'Employee ID',
    'setting-snu3uYWdZ3Jm': 'Enterprise Email',
    'setting-dOFJBEYRTztL': 'Mobile',
    'setting-yYvMi9ys4FnR': 'Last Login Time',
    'setting-lf26vCRjmEY9': 'Account Status',
    'setting-EbAIaVuSK9NE': 'Transfer This Member',

    // components/baseMsg.jsx - Basic Info Component
    'setting-gVivCWiwR8oa': 'Role Name',
    'setting-ip8uIsCN9zBG': 'Please enter role name',
    'setting-21cnGR12dXmi': 'Maximum length limit is 16 characters',
    'setting-3X3NDD65GKkG': 'This role name already exists',
    'setting-MTqXnbUtPBPq': 'Required, name cannot be duplicated',
    'setting-PdqmkVEcbpQu': 'Role Description',
    'setting-4WRD8wNqd8ij': 'Role description too long',
    'setting-32A2jPsPe63x': 'Please enter role description, no more than 150 characters',

    // components/transferSuperAdmin/index.jsx - Transfer Super Admin Component
    'setting-8fxCsyCHMyyu': 'Transfer permission to',
    'setting-XDOhY1t8am5A': 'Must select a role',
    'setting-6TONWuN2laQ7': 'Please select',
    'setting-ObjCi5hxksdZ': 'Set my permission to',
    'setting-AhfB1zWGt4oF': 'Must select a permission',
    'setting-u5rjwtvxZ54Z': '{{count}} people',
    'setting-zaTr3s1AgW1X': 'Cancel',
    'setting-SZZ7Ad83i10p': 'Confirm Transfer',

    // components/addUser/index.jsx - Add User Component
    'setting-f5UnV0rHXwzr': 'Enterprise Email',
    'setting-L8CO3FwmFgc4': 'Name',
    'setting-CSc59jkPa8IB': 'Permission Role',
    'setting-ZCIxnfINWszw': 'Search enterprise email',
    'setting-gqnt2xArruRS': 'Please select',
    'setting-f1NU2dOW6BNJ': 'Selected {{count}} items',

    // config.js - Step Configuration
    'setting-2TkKEVGPLmKE': 'Basic Info',
    'setting-gMWCpFEn68t1': 'Data Permission',
    'setting-TS1o3IP3i74S': 'Function Permission',
    'setting-MbKOc7xAtqNz': 'Approval Permission',

    // components/addComponents/functionPermission.jsx - Function Permission Component
    'setting-PGsnGw60GVwF': 'Primary Category',
    'setting-VMsGZxTuBzWB': 'Secondary Category',
    'setting-JUDlaskuG9nu': 'Operation Description',
    'setting-tVA9btMpkIKA': 'Please select function permissions first',

    // components/addComponents/dataPermission.jsx - Data Permission Component
    'setting-RQlDyP4K49Fu': 'Data Category',
    'setting-ausZKKNGG4P4': 'Permission',
    'setting-t0KtxHIgJBON': 'No Restrictions',
    'setting-KbuOkPmU2Bcn': 'Add Restriction',
    'setting-vxqNlusZek6Z': 'Clear Restrictions',
    'setting-snF9YxbK0DmT': 'Desensitized Data',
    'setting-FAmqnPsHdGD8': 'Please select data permissions',

    // components/transferUser/index.jsx - Transfer User Component
    'setting-RFSVJIU4fC1U': 'Transfer to Permission Role',
    'setting-uMPKgpf11EXR': 'Must select a permission role',
    'setting-lc9PrF68eu5i': 'Confirm',

    // components/addComponents/addTags.jsx - Add Tags Component
    'setting-eSUtfowf3ZwI': 'Data Permissions',
    'setting-q074AThJhK9k': 'At least select one data permission',
    'setting-wWAQ43CeyGCC': 'Maximum 50 items',

    // components/addComponents/definition.jsx - Approval Definition Component
    'setting-NqAuc3ZhmCWE': 'Approval Category'
  }
};
