import React, { Component } from 'react';
import { Button, Table, message, Input, Descriptions, Breadcrumb, <PERSON><PERSON>, Drawer, Modal } from 'antd';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import PermissionService from 'service/permissionService';
import dayjs from 'dayjs';
import { t } from 'utils/translation';
import { getPermissionUserColumns } from '../config';
import TransferUser from '../components/transferUser';
import AddUser from '../components/addUser';
import './index.scss';
import CheckAuth from '@/utils/checkAuth';

const SIZE = 10;
const { Search } = Input;
const service = new PermissionService();

class PermissionUserList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      roleId: Number(props.match.params.id) > 0 ? Number(props.match.params.id) : 0,
      permissionDetails: {}, // 角色、权限信息
      transferUserVisible: false, // 移除成员
      transferAll: false, // 是否批量移除
      alertVisible: false,
      choseTablekeys: [],
      transferUserData: {},
      addUserVisible: false, // 添加成员
      loading: false,
      tableParams: {
        size: SIZE,
        page: +localStorage.getItem('permissionUserPage') || 1,
        sorts: [],
        search: []
      },
      totalCount: null,
      tableData: []
    };
  }

  componentDidMount() {
    this.getPermissionData();
    this.getUserData();
  }

  // 获取permission信息
  getPermissionData = async () => {
    const { roleId } = this.state;
    if (!roleId) return false;
    try {
      const response = await service.reqGetPermissionDetails({ id: roleId });
      this.setState({
        permissionDetails: response
      });
    } catch (error) {
      // error
    }
  };

  getUserData = async () => {
    this.setState({ loading: true });
    const { tableParams, roleId } = this.state;
    if (!roleId) return message.error(t('setting-Fkl9wXHcux4q'));
    const params = {
      ...tableParams,
      search: [
        ...tableParams.search,
        {
          propertyName: 'roleId',
          operator: 'EQ',
          value: roleId
        }
      ]
    };
    try {
      const response = await service.reqGetUserTableData(params);
      this.setState({ loading: false });
      const { content, totalElements } = response;
      this.setState({
        tableData: content,
        totalCount: totalElements,
        loading: false
      });
    } catch (error) {
      this.setState({ loading: false });
    }
  };

  handleSearch = (text) => {
    const { tableParams } = this.state;
    this.setState(
      {
        tableParams: {
          ...tableParams,
          search: [
            {
              propertyName: 'user.email',
              value: text,
              nodeType: 'input',
              operator: 'LIKE'
            }
          ],
          page: 1
        }
      },
      () => {
        this.getUserData();
      }
    );
  };

  /** 表格操作包含排序/翻页/每页显示条数 */
  handleTableChange = (page, _, sorter) => {
    localStorage.setItem('permissionUserPage', page.current || 1);
    const { tableParams } = this.state;
    const { current, pageSize } = page;
    const sorts = [];
    if (sorter.order) {
      const direction = sorter.order.substr(0, sorter.order.length - 3);
      const propertyName = sorter.field;
      sorts.push({ direction, propertyName });
    }
    this.setState(
      {
        tableParams: {
          ...tableParams,
          sorts,
          page: current,
          size: pageSize
        }
      },
      () => {
        this.getUserData();
      }
    );
  };

  handleDetailsClick = () => {
    const { roleId } = this.state;
    if (!roleId) return false;
    this.props.history.push(`/aimarketer/home/<USER>/permission/details/${roleId}`);
  };

  // 移入成员
  handleAddUserClick = () => {
    this.setState({
      addUserVisible: true
    });
  };

  // 移出成员
  handleTransferUserClick = (record) => {
    this.setState({
      transferUserVisible: true,
      transferUserData: record,
      transferAll: false
    });
  };

  // 批量移出成员
  handleTransferUserAllClick = () => {
    const { choseTablekeys } = this.state;
    if (choseTablekeys.length === 0) return message.error(t('setting-ObYqIOBAxDXx'));
    this.setState({
      transferUserVisible: true,
      transferAll: true
    });
  };

  handleTransferUserCancel = () => {
    this.setState({
      transferUserVisible: false
    });
  };

  // 选择成员
  handleChoseChange = (selectedRowKeys) => {
    this.setState({
      choseTablekeys: selectedRowKeys
    });
  };

  // 移除成员完成
  handleTransferUserOk = async (data) => {
    const { transferUserData, transferAll, choseTablekeys } = this.state;
    const params = {
      ...data,
      userIds: transferAll ? choseTablekeys : [transferUserData.user.id]
    };
    try {
      await service.reqTransferUserPermission(params);
      this.setState({
        choseTablekeys: []
      });
      this.getUserData();
      this.getPermissionData();
      this.handleTransferUserCancel();
    } catch (error) {
      // error
    }
  };

  handleAlertClose = () => {
    this.setState({ alertVisible: false });
  };

  // 添加成员确定
  handleAddUserOk = async () => {
    const chose = this.userRef.getChoseTableKeys();
    if (chose.length === 0) return message.error(t('setting-ObYqIOBAxDXx'));
    const { roleId } = this.state;
    const params = {
      roleId,
      userIds: chose
    };
    try {
      await service.reqTransferUserPermission(params);
      this.getUserData();
      this.getPermissionData();
      this.handleAddUserCancel();
    } catch (error) {
      // error
    }
  };

  // 添加成员取消
  handleAddUserCancel = () => {
    this.setState({
      addUserVisible: false
    });
  };

  addUserRef = (ref) => {
    this.userRef = ref;
  };

  render() {
    const {
      tableData,
      alertVisible,
      loading,
      tableParams,
      roleId,
      totalCount,
      permissionDetails = {},
      transferUserVisible,
      choseTablekeys,
      addUserVisible,
      transferAll
    } = this.state;
    const { page, size } = tableParams;
    return (
      <div className="permissionUser">
        <header>
          <Breadcrumb>
            <Breadcrumb.Item>
              <Link to="/aimarketer/home/<USER>/permission">{t('setting-xUVjsbpZgeNd')}</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>{t('setting-YVDtvhrpmDXX')}</Breadcrumb.Item>
          </Breadcrumb>
          <div className="headerContainer">
            <Descriptions title={permissionDetails.name} column={2}>
              <Descriptions.Item label={t('setting-SlX2c9wx6asO')}>{permissionDetails.memberCount}</Descriptions.Item>
              <Descriptions.Item label={t('setting-sksPHrxqwhvu')}>
                {permissionDetails.createUserName}
              </Descriptions.Item>
              <Descriptions.Item label={t('setting-ZW74Folnzdyz')}>
                {dayjs(permissionDetails.createTime).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label={t('setting-xVy9vLpjLesU')}>
                {permissionDetails.id >= 1000 ? t('setting-CzLbrMnKsz4y') : t('setting-aZArbb3hg6n7')}
              </Descriptions.Item>
              <Descriptions.Item label={t('setting-BJD27TIijqte')}>{permissionDetails.remark}</Descriptions.Item>
            </Descriptions>
            <div className="btnGroup">
              {/* <Popconfirm
                placement="bottom"
                title={t('setting-ytEgdKQpbb6d')}
                onConfirm={this.handleDeletePermissionClick}
                okText={t('setting-Z2kJOouTsL16')}
                cancelText={t('setting-B49k1OaCx5Cr')}
              >
                {permissionDetails.id >= 1000 ? <Button>{t('setting-2I6vpRTNIgP3')}</Button> : null}
              </Popconfirm> */}
              <Button type="primary" onClick={this.handleDetailsClick}>
                {t('setting-LEMJAdfVbWBW')}
              </Button>
            </div>
          </div>
        </header>
        <div className="table1">
          {alertVisible ? (
            <Alert
              style={{ marginBottom: 10 }}
              message="若要删除角色权限，成员数必须为0。请先转移该角色下所有成员"
              type="error"
              showIcon
              closable
              onClose={this.handleAlertClose}
            />
          ) : null}
          {permissionDetails.memberCount > 0 && (
            <div className="TableHeader">
              <Search
                placeholder={t('setting-mniJ84a7LqPn')}
                allowClear
                onSearch={this.handleSearch}
                style={{ width: 200 }}
              />
              <div>
                <CheckAuth code="aim_role_manage_member_transfer">
                  <Button onClick={this.handleTransferUserAllClick}>{t('setting-x5K4hKVPHnyT')}</Button>
                </CheckAuth>
                <CheckAuth code="aim_role_manage_member_transfer">
                  <Button type="primary" onClick={this.handleAddUserClick}>
                    {t('setting-xhIK8kquzU6m')}
                  </Button>
                </CheckAuth>
              </div>
            </div>
          )}
          <Table
            rowKey={(record) => record.user.id}
            rowSelection={{
              selectedRowKeys: choseTablekeys,
              onChange: this.handleChoseChange
            }}
            columns={getPermissionUserColumns(this)}
            dataSource={tableData}
            bordered={false}
            loading={loading}
            onChange={this.handleTableChange}
            pagination={{
              current: page,
              total: totalCount,
              defaultPageSize: size,
              showQuickJumper: true,
              showSizeChanger: true,
              showLessItems: true,
              pageSizeOptions: ['10', '20', '50']
            }}
            locale={{
              emptyText: (
                <div style={{ margin: '120px 0px' }}>
                  <img
                    width="80px"
                    // eslint-disable-next-line global-require
                    src={require('@/assets/images/empty.png')}
                    alt=""
                  />
                  <div style={{ marginTop: 20 }}>
                    {CheckAuth.checkAuth('aim_role_manage_member_transfer') ? (
                      <Button type="primary" onClick={this.handleAddUserClick}>
                        {t('setting-xhIK8kquzU6m')}
                      </Button>
                    ) : (
                      t('setting-MbKOc7xAtqNz')
                    )}
                  </div>
                </div>
              )
            }}
            handleTransferUserClick={this.handleTransferUserClick}
          />
        </div>
        <Drawer
          title={t('setting-Ej8Ej8Ej8Ej8', {
            transferAll: transferAll ? t('setting-x5K4hKVPHnyT').substring(0, 2) : ''
          })}
          width={500}
          open={transferUserVisible}
          destroyOnClose
          maskClosable={false}
          onClose={this.handleTransferUserCancel}
        >
          <TransferUser
            roleId={roleId}
            handleCancel={this.handleTransferUserCancel}
            handleOk={this.handleTransferUserOk}
          />
        </Drawer>
        <Modal
          title={t('setting-xhIK8kquzU6m')}
          open={addUserVisible}
          width={820}
          destroyOnClose
          maskClosable={false}
          onOk={this.handleAddUserOk}
          onCancel={this.handleAddUserCancel}
          okText={t('setting-xhIK8kquzU6m').substring(0, 2)}
        >
          <AddUser roleId={roleId} onRef={this.addUserRef} />
        </Modal>
      </div>
    );
  }
}

export default connect()(PermissionUserList);
