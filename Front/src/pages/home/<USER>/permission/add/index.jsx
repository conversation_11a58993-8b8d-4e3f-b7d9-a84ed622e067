import React, { Component } from 'react';
import { Steps, But<PERSON>, Popconfirm, message } from 'antd';
import { t } from 'utils/translation';
import { BaseMsg, DataPermission, FunctionPermission, Definition } from '../components/addComponents';
import { permissionSteps as steps } from '../config';
import './index.scss';

const { Step } = Steps;
class AddPermission extends Component {
  state = {
    currentSteps: 0, // 从0开始
    permissionData: {},
    saving: false
  };

  handlePrevClick = () => {
    const { currentSteps, saving } = this.state;
    if (currentSteps === 0 || saving) return false;
    this.setState({
      currentSteps: currentSteps - 1
    });
  };

  handleNextClick = async () => {
    const { currentSteps, permissionData } = this.state;
    let data = false;
    this.setState({
      saving: true
    });
    switch (currentSteps) {
      case 0:
        data = await this.baseMsg.handleSubmit();
        break;
      case 1:
        data = await this.dataPermission.handleSubmit();
        break;
      case 2:
        data = await this.functionPermission.handleSubmit();
        break;
      case 3:
        data = await this.definition.handleSubmit();
        break;
      default:
        break;
    }
    this.setState({
      saving: false
    });
    if (!data) return false;
    // ? 是否每一步都保存
    //! 所有的组件都会将结果保存，并存储然后返回存储的结果
    if (currentSteps !== 3) {
      this.setState({
        currentSteps: currentSteps + 1,
        permissionData: {
          ...permissionData,
          ...data
        }
      });
    } else {
      message.success(t('setting-ZwTmfS0tC9N3'));
      this.hancleCancelClick();
    }
  };

  hancleCancelClick = () => {
    this.props.history.replace('/aimarketer/home/<USER>/permission');
  };

  render() {
    const { currentSteps, permissionData, saving } = this.state;
    return (
      <div className="addPermission">
        <header>
          <h1>{t('setting-vftC42fNjMvD')}</h1>
        </header>
        <div className="container">
          <Steps current={currentSteps} className="containerSteps">
            {steps.map((v) => (
              <Step title={v} key={v} />
            ))}
          </Steps>
          {currentSteps === 0 && (
            <div className="baseMsg">
              <BaseMsg data={permissionData} onRef={(ref) => (this.baseMsg = ref)} />
            </div>
          )}
          {currentSteps === 1 && (
            <div className="dataPermission">
              <DataPermission data={permissionData} onRef={(ref) => (this.dataPermission = ref)} />
            </div>
          )}
          {currentSteps === 2 && (
            <div className="functionPermission">
              <FunctionPermission data={permissionData} onRef={(ref) => (this.functionPermission = ref)} />
            </div>
          )}
          {currentSteps === 3 && (
            <div className="definition">
              <Definition data={permissionData} onRef={(ref) => (this.definition = ref)} />
            </div>
          )}
        </div>
        <div className="fixedBtns">
          <Popconfirm
            placement="topLeft"
            disabled={saving}
            getPopupContainer={() => document.getElementsByClassName('addPermission')[0]}
            title={t('setting-SvRbk7Bv0sB4', { step: steps[currentSteps] })}
            onConfirm={this.hancleCancelClick}
            okText={t('setting-4MqFIwel1KQT')}
            cancelText={t('setting-B49k1OaCx5Cr')}
          >
            <Button style={{ float: 'left' }}>{t('setting-G7Db8ld1hYGq')}</Button>
          </Popconfirm>
          {currentSteps !== 0 && (
            <Button style={{ marginRight: 8 }} onClick={this.handlePrevClick}>
              {t('setting-AaEa2EAfF2QM')}
            </Button>
          )}
          <Button type="primary" onClick={this.handleNextClick} loading={saving}>
            {currentSteps !== 3 ? t('setting-6zJatGVgz7B4') : t('setting-iO6KZdvqLlt3')}
          </Button>
        </div>
      </div>
    );
  }
}

export default AddPermission;
