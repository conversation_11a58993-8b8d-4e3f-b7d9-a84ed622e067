import { Bad<PERSON>, But<PERSON>, Dropdown } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import CheckAuth from 'utils/checkAuth';
import { t } from 'utils/translation';
import { statusList, SUPER_ADMIN_ROLE_ID } from '../role/config';

export const getPermissionListColumns = (props) => [
  {
    title: t('setting-tc4ZIEsBmGCC'),
    dataIndex: 'name',
    className: 'maxWidth'
  },
  {
    title: t('setting-SlX2c9wx6asO'),
    dataIndex: 'memberCount'
  },
  {
    title: t('setting-cFgmZpFXPvRG'),
    dataIndex: 'status',
    render: (_, record) => (record.projectId === '0' ? t('setting-aZArbb3hg6n7') : t('setting-CzLbrMnKsz4y'))
  },
  {
    title: t('setting-ZW74Folnzdyz'),
    dataIndex: 'createTime',
    render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : null)
  },
  {
    title: t('setting-TTz6rxUIwN4T'),
    dataIndex: 'createUserName',
    className: 'maxWidth'
  },
  {
    title: t('setting-R8WDPnqx0y19'),
    dataIndex: 'set',
    className: 'td-set',

    render: (_, record) => {
      return (
        <>
          {
            record.id !== SUPER_ADMIN_ROLE_ID && CheckAuth.checkAuth('aim_role_manage_member_edit') ? (
              <Button type="link" onClick={() => props.handlePersionClick(record)}>
                {t('setting-BaYuMMGVPszX')}
              </Button>
            ) : null
            // (
            //   <div style={{ width: 88 }} className="ant-btn ant-btn-link" />
            // )
          }
          <Button type="link" onClick={() => props.handleDetails(record)}>
            {t('setting-7fRKQWl0xiBA')}
          </Button>
          {record.id > 1000 ? (
            <CheckAuth code="aim_role_manage_delete">
              <Dropdown
                menu={{
                  items: [{ label: t('setting-82ut8dY8DqTS'), key: 'delete' }],
                  onClick: () => props.handleDeletePermissionClick(record)
                }}
              >
                <Button type="link">{t('setting-Y9gLF8n26OE1')}</Button>
              </Dropdown>
            </CheckAuth>
          ) : null}
        </>
      );
    }
  }
];

export const getPermissionUserColumns = (props) => [
  {
    title: t('setting-kfl4D1URd9Of'),
    dataIndex: ['user', 'name'],
    className: 'maxWidth'
  },
  {
    title: t('setting-Lxbbb02vpz6O'),
    dataIndex: ['user', 'jobNo'],
    className: 'maxWidth'
  },
  {
    title: t('setting-snu3uYWdZ3Jm'),
    dataIndex: ['user', 'email'],
    className: 'maxWidth'
  },
  {
    title: t('setting-dOFJBEYRTztL'),
    dataIndex: ['user', 'mobile']
  },
  {
    title: t('setting-yYvMi9ys4FnR'),
    dataIndex: ['user', 'lastLoginTime'],
    render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : null)
  },
  {
    title: t('setting-lf26vCRjmEY9'),
    dataIndex: 'status',
    sorter: true,
    render: (status) => {
      const data = statusList.find((v) => v.value === status) || {};
      return <Badge status={data.status} text={data.name} />;
    }
  },
  {
    title: t('setting-R8WDPnqx0y19'),
    dataIndex: 'set',
    className: 'td-set',
    render: (_, record) => (
      <>
        {/* {permissionDetails.id >= 1000 ? <Button>删除</Button> : null} */}
        <CheckAuth code="aim_role_manage_member_transfer">
          <Button type="link" onClick={() => props.handleTransferUserClick(record)}>
            {t('setting-EbAIaVuSK9NE')}
          </Button>
        </CheckAuth>
      </>
    )
  }
];

export const permissionSteps = [
  t('setting-2TkKEVGPLmKE'),
  t('setting-gMWCpFEn68t1'),
  t('setting-TS1o3IP3i74S'),
  t('setting-MbKOc7xAtqNz')
];
