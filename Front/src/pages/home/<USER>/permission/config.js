import { Badge, Button, Dropdown } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import CheckAuth from 'utils/checkAuth';
import { t } from 'utils/translation';
import { statusList, SUPER_ADMIN_ROLE_ID } from '../role/config';

export const getPermissionListColumns = (props) => [
  {
    title: '名称',
    dataIndex: 'name',
    className: 'maxWidth'
  },
  {
    title: t('setting-SlX2c9wx6asO'),
    dataIndex: 'memberCount'
  },
  {
    title: '类型',
    dataIndex: 'status',
    render: (_, record) => (record.projectId === '0' ? '预设' : '自定义')
  },
  {
    title: t('setting-ZW74Folnzdyz'),
    dataIndex: 'createTime',
    render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : null)
  },
  {
    title: '创建账号',
    dataIndex: 'createUserName',
    className: 'maxWidth'
  },
  {
    title: t('setting-R8WDPnqx0y19'),
    dataIndex: 'set',
    className: 'td-set',

    render: (_, record) => {
      return (
        <>
          {
            record.id !== SUPER_ADMIN_ROLE_ID && CheckAuth.checkAuth('aim_role_manage_member_edit') ? (
              <Button type="link" onClick={() => props.handlePersionClick(record)}>
                成员列表
              </Button>
            ) : null
            // (
            //   <div style={{ width: 88 }} className="ant-btn ant-btn-link" />
            // )
          }
          <Button type="link" onClick={() => props.handleDetails(record)}>
            权限详情
          </Button>
          {record.id > 1000 ? (
            <CheckAuth code="aim_role_manage_delete">
              <Dropdown
                menu={{
                  items: [{ label: t('setting-82ut8dY8DqTS'), key: 'delete' }],
                  onClick: () => props.handleDeletePermissionClick(record)
                }}
              >
                <Button type="link">更多</Button>
              </Dropdown>
            </CheckAuth>
          ) : null}
        </>
      );
    }
  }
];

export const getPermissionUserColumns = (props) => [
  {
    title: t('setting-kfl4D1URd9Of'),
    dataIndex: ['user', 'name'],
    className: 'maxWidth'
  },
  {
    title: '员工号',
    dataIndex: ['user', 'jobNo'],
    className: 'maxWidth'
  },
  {
    title: t('setting-snu3uYWdZ3Jm'),
    dataIndex: ['user', 'email'],
    className: 'maxWidth'
  },
  {
    title: '手机号',
    dataIndex: ['user', 'mobile']
  },
  {
    title: '最近登录时间',
    dataIndex: ['user', 'lastLoginTime'],
    render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : null)
  },
  {
    title: '账号状态',
    dataIndex: 'status',
    sorter: true,
    render: (status) => {
      const data = statusList.find((v) => v.value === status) || {};
      return <Badge status={data.status} text={data.name} />;
    }
  },
  {
    title: t('setting-R8WDPnqx0y19'),
    dataIndex: 'set',
    className: 'td-set',
    render: (_, record) => (
      <>
        {/* {permissionDetails.id >= 1000 ? <Button>删除</Button> : null} */}
        <CheckAuth code="aim_role_manage_member_transfer">
          <Button type="link" onClick={() => props.handleTransferUserClick(record)}>
            转移该成员
          </Button>
        </CheckAuth>
      </>
    )
  }
];

export const permissionSteps = [
  t('setting-2TkKEVGPLmKE'),
  t('setting-Ej8Ej8Ej8Ej8'),
  t('setting-Ej8Ej8Ej8Ej8'),
  t('setting-Ej8Ej8Ej8Ej8')
];
