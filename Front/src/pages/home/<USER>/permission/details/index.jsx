import React, { Component } from 'react';
import { <PERSON>u, <PERSON><PERSON>, message, Breadcrumb } from 'antd';
import { Link } from 'react-router-dom';
import PermissionService from 'service/permissionService';
import { BaseMsg, DataPermission, FunctionPermission, Definition } from '../components/addComponents';
import CheckAuth from '@/utils/checkAuth';
import { t } from 'utils/translation';
import './index.scss';

import { permissionSteps } from '../config';

const service = new PermissionService();

class PermissionDetails extends Component {
  state = {
    currentStep: permissionSteps[0],
    permissionData: {
      id: this.props.match.params.id
    },
    readOnly: true,
    saving: false,
    permissionDetails: {}
  };

  componentDidMount() {
    this.getPermissionData();
  }

  // 获取permission信息
  getPermissionData = async () => {
    try {
      const response = await service.reqGetPermissionDetails({
        id: this.props.match.params.id
      });
      this.setState({
        permissionDetails: response
      });
    } catch (error) {
      // error
    }
  };

  handleMenuClick = (e) => {
    const { saving } = this.state;
    if (saving) return false;
    this.setState({
      currentStep: e.key,
      readOnly: true
    });
  };

  handleEditClick = () => {
    const { saving } = this.state;
    if (saving) return false;
    this.setState({
      readOnly: false
    });
  };

  handleEditCancel = () => {
    const { saving, currentStep } = this.state;
    if (saving) return false;
    this.setState({
      readOnly: true
    });
    // 还原数据
    switch (currentStep) {
      case permissionSteps[0]:
        this.baseMsg.initData();
        break;
      case permissionSteps[1]:
        this.dataPermission.initData();
        break;
      case permissionSteps[2]:
        this.functionPermission.initData();
        break;
      case permissionSteps[3]:
        this.definitionPermission.initData();
        break;
      default:
        break;
    }
  };

  handleSaveClick = async () => {
    const { currentStep } = this.state;
    this.setState({
      saving: true
    });
    let data = false;
    switch (currentStep) {
      case permissionSteps[0]:
        data = await this.baseMsg.handleSubmit();
        break;
      case permissionSteps[1]:
        data = await this.dataPermission.handleSubmit();
        break;
      case permissionSteps[2]:
        data = await this.functionPermission.handleSubmit();
        break;
      case permissionSteps[3]:
        data = await this.definitionPermission.handleSubmit();
        break;
      default:
        break;
    }
    this.setState({
      saving: false
    });
    data &&
      this.setState({
        readOnly: true
      });
    if (data) message.success(t('setting-mIJjAdQz7pwM'));
  };

  render() {
    const { currentStep, permissionData, readOnly, saving, permissionDetails } = this.state;
    return (
      <div className="permissionDetails">
        <header>
          <Breadcrumb>
            <Breadcrumb.Item>
              <Link to="/aimarketer/home/<USER>/permission">{t('setting-xUVjsbpZgeNd')}</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>{t('setting-PyEOF3ISwlk3')}</Breadcrumb.Item>
          </Breadcrumb>
          <h1>{t('setting-PyEOF3ISwlk3')}</h1>
        </header>
        <div className="container">
          <Menu selectedKeys={[currentStep]} mode="horizontal" onClick={this.handleMenuClick}>
            {permissionSteps.map((step) => (
              <Menu.Item key={step}>{step}</Menu.Item>
            ))}
          </Menu>
          <div className="wrapper">
            {currentStep === permissionSteps[1] ? <div>{t('setting-2CWxnrCc09ku')}</div> : <div />}
            {permissionDetails.type === 'CUSTOM' && (
              <div className="btnGroup">
                {readOnly || <Button onClick={this.handleEditCancel}>{t('setting-BTDojge8VuCz')}</Button>}
                {readOnly || (
                  <Button type="primary" onClick={this.handleSaveClick} loading={saving}>
                    {t('setting-9hUqAs1QhqEd')}
                  </Button>
                )}
                {readOnly && (
                  <CheckAuth code="aim_role_manage_edit">
                    <Button type="primary" onClick={this.handleEditClick}>
                      {t('setting-6hjF3likSXNH')}
                    </Button>
                  </CheckAuth>
                )}
              </div>
            )}
          </div>
          {currentStep === permissionSteps[0] && (
            <div className="baseMsg">
              <BaseMsg data={permissionData} readOnly={readOnly} onRef={(ref) => (this.baseMsg = ref)} />
            </div>
          )}
          {currentStep === permissionSteps[1] && (
            <div className="dataPermission">
              <DataPermission data={permissionData} readOnly={readOnly} onRef={(ref) => (this.dataPermission = ref)} />
            </div>
          )}
          {currentStep === permissionSteps[2] && (
            <div className="functionPermission">
              <FunctionPermission
                data={permissionData}
                readOnly={readOnly}
                onRef={(ref) => (this.functionPermission = ref)}
              />
            </div>
          )}
          {currentStep === permissionSteps[3] && (
            <div className="definitionPermission">
              <Definition
                data={permissionData}
                readOnly={readOnly}
                onRef={(ref) => (this.definitionPermission = ref)}
                permissionDetails={permissionDetails}
              />
            </div>
          )}
        </div>
      </div>
    );
  }
}

export default PermissionDetails;
