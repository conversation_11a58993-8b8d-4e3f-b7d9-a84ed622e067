import { Input, Select, Table } from 'antd';
import React, { Component } from 'react';
import RoleService from 'service/roleService';
import { t } from 'utils/translation';
import './index.scss';

const { Search } = Input;
const { Option } = Select;
const roleServie = new RoleService();

const getPermissionAddUserColumns = () => [
  {
    title: t('setting-f5UnV0rHXwzr'),
    dataIndex: ['user', 'email'],
    className: 'maxWidth'
  },
  {
    title: t('setting-L8CO3FwmFgc4'),
    dataIndex: ['user', 'name'],
    className: 'maxWidth'
  },
  {
    title: t('setting-CSc59jkPa8IB'),
    dataIndex: 'roleName',
    className: 'maxWidth',
    render: (_, record) => record.roleName || record.roleTemplateName
  }
];

class AddUser extends Component {
  state = {
    choseTablekeys: [],
    tableData: [],
    roleList: [],
    tableParams: {
      size: 10,
      page: 1,
      search: [
        {
          propertyName: 'user.email',
          value: '',
          nodeType: 'input',
          operator: 'LIKE'
        },
        {
          propertyName: 'roleId',
          value: '',
          nodeType: 'select',
          operator: 'EQ'
        },
        {
          propertyName: 'roleId',
          value: this.props.roleId,
          operator: 'NE'
        },
        {
          propertyName: 'status',
          operator: 'EQ',
          value: 'NORMAL'
        }
      ]
    },
    totalCount: 0,
    loading: false
  };

  componentDidMount() {
    this.props.onRef(this);
    this.getUserData();
    this.getRoleList();
  }

  getRoleList = async () => {
    const params = {
      excludes: [this.props.roleId]
    };
    try {
      const response = await roleServie.reqGetRoleListWithoutSuperAdmin(params);
      this.setState({
        roleList: response
      });
    } catch (error) {
      //
    }
  };

  getUserData = async () => {
    this.setState({ loading: true });
    const { tableParams } = this.state;
    try {
      const response = await roleServie.reqGetSeleteDataWithoutSuperAdmin(tableParams);
      this.setState({ loading: false });
      const { content, totalElements } = response;
      const newContent = content.map((n) => ({ ...n, id: n.user.id }));
      this.setState({
        tableData: newContent,
        totalCount: totalElements,
        loading: false
      });
    } catch (error) {
      this.setState({ loading: false });
    }
  };

  // 选择成员
  handleChoseChange = (selectedRowKeys) => {
    this.setState({
      choseTablekeys: selectedRowKeys
    });
  };

  /** 表格操作包含排序/翻页/每页显示条数 */
  handleTableChange = (page) => {
    const { tableParams } = this.state;
    const { current, pageSize } = page;
    this.setState(
      {
        tableParams: {
          ...tableParams,
          page: current,
          size: pageSize
        }
      },
      () => {
        this.getUserData();
      }
    );
  };

  handleSearch = (text) => {
    const { tableParams } = this.state;
    tableParams.search[0].value = text;
    this.setState(
      {
        tableParams: {
          ...tableParams,
          page: 1
        }
      },
      () => {
        this.getUserData();
      }
    );
  };

  handleSelectChange = (item) => {
    const { tableParams } = this.state;
    tableParams.search[1].value = item;
    this.setState(
      {
        tableParams: {
          ...tableParams,
          page: 1
        }
      },
      () => {
        this.getUserData();
      }
    );
  };

  // 获取当前选中的信息
  getChoseTableKeys = () => this.state.choseTablekeys;

  render() {
    const { tableData, loading, tableParams, totalCount, choseTablekeys, roleList } = this.state;
    const { page, size } = tableParams;
    return (
      <div className="permissionAddUser">
        <div className="TableHeader">
          <Search
            placeholder={t('setting-ZCIxnfINWszw')}
            allowClear
            onSearch={this.handleSearch}
            style={{ width: 200, marginRight: 10 }}
          />
          <Select
            style={{ width: 200 }}
            placeholder={t('setting-gqnt2xArruRS')}
            showSearch
            allowClear
            filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
            onChange={this.handleSelectChange}
          >
            {roleList.map((v) => (
              <Option key={v.id} value={v.id}>
                {v.name}
              </Option>
            ))}
          </Select>
          {choseTablekeys.length > 0 ? (
            <div className="TableRight">{t('setting-f1NU2dOW6BNJ', { count: choseTablekeys.length })}</div>
          ) : null}
        </div>
        <Table
          className="table1"
          rowKey="id"
          rowSelection={{
            selectedRowKeys: choseTablekeys,
            preserveSelectedRowKeys: true,
            onChange: this.handleChoseChange
          }}
          columns={getPermissionAddUserColumns(this)}
          dataSource={tableData}
          bordered={false}
          loading={loading}
          onChange={this.handleTableChange}
          pagination={{
            current: page,
            total: totalCount,
            defaultPageSize: size,
            showQuickJumper: true,
            showSizeChanger: true,
            showLessItems: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </div>
    );
  }
}

export default AddUser;
