import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Select } from 'antd';
import RoleService from 'service/roleService';
import { t } from 'utils/translation';
import './index.scss';

const service = new RoleService();
const { Option } = Select;
class TransferUser extends Component {
  state = {
    roleList: [],
    userList: []
  };

  componentDidMount() {
    this.getRoleList();
    this.getUserList();
  }

  getRoleList = async () => {
    try {
      const response = await service.reqGetRoleListWithoutSuperAdmin();
      this.setState({
        roleList: response
      });
    } catch (error) {
      //
    }
  };

  getUserList = async () => {
    try {
      const params = {
        size: 1000,
        search: [
          {
            propertyName: 'status',
            operator: 'EQ',
            value: 'NORMAL'
          }
        ]
      };
      const response = await service.reqGetSeleteDataWithoutSuperAdmin(params);
      this.setState({
        userList: response.content
      });
    } catch (error) {
      //
    }
  };

  handleSubmit = (e) => {
    e.preventDefault();
    const _this = this;
    this.props.form.validateFields(async (err, values) => {
      if (!err) {
        _this.props.handleOk(values);
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const { roleList = [], userList = [] } = this.state;
    return (
      <Form
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        onSubmit={this.handleSubmit}
        className="transferSuperAdmin"
      >
        <Form.Item label={t('setting-8fxCsyCHMyyu')}>
          {getFieldDecorator('userId', {
            rules: [{ required: true, message: t('setting-XDOhY1t8am5A') }]
          })(
            <Select
              placeholder={t('setting-6TONWuN2laQ7')}
              showSearch
              filterOption={(input, option) =>
                option.props.children[0].props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {userList.map((v) => (
                <Option key={v.user.id} value={v.user.id} label={v.user.name}>
                  <span style={{ minWidth: '60%', display: 'inline-block' }}>{v.user ? v.user.email : ''}</span>
                  <span className="selectedDesc">{v.user ? v.user.name : ''}</span>
                </Option>
              ))}
            </Select>
          )}
        </Form.Item>
        <Form.Item label={t('setting-ObjCi5hxksdZ')}>
          {getFieldDecorator('roleId', {
            rules: [{ required: true, message: t('setting-AhfB1zWGt4oF') }]
          })(
            <Select
              placeholder={t('setting-6TONWuN2laQ7')}
              showSearch
              filterOption={(input, option) =>
                option.props.children[0].props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {roleList.map((v) => (
                <Option key={v.id} value={v.id}>
                  <span style={{ minWidth: '60%', display: 'inline-block' }}>{v.name}</span>
                  <span className="selectedDesc">{t('setting-u5rjwtvxZ54Z', { count: v.memberCount || 0 })}</span>
                </Option>
              ))}
            </Select>
          )}
        </Form.Item>
        <div
          style={{
            position: 'absolute',
            right: 0,
            bottom: 0,
            width: '100%',
            boxShadow: '0 -4px 10px 0 rgba(0,0,0,0.10)',
            padding: '10px 16px',
            background: '#fff',
            textAlign: 'right'
          }}
        >
          <Button onClick={this.props.handleCancel} style={{ marginRight: 8 }}>
            {t('setting-zaTr3s1AgW1X')}
          </Button>
          <Button type="primary" htmlType="submit">
            {t('setting-SZZ7Ad83i10p')}
          </Button>
        </div>
      </Form>
    );
  }
}

export default Form.create()(TransferUser);
