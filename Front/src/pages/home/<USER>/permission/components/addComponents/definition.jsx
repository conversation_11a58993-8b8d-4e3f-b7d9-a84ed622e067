import { Checkbox, Table } from 'antd';
import _ from 'lodash';
import React, { Component } from 'react';
import PermissionService from 'service/permissionService';
import MyToDoListService from 'service/myToDoListService';
import { t } from 'utils/translation';
import './index.scss';

const permissionService = new PermissionService();

const getTableListColumns = (props, readOnly) => [
  {
    title: '审批分类',
    dataIndex: 'name',
    type: 'CAMPAIGN',
    width: 200
  },
  {
    title: '权限',
    dataIndex: 'permission',
    render: (text, record) => (
      <div>
        {readOnly ? (
          <Checkbox checked={record.checked} disabled />
        ) : (
          <div>
            <Checkbox checked={record.checked} onChange={() => props.handleCheckboxChange(record)} />
            <br />
          </div>
        )}
      </div>
    )
  }
];

class Definition extends Component {
  state = {
    tableData: [],
    loading: false
  };

  componentDidMount() {
    this.props.onRef(this);
    this.initData();
  }

  initData = () => {
    const { data } = this.props;
    this.getFunctionData(data.id);
  };

  getFunctionData = async (id) => {
    // ! 处理后端返回的数据，转化成table解析
    this.setState({ loading: true });
    try {
      let permissionList = [];
      const res = await MyToDoListService.getDictType([
        {
          operator: 'EQ',
          propertyName: 'businessType',
          value: 'APPROVAL'
        },
        {
          operator: 'EQ',
          propertyName: 'code',
          value: 'sys_approval_scope'
        }
      ]);

      permissionList = res.map((item) => {
        return {
          name: item.label,
          type: item.value,
          checked: false
        };
      });

      const response = await permissionService.definitionGetPermission([
        { operator: 'EQ', propertyName: 'roleId', value: id }
      ]);
      const superUser = await permissionService.reqGetPermissionDetails({ id });
      const tableData = [];

      if (response.length) {
        response.forEach((item) => {
          tableData.push({
            id: item.id,
            name: permissionList.find((items) => items.type === item.type).name,
            type: permissionList.find((items) => items.type === item.type).type,
            checked: item.status
          });
        });
      } else {
        tableData.push(...permissionList);
      }

      if (superUser.id === 1) {
        const _tableData = _.cloneDeep(tableData);
        this.setState({
          tableData: _tableData.map((item) => {
            return { ...item, checked: true };
          }),
          loading: false
        });
      } else {
        this.setState({
          tableData,
          loading: false
        });
      }
    } catch (error) {
      this.setState({ loading: false });
    }
  };

  handleCheckboxChange(item) {
    const { tableData } = this.state;
    let _tableData = _.cloneDeep(tableData);
    _tableData = _tableData.map((v) => {
      if (v.type === item.type) v.checked = !v.checked;
      return v;
    });
    // tableData.map((data) => (
    //   data.permissionList.map((v) => {
    //     if (v.id === item.id) v.checked = !v.checked;
    //     return v;
    //   })
    // ));

    this.setState({
      tableData: _tableData
    });
  }

  uniqueAfterArr = (arr, name) => {
    const hash = {};
    return arr.reduce((acc, cru) => {
      if (!hash[cru[name]]) {
        hash[cru[name]] = { index: acc.length };
        acc.push(cru);
      } else {
        acc.splice(hash[cru[name]].index, 1, cru);
      }
      return acc;
    }, []);
  };

  handleSubmit = async () => {
    const { tableData } = this.state;
    let _tableData = _.cloneDeep(tableData);
    _tableData = _tableData.map((item) => {
      return {
        id: item.id || undefined,
        roleId: this.props.data.id,
        type: item.type,
        status: item.checked ? 1 : 0
      };
    });

    try {
      const response = await permissionService.definitionPermission(this.uniqueAfterArr(_tableData, 'type'));
      return response;
    } catch (error) {
      return false;
    }
  };

  render() {
    const { tableData, loading } = this.state;
    const { readOnly = false } = this.props;
    return (
      <Table
        className="functionPermission"
        rowKey="id"
        columns={getTableListColumns(this, readOnly)}
        dataSource={tableData}
        bordered={false}
        loading={loading}
        handleCheckboxChange={this.handleCheckboxChange}
        pagination={false}
      />
    );
  }
}

export default Definition;
