import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Spin } from 'antd';
import PermissionService from 'service/permissionService';
import { t } from 'utils/translation';
import './index.scss';

const permissionService = new PermissionService();
const { TextArea } = Input;
class BaseMsg extends Component {
  state = {
    loading: false
  };

  componentDidMount() {
    this.props.onRef(this);
    this.initData();
  }

  initData = () => {
    const { data = {} } = this.props;
    data.id && this.getBaseData(data.id);
    data.id || this.setFieldsValue(data);
  };

  setFieldsValue = (data = {}) => {
    this.props.form.setFieldsValue({
      name: data.name,
      remark: data.remark
    });
  };

  // 存在id，查询权限基本信息
  getBaseData = async (id) => {
    this.setState({
      loading: true
    });
    try {
      const response = await permissionService.reqGetRoleById({ id });
      this.setState({
        loading: false
      });
      this.setFieldsValue(response);
    } catch (error) {
      this.setState({
        loading: false
      });
    }
  };

  // 提交方法，由外部调用
  handleSubmit = async () => {
    const values = await this.getValidateFields();
    if (!values) return false;
    // eslint-disable-next-line
    return await this.savePermissionData(values);
  };

  // 验证字段是否符合规则
  getValidateFields = async () => {
    const {
      form: { validateFields }
    } = this.props;
    try {
      return await validateFields(async (err, values) => {
        if (err) return false;
        return values;
      });
    } catch (error) {
      return false;
    }
  };

  // 数据提交方法
  savePermissionData = async (values) => {
    const { data } = this.props;
    try {
      const params = {
        id: data.id,
        ...values
      };
      const response = await permissionService.reqAddPermission(params);
      return response;
    } catch (error) {
      return false;
    }
  };

  // 检验name是否存在
  checkName = async (_, value, callback) => {
    if (!value) return;
    const params = {
      name: value,
      id: this.props.data.id
    };
    try {
      const tableNameUnique = await permissionService.ensureTableNameUnique(params);
      if (!tableNameUnique) {
        callback(true);
      } else {
        callback();
      }
    } catch (error) {
      callback();
    }
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const { readOnly = false } = this.props;
    const { loading } = this.state;
    return (
      <div className="baseMsg">
        <Spin spinning={loading}>
          <Form
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            onSubmit={this.handleSubmit}
            className={`${readOnly ? 'readOnly' : ''} businessListEdit`}
          >
            <Form.Item label={t('setting-gVivCWiwR8oa')}>
              {getFieldDecorator('name', {
                validateTrigger: 'onBlur',
                rules: readOnly
                  ? []
                  : [
                      { required: true, message: t('setting-ip8uIsCN9zBG') },
                      { max: 16, message: t('setting-21cnGR12dXmi') },
                      { validator: this.checkName, message: t('setting-3X3NDD65GKkG') }
                    ]
              })(
                <Input autoComplete="off" readOnly={readOnly} placeholder={readOnly ? '' : t('setting-MTqXnbUtPBPq')} />
              )}
            </Form.Item>
            <Form.Item label={t('setting-PdqmkVEcbpQu')} className="formTopWrapper">
              {getFieldDecorator('remark', {
                validateTrigger: 'onBlur',
                rules: readOnly ? [] : [{ max: 150, message: t('setting-4WRD8wNqd8ij') }]
              })(
                <TextArea
                  placeholder={readOnly ? '' : t('setting-32A2jPsPe63x')}
                  autoComplete="off"
                  readOnly={readOnly}
                  autoSize={{ minRows: 3, maxRows: 9 }}
                />
              )}
            </Form.Item>
          </Form>
        </Spin>
      </div>
    );
  }
}

export default Form.create()(BaseMsg);
