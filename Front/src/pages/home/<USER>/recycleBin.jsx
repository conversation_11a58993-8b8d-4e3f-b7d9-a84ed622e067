import { useStore } from '@/store/share';
import { FilterOutlined } from '@ant-design/icons';
import { Breadcrumb, Button, message, Modal, Table, Tooltip } from 'antd';
import QueryForList from 'components/bussinesscoms/queryforlist/index';
import ColumnActionCom from 'components/featurecoms/tableactioncom/index';
import dayjs from 'dayjs';
import _, { isArray } from 'lodash';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import CampaignV2Service from 'service/CampaignV2Service';
import scenarioService from 'service/ScenarioService';
import UserService from 'service/UserService';
import Log from 'utils/log';
import { DatatistIcon } from 'utils/myIcon';
import { t } from 'utils/translation';
import { calcPageNo } from 'utils/universal';
import {
  elementsWithoutApprove,
  phaseList,
  proessStatusList,
  elements as queryElements,
  ruleList,
  typeList
} from './config';
import './index.scss';
import ActivityBoard from './list/activityboard/activityboard';

const { confirm } = Modal;

const campaignV2Service = new CampaignV2Service();

const log = Log.getLogger('userGroupList');
// const campaignV2Service = new CampaignV2Service();

const stateToProps = (state) => {
  return {
    loginUser: state.loginUser,
    userList: state.userList,
    filterList: state.filterList,
    isFold: state.toggle.campaign,
    meunData: state.meunData.menus.main
  };
};

const withStore = (BaseComponent) => (props) => {
  const store = useStore();
  return <BaseComponent {...props} store={store} />;
};

class List extends Component {
  constructor(props) {
    super(props);
    this.userService = new UserService();
    this.state = {
      visible: false,
      isFold: this.props.isFold || false, // 筛选是否展开
      isActivityFold: false,
      loading: false,
      value: {},
      config: [],
      proessStatus: false,
      // activeKey: localStorage.getItem('userGroupTabKey') && localStorage.getItem('userGroupCache') ? localStorage.getItem('userGroupTabKey') : 'RECENT',
      activeKey:
        localStorage.getItem('campaignTabKey') && localStorage.getItem('campaignCache')
          ? localStorage.getItem('campaignTabKey')
          : 'ALL',
      sorterInfo: { columnKey: null, order: null },
      isCollect: false,
      userId: null,
      recentRange: null,
      tableParams: {
        search: [],
        page: +localStorage.getItem('recyclePage') || 1,
        size: 10,
        sorts: [
          {
            direction: 'desc',
            propertyName: 'updateTime'
          }
        ]
      },
      totalCount: null,
      dataList: [],
      elements: queryElements,
      defaultFormData: undefined,
      scenarioOptions: [],
      projectShareStatus: false,
      modalData: [
        {
          title: t('operationCenter-FEC3gnFiQTck'),
          des: t('operationCenter-CTy9YvF69mqK'),
          src: <DatatistIcon type="icon-Flow-Custom" />,
          type: 'CUSTOM',
          tip: ''
        },
        {
          title: t('operationCenter-KgHzF3eIcjZK'),
          des: t('operationCenter-nm21j5LfgOvz'),
          src: <DatatistIcon type="icon-Flow-Templane" />,
          type: 'TELPLATE'
        }
      ],
      recentSelectKey:
        localStorage.getItem('campaignSelectKey') && localStorage.getItem('campaignCache')
          ? localStorage.getItem('campaignSelectKey')
          : null
    };

    this.toggle = this.toggle.bind(this);
    this.queryData = this.queryData.bind(this);
    this.hideModal = this.hideModal.bind(this);
    this.selectCampaignV2Type = this.selectCampaignV2Type.bind(this);
    this.dispatch = this.props.store.dispatch;
  }

  UNSAFE_componentWillMount() {
    window.addEventListener('beforeunload', this.beforeunload);
  }

  async componentDidMount() {
    const projectShareRes = await campaignV2Service.projectShareAuth({ projectId: localStorage.getItem('projectId') });

    const authShare = await campaignV2Service.getActionGroupByRoleShare({
      id: Number(localStorage.getItem('roleId'))
    });

    if (projectShareRes && !_.isEmpty(authShare) && authShare.find((item) => item.code === 'CAMPAIGN').checked) {
      this.setState({
        projectShareStatus: true
      });
    }
    log.debug('componentDidMount', this.state);
    const { id } = await this.userService.getCurrentUser();
    this.setState({ userId: id });

    const processAuth = await campaignV2Service.getProcessByType({
      type: 'CAMPAIGN',
      projectId: localStorage.getItem('projectId'),
      status: 'ENABLE'
    });
    this.setState({
      proessStatus: processAuth.status
    });

    const campaignOverviewValue = JSON.parse(sessionStorage.getItem('query'));

    if (sessionStorage.getItem('query')) {
      // 判断是否是活动概览跳转过来
      this.setState({
        defaultFormData: campaignOverviewValue.reduce((acc, item) => {
          acc[item.propertyName] = item.value;
          return acc;
        }, {})
      });
      this.queryData(campaignOverviewValue, true);
    } else {
      // if (this.props.filterList.length && localStorage.getItem('campaignCache')) {
      //   this.queryData(this.props.filterList, false);
      // } else {
      //   let _tableParams = _.cloneDeep(this.state.tableParams);
      //   if (this.state.activeKey === 'ALL' || this.state.activeKey === 'MINE') {
      //     _tableParams = {
      //       ..._tableParams,
      //       page: localStorage.getItem('campaignCache') ? parseInt(localStorage.getItem('recyclePage')) : undefined,
      //       sorts: [{ direction: 'desc', propertyName: 'updateTime' }]
      //     };
      //   } else {
      //     _tableParams = {
      //       ..._tableParams,
      //       page: localStorage.getItem('campaignCache') ? parseInt(localStorage.getItem('recyclePage')) : undefined,
      //       sorts: [{ direction: 'desc', propertyName: 'operationTime' }]
      //     };
      //   }
      //   this.queryListData(_tableParams, this.state.activeKey);
      // }
      let _tableParams = _.cloneDeep(this.state.tableParams);
      if (this.state.activeKey === 'ALL' || this.state.activeKey === 'MINE') {
        _tableParams = {
          ..._tableParams,
          page: localStorage.getItem('campaignCache') ? parseInt(localStorage.getItem('recyclePage')) : undefined,
          sorts: [{ direction: 'desc', propertyName: 'updateTime' }]
        };
      } else {
        _tableParams = {
          ..._tableParams,
          page: localStorage.getItem('campaignCache') ? parseInt(localStorage.getItem('recyclePage')) : undefined,
          sorts: [{ direction: 'desc', propertyName: 'operationTime' }]
        };
      }
      this.queryListData(_tableParams, this.state.activeKey);
    }
    // 处理下elements的options
    const scenarioList = await scenarioService.scenarioList([]);
    const _elements = _.cloneDeep(processAuth.status === 'ENABLE' ? queryElements : elementsWithoutApprove);
    _elements['createUser.id'].componentOptions.onSearch = (val) => this.onSearch(val);
    _elements['createUser.id'].componentOptions.onClear = (val) => this.onSearch(val);
    _elements['createUser.id'].componentOptions.options = _.map(this.state.scenarioOptions, (item) => ({
      key: item.id,
      value: item.id,
      text: item.name
    }));
    _elements.lastEnableUser.componentOptions.onSearch = (val) => this.onExecutorSearch(val);
    _elements.lastEnableUser.componentOptions.onClear = (val) => this.onExecutorSearch(val);
    _elements.lastEnableUser.componentOptions.options = _.map(this.state.scenarioOptions, (item) => ({
      key: item.id,
      value: item.id,
      text: item.name
    }));
    _elements['createUser.id'].componentOptions.options = _.map(this.props.userList, (item) => ({
      key: item.id,
      value: item.id,
      text: item.name
    }));
    _elements.lastEnableUser.componentOptions.options = _.map(this.props.userList, (item) => ({
      key: item.id,
      value: item.id,
      text: item.name
    }));
    _elements['scenario.code'].componentOptions.options = _.map(
      scenarioList.sort((a, b) => a.orderNum - b.orderNum),
      (item) => ({
        key: item.code,
        value: item.code,
        text: `${item.name}[${item.code}]`
      })
    );
    this.setState({
      elements: _.omit(_elements, ['favoriteCount', 'copyCount'])
    });

    // if (this.props.filterList.length && localStorage.getItem('campaignCache')) {
    //   const defaultFilter = {};
    //   for (const item of this.props.filterList) {
    //     if (item.value !== '' && item.propertyName !== 'operationTime') {
    //       if (
    //         item.propertyName === 'passedCount' ||
    //         item.propertyName === 'joinCount' ||
    //         item.propertyName === 'favoriteCount' ||
    //         item.propertyName === 'copyCount'
    //       ) {
    //         defaultFilter[`${item.propertyName}`] = `${item.operator},${item.value}`;
    //       } else if (item.propertyName === 'id') {
    //         defaultFilter[`${item.propertyName}`] = item.value.split(',');
    //       } else {
    //         defaultFilter[`${item.propertyName}`] = item.value;
    //       }
    //     }
    //   }
    //   this.setState({
    //     defaultFormData: defaultFilter
    //   });
    // }
    setTimeout(() => {
      sessionStorage.removeItem('query');
    }, 2000);
  }

  componentWillUnmount() {
    window.removeEventListener('beforeunload', this.beforeunload);
    localStorage.removeItem('campaignCache');
  }

  saveFavoriteInfo = async (id, userId) => {
    await campaignV2Service.saveUserOperationRecord({
      targetId: id,
      targetType: 'CAMPAIGN',
      type: 'FAVORITE',
      createUserId: userId,
      updateUserId: userId,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });

    this.queryListData(this.state.tableParams, this.state.activeKey);
    message.success(t('operationCenter-bQLQPqzpNyns'));
  };

  delCollect = async (val) => {
    const { id } = val;
    try {
      await campaignV2Service.delByCondition({
        targetId: id,
        targetType: 'CAMPAIGN',
        type: 'FAVORITE',
        loginId: this.state.userId
      });

      await campaignV2Service.saveUserOperationRecord({
        targetId: id,
        targetType: 'CAMPAIGN',
        type: 'RECENT',
        createUserId: this.state.userId,
        updateUserId: this.state.userId,
        createTime: dayjs().valueOf(),
        updateTime: dayjs().valueOf()
      });

      this.queryListData(this.state.tableParams, this.state.activeKey);
      message.success(t('operationCenter-YU9wgJdZ9WVb'));
    } catch (error) {
      console.error(error);
    }
  };

  onSearch = _.debounce(async (value) => {
    const res = await this.userService.findAllName({ name: value });
    const _elements = _.cloneDeep(this.state.elements);
    _elements['createUser.id'].componentOptions.options = _.map(res, (item) => ({
      key: item.id,
      value: item.id,
      text: item.name
    }));
    this.setState({
      scenarioOptions: res,
      elements: _elements
    });
  }, 500);

  onExecutorSearch = _.debounce(async (value) => {
    const res = await this.userService.findAllName({ name: value });
    const _elements = _.cloneDeep(this.state.elements);
    _elements.lastEnableUser.componentOptions.options = _.map(res, (item) => ({
      key: item.id,
      value: item.id,
      text: item.name
    }));
    this.setState({
      scenarioOptions: res,
      elements: _elements
    });
  }, 500);

  beforeunload() {
    localStorage.removeItem('recyclePage');
    localStorage.removeItem('campaignTabKey');
    localStorage.removeItem('campaignSelectKey');
    localStorage.removeItem('campaignCache');
  }

  // 筛选
  toggle() {
    const { isFold } = this.state;
    if (this.props.filterList.length && localStorage.getItem('campaignCache')) {
      const defaultFilter = {};
      for (const item of this.props.filterList) {
        if (item.value !== '' && item.propertyName !== 'operationTime') {
          defaultFilter[`${item.propertyName}`] = item.value;
        }
      }
      this.setState({
        defaultFormData: defaultFilter
      });
    }
    this.props.dispatch({ type: 'CAMPAIGN', isFold: !isFold });
    this.setState({ isFold: !isFold, isActivityFold: false });
  }

  onColumnActionClick = (key, feature, record) => {
    const _this = this;
    if (key === 'edit') {
      this.props.history.push(`/aimarketer/home/<USER>/create?id=${record.id}`);
    } else if (key === 'share' || key === 'cooperateManage') {
      this.dispatch(
        key === 'share'
          ? {
              shareOpen: true,
              shareInfo: { type: 'CAMPAIGN', id: record.id }
            }
          : {
              cooperateOpen: true,
              shareInfo: { type: 'CAMPAIGN', id: record.id }
            }
      );
    } else if (key === 'collect') {
      if (feature.text === t('operationCenter-tFSwCfzxQfmp')) {
        this.saveFavoriteInfo(record.id, this.state.userId);
      } else {
        this.delCollect(record);
      }
    } else if (key === 'delete') {
      confirm({
        content: (
          <div>
            <div className="text-[rgba(0,0,0,.65)]">
              {t('operationCenter-SoUb0tN7kveZ')}“{record.name}”
            </div>
            <div className="text-[rgba(0,0,0,.65)]">{t('operationCenter-aAwyWMgBh6oS')}</div>
          </div>
        ),
        okText: t('operationCenter-Tv2A9t9VUwCG'),
        okType: 'primary',
        className: 'recycle',
        title: t('operationCenter-Tv2A9t9VUwCG'),
        cancelText: t('operationCenter-tmjArFBnxtNP'),
        async onOk() {
          try {
            await campaignV2Service.delete({ id: record.id }, true);
            const page = calcPageNo(_this.state.totalCount, _this.state.tableParams.page, _this.state.tableParams.size);
            _this.setState(
              {
                tableParams: {
                  ..._this.state.tableParams,
                  page
                }
              },
              () => {
                setTimeout(() => {
                  _this.queryListData(_this.state.tableParams, _this.state.activeKey);
                  message.success(t('operationCenter-4yMNlDW3wZJE'), 1);
                }, 2000);
              }
            );
          } catch (error) {
            console.error(error);
          }
        }
      });
    } else if (key === 'reduction') {
      confirm({
        content: (
          <div className="text-[rgba(0,0,0,.65)]">
            {t('operationCenter-NoPr6HX79oSZ')}“{record.name}“{t('operationCenter-HxNRRrEMNkSB')}
          </div>
        ),
        okText: t('operationCenter-Mbe0bo4BXTee'),
        okType: 'primary',
        className: 'recycle',
        cancelText: t('operationCenter-tmjArFBnxtNP'),
        title: t('operationCenter-TSUugqNAF5kA'),
        async onOk() {
          try {
            await campaignV2Service.restore({ id: record.id });
            const page = calcPageNo(_this.state.totalCount, _this.state.tableParams.page, _this.state.tableParams.size);
            _this.setState(
              {
                tableParams: {
                  ..._this.state.tableParams,
                  page
                }
              },
              () => {
                setTimeout(() => {
                  _this.queryListData(_this.state.tableParams, _this.state.activeKey);
                  message.success(t('operationCenter-mro0EyHXpXfg'), 1);
                }, 2000);
              }
            );
          } catch (error) {
            console.error(error);
          }
        }
      });
    } else if (key === 'view') {
      this.props.history.push(`/aimarketer/home/<USER>/detail?id=${record.id}&recycleFlag=true`, {
        totalCount: _this.state.totalCount,
        page: _this.state.tableParams.page,
        size: _this.state.tableParams.size
      });
    }
  };

  // 子组件查询数组
  queryData(data, resetCurrentPage) {
    log.debug(t('operationCenter-lzMHodklRQsq'), data);
    if (!_.isEmpty(data)) {
      _.forEach(data, (value) => {
        if (value.value === undefined || value.value === false) {
          value.value = '';
        }
      });
    }

    const _tableParams = _.cloneDeep(this.state.tableParams);
    if (this.state.activeKey === 'ALL' || this.state.activeKey === 'MINE') {
      _tableParams.sorts = [{ direction: 'desc', propertyName: 'updateTime' }];
    } else {
      _tableParams.sorts = [{ direction: 'desc', propertyName: 'operationTime' }];
    }

    this.setState(
      {
        tableParams: {
          ..._tableParams,
          search: data,
          page: resetCurrentPage ? 1 : this.state.tableParams.page
        }
      },
      () => {
        this.queryListData(this.state.tableParams, this.state.activeKey);
      }
    );
  }

  // 隐藏modal
  hideModal() {
    this.setState({ visible: false });
  }

  // 选择分群方式
  selectCampaignV2Type(value) {
    localStorage.setItem('totalCount', this.state.totalCount);
    if (value.type === 'CUSTOM') {
      this.props.history.push(`/aimarketer/home/<USER>/create?type=${value.type}`);
    } else if (value.type === 'TELPLATE') {
      this.props.history.push('/aimarketer/home/<USER>/template');
      // this.props.history.push(`/aimarketer/home/<USER>/temList?type=${value.type}`);
    }
    // localStorage.setItem('campaign_type', value.type);
  }

  // 加载list数据
  queryListData = async (tableParams, key = 'RECENT') => {
    this.setState({
      loading: true
    });
    const _tableParams = _.cloneDeep(tableParams);
    let params;
    if (key === 'RECENT' || key === 'FAVORITE') {
      if (key === 'RECENT') {
        params = {
          ..._tableParams,
          search: [
            ..._tableParams.search,
            {
              propertyName: 'recordType',
              operator: 'IN',
              value: 'RECENT,FAVORITE'
            },
            {
              operator: 'EQ',
              propertyName: 'deptId',
              value: window.getDeptId()
            }
          ]
        };
      } else {
        params = {
          ..._tableParams,
          search: [
            ..._tableParams.search,
            { propertyName: 'recordType', operator: 'EQ', value: 'FAVORITE' },
            {
              operator: 'EQ',
              propertyName: 'deptId',
              value: window.getDeptId()
            }
          ]
        };
      }
    } else if (key === 'MINE') {
      _tableParams.search = [
        ...tableParams.search,
        {
          operator: 'EQ',
          propertyName: 'createUser.id',
          value: this.state.userId
        },
        {
          operator: 'EQ',
          propertyName: 'deptId',
          value: window.getDeptId()
        }
      ];
      // _tableParams.search = [...tableParams.search, { operator: 'EQ', propertyName: 'createUser.id', value: this.state.userId }];
      const index = _.findIndex(_tableParams.search, (v) => v.propertyName === 'operationTime');
      if (index !== -1) {
        _tableParams.search.splice(index, 1);
      }
      params = _tableParams;
    } else {
      _tableParams.search = [
        ...tableParams.search,
        {
          operator: 'EQ',
          propertyName: 'deptId',
          value: window.getDeptId()
        }
      ];
      // _tableParams.search = [...tableParams.search];
      const index = _.findIndex(_tableParams.search, (v) => v.propertyName === 'operationTime');
      if (index !== -1) {
        _tableParams.search.splice(index, 1);
      }
      params = _tableParams;
    }

    try {
      const { page } = params;
      localStorage.setItem('recyclePage', page || 1);
      // let data = {
      //   page,
      //   search,
      //   size,
      //   sorts
      // };
      // let res = await campaignV2Service.query2(data);

      const res = await campaignV2Service.queryRecyle(params);

      this.setState({
        dataList: res ? res.content : [],
        totalCount: res.totalElements,
        loading: false,
        tableParams
      });
    } catch (error) {
      this.setState({
        loading: false
      });
      // todo 8.2日 测试要求取消 弹窗只弹一个
      // message.error('加载失败，请重试', 1);
    }
  };

  // queryListData = async () => {
  //   this.setState({
  //     loading: true
  //   });
  //   try {
  //     const { page, size, search, sorts } = this.state.tableParams;
  //     localStorage.setItem('recyclePage', page || 1);
  //     let data = {
  //       page,
  //       search,
  //       size,
  //       sorts
  //     };
  //     let res = await campaignV2Service.query2(data);

  //     this.setState({ dataList: res.content, totalCount: res.totalElements, loading: false });
  //   } catch (error) {
  //     this.setState({
  //       loading: false
  //     });
  //     message.error('加载失败，请重试', 1);
  //   }
  // }

  /** 表格操作包含排序/翻页/每页显示条数 */
  handleTableChange = (page, filter, sorter) => {
    log.debug('goPage', page, filter, sorter);
    localStorage.setItem('recyclePage', page.current || 1);
    const { tableParams } = this.state;
    const { current, pageSize } = page;
    const direction = sorter.order ? sorter.order.substr(0, sorter.order.length - 3) : tableParams.sorts[0].direction;
    const propertyName =
      sorter.field && sorter.order
        ? Array.isArray(sorter.field)
          ? sorter.field.join('.')
          : sorter.field
        : tableParams.sorts[0].propertyName;

    if (sorter.order) {
      this.setState({
        sorterInfo: { columnKey: sorter.field, order: sorter.order }
      });
    } else {
      this.setState({ sorterInfo: { columnKey: null, order: null } });
    }

    this.setState(
      {
        tableParams: {
          ...tableParams,
          sorts: [{ direction, propertyName }],
          page: current,
          size: pageSize
        }
      },
      () => {
        this.queryListData(this.state.tableParams, this.state.activeKey);
      }
    );
  };

  // 切换活动概览的显示
  toggleActivity = () => {
    this.setState({
      isActivityFold: !this.state.isActivityFold,
      isFold: false
    });
  };

  viewGlobalActivityDetail = () => {
    this.props.history.push('/aimarketer/home/<USER>/detail/activityDetailV2');
  };

  onClickPhaseMetrics = (data) => {
    let search = _.cloneDeep(this.state.tableParams.search);
    const findIndex = _.findIndex(search, (item) => item.propertyName === 'phase');
    if (findIndex === -1) {
      search = [{ operator: 'EQ', propertyName: 'phase', value: data.phase }];
    } else {
      search[findIndex] = {
        operator: 'EQ',
        propertyName: 'phase',
        value: data.phase
      };
    }
    this.setState(
      {
        tableParams: {
          ...this.state.tableParams,
          search
        }
      },
      () => {
        this.queryListData(this.state.tableParams, this.state.activeKey);
      }
    );
  };

  onClickReach = (data) => {
    if (!_.isEmpty(data.directiveList)) {
      this.props.history.push({
        pathname: '/aimarketer/home/<USER>/detail/activityDetail',
        state: { data }
      });
    }
  };

  queryData1 = (data) => {
    try {
      const _filters = _.map(data, (item) => {
        if (
          item.propertyName === 'passedCount' ||
          item.propertyName === 'joinCount' ||
          item.propertyName === 'favoriteCount' ||
          item.propertyName === 'copyCount'
        ) {
          const valueArr = item.value.split(',');
          return {
            connector: 'AND',
            propertyName: item.propertyName,
            operator: valueArr[0] === '' ? 'EQ' : valueArr[0],
            value: valueArr.splice(1).toString()
          };
        }
        if (item.propertyName === 'id') {
          return {
            connector: 'AND',
            propertyName: item.propertyName,
            operator: item.operator,
            value: _.join(item.value, ',')
          };
        }
        if (item.operator === 'IN') {
          return {
            connector: 'AND',
            propertyName: item.propertyName,
            operator: item.operator,
            value: _.join(item.value, ',')
          };
        }
        if (item.propertyName !== 'operationTime' && item.operator === 'DATE_BETWEEN') {
          return {
            connector: 'AND',
            propertyName: item.propertyName,
            operator: item.operator,
            value: isArray(item.value)
              ? `${dayjs(item.value[0]).valueOf().toString()},${dayjs(item.value[1]).valueOf().toString()}`
              : item.value
            // value: item.value
          };
        }
        return {
          connector: 'AND',
          propertyName: item.propertyName,
          operator: item.operator,
          value: item.value
        };
      });

      _.forEach(_filters, (item) => {
        if (
          item.propertyName === 'passedCount' ||
          item.propertyName === 'joinCount' ||
          item.propertyName === 'favoriteCount' ||
          item.propertyName === 'copyCount'
        ) {
          const valueArr = item.value.split(',');
          if (item.operator === 'DATE_BETWEEN') {
            if (!valueArr[0] || !valueArr[1] || valueArr[0] === '' || valueArr[1] === '') {
              throw new Error(t('operationCenter-nvNd7Fg2y9RX'));
            }
          }
        } else if (item.propertyName === 'id') {
          const valueArr = item.value.split(',');
          _.forEach(valueArr, (item) => {
            if (_.isNaN(Number(item)) || item.indexOf(' ') > -1) {
              throw new Error(t('operationCenter-eb3D03wfL9A2'));
            } else if (item.length > 19) {
              throw new Error(t('operationCenter-f8yzgrbj1yZg'));
            }
          });
        }
      });

      if (this.state.activeKey === 'RECENT' && this.state.recentRange) {
        _filters.push({
          connector: 'AND',
          propertyName: 'operationTime',
          operator: 'DATE_BETWEEN',
          value: this.getRecentRange(this.state.recentRange)
        });
      }

      this.props.dispatch({
        type: 'marketActive',
        filterList: _filters,
        campaignIsFold: this.state.isFold
      });
      this.queryData(_filters, true);
    } catch (err) {
      message.error(err.message);
    }
  };

  getRecentRange = (key) => {
    let startTime;
    const endTime = dayjs().endOf('day').valueOf();
    if (key === 'WEEK') {
      startTime = dayjs().subtract(1, 'week').startOf('day').valueOf();
    } else if (key === 'MON') {
      startTime = dayjs().subtract(1, 'months').startOf('day').valueOf();
    } else if (key === 'SIXMON') {
      startTime = dayjs().subtract(6, 'months').startOf('day').valueOf();
    } else {
      startTime = dayjs().subtract(3, 'months').startOf('day').valueOf();
    }

    return `${startTime},${endTime}`;
  };

  saveCollect = async (val) => {
    const { id } = val;
    try {
      await campaignV2Service.saveUserOperationRecord({
        targetId: id,
        targetType: 'CAMPAIGN',
        type: 'FAVORITE',
        createUserId: this.state.userId,
        updateUserId: this.state.userId,
        createTime: dayjs().valueOf(),
        updateTime: dayjs().valueOf()
      });
      this.queryListData(this.state.tableParams, this.state.activeKey);
      message.success(t('operationCenter-bQLQPqzpNyns'));
    } catch (error) {
      console.error(error);
    }
  };

  delCollect = async (val) => {
    const { id } = val;
    try {
      await campaignV2Service.delByCondition({
        targetId: id,
        targetType: 'CAMPAIGN',
        type: 'FAVORITE',
        loginId: this.state.userId
      });

      await campaignV2Service.saveUserOperationRecord({
        targetId: id,
        targetType: 'CAMPAIGN',
        type: 'RECENT',
        createUserId: this.state.userId,
        updateUserId: this.state.userId,
        createTime: dayjs().valueOf(),
        updateTime: dayjs().valueOf()
      });

      this.queryListData(this.state.tableParams, this.state.activeKey);
      message.success(t('operationCenter-YU9wgJdZ9WVb'));
    } catch (error) {
      console.error(error);
    }
  };

  getFavoriteStatus = () => {
    // const res = await campaignV2Service.findCondition({
    //   targetId: val.id,
    //   type: 'FAVORITE',
    //   targetType: 'CAMPAIGN',
    //   loginId: this.state.userId
    // });
    // if (res) {
    //   this.setState({
    //     isCollect: true
    //   });
    // } else {
    //   this.setState({
    //     isCollect: false
    //   });
    // }
  };

  renderNoProcessJoinAndPassedCount = (record, type) => {
    if (record.phase === 'DRAFT' || record.phase === 'TESTING' || record.phase === 'TEST_SUC') {
      return t('operationCenter-Mzs9TB3Nh2kU');
    } else if (typeof type === 'undefined') {
      return t('operationCenter-Mzs9TB3Nh2kU');
    } else {
      return type;
    }
  };

  renderJoinAndPassedCount = (record, type) => {
    if (record.phase === 'DRAFT' || record.phase === 'TESTING' || record.phase === 'TEST_SUC') {
      return t('operationCenter-Mzs9TB3Nh2kU');
    } else if (typeof type === 'undefined') {
      return t('operationCenter-Mzs9TB3Nh2kU');
    } else if (
      record.approvalStatus &&
      (record.approvalStatus === 'PENDING' ||
        record.approvalStatus === 'RUNNING' ||
        record.approvalStatus === 'REJECT') &&
      record.phase === 'STOPPED'
    ) {
      return t('operationCenter-Mzs9TB3Nh2kU');
    } else {
      return type;
    }
  };

  render() {
    const { isFold, tableParams, loading, dataList, totalCount, isActivityFold, elements, sorterInfo, proessStatus } =
      this.state;
    const { page } = tableParams;

    const columns = [
      {
        title: 'ID',
        dataIndex: 'id',
        width: 100,
        sortOrder: sorterInfo.columnKey === 'id' ? sorterInfo.order : null,
        sorter: true,
        fixed: 'left'
      },
      {
        title: t('operationCenter-YM8YGxy7zX2s'),
        dataIndex: 'name',
        render: (text, val) => {
          if (val.phase === 'DRAFT') {
            return (
              <Tooltip title={text}>
                <span>{text}</span>
              </Tooltip>
            );
          } else {
            return (
              <Tooltip title={text}>
                <a
                  onClick={() =>
                    this.props.history.push(`/aimarketer/home/<USER>/detail?id=${val.id}&recycleFlag=true`, {
                      totalCount: this.state.totalCount,
                      page: this.state.tableParams.page,
                      size: this.state.tableParams.size
                    })
                  }
                >
                  <span>{text}</span>
                </a>
              </Tooltip>
            );
          }
        },
        // ellipsis: true,
        width: 320,
        height: 100,
        fixed: 'left'
      },
      {
        title: t('operationCenter-M3GUYWcHt703'),
        dataIndex: ['scenario', 'name'],
        width: 160,
        ellipsis: {
          showTitle: false
        },
        render: (text) => (
          <Tooltip placement="topLeft" title={text}>
            {text}
          </Tooltip>
        )
      },
      {
        title: t('operationCenter-IOGJslDz6P3w'),
        dataIndex: 'campaignType',
        render: (text) => (text ? _.filter(typeList, (v) => v.value === text)[0]?.name : '-'),
        width: 150
      },
      {
        title: t('operationCenter-PUDMoFD9vyOu'),
        width: 150,
        dataIndex: 'joinCount',
        render: (text, record) => (
          <span>
            {proessStatus && proessStatus === 'ENABLE'
              ? this.renderJoinAndPassedCount(record, record.joinCount)
              : this.renderNoProcessJoinAndPassedCount(record, record.joinCount)}
          </span>
        )
      },
      {
        title: t('operationCenter-EOLMkV5hd9l2'),
        width: 150,
        dataIndex: 'passedCount',
        render: (text, record) => (
          <span>
            {proessStatus && proessStatus === 'ENABLE'
              ? this.renderJoinAndPassedCount(record, record.passedCount)
              : this.renderNoProcessJoinAndPassedCount(record, record.passedCount)}
          </span>
        )
      },
      // {
      //   title: '进入人数',
      //   width: 150,
      //   dataIndex: 'joinCount',
      //   render: (text, record) => <span>{typeof (record.passedCount) === 'undefined' ? record.phase === 'ENABLE' ? '待计算' : '暂无数据' : record.processInstance && (record.processInstance.status === 'DRAFT' || record.processInstance.status === 'RUNNING' || record.processInstance.status === 'REJECT' || record.processInstance.status === 'CANCEL') && record.phase === 'STOPPED' ? '暂无数据' : record.processInstance ? record.passedCount : '暂无数据'}</span>
      // },
      // {
      //   title: '进入人次',
      //   width: 150,
      //   dataIndex: 'passedCount',
      //   render: (text, record) => <span>{typeof (record.passedCount) === 'undefined' ? record.phase === 'ENABLE' ? '待计算' : '暂无数据' : record.processInstance && (record.processInstance.status === 'DRAFT' || record.processInstance.status === 'RUNNING' || record.processInstance.status === 'REJECT' || record.processInstance.status === 'CANCEL') && record.phase === 'STOPPED' ? '暂无数据' : record.processInstance ? record.passedCount : '暂无数据'}</span>
      // },
      // {
      //   title: '流程目标',
      //   dataIndex: 'campaignTarget',
      //   ellipsis: true,
      //   width: 200
      // },
      {
        title: t('operationCenter-oL2uEkjObqec'),
        dataIndex: 'phase',
        render: (text) => {
          return (
            <div className="status">
              {/* <span className="circle" style={{ backgroundColor: _.filter(phaseList, (v) => v.value === text)[0]?.color }} /> */}
              {text ? _.filter(phaseList, (v) => v.value === text)[0]?.name : '-'}
            </div>
          );
        },
        sortOrder: sorterInfo.columnKey === 'phase' ? sorterInfo.order : null,
        sorter: true,
        width: 110
      },
      // {
      //   title: '最后批次运行状态',
      //   dataIndex: 'lastCalcStatus',
      //   render: (text) => <div className="status">
      //     <span className="circle" style={{ backgroundColor: _.filter(lastCalcStatus, (v) => v.value === text)[0]?.color }} />
      //     {text ? _.filter(lastCalcStatus, (v) => v.value === text)[0]?.name : '-'}
      //   </div>,
      //   sorter: true,
      //   width: 200
      // },
      {
        title: t('operationCenter-g5Wt1ET6WzCd'),
        dataIndex: 'processStatus',
        render: (text, record) => (record.approvalStatus ? proessStatusList[record.approvalStatus] : '-'),
        width: 150
      },
      {
        title: t('operationCenter-PG7tqyTZk4oR'),
        dataIndex: 'calcRule',
        render: (text) => (text ? _.filter(ruleList, (v) => v.value === text)[0].name : '-'),
        width: 110
      },
      {
        title: t('operationCenter-egVl6W5YNPTb'),
        dataIndex: 'favoriteCount',
        width: 110,
        render: (val) => val || 0,
        sorter: true,
        sortOrder: sorterInfo.columnKey === 'favoriteCount' ? sorterInfo.order : null
      },
      {
        title: t('operationCenter-AxjdNmKN2aRp'),
        dataIndex: 'copyCount',
        width: 110,
        render: (val) => val || 0,
        sorter: true,
        sortOrder: sorterInfo.columnKey === 'copyCount' ? sorterInfo.order : null
      },
      {
        title: t('operationCenter-qtHd5rczU4ou'),
        dataIndex: 'beginTime',
        render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
        width: 180,
        sortOrder: sorterInfo.columnKey === 'beginTime' ? sorterInfo.order : null,
        sorter: true
      },
      {
        title: t('operationCenter-6dyKUbDbwQkN'),
        dataIndex: 'endTime',
        render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
        width: 180,
        sortOrder: sorterInfo.columnKey === 'endTime' ? sorterInfo.order : null,
        sorter: true
      },
      {
        title: t('operationCenter-0Y2qZNzHxnWj'),
        width: 150,
        dataIndex: 'createUserName'
      },
      {
        title: t('operationCenter-gUiBOHIOhOYW'),
        dataIndex: 'createTime',
        width: 180,
        render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
        sortOrder: sorterInfo.columnKey === 'createTime' ? sorterInfo.order : null,
        sorter: true
      },
      {
        title: t('operationCenter-9y8sh1Wwzhdc'),
        width: 150,
        dataIndex: 'updateUserName'
      },
      {
        title: t('operationCenter-Ixk6tkHxTIGB'),
        dataIndex: 'updateTime',
        width: 180,
        render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
        sortOrder: sorterInfo.columnKey === 'updateTime' ? sorterInfo.order : null,
        sorter: true
      },
      {
        title: t('operationCenter-vkgIfK79ZDwd'),
        width: 150,
        dataIndex: 'updateUserName'
      },
      {
        title: t('operationCenter-TdiJuDkbXjmo'),
        dataIndex: 'updateTime',
        width: 180,
        render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
        sortOrder: sorterInfo.columnKey === 'updateTime' ? sorterInfo.order : null,
        sorter: true
      },
      {
        title: t('operationCenter-I1zpxZOIYlWp'),
        width: 150,
        dataIndex: 'lastEnableUserName',
        render: (text, record) => {
          if (record.phase === 'ENABLE' || record.phase === 'STOPPED') {
            return text || '-';
          } else {
            return '-';
          }
        }
      },
      {
        title: t('operationCenter-IY5qBMCxcw8R'),
        dataIndex: 'lastEnableTime',
        width: 180,
        // render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
        render: (text, record) => {
          if (record.phase === 'ENABLE' || record.phase === 'STOPPED') {
            return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
          } else {
            return '-';
          }
        },
        sortOrder: sorterInfo.columnKey === 'lastEnableTime' ? sorterInfo.order : null,
        sorter: true
      },
      {
        title: t('operationCenter-7IJ5KypPMDsD'),
        className: 'td-set',
        width: 120,
        fixed: 'right',
        render: (text, record) => {
          const featureData = {
            dropdownData: {
              view: {
                text: t('operationCenter-qN4Uv5SMyd4x'),
                code: 'aim_campaignV2_recycle'
              },
              delete: {
                text: t('operationCenter-Vu7jz3gRPFsw'),
                code: 'aim_campaignV2_recycle'
              },
              reduction: {
                text: t('operationCenter-TSUugqNAF5kA'),
                code: 'aim_campaignV2_recycle'
              }
            }
          };
          return (
            <ColumnActionCom
              closeDropdown
              featureData={featureData}
              record={record}
              onClick={this.onColumnActionClick}
            />
          );
        }
      }
    ];

    // const recentDomSet = () => {
    //   return (
    //     <Menu onClick={onRecentMenuChange} selectedKeys={this.state.recentSelectKey}>
    //       <Menu.Item key="WEEK">一周</Menu.Item>
    //       <Menu.Item key="MON">一个月</Menu.Item>
    //       <Menu.Item key="TREEEMON">三个月</Menu.Item>
    //       <Menu.Item key="SIXMON">六个月</Menu.Item>
    //     </Menu>
    //   );
    // };

    return (
      <div className="campaignV2">
        <div className="mt-[16px]">
          <Breadcrumb>
            <Breadcrumb.Item>
              <Link to="/aimarketer/home/<USER>">{t('operationCenter-dEyHpLRb5cS6')}</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>{t('operationCenter-NfRy8TTj4FVS')}</Breadcrumb.Item>
          </Breadcrumb>
        </div>
        <header style={{ paddingTop: 8 }}>
          <h1>{t('operationCenter-NfRy8TTj4FVS')}</h1>
          <div className="btnGroup">
            <Button onClick={this.toggle}>
              <FilterOutlined />
              {t('operationCenter-pXReQJs1rb2J')}
            </Button>
          </div>
        </header>
        {isFold ? (
          <QueryForList
            show
            elements={elements}
            onQuery={this.queryData1}
            defaultFormData={this.state.defaultFormData}
          />
        ) : null}
        {isActivityFold ? (
          <ActivityBoard onClickPhaseMetrics={this.onClickPhaseMetrics} onClickReach={this.onClickReach} />
        ) : null}

        <div className="table1">
          <Table
            // columns={this.state.proessStatus && this.state.proessStatus === 'ENABLE' ? processColumns : columns}
            columns={columns.filter((item) => {
              if (
                item.dataIndex === 'processStatus' &&
                this.state.proessStatus &&
                this.state.proessStatus === 'DISABLE'
              )
                return false;
              return true;
            })}
            dataSource={dataList}
            bordered={false}
            rowKey={(record) => record.id}
            loading={loading}
            scroll={{ x: 1280 }}
            onChange={this.handleTableChange}
            pagination={{
              current: page,
              total: totalCount,
              defaultPageSize: 10,
              showQuickJumper: true,
              showSizeChanger: true,
              showLessItems: true,
              pageSizeOptions: ['10', '20', '50'],
              showTotal: (e) => `t('operationCenter-XhW8GbvcXPVW') ${e} t('operationCenter-BHVgk8pqLoxp')`
            }}
          />
        </div>
      </div>
    );
  }
}

export default withStore(connect(stateToProps)(List));
