.dimensionItem {
  border-radius: 6px;
  border: 1px solid #e9e9e9;
  margin-bottom: 16px;

  .stepTitle {
    padding: 0;
  }
  
  &:hover {
    .icons {
      display: block;
    }
  }

  &>div {
    // height: 40px;
    line-height: 40px;
    border-radius: 6px;
  }

  .option {
    display: flex;
    justify-content: space-between;

    &>div:nth-child(2) {
      display: none;
      margin-right: 20px;
      cursor: pointer;
    }

    &:hover {
      &>div:nth-child(2) {
        display: block;
      }
    }
  }
}

.dimensionTitle {
  display: flex;
  height: 36px;

  .mouse {
    margin: 0px 8px;
    font-size: 16px;
  }

  .dimensionContent {
    width: calc(100% - 60px);
    padding-left: 6px;
    align-items: center;
    cursor: pointer;

    &:hover {
      background-color: $active_color;
      border-radius: 6px;
    }
  }

  .icons {
    width: 20px;
    display: none;
    cursor: pointer;
  }
}