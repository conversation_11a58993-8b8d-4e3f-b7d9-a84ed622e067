/*
 * @Author: Wxw
 * @Date: 2022-07-26 11:04:29
 * @LastEditTime: 2023-02-23 11:38:45
 * @LastEditors: 卜广硕 <EMAIL>
 * @Description:维度分组
 * @FilePath: \Front\src\pages\home\analysisCenter\funnelAnalysis\dimension\index.jsx
 */
import { Button, message } from 'antd';
import _ from 'lodash';
import React, { useContext } from 'react';
import DimensionItem from './dimensionItem';
// import icon from '../icon';

import { t } from 'utils/translation';
import { eventAnalysisContext } from '../eventAnalysisContext';

export default function Dimension() {
  const { state, dispatch } = useContext(eventAnalysisContext);
  const { dimensionGroup, isMultiStep } = state;

  const addDimensionGroup = () => {
    if (dimensionGroup.length >= 1) {
      return message.warning(t('analysisCenter-MvHKoqLyAIP9'));
    }
    const _dimensionGroup = _.cloneDeep(dimensionGroup);
    _dimensionGroup.push({});
    dispatch({ dimensionGroup: _dimensionGroup });
  };

  return (
    <eventAnalysisContext.Provider value={{ state, dispatch }}>
      {dimensionGroup &&
        dimensionGroup.map((item, index) => {
          return <DimensionItem key={index} dimensionIndex={index} value={dimensionGroup} />;
        })}
      <Button
        disabled={isMultiStep}
        className="addButton"
        type="dashed"
        block
        onClick={addDimensionGroup}
        hidden={dimensionGroup.length >= 1}
      >
        +{t('analysisCenter-cFCj1ZnpT77J')}
      </Button>
    </eventAnalysisContext.Provider>
  );
}
