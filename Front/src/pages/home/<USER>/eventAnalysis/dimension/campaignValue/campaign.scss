.renderCampaign {
  .campaignTableBox {
    display: flex;

    .table1{
      max-height: 447px;
      overflow-y: auto;
      margin-bottom: 10px;
    }

    .ant-table-wrapper {
      margin-top: 16px;
    }

    .ant-input-search {
      padding: 0 2px;
    }

    .batch {
      width: 640px;
      margin-left: 16px;

      .batchTitle {
        font-size: 16px;
        font-weight: bold;
      }

      .ant-popover {
        background-color: #fff;
        // position: relative;

        .ant-popover-inner-content {
          padding: 12px 0;

          .popoverBatch {

            .flowNodeTitle {
              margin: 0 0 17px 16px;

              span {
                margin-left: 8px;
                color: rgba(0, 0, 0, 0.45);
              }
            }

            .flowNode {
              padding: 0 16px;
              margin-bottom: 16px;

              .flowNodeItem {
                border-radius: 4px;
                background: #fafafa;
                padding: 1px 8px;
                border: 1px solid #E9E9E9;
                display: inline-block;
                white-space: nowrap;
                margin-right: 4px;
                margin: 0 4px 4px 0px;
                // width: 106px;
                height: 24px;
              }
            }

            .renderFlow {
              width: 700px;
              height: 400px;

              // background: red;
            }

            .buttons {
              // width: 100%;
              // height: 40px;
              // background: #fff;
              // z-index: 2000;
              // position: absolute;
              // bottom: 0;
              // right: 0;

              .ant-btn {
                display: flex;
                margin-right: 10px;
              }
            }
          }
        }
      }
    }
  }

  .buttons {
    display: flex;
    justify-content: flex-end;

    // .ant-btn {
    //   border-radius: 6px;
    // }

    .ant-btn:first-child {
      margin-right: 10px;
    }
  }
}