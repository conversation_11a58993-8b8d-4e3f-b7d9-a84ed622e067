import { Button, Input, Table, Tabs, Typography, message } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useContext, useEffect, useRef, useState } from 'react';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { useDebounce } from 'utils/customhooks';
import './campaign.scss';
// import icon from '../icon';
import { t } from 'utils/translation';
import { eventAnalysisContext } from '../../eventAnalysisContext';
import RenderBatch from './renderBatch';

const { TabPane } = Tabs;
const { Search } = Input;
const { Text } = Typography;

const phaseList = [
  {
    name: t('analysisCenter-sRSLa8jPKG1Q'),
    text: t('analysisCenter-sRSLa8jPKG1Q'),
    value: 'DRAFT',
    key: 'DRAFT',
    color: 'rgba(0, 0, 0, 0)'
  },
  {
    name: t('analysisCenter-632bzUXI5YYh'),
    text: t('analysisCenter-632bzUXI5YYh'),
    value: 'TESTING',
    key: 'TESTING',
    color: '#52c41a'
  },
  {
    name: t('analysisCenter-0EgEhecCMUQy'),
    text: t('analysisCenter-0EgEhecCMUQy'),
    value: 'TEST_SUC',
    key: 'TEST_SUC',
    color: '#1890ff'
  },
  {
    name: t('analysisCenter-ENOvzPED2xJw'),
    text: t('analysisCenter-ENOvzPED2xJw'),
    value: 'ENABLE',
    key: 'ENABLE',
    color: '#f5222d'
  },
  {
    name: t('analysisCenter-2lCRlKUIqt8w'),
    text: t('analysisCenter-2lCRlKUIqt8w'),
    value: 'STOPPED',
    key: 'STOPPED',
    color: '#faad14'
  }
];

/**
 * @description: 维度分组-营销活动
 * @param {*} type 营销活动选择的类型
 * @param {*} setVisible 关闭弹窗
 * @return {*}
 */
export default function RenderCampaign({ type, setVisible }) {
  const { state, dispatch, dimensionIndex } = useContext(eventAnalysisContext);
  const { scenarioId, dimensionGroup, scenarioList } = state;
  const [tabsKey, setTabsKey] = useState(0);
  const [activeKey, setActiveKey] = useState(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [loading, setLoading] = useState(false);
  const [campaignTable, setCampaignTable] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [editDimensionGroup, setEditDimensionGroup] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    search: [
      {
        connector: 'AND',
        propertyName: 'phase',
        operator: 'IN',
        value: 'ENABLE,STOPPED'
      },
      {
        connector: 'AND',
        propertyName: 'scenario.code',
        operator: 'EQ',
        value: (scenarioList && scenarioList.find((i) => i.id === scenarioId)?.code) || ''
      },
      {
        operator: 'EQ',
        propertyName: 'deptId',
        value: window.getDeptId()
      }
    ],
    size: 10,
    sorts: [{ direction: 'desc', propertyName: 'id' }]
  });
  const columns = [
    {
      title: t('analysisCenter-M3jaGOyxL01B'),
      dataIndex: 'name',
      render: (text) => {
        return (
          <Text style={{ width: '170px' }} ellipsis={{ tooltip: text }}>
            {text}
          </Text>
        );
      },
      width: 170,
      height: 100
    },
    {
      title: t('analysisCenter-GkUGqyz7WYvB'),
      dataIndex: 'phase',
      render: (text) => {
        return <div className="status">{text ? _.filter(phaseList, (v) => v.value === text)[0]?.name : '-'}</div>;
      },
      width: 120
    },
    {
      title: t('analysisCenter-2GTwYXF4JDtt'),
      dataIndex: 'beginTime',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 200
    },
    {
      title: t('analysisCenter-6b7u1nlEQzOe'),
      dataIndex: 'endTime',
      render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
      width: 200
    }
  ];

  const unqieConcat = (arr) => {
    const _arr = _.cloneDeep(arr);
    if (_arr[0].type === 'CAMPAIGN' && _arr[0].filters[0].campaignGroup.filters.length) {
      const result = Object.values(
        _arr[0].filters[0].campaignGroup.filters.reduce((acc, item) => {
          // const result = Object.values(arr[findRes].campaignGroup.filters.reduce((acc, item) => {
          if (!acc[item.id]) {
            acc[item.id] = { ...item };
          } else {
            acc[item.id].logList = [...acc[item.id].logList, ...item.logList];
          }
          return acc;
        }, {})
      );
      _arr[0].filters[0].campaignGroup.filters = result;
      return _arr;
    } else {
      return arr;
    }
  };

  useEffect(() => {
    const ini = async () => {
      const _dimensionGroup = _.cloneDeep(unqieConcat(dimensionGroup));
      const campaignList = _dimensionGroup[dimensionIndex].filters[0].campaignGroup.filters;
      // 回显
      if (campaignList) {
        const defaultSelectedRowKeys = [];
        setActiveKey(`${campaignList[0]?.id}`);
        _.forEach(campaignList, (v) => defaultSelectedRowKeys.push(v.id));
        setSelectedRowKeys(defaultSelectedRowKeys);
        setEditDimensionGroup(campaignList);
      }
      setLoading(true);
    };
    ini();
    return () => {
      localStorage.removeItem('popOverPage');
    };
  }, []);

  useEffect(() => {
    init();
  }, [pagination]);

  const init = async () => {
    setLoading(true);
    const campaignTable = await FunnelAnalysis.query2(pagination);
    setCampaignTable(campaignTable.content);
    setTotalCount(campaignTable.totalElements);
    setLoading(false);
  };

  const handleTableChange = (page) => {
    setPagination({
      ...pagination,
      page: page.current,
      size: page.pageSize
    });
  };

  const selectTable = (rowValue, isTrue) => {
    const _selectedRowKeys = _.cloneDeep(selectedRowKeys);
    let campaignList = editDimensionGroup;
    if (isTrue) {
      campaignList.push(rowValue);
      setEditDimensionGroup(campaignList);
      setSelectedRowKeys(_.concat(_selectedRowKeys, rowValue.id));
      setActiveKey(`${rowValue.id}`);
    } else {
      campaignList = _.filter(campaignList, (v) => v.id !== rowValue.id);
      setEditDimensionGroup(campaignList);
      const newSelectRowKeys = _.without(_selectedRowKeys, rowValue.id);
      setSelectedRowKeys(newSelectRowKeys);
      setActiveKey(`${newSelectRowKeys[newSelectRowKeys.length - 1]}`);
    }
  };

  const changeTabs = (e) => {
    localStorage.setItem('popOverPage', '1');
    setActiveKey(e);
  };

  const saveSteps = () => {
    try {
      if (_.isEmpty(editDimensionGroup)) throw new Error(t('analysisCenter-mv4rZfevCjCz'));
      _.forEach(editDimensionGroup, (i) => {
        if (_.isEmpty(i.logList)) throw new Error(t('analysisCenter-qXeV8Gboi6FF'));
        if (type === 'CAMPAIGN_NODE')
          if (_.isEmpty(i.logList[0].selectedFlows)) throw new Error(t('analysisCenter-kxdUF9qpEzpw'));
      });
      const _dimensionGroup = _.cloneDeep(dimensionGroup);
      _dimensionGroup[dimensionIndex].filters[0].campaignGroup.filters = editDimensionGroup;
      dispatch({ dimensionGroup: _dimensionGroup });
      setVisible(false);
    } catch (err) {
      message.error(err.message);
    }
  };

  const searchRef = useRef();

  const changeSearchName = useDebounce(() => {
    const getValue = async () => {
      let pageSearchName = _.cloneDeep(pagination);
      const config = {
        connector: 'AND',
        propertyName: 'name',
        operator: 'LIKE',
        value: searchRef.current.input.value
      };
      const searchConfig = pageSearchName.search;
      if (searchConfig.length === 3) {
        searchConfig.push(config);
      } else {
        searchConfig[searchConfig.length - 1] = config;
      }
      pageSearchName = {
        ...pageSearchName,
        page: 1,
        search: searchConfig
      };
      setPagination(pageSearchName);
    };
    getValue();
  }, 400);

  return (
    <div className="renderCampaign">
      <eventAnalysisContext.Provider value={{ state, dispatch, editDimensionGroup, setEditDimensionGroup }}>
        <div className="campaignTableBox">
          <div className="table1">
            <Search
              placeholder={t('analysisCenter-jE4LSpPiLN9w')}
              allowClear
              onChange={changeSearchName}
              ref={searchRef}
            />
            <Table
              size="middle"
              columns={columns}
              dataSource={campaignTable || []}
              bordered={false}
              rowKey={(record) => record.id}
              loading={loading}
              rowSelection={{
                selectedRowKeys,
                // onChange: onSelectChange,
                onSelect: selectTable,
                hideSelectAll: true
              }}
              onChange={handleTableChange}
              pagination={{
                current: pagination.page,
                total: totalCount,
                defaultPageSize: 10,
                showQuickJumper: true,
                showSizeChanger: true,
                showLessItems: true,
                pageSizeOptions: ['10', '20', '50'],
                showTotal: (e) => `${t('analysisCenter-tl4fJswMkTr8')} ${e} ${t('analysisCenter-tnaSZzXiIyw6')}`
              }}
            />
          </div>
          {!_.isEmpty(selectedRowKeys) && (
            <div className="batch">
              <div className="batchTitle">
                {type === 'CAMPAIGN_BATCH' ? t('analysisCenter-Eo6Oyb4HpwTX') : t('analysisCenter-DG6shTjpA7bp')}
              </div>
              <div className="batchContent">
                <Tabs
                  key={tabsKey}
                  hideAdd
                  // destroyInactiveTabPane
                  activeKey={activeKey}
                  onChange={changeTabs}
                >
                  {editDimensionGroup.map((item) => {
                    return (
                      <TabPane tab={item.name} key={`${item.id}`} id={item.id}>
                        <RenderBatch
                          setTabsKey={setTabsKey}
                          batchValue={item}
                          editDimensionGroup={editDimensionGroup}
                          type={type}
                        />
                      </TabPane>
                    );
                  })}
                </Tabs>
              </div>
            </div>
          )}
        </div>
      </eventAnalysisContext.Provider>
      <div className="buttons">
        {/* <Button>取消</Button> */}
        <Button type="primary" onClick={saveSteps}>
          {t('analysisCenter-0wgB5xxJYP8b')}
        </Button>
      </div>
    </div>
  );
}
