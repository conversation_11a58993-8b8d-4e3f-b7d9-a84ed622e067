/*
 * @Author: Wxw
 * @Date: 2022-07-26 11:04:29
 * @LastEditTime: 2023-04-20 18:30:56
 * @LastEditors: 卜广硕 <EMAIL>
 * @Description:漏斗流程步骤-营销活动
 * @FilePath: \Front\src\pages\home\analysisCenter\funnelAnalysis\steps\campaign.jsx
 */
import { Button, Input, Table, Tabs, Typography, message } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useContext, useEffect, useRef, useState } from 'react';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { useDebounce } from 'utils/customhooks';
import './campaign.scss';
// import icon from '../icon';
import { eventAnalysisContext } from '../eventAnalysisContext';
import RenderBatch from './renderBatch';

const { Search } = Input;
const { Text } = Typography;

const phaseList = [
  {
    name: '草稿',
    text: '草稿',
    value: 'DRAFT',
    key: 'DRAFT',
    color: 'rgba(0, 0, 0, 0)'
  },
  {
    name: '测试中',
    text: '测试中',
    value: 'TESTING',
    key: 'TESTING',
    color: '#52c41a'
  },
  {
    name: '测试完成',
    text: '测试完成',
    value: 'TEST_SUC',
    key: 'TEST_SUC',
    color: '#1890ff'
  },
  {
    name: '流程启用',
    text: '流程启用',
    value: 'ENABLE',
    key: 'ENABLE',
    color: '#f5222d'
  },
  {
    name: '流程结束',
    text: '流程结束',
    value: 'STOPPED',
    key: 'STOPPED',
    color: '#faad14'
  }
];

/**
 * @description: 营销活动步骤过滤
 * @param {*} id
 * @param {*} stepIndex
 * @param {*} isMulti
 * @param {*} ind
 * @param {*} flag 是否是全局过滤
 * @param {*} filterIndex 过滤下标
 * @return {*}
 */
export default function RenderCampaign({ id, stepIndex, isMulti, ind, flag, filterIndex, isFilter, type, setVisible }) {
  const { state, dispatch, editSteps, setEditSteps } = useContext(eventAnalysisContext);
  const { stepList, scenarioId, globalFilters, scenarioList } = state;
  const [tabsKey, setTabsKey] = useState(0);
  const [activeKey, setActiveKey] = useState(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [loading, setLoading] = useState(false);
  const [campaignTable, setCampaignTable] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [editGlobalFilter, setEditGlobalFilter] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    search: [
      {
        connector: 'AND',
        propertyName: 'phase',
        operator: 'IN',
        value: 'ENABLE,STOPPED'
      },
      {
        connector: 'AND',
        propertyName: 'scenario.code',
        operator: 'EQ',
        value: (scenarioList && scenarioList.find((i) => i.id === scenarioId)?.code) || ''
      },
      {
        operator: 'EQ',
        propertyName: 'deptId',
        value: window.getDeptId()
      }
    ],
    size: 10,
    sorts: [{ direction: 'desc', propertyName: 'id' }]
  });
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      render: (text) => {
        return (
          <Text style={{ width: '170px' }} ellipsis={{ tooltip: text }}>
            {text}
          </Text>
        );
      },
      width: 170,
      height: 100
    },
    {
      title: '阶段',
      dataIndex: 'phase',
      render: (text) => {
        return <div className="status">{text ? _.filter(phaseList, (v) => v.value === text)[0]?.name : '-'}</div>;
      },
      width: 80
    },
    {
      title: '期限(开始)',
      dataIndex: 'beginTime',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 160
    },
    {
      title: '期限(结束)',
      dataIndex: 'endTime',
      render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
      width: 160
    }
  ];

  const unqieConcat = (arr) => {
    const _arr = _.cloneDeep(arr);
    const findRes = _.findIndex(_arr, (e) => _.find(Object.keys(e), (o) => o === 'campaignGroup'));

    if (findRes !== -1) {
      const result = Object.values(
        arr[findRes].campaignGroup.filters.reduce((acc, item) => {
          if (!acc[item.id]) {
            acc[item.id] = { ...item };
          } else {
            acc[item.id].logList = [...acc[item.id].logList, ...item.logList];
          }
          return acc;
        }, {})
      );

      _arr[findRes] = {
        connector: _arr[findRes].connector,
        campaignGroup: {
          ..._arr[findRes].campaignGroup,
          filters: result
        }
      };
      return _arr;
    } else {
      return arr;
    }
  };

  const stepUnqieConcat = (arr) => {
    const _arr = _.cloneDeep(arr);
    _arr.map((item) => {
      const findRes = _.findIndex(item.filters, (e) => _.find(Object.keys(e), (o) => o === 'campaignGroup'));
      if (findRes !== -1) {
        const result = Object.values(
          item.filters[findRes].campaignGroup.filters.reduce((acc, item) => {
            if (!acc[item.id]) {
              acc[item.id] = { ...item };
            } else {
              acc[item.id].logList = [...acc[item.id].logList, ...item.logList];
            }
            return acc;
          }, {})
        );

        item.filters[findRes] = {
          connector: item.filters[findRes].connector,
          campaignGroup: {
            ...item.filters[findRes].campaignGroup,
            filters: result
          }
        };
        return item.filters;
      } else {
        return item.filters;
      }
    });
    return _arr;
  };

  useEffect(() => {
    const ini = async () => {
      if (flag && isFilter) {
        const _globalFilters = _.cloneDeep(unqieConcat(globalFilters));
        const campaignList = _globalFilters[filterIndex]?.campaignGroup.filters;
        if (campaignList) {
          const defaultSelectedRowKeys = [];
          setActiveKey(`${campaignList[0]?.id}`);
          _.forEach(campaignList, (v) => defaultSelectedRowKeys.push(v.id));
          setSelectedRowKeys(defaultSelectedRowKeys);
          setEditGlobalFilter(campaignList);
        }
      } else if (isFilter) {
        const _steps = _.cloneDeep(stepUnqieConcat(stepList));
        const campaignList = _steps[stepIndex].filters[filterIndex]?.campaignGroup.filters;
        if (campaignList) {
          const defaultSelectedRowKeys = [];
          setActiveKey(`${campaignList[0]?.id}`);
          _.forEach(campaignList, (v) => defaultSelectedRowKeys.push(v.id));
          setSelectedRowKeys(defaultSelectedRowKeys);
          setEditGlobalFilter(campaignList);
        }
      } else {
        const _steps = _.cloneDeep(stepList);
        const campaignList = isMulti
          ? _steps[stepIndex].multiStepList[ind].campaignList
          : _steps[stepIndex].campaignList;
        // 回显
        if (campaignList) {
          const defaultSelectedRowKeys = [];
          setActiveKey(`${campaignList[0]?.id}`);
          _.forEach(campaignList, (v) => defaultSelectedRowKeys.push(v.id));
          setSelectedRowKeys(defaultSelectedRowKeys);
        }
      }
      setLoading(true);
    };
    ini();
    return () => {
      localStorage.removeItem('popOverPage');
    };
  }, []);

  useEffect(() => {
    init();
  }, [pagination]);

  const init = async () => {
    setLoading(true);
    const campaignTable = await FunnelAnalysis.query2(pagination);
    setCampaignTable(campaignTable.content);
    setTotalCount(campaignTable.totalElements);
    setLoading(false);
  };

  const handleTableChange = (page) => {
    setPagination({
      ...pagination,
      page: page.current,
      size: page.pageSize
    });
  };

  // const onSelectChange = (newSelectedRowKeys, List) => {
  // console.log(newSelectedRowKeys, List);
  // let _steps = _.cloneDeep(editSteps);
  // let findIndex = _.findIndex(_steps, { step: id });
  // let list = isMulti ? _steps[findIndex].multiStepList[ind].campaignList : _steps[findIndex].campaignList;
  // let campaignList = [];
  // _.forEach(List, (v) => {
  //   if (_.find(list, { id: v.id })) {
  //     campaignList.push(_.find(list, { id: v.id }));
  //   } else {
  //     campaignList.push(v);
  //   }
  // });
  // if (isMulti) {
  //   _steps[findIndex].multiStepList[ind] = {
  //     step: _steps[findIndex].multiStepList[ind].step,
  //     campaignList,
  //     type: 'CAMPAIGN'
  //   };
  //   setEditSteps(_steps);
  // } else {
  //   _steps[findIndex] = {
  //     step: id,
  //     type: 'CAMPAIGN',
  //     campaignList
  //   };
  //   setEditSteps(_steps);
  // }
  // setActiveKey(`${newSelectedRowKeys[newSelectedRowKeys.length - 1]}`);
  // setSelectedRowKeys(newSelectedRowKeys);
  // dispatch({})
  // };

  const selectTable = (rowValue, isTrue) => {
    const _selectedRowKeys = _.cloneDeep(selectedRowKeys);
    if (flag || isFilter) {
      let campaignList = editGlobalFilter;
      if (isTrue) {
        campaignList.push(rowValue);
        setEditGlobalFilter(campaignList);
        setSelectedRowKeys(_.concat(_selectedRowKeys, rowValue.id));
        setActiveKey(`${rowValue.id}`);
      } else {
        campaignList = _.filter(campaignList, (v) => v.id !== rowValue.id);
        setEditGlobalFilter(campaignList);
        const newSelectRowKeys = _.without(_selectedRowKeys, rowValue.id);
        setSelectedRowKeys(newSelectRowKeys);
        setActiveKey(`${newSelectRowKeys[newSelectRowKeys.length - 1]}`);
      }
    } else {
      const _steps = _.cloneDeep(editSteps);
      const findIndex = _.findIndex(_steps, { step: id });
      let campaignList = isMulti
        ? _steps[findIndex].multiStepList[ind]?.campaignList
        : _steps[findIndex]?.campaignList || [];
      if (isTrue) {
        campaignList.push(rowValue);
        if (isMulti) {
          _steps[findIndex].multiStepList[ind] = {
            step: _steps[findIndex].multiStepList[ind].step,
            campaignList,
            type: 'CAMPAIGN'
          };
          setEditSteps(_steps);
        } else {
          _steps[findIndex] = {
            step: id,
            type: 'CAMPAIGN',
            campaignList
          };
          setEditSteps(_steps);
        }
        setSelectedRowKeys(_.concat(_selectedRowKeys, rowValue.id));
        setActiveKey(`${rowValue.id}`);
      } else {
        campaignList = _.filter(campaignList, (v) => v.id !== rowValue.id);
        if (isMulti) {
          _steps[findIndex].multiStepList[ind] = {
            step: _steps[findIndex].multiStepList[ind].step,
            campaignList,
            type: 'CAMPAIGN'
          };
          setEditSteps(_steps);
        } else {
          _steps[findIndex] = {
            step: id,
            type: 'CAMPAIGN',
            campaignList
          };
          setEditSteps(_steps);
        }
        const newSelectRowKeys = _.without(_selectedRowKeys, rowValue.id);
        setSelectedRowKeys(newSelectRowKeys);
        setActiveKey(`${newSelectRowKeys[newSelectRowKeys.length - 1]}`);
      }
    }
  };

  const changeTabs = (e) => {
    localStorage.setItem('popOverPage', '1');
    setActiveKey(e);
  };

  const saveSteps = () => {
    try {
      const arrTitle = [];
      if (flag) {
        if (_.isEmpty(editGlobalFilter)) throw new Error('没有流程');
        _.forEach(editGlobalFilter, (i) => {
          if (_.isEmpty(i.logList)) throw new Error('没有选择批次');
          if (type === 'CAMPAIGN_NODE') if (_.isEmpty(i.logList[0].selectedFlows)) throw new Error('没有选择节点');
        });
        const _globalFilters = _.cloneDeep(globalFilters);
        _globalFilters[filterIndex].campaignGroup.filters = editGlobalFilter;
        dispatch({ globalFilters: _globalFilters });
        setVisible(false);
      } else if (isFilter) {
        const _stepList = _.cloneDeep(stepList);
        if (_.isEmpty(editGlobalFilter)) throw new Error('没有流程');
        _.forEach(editGlobalFilter, (i) => {
          if (_.isEmpty(i.logList)) throw new Error('没有选择批次');
          if (type === 'CAMPAIGN_NODE') if (_.isEmpty(i.logList[0].selectedFlows)) throw new Error('没有选择节点');
        });
        if (!isMulti) {
          _stepList[stepIndex].filters[filterIndex].campaignGroup.filters = editGlobalFilter;
        } else {
          _stepList[stepIndex].multiStepList[ind].filters[0].campaignGroup.filters = editGlobalFilter;
        }
        _stepList[stepIndex].visibleSetp = false;
        // debugger
        setVisible(false);
        dispatch({ stepList: _stepList });
      } else {
        if (isMulti) {
          const _steps = editSteps.map((item) => {
            if (item.step === id) {
              if (_.isEmpty(item.multiStepList[ind].campaignList)) throw new Error('没有流程');
              _.forEach(item.multiStepList[ind].campaignList, (i) => {
                if (_.isEmpty(i.logList)) throw new Error('没有选择批次');
                arrTitle.push(i.name);
              });
              item.multiStepList[ind] = {
                ...item.multiStepList[ind],
                visibleSetp: false,
                name: arrTitle.join(', '),
                filters: [],
                step: ind + 1
              };
            }
            return item;
          });

          dispatch({ stepList: _steps });
        } else {
          const _steps = editSteps.map((item) => {
            if (item.step === id) {
              if (_.isEmpty(item?.campaignList)) throw new Error('没有选择流程');
              _.forEach(item.campaignList, (i) => {
                if (_.isEmpty(i.logList)) throw new Error('没有选择批次');
                arrTitle.push(i.name);
              });
              return {
                ...item,
                visibleSetp: false,
                name: arrTitle.join(', '),
                filters: []
              };
            }
            return item;
          });
          setVisible(false);
          dispatch({
            stepList: _steps
          });
        }
      }
      if (!flag) dispatch({ dimensionGroup: [], globalFilters: [] });
    } catch (err) {
      message.error(err.message);
    }
  };

  const searchRef = useRef();

  const changeSearchName = useDebounce(() => {
    const getValue = async () => {
      let pageSearchName = _.cloneDeep(pagination);
      const config = {
        connector: 'AND',
        propertyName: 'name',
        operator: 'LIKE',
        value: searchRef.current.input.value
      };
      const searchConfig = pageSearchName.search;
      if (searchConfig.length === 3) {
        searchConfig.push(config);
      } else {
        searchConfig[searchConfig.length - 1] = config;
      }
      pageSearchName = {
        ...pageSearchName,
        page: 1,
        search: searchConfig
      };
      setPagination(pageSearchName);
    };
    getValue();
  }, 400);

  return (
    <div className="renderCampaign">
      <eventAnalysisContext.Provider
        value={{
          state,
          dispatch,
          editSteps,
          setEditSteps,
          editGlobalFilter,
          setEditGlobalFilter
        }}
      >
        <div className="campaignTableBox">
          <div className="table1">
            <Search placeholder="搜索流程名称" allowClear onChange={changeSearchName} ref={searchRef} />
            <Table
              size="middle"
              columns={columns}
              dataSource={campaignTable || []}
              bordered={false}
              rowKey={(record) => record.id}
              loading={loading}
              rowSelection={{
                selectedRowKeys,
                preserveSelectedRowKeys: true,
                // onChange: onSelectChange,
                onSelect: selectTable,
                hideSelectAll: true
              }}
              onChange={handleTableChange}
              pagination={{
                current: pagination.page,
                total: totalCount,
                defaultPageSize: 10,
                showQuickJumper: true,
                showSizeChanger: true,
                showLessItems: true,
                pageSizeOptions: ['10', '20', '50'],
                showTotal: (e) => `共 ${e} 条`
              }}
            />
          </div>

          {!_.isEmpty(selectedRowKeys) && (
            <div className="batch">
              <div className="batchTitle">
                {type === 'CAMPAIGN_BATCH' ? '选择流程画布批次' : '选择流程画布批次、流程节点'}
              </div>
              <div className="batchContent">
                <Tabs
                  key={tabsKey}
                  hideAdd
                  // destroyInactiveTabPane
                  activeKey={activeKey}
                  onChange={changeTabs}
                  items={
                    flag || isFilter
                      ? editGlobalFilter.map((item) => {
                          return {
                            label: item.name,
                            key: `${item.id}`,
                            children: (
                              <RenderBatch
                                setTabsKey={setTabsKey}
                                batchValue={item}
                                flag={flag}
                                filterIndex={filterIndex}
                                editGlobalFilter={editGlobalFilter}
                                isFilter
                                type={type}
                              />
                            )
                          };
                        })
                      : !isMulti
                        ? editSteps[stepIndex]?.campaignList.map((item) => {
                            return {
                              label: item.name,
                              key: `${item.id}`,
                              children: (
                                <RenderBatch
                                  isMulti={isMulti}
                                  ind={ind}
                                  batchValue={item}
                                  id={id}
                                  setTabsKey={setTabsKey}
                                  editSteps={editSteps}
                                  editGlobalFilter={editGlobalFilter}
                                  type={type}
                                />
                              )
                            };
                          })
                        : editSteps[stepIndex]?.multiStepList[ind].campaignList.map((item) => {
                            return {
                              label: item.name,
                              key: `${item.id}`,
                              children: (
                                <RenderBatch
                                  isMulti={isMulti}
                                  ind={ind}
                                  batchValue={item}
                                  id={id}
                                  setTabsKey={setTabsKey}
                                  editSteps={editSteps}
                                  editGlobalFilter={editGlobalFilter}
                                  type={type}
                                />
                              )
                            };
                          })
                  }
                />
              </div>
            </div>
          )}
        </div>
      </eventAnalysisContext.Provider>
      <div className="buttons">
        {/* <Button>取消</Button> */}
        <Button type="primary" onClick={saveSteps}>
          确定
        </Button>
      </div>
    </div>
  );
}
