/*
 * @Author: Wxw
 * @Date: 2022-07-26 11:04:29
 * @LastEditTime: 2023-05-06 18:19:36
 * @LastEditors: 卜广硕 <EMAIL>
 * @Description:事件分析步骤
 * @FilePath: \Front\src\pages\home\analysisCenter\eventAnalysis\steps\index.jsx
 */
import { Button, Cascader, Dropdown, Input, Modal, Popover, Select, Tabs, Tooltip, Typography } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import { MyIconV2 } from 'utils/myIcon';
import { t } from 'utils/translation';
import { eventAnalysisContext } from '../eventAnalysisContext';
import FilterItem from '../filter/filterItem';
import icon from '../icon';
import FormulaComponent from './calcFormula/calcFormula';
import Event from './event';
import './index.scss';

const { Option } = Select;

const { Text } = Typography;

const mock = [t('analysisCenter-CsfIyi85Tl8H'), t('analysisCenter-4YTqXFWvNWg0')];

export default function Steps() {
  const { state, dispatch, scenarioList } = useContext(eventAnalysisContext);
  const { stepList, calcTypeList, chartConfig } = state;
  const [sort] = useState({ oldIndex: null, newIndex: null });
  const [showNameVisiable, setShowNameVisiable] = useState(false);
  const [editCalcOpen, setEditCalcOpen] = useState(false);
  const [editSteps, setEditSteps] = useState(stepList);
  const [calcValue, setCalcValue] = useState({});
  const [editName, setEditName] = useState({
    name: null,
    step: null,
    flag: false,
    index: null,
    ind: null,
    defaultName: null
  });

  const HandleSelectionChange = () => {
    const sel = window.getSelection && window.getSelection();
    if (sel && sel.rangeCount) {
      sel.getRangeAt(0);
    }
  };

  useEffect(() => {
    document.addEventListener('selectionchange', HandleSelectionChange, false);
  }, []);

  useEffect(() => {
    setEditSteps(stepList);
  }, [stepList]);

  useEffect(() => {
    const { oldIndex, newIndex } = sort;
    if (oldIndex !== null && newIndex !== null) {
      const _stepList = _.cloneDeep(stepList);
      _stepList.splice(newIndex, 0, _stepList.splice(oldIndex, 1)[0]);
      _stepList.map((item, index) => (item.step = `${index + 1}`));
      dispatch({ stepList: _stepList });
    }
    dispatch({ dimensionGroup: [], globalFilters: [] });
  }, [sort]);

  const addStep = (addType) => {
    const id = `${stepList.length + 1}`;

    if (addType === 'event') {
      dispatch({
        stepList: [
          ...stepList,
          {
            name: t('analysisCenter-HilOEZwLOZLl'),
            step: id,
            visibleSetp: false,
            // metricsType: 'BASE',
            clsType: 'BaseMetric',
            type: 'BASE',
            filters: []
          }
        ],
        dimensionGroup: [],
        globalFilters: []
      });
    } else {
      const _stepList = _.cloneDeep(stepList);
      const customList = _stepList.filter((item) => item.type === 'CUSTOM');
      const res = [];
      if (!stepList.filter((item) => item.type === 'CUSTOM').length) {
        res.push({ customId: 1 });
      } else {
        stepList
          .filter((item) => item.type === 'CUSTOM')
          .forEach((item2, index) => {
            res.push({ customId: index + 2 });
          });
      }

      dispatch({
        stepList: [
          ...stepList,
          {
            name: `${t('analysisCenter-fC3nLULbMMrv')}${customList.length + 1}`,
            step: id,
            clsType: 'CustomMetric',
            visibleSetp: false,
            type: 'CUSTOM',
            formula: '',
            format: 'PERCENTAGE',
            filters: []
          }
        ],
        dimensionGroup: [],
        globalFilters: []
      });
    }
  };

  /**
   * @description: 下拉栏操作
   * @param {*} stepId 步骤id
   * @param {*} flag 是否多路径对比
   * @param {*} index 步骤索引
   * @param {*} ind 对比索引
   * @return {*}
   */
  const menu = (type, stepId, flag, index, ind) => {
    return [
      {
        label: t('analysisCenter-TRXz1vOvd1Ok'),
        onClick: () => rename(stepId, flag, index, ind),
        key: 'rename'
      }
    ];
  };

  const rename = (stepId, flag, index, ind) => {
    setEditName({
      step: stepId,
      flag,
      index,
      ind,
      name: flag
        ? !_.isEmpty(editSteps[index].multiStepList[ind].displayName)
          ? editSteps[index].multiStepList[ind].displayName
          : editSteps[index].multiStepList[ind].name
        : !_.isEmpty(editSteps[index].displayName)
          ? editSteps[index].displayName
          : editSteps[index].name,
      defaultName: flag ? editSteps[index].multiStepList[ind].name : editSteps[index].name
    });
    setShowNameVisiable(true);
  };

  // 删除筛选
  const delSteps = (stepId) => {
    const newSteps = _.filter(stepList, (item) => item.step !== stepId);

    const newStepsSort = newSteps.map((item, index) => {
      return { ...item, step: `${index + 1}` };
    });
    const delItem = _.find(stepList, (item) => item.step === stepId);
    let _axisArr = _.cloneDeep(chartConfig.eventAnalysis.axisConf);
    _axisArr = _axisArr?.filter((item) => item.eventName !== delItem.name);

    dispatch({
      stepList: newStepsSort,
      chartConfig: { ...chartConfig, eventAnalysis: { axisConf: _axisArr } }
    });
  };

  // 添加过滤条件
  const openDrawer = (index, ind) => {
    const _stepList = _.cloneDeep(stepList);
    if (!_.isNaN(parseInt(ind))) {
      if (_stepList[index].multiStepList[ind].filters) {
        _stepList[index].multiStepList[ind].filters = [
          ..._stepList[index].multiStepList[ind].filters,
          { id: _stepList[index].multiStepList[ind].filters.length + 1 }
        ];
      } else {
        _stepList[index].multiStepList[ind].filters = [{ id: 1 }];
      }
    } else {
      if (_stepList[index].filters) {
        _stepList[index].filters = [..._stepList[index].filters, { id: _stepList[index].filters.length + 1 }];
      } else {
        _stepList[index].filters = [{ id: 1 }];
      }
    }
    dispatch({ stepList: _stepList });
    // dispatch({
    //   stepList: stepList.map(item => {
    //     if (item.step === id) {
    //       return { ...item, open: !item.open };
    //     }
    //     return item;
    //   })
    // });
  };

  /**
   * @description: 点击步骤
   * @param {*} id 步骤id
   * @param {*} flag 是否是多路径对比
   * @param {*} flag 是否是多路径对比
   * @return {*}
   */
  const handleVisibleChange = async (stepIndex) => {
    const _steps = _.cloneDeep(stepList);
    _steps[stepIndex].visibleSetp = !_steps[stepIndex].visibleSetp;
    dispatch({
      stepList: _steps
    });
  };

  const changeTabs = (e, index) => {
    const _steps = _.cloneDeep(editSteps);
    const { step, visibleSetp } = _steps[index];
    _steps[index] = {
      // ..._steps[index],
      type: e,
      step,
      name: t('analysisCenter-HilOEZwLOZLl'),
      visibleSetp,
      campaignList: [],
      event: {},
      bizTable: {}
    };
    setEditSteps(_steps);
  };

  /**
   * @description: 设置漏斗步骤
   * @param {*} item  漏斗步骤
   * @param {*} index 下标
   * @param {*} flag 是否是多路径对比
   * @param {*} i 对应的步骤
   * @param {*} ind 多路径下标
   * @return {*}
   */
  const RenderSetStep = (item, index, flag, i, ind) => {
    return (
      <div className="stepItem">
        <Tabs
          defaultActiveKey="EVENT"
          onChange={(e) => changeTabs(e, index, flag, i, ind)}
          destroyInactiveTabPane
          items={[
            {
              label: (
                <span>
                  {' '}
                  <MyIconV2 type="icon-icon-event" style={{ marginRight: 0, fontSize: 16 }} />{' '}
                  {t('analysisCenter-3GrxjphjT8Mm')}
                </span>
              ),
              key: 'EVENT',
              children: <Event id={item.step} stepIndex={index} flag={flag} i={i} ind={ind} />
            }
          ]}
        />
      </div>
    );
  };

  const renderTitle = (name, displayName) => {
    const title = !_.isEmpty(displayName) ? displayName : name;
    return (
      <Text
        style={{
          width: 'calc(100% - 20px)',
          color: title === t('analysisCenter-HilOEZwLOZLl') ? 'rgba(0,0,0,.25)' : 'rgba(0,0,0,.85)'
        }}
        ellipsis={{ tooltip: title }}
      >
        {title}
      </Text>
    );
  };

  const onCustomTitleChange = (e, step) => {
    const _stepList = _.cloneDeep(stepList);
    _stepList.forEach((item) => {
      if (item.step === step.step) {
        item.displayName = e.target.value;
        item.name = e.target.value;
      }
    });

    dispatch({ stepList: _stepList });
  };

  const renderCustomTitle = (name, displayName, step) => {
    const title = !_.isEmpty(displayName) ? displayName : name;

    return (
      <Input
        placeholder={t('analysisCenter-BKVtcqzcWDJl')}
        value={title}
        bordered={false}
        style={{ marginTop: 4, paddingLeft: 0 }}
        onChange={(e) => onCustomTitleChange(e, step)}
      />
    );
  };

  const renderCalcTitle = (calcValue) => {
    const title = calcValue || t('analysisCenter-J6NK5ei89kV2');
    return (
      <Text
        style={{
          width: 'calc(100% - 20px)',
          color: title === t('analysisCenter-J6NK5ei89kV2') ? 'rgba(0,0,0,.25)' : 'rgba(0,0,0,.85)'
        }}
        ellipsis={{ tooltip: title }}
      >
        {title}
      </Text>
    );
  };

  const handleOk = (reset) => {
    const { name, flag, index, ind, defaultName } = editName;
    const _steps = _.cloneDeep(stepList);
    if (flag) {
      _steps[index].multiStepList[ind].displayName = !reset
        ? name === ''
          ? _steps[index].multiStepList[ind].name
          : name
        : defaultName;
    } else {
      _steps[index].displayName = !reset ? (name === '' ? _steps[index].name : name) : defaultName;
    }
    dispatch({ stepList: _steps });
    setShowNameVisiable(false);
  };

  const handleCancel = () => {
    setShowNameVisiable(false);
  };

  const renderIcon = () => {
    return <MyIconV2 type="icon-icon-event" />;
  };

  // hover 类名conversion 显示stepTitle
  // const handleHover = (id) => {
  // const showStep = document.getElementsByClassName(`showStep${id}`)[0];
  // if (showStep) showStep.style.display = 'block';
  // };

  // const handleonMouseOut = (id) => {
  // const showStep = document.getElementsByClassName(`showStep${id}`)[0];
  // if (showStep) showStep.style.display = 'none';
  // };

  const eventItemClick = (e) => {
    const _calcNodeList = _.cloneDeep(state.formulaList);
    const index = _calcNodeList.findIndex((item) => item.type === 'input');
    if (
      (_calcNodeList[index - 1].type === 'symbol' && _calcNodeList[index + 1].type === 'symbol') ||
      (_calcNodeList[index - 1].type === 'symbol' && _calcNodeList[index + 1].type === 'span') ||
      (_calcNodeList[index + 1].type === 'symbol' && _calcNodeList[index - 1].type === 'span') ||
      (_calcNodeList[index + 1].type === 'span' && _calcNodeList[index - 1].type === 'span')
    ) {
      _calcNodeList.splice(index, 0, {
        type: 'div',
        value: e,
        key: new Date().valueOf()
      });

      dispatch({ formulaList: _calcNodeList });
    }
  };

  const onCalcClick = (item) => {
    setCalcValue(item);
    setEditCalcOpen(true);
  };

  const onCalcSubmit = () => {
    let result = '';
    const _stepList = _.cloneDeep(stepList);

    state.formulaList.forEach((item) => {
      if (item.type === 'div' || item.type === 'symbol') {
        result += item.value;
      }
    });

    _stepList.forEach((item) => {
      if (item.step === calcValue.step) {
        item.formula = result;
      }
    });

    setEditCalcOpen(false);
    dispatch({ stepList: _stepList });
  };

  const onMouseEvent = (event) => {
    event.preventDefault();
    return false;
  };

  const customMetricList = [
    { label: t('analysisCenter-eTFj28zIAnUY'), value: 'PERCENTAGE', key: 'PERCENTAGE' },
    { label: t('analysisCenter-Ljp2zWGmvO8J'), value: 'TWO_DECIMAL', key: 'TWO_DECIMAL' },
    { label: t('analysisCenter-pHnnEvxK6Ehf'), value: 'THREE_DECIMAL', key: 'THREE_DECIMAL' },
    { label: t('analysisCenter-dstV9pUJtpoP'), value: 'INTEGER', key: 'INTEGER' }
  ];

  // const displayRender = (labels) => labels[labels.length - 1];
  const displayRender = (labels) => {
    const labelEnum = {
      [t('analysisCenter-uCtupOovejKJ')]: t('analysisCenter-HSkiZv5ArR0P'),
      [t('analysisCenter-sAxK6cERoMS8')]: t('analysisCenter-4KBSE3wEhYn4'),
      [t('analysisCenter-89POFWX4VMku')]: t('analysisCenter-WORSyzDpIOBQ'),
      [t('analysisCenter-WsZfnvORgVce')]: t('analysisCenter-4NL3uTfSoFld'),
      [t('analysisCenter-DQQciTCw0X0N')]: t('analysisCenter-oWHcUE5dH2jg'),
      [t('analysisCenter-9Ol2yKp02hXu')]: t('analysisCenter-fADiyLBr3214')
    };

    if (labels.length === 2) {
      return `${t('analysisCenter-NdXZxkpDLoLj')} ${labels[1]} ${labelEnum[labels[0].props.children]}`;
    } else {
      return labels[0];
    }
  };

  const onCustomSelectChange = (calcValue, value) => {
    const _stepList = _.cloneDeep(stepList);

    _stepList.forEach((item) => {
      if (item.step === calcValue.step) {
        item.format = value;
      }
    });

    dispatch({ stepList: _stepList });
  };

  const onSelectChange = async (event, value) => {
    if (event) {
      const _calcTypelist = _.cloneDeep(calcTypeList);
      const _value = _.cloneDeep(value);
      _value.function = event[0];
      if (event.length === 2) {
        const eventInfo = value.eventList.find((item) => item.fieldName === event[1]);
        _value.eventProp = eventInfo;
      } else {
        _value.eventProp = undefined;
      }

      const calcIndex = _.findIndex(_calcTypelist, (o) => Number(o.step) === Number(value.step));

      if (calcIndex !== -1) {
        _calcTypelist.splice(calcIndex, 1, _value);
        dispatch({ calcTypeList: _calcTypelist });
      } else {
        dispatch({ calcTypeList: [...calcTypeList, _value] });
      }
      const _stepList = _.cloneDeep(stepList);
      const stepIndex = _.findIndex(_stepList, (o) => Number(o.step) === Number(value.step));
      _stepList[stepIndex].calcType = event;
      _stepList[stepIndex].eventProp = undefined;

      dispatch({ stepList: _stepList });
    } else {
      const _stepList = _.cloneDeep(stepList);
      const stepIndex = _.findIndex(_stepList, (o) => Number(o.step) === Number(value.step));
      _stepList[stepIndex].calcType = undefined;
      _stepList[stepIndex].eventProp = undefined;

      const _calcTypeList = _.cloneDeep(calcTypeList);
      _calcTypeList[stepIndex].calcType = undefined;
      _calcTypeList[stepIndex].eventProp = undefined;

      dispatch({ calcTypeList: _calcTypeList, stepList: _stepList });
    }
  };
  const renderBaseList = (event) => {
    const baseChildrens = [];
    let unqieChildrens = [];
    if (event) {
      event.forEach((item) => {
        if (item.fieldType === 'INT' || item.fieldType === 'LONG' || item.fieldType === 'DOUBLE') {
          // baseChildrens.push({
          //   label: `[${item.level1}]${item.fieldName}`,
          //   value: `${item.level1}-${item.fieldName}`
          // });
          baseChildrens.push({
            label: item.fieldName,
            value: item.fieldName
          });
        }
        // unqieChildrens.push({
        //   label: item.fieldName,
        //   value: item.fieldName
        // });
      });

      const res = new Map();
      unqieChildrens = baseChildrens.filter((item) => !res.has(item.label) && res.set(item.label, 1));
    }

    const baseMetricList = [
      {
        label: <Tooltip title={t('analysisCenter-YEz4s9sV6bPh')}>{t('analysisCenter-TMVUQDkIfVMC')}</Tooltip>,
        value: 'COUNT_DT_ID'
      },
      {
        label: <Tooltip title={t('analysisCenter-2XRDmZBGC0sI')}>{t('analysisCenter-ruiR0Vy2znPu')}</Tooltip>,
        value: 'UNI_DT_ID'
      },
      {
        label: <Tooltip title={t('analysisCenter-7HKzlDuxFiC0')}>{t('analysisCenter-KSobarbjPxkg')}</Tooltip>,
        value: 'COUNT_DT_ID_DIV_UNI_DT_ID'
      },
      {
        label: <Tooltip title={t('analysisCenter-CWoHwvoRKxb5')}>{t('analysisCenter-xoq43yfVk8Ni')}</Tooltip>,
        value: 'SUM_COL',
        disabled: !unqieChildrens.length,
        children: unqieChildrens
      },
      {
        label: <Tooltip title={t('analysisCenter-ml3Ec7cVVcMw')}>{t('analysisCenter-GgZ0WakjXFhV')}</Tooltip>,
        value: 'MAX_COL',
        disabled: !unqieChildrens.length,
        children: unqieChildrens
      },
      {
        label: <Tooltip title={t('analysisCenter-7xjAw1smMQQi')}>{t('analysisCenter-2rQNtTFo1s5M')}</Tooltip>,
        value: 'MIN_COL',
        disabled: !unqieChildrens.length,
        children: unqieChildrens
      },
      {
        label: <Tooltip title={t('analysisCenter-3aszdF15BBhn')}>{t('analysisCenter-XImHchyeysLV')}</Tooltip>,
        value: 'AVG_COL',
        disabled: !unqieChildrens.length,
        children: unqieChildrens
      },
      {
        label: <Tooltip title={t('analysisCenter-W60zujVmxl1W')}>{t('analysisCenter-Y2rJgwiUjq7q')}</Tooltip>,
        value: 'COUNT_COL_DIV_UNI_DT_ID',
        disabled: !unqieChildrens.length,
        children: unqieChildrens
      },
      {
        label: <Tooltip title={t('analysisCenter-LhJyH2IpzTb7')}>{t('analysisCenter-Shtp0pVcTJt5')}</Tooltip>,
        value: 'UNI_COL',
        disabled: !unqieChildrens.length,
        children: unqieChildrens
      }
    ];
    return baseMetricList;
  };

  return (
    <eventAnalysisContext.Provider
      value={{
        state,
        dispatch,
        handleVisibleChange,
        editSteps,
        setEditSteps,
        scenarioList
      }}
    >
      <div className="steps">
        {_.map(stepList, (item, index) => {
          return (
            <div className="step" key={index}>
              <div className="stepsItem">
                <div className="sort">{String.fromCharCode(64 + parseInt(item.step))}</div>
                <div className="mouse">{renderIcon(item.type)}</div>
                <div className="stepContent">
                  {/* <div className="stepTitle">{renderTitle(item)}</div> */}
                  {item.type === 'CUSTOM' ? (
                    <div className="stepTitle">{renderCustomTitle(item.name, item?.displayName, item)}</div>
                  ) : (
                    <Popover
                      getPopupContainer={() => document.getElementsByClassName('content')[0]}
                      content={RenderSetStep(item, index)}
                      trigger="click"
                      destroyTooltipOnHide
                      overlayStyle={{ minWidth: '320px' }}
                      autoAdjustOverflow
                      open={item.visibleSetp === true}
                      onOpenChange={() => handleVisibleChange(index)}
                      placement="bottom"
                      className="conversion"
                    >
                      <div className="stepTitle">{renderTitle(item.name, item?.displayName)}</div>
                    </Popover>
                  )}
                </div>
                <div className="icons">
                  <div className="stepIcon">
                    <span onClick={() => openDrawer(index)}>
                      <MyIconV2 type="icon-icon-filter" />
                    </span>
                    <span onClick={() => delSteps(item.step)}>
                      <MyIconV2 type="icon-icon-close" />
                    </span>
                  </div>
                  <div className="more">
                    <Dropdown
                      getPopupContainer={(triggerNode) => triggerNode.parentNode}
                      menu={{ items: menu(item.type, item.step, false, index) }}
                    >
                      {icon.MORE}
                    </Dropdown>
                  </div>
                </div>
                {/* <div className={`stepHover showStep${item.id}`}>假装是个弹窗</div> */}
              </div>

              {item.type === 'CUSTOM' ? (
                <div className="calcContent" onClick={() => onCalcClick(item)}>
                  {renderCalcTitle(item.formula)}
                </div>
              ) : null}

              <div className="stepMetric">
                {item.type === 'CUSTOM' ? (
                  <Select
                    defaultValue={item.metricValue || customMetricList[0].label}
                    bordered={false}
                    showArrow={false}
                    onChange={(e) => onCustomSelectChange(item, e)}
                  >
                    {customMetricList.map((item) => (
                      <Option key={item.key} value={item.value}>
                        {item.label}
                      </Option>
                    ))}
                  </Select>
                ) : (
                  <Cascader
                    options={renderBaseList(item.eventList)}
                    bordered={false}
                    dropdownMatchSelectWidth
                    popupClassName="cascader"
                    placeholder={t('analysisCenter-RxDyaHEBYvAl')}
                    displayRender={displayRender}
                    expandTrigger="click"
                    key={`seperator-${index}`}
                    onChange={(e) => onSelectChange(e, item)}
                    value={item.calcType || []}
                    disabled={!item.eventList}
                    allowClear
                    showArrow={false}
                  />
                )}
              </div>
              {item.filters &&
                item.filters.map((filterItem, filterIndex) => {
                  return (
                    <div className="drawerItem" key={filterIndex}>
                      <FilterItem index={index} filterIndex={filterIndex} value={item.filters} />
                    </div>
                  );
                })}
            </div>
          );
        })}
      </div>
      <Button className="addButton" onClick={() => addStep('event')} type="dashed" block>
        + {t('analysisCenter-xcrYt23QTj5X')}
      </Button>
      {/* <Button className="addButton" onClick={() => addStep('custom')} type="dashed" block>+ 添加自定义指标</Button> */}
      <Modal
        title={t('analysisCenter-qCpxibsOt6K5')}
        open={editCalcOpen}
        className="editCalcModal"
        destroyOnClose
        onCancel={() => setEditCalcOpen(false)}
        footer={null}
      >
        <div className="content">
          <div className="eventList" onMouseDown={onMouseEvent}>
            <div className="title">{t('analysisCenter-ujBMC3IJehel')}</div>
            <div className="search">
              <Input placeholder={t('analysisCenter-z9PMmELdnIfK')} width={192} />
            </div>
            <div className="eventContent">
              {mock.map((item) => (
                <div className="eventItem" key={item} onClick={() => eventItemClick(item)}>
                  {item}
                </div>
              ))}
            </div>
          </div>
          <div className="calcFormula">
            <div className="title">{t('analysisCenter-tcKEiYoXQaxs')}</div>
            <div className="calcContent">
              {/* <TextArea /> */}
              {/* <div
                contentEditable="true"
                className="ant-input calcInput"
                ref={calcRef}
              /> */}
              <FormulaComponent onEventAdd={eventItemClick} formulaData={calcValue} />
            </div>
            <div className="desc">{t('analysisCenter-MGOdzjFGPWcb')}</div>
            <div className="btnGroup">
              <Button style={{ marginRight: 8 }}>{t('analysisCenter-rhAUGkJSq22N')}</Button>
              <Button type="primary" onClick={() => onCalcSubmit()}>
                {t('analysisCenter-6ZjtcTqx0Vsw')}
              </Button>
            </div>
          </div>
        </div>
      </Modal>
      <Modal
        title={<h3>{t('analysisCenter-TRXz1vOvd1Ok')}</h3>}
        open={showNameVisiable}
        // onOk={handleOk}
        onCancel={() => setShowNameVisiable(false)}
        footer={[
          <Button key="reset" onClick={() => handleOk(true)}>
            {t('analysisCenter-flz6pXW3fb31')}
          </Button>,
          <Button key="back" onClick={handleCancel}>
            {t('analysisCenter-VrEQkefI5GEf')}
          </Button>,
          <Button key="submit" type="primary" onClick={() => handleOk(false)}>
            {t('analysisCenter-6ZjtcTqx0Vsw')}
          </Button>
        ]}
      >
        <Input
          value={editName.name}
          maxLength={30}
          onChange={(e) =>
            setEditName({
              ...editName,
              name: e.target.value
            })
          }
        />
      </Modal>
    </eventAnalysisContext.Provider>
  );
}
