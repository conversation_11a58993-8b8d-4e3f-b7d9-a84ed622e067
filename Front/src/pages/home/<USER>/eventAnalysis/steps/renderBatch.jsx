import { Button, message, Popover, Select, Table } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import CampaignV2Service from 'service/CampaignV2Service';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { FlowCanvas } from 'wolf-static-cpnt';
import { eventAnalysisContext } from '../eventAnalysisContext';
import icon from '../icon';

const campaignV2Service = new CampaignV2Service();
const { Option } = Select;

const RenderBatch = ({ flag, batchValue, id, setTabsKey, isMulti, ind, isFilter, type }) => {
  const contextValue = useContext(eventAnalysisContext);
  const { editSteps, setEditSteps, editGlobalFilter, setEditGlobalFilter } = contextValue;
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [batchList, setBatchList] = useState([]);
  const [findIndex, setFindIndex] = useState(null);
  const [page, setPage] = useState(localStorage.getItem('popOverPage') || 1);

  useEffect(() => {
    localStorage.setItem('popOverPage', page);
  }, [page]);
  const [findBatchIndex, setFindBatchIndex] = useState(null);
  const pageConfig = {
    search: [
      {
        propertyName: 'campaignId',
        operator: 'EQ',
        value: batchValue.id
      },
      {
        connector: 'AND',
        propertyName: 'faked',
        operator: 'EQ',
        value: 'false'
      }
    ],
    size: 1000,
    sorts: [{ direction: 'desc', propertyName: 'id' }]
  };
  const columns = [
    {
      title: '活动批次',
      dataIndex: 'id',
      width: 170,
      height: 100
    },
    {
      title: '批次参与人数',
      dataIndex: 'joinCount',
      width: 120
    },
    {
      title: '流程节点',
      dataIndex: 'text',
      width: 200,
      render: (text, record) => {
        const _logList =
          flag || isFilter
            ? editGlobalFilter[findBatchIndex]?.logList
            : isMulti
              ? editSteps[findIndex].multiStepList[ind].campaignList[findBatchIndex]?.logList
              : editSteps[findIndex].campaignList[findBatchIndex]?.logList;
        const findValue = _.find(_logList, (item) => item.id === record.id);
        let isShow = false;
        const findValueArr = [];
        if (!_.isEmpty(findValue?.selectedFlows)) {
          isShow = true;
          _.forEach(findValue?.selectedFlows, (item) => {
            findValueArr.push(item.name);
          });
        }
        return (
          // 如果过滤选择了批次
          type === 'CAMPAIGN_BATCH' ? null : (
            <Popover
              // key={Number}
              getPopupContainer={() => document.getElementsByClassName('batch')[0]}
              content={<RenderFlows id={record.id} />}
              title="选择节点"
              trigger="click"
              destroyTooltipOnHide
              // overlayStyle={{ width: '300px', height: '300px', overflowY: 'auto' }}
              className="sketchcolorcom"
              open={record?.visible}
              onOpenChange={(e) => handleVisibleChange(e, record.id)}
            >
              {!isShow ? (
                <Select
                  mode="multiple"
                  maxTagCount={1}
                  style={{ width: '100%' }}
                  placeholder="请选择节点"
                  dropdownRender={null}
                  dropdownStyle={{ display: 'none' }}
                />
              ) : (
                <Select
                  style={{ width: '100%' }}
                  mode="multiple"
                  maxTagCount={1}
                  placeholder="请选择节点"
                  // disabled
                  dropdownRender={null}
                  dropdownStyle={{ display: 'none' }}
                  defaultValue={findValueArr}
                >
                  {findValueArr.map((item, index) => {
                    return (
                      <Option disabled key={index} value={item}>
                        {item}
                      </Option>
                    );
                  })}
                </Select>
              )}
              {/* <Input maxLength={7} readOnly placeholder={record.id} /> */}
            </Popover>
          )
        );
      }
    },
    {
      title: '运行开始时间',
      dataIndex: 'beginTime',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 200
    },
    {
      title: '运行结束时间',
      dataIndex: 'endTime',
      render: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
      width: 200
    }
  ];

  const handleVisibleChange = (e, recordId) => {
    const _batchList = _.cloneDeep(batchList);
    const findIndex = _batchList.findIndex((item) => item.id === recordId);
    _batchList[findIndex].visible = e;
    setBatchList(_batchList);
  };

  const RenderFlows = ({ id }) => {
    const [selectedNodes, setSelectedNodes] = useState([]);

    // 回显每个批次流程节点
    useEffect(() => {
      const _steps = _.cloneDeep(editSteps);
      const _logList =
        flag || isFilter
          ? editGlobalFilter[findBatchIndex]?.logList
          : isMulti
            ? _steps[findIndex].multiStepList[ind]?.campaignList[findBatchIndex].logList
            : _steps[findIndex]?.campaignList[findBatchIndex].logList || [];
      if (!_.isEmpty(_logList)) {
        const findLogListIndex = _.findIndex(_logList, (item) => item.id === id);
        const _selectedNodes = _logList[findLogListIndex]?.selectedFlows || [];
        setSelectedNodes(_selectedNodes);
      }
    }, [batchList]);

    const onFlowCanvasClick = (data) => {
      if (selectedNodes.length >= 10) {
        message.error('节点最多选择10个', 1);
        return;
      }
      if (!_.find(selectedNodes, (item) => item.nodeId === data.nodeId)) {
        const _selectedNodes = _.cloneDeep(selectedNodes);
        _selectedNodes.push(data);
        setSelectedNodes(_selectedNodes);
      }
    };

    const delNode = (id) => {
      let _selectedNodes = _.cloneDeep(selectedNodes);
      _selectedNodes = _selectedNodes.filter((item) => item.nodeId !== id);
      setSelectedNodes(_selectedNodes);
    };

    /**
     * @description: 保存节点
     * @return {*}
     */
    const saveBatchId = () => {
      const _steps = _.cloneDeep(editSteps);
      const _globalFilters = _.cloneDeep(editGlobalFilter);
      const _selectedNodes = _.cloneDeep(selectedNodes);
      const _logList =
        flag || isFilter
          ? _globalFilters[findBatchIndex].logList
          : isMulti
            ? _steps[findIndex].multiStepList[ind].campaignList[findBatchIndex].logList
            : _steps[findIndex].campaignList[findBatchIndex].logList;
      if (!_.isEmpty(_logList)) {
        const findLogListIndex = _.findIndex(_logList, (item) => item.id === id);
        if (findLogListIndex !== -1) {
          _logList[findLogListIndex].selectedFlows = _selectedNodes;
          if (flag || isFilter) {
            _globalFilters[findBatchIndex].logList = _logList;
            setEditGlobalFilter(_globalFilters);
          } else {
            setEditSteps(_steps);
          }
          setBatchList(() => {
            return _.cloneDeep(batchList);
          });
          message.success('保存成功');
          setTabsKey(new Date().getTime());
          handleVisibleChange(false, id);
        } else {
          message.error('请先勾选活动批次');
        }
      } else {
        message.error('请先勾选活动批次');
      }
    };

    const findValue = _.find(batchList, (item) => item.id === id);
    return (
      <div className="popoverBatch">
        <div className="flowNodeTitle">
          已选择节点[{selectedNodes.length}/10]
          <span>最多选择10个节点</span>
        </div>
        <div className="flowNode">
          {selectedNodes.map((item) => {
            return (
              <span className="flowNodeItem" key={item.nodeId}>
                {`[${item.nodeId}]${item.name}`} <span onClick={() => delNode(item.nodeId)}>{icon.CLOSE}</span>
              </span>
            );
          })}
        </div>
        <div className="renderFlow">
          <FlowCanvas
            dataProvider={dataProvider}
            value={findValue?.flows ?? []}
            onClickNode={(e) => onFlowCanvasClick(e, id)}
            mode="detail"
          />
        </div>
        <div className="buttons">
          <Button onClick={() => handleVisibleChange(false, id)}>取消</Button>
          <Button type="primary" onClick={saveBatchId}>
            确认
          </Button>
        </div>
      </div>
    );
  };

  const dataProvider = {
    getAtTimeNodesData: (flows) => {
      return campaignV2Service.getNextTimeInAtTimeNodes(flows);
    },
    getEventCountLogsByProjectId: async () =>
      FunnelAnalysis.getEventCountLogsByProjectId({
        projectId: localStorage.getItem('projectId')
      })
  };

  useEffect(() => {
    const init = async () => {
      setLoading(true);
      const _steps = _.cloneDeep(editSteps);
      const _editGlobalFilter = _.cloneDeep(editGlobalFilter);
      let logList;
      if (flag || isFilter) {
        const findBatchIndex = _.findIndex(_editGlobalFilter, {
          id: batchValue.id
        });
        setFindBatchIndex(findBatchIndex);
        logList = _editGlobalFilter[findBatchIndex].logList;
      } else {
        // 获取当前步骤的索引
        const findIndex = _.findIndex(_steps, { step: id });
        setFindIndex(findIndex);
        // 获取当前批次的索引
        const findBatchIndex = isMulti
          ? _.findIndex(_steps[findIndex].multiStepList[ind].campaignList, {
              id: batchValue.id
            })
          : _.findIndex(_steps[findIndex].campaignList, { id: batchValue.id });
        setFindBatchIndex(findBatchIndex);
        logList = isMulti
          ? _steps[findIndex].multiStepList[ind].campaignList[findBatchIndex].logList
          : _steps[findIndex].campaignList[findBatchIndex].logList;
      }

      // 回显
      const _selectedRowKeys = [];
      _.forEach(logList, (v) => _selectedRowKeys.push(v.id));
      setSelectedRowKeys(_selectedRowKeys);
      try {
        const batchList = await FunnelAnalysis.listCalcLogs(pageConfig);
        setBatchList(batchList.content);
        setLoading(false);
      } catch (err) {
        setLoading(false);
      }
    };
    init();
    return () => {
      localStorage.removeItem('popOverPage');
    };
  }, []);

  const selectTable = (rowValue, isTrue) => {
    const _selectedRowKeys = _.cloneDeep(selectedRowKeys);
    if (flag || isFilter) {
      // 过滤
      const _globalFilters = _.cloneDeep(editGlobalFilter);
      const findBatchIndex = _.findIndex(_globalFilters, { id: batchValue.id });
      let _logList = _globalFilters[findBatchIndex].logList || [];
      if (isTrue) {
        _logList.push(rowValue);
        setSelectedRowKeys(_.concat(_selectedRowKeys, rowValue.id));
      } else {
        _logList = _.filter(_logList, (item) => item.id !== rowValue.id);
        setSelectedRowKeys(_.without(_selectedRowKeys, rowValue.id));
      }
      // setTabsKey(new Date().getTime());
      _globalFilters[findBatchIndex].logList = _logList;
      setEditGlobalFilter(_globalFilters);
    } else {
      const _steps = _.cloneDeep(editSteps);
      const findBatchIndex = isMulti
        ? _.findIndex(_steps[findIndex].multiStepList[ind].campaignList, {
            id: batchValue.id
          })
        : _.findIndex(_steps[findIndex].campaignList, { id: batchValue.id });
      if (isMulti) {
        let _logList = _steps[findIndex].multiStepList[ind].campaignList[findBatchIndex].logList || [];
        if (isTrue) {
          _logList.push(rowValue);
          setSelectedRowKeys(_.concat(_selectedRowKeys, rowValue.id));
        } else {
          _logList = _.filter(_logList, (item) => item.id !== rowValue.id);
          setSelectedRowKeys(_.without(_selectedRowKeys, rowValue.id));
        }
        // setTabsKey(new Date().getTime());
        _steps[findIndex].multiStepList[ind].campaignList[findBatchIndex].logList = _logList;
        setEditSteps(_steps);
      } else {
        let _logList = _steps[findIndex].campaignList[findBatchIndex]?.logList || [];
        if (isTrue) {
          _logList.push(rowValue);
          setSelectedRowKeys(_.concat(_selectedRowKeys, rowValue.id));
        } else {
          _logList = _.filter(_logList, (item) => item.id !== rowValue.id);
          setSelectedRowKeys(_.without(_selectedRowKeys, rowValue.id));
        }
        setTabsKey(new Date().getTime());
        _steps[findIndex].campaignList[findBatchIndex].logList = _logList;
        setEditSteps(_steps);
      }
    }
  };

  const rowSelection = {
    selectedRowKeys,
    // onChange: onSelectChange,
    onSelect: selectTable,
    hideSelectAll: true
  };

  return (
    <div className="renderBatch" style={{ overflow: 'auto' }}>
      <Table
        size="middle"
        columns={columns}
        dataSource={batchList || []}
        bordered={false}
        rowKey={(record) => record.id}
        loading={loading}
        rowSelection={rowSelection}
        scroll={{ x: 640, y: 300 }}
        pagination={{
          position: 'bottomRight',
          defaultPageSize: 20,
          current: parseInt(page) || 1,
          total: batchList?.length || 0,
          onChange: (page) => {
            localStorage.setItem('popOverPage', page);
            setPage(page);
          },
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: ['20', '50', '100'],
          showLessItems: true,
          showTotal: (e) => `共 ${e} 条`
        }}
      />
    </div>
  );
};
export default RenderBatch;
