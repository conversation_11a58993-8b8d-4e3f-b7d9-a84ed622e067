.steps {

  .title {
    margin-bottom: 16px;
  }

  .step {
    border: 1px solid #E9E9E9;
    // border-bottom: none;
    margin-bottom: 16px;
    border-radius: 6px;

    &>div {
      border-top: 1px solid #E9E9E9;

      &:first-child {
        border-top: none;
      }
    }

    .stepsItem {
      position: relative;
      // width: 392px;
      height: 40px;
      display: flex;
      // background-color: antiquewhite;
      // margin-top: 16px;
      border-radius: 6px;
      // border: 1px solid #E9E9E9;
      padding: 10px 12px;

      .ant-popover {}

      .sort {
        text-align: center;
        position: relative;
        width: 22px;
        height: 22px;
        border-radius: 4px;
        background: $primary_color;
        color: #fff;
        font-size: 12px;
        line-height: 22px;

        span {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }

      .mouse {
        margin: 0 8px;
        font-size: 16px;
      }

      .stepContent {
        width: calc(100% - 130px);
        // min-width: 46%;
        margin: -8px 0;
        align-items: center;
        cursor: pointer;

        &:hover {
          background-color: $active_color;
          border-radius: 6px;
        }

        .stepTitle {

          line-height: 36px;
          padding-left: 8px;
          color: rgba(0, 0, 0, 0.85);
          //定一个宽度,不换行 超出自动省略
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px;
          font-weight: 500;
          width: 100%;
        }
      }

      .icons {
        display: flex;
        margin-left: auto;

        .stepIcon {
          font-size: 16px;
          display: none;
          color: rgba(0, 0, 0, 0.45);

          span {
            margin-right: 4px;
            cursor: pointer;
          }
        }

        .more {
          padding-top: 2px;
          cursor: pointer;
        }
      }

      .stepHover {
        position: absolute;
        display: none;
        right: 0;
      }

      //hover的时候 显示.stepIcon
      &:hover {
        .stepIcon {
          display: flex;
        }
      }
    }

    .calcContent {
      padding: 5px 0 8px 12px;
    }

    .stepMetric {
      height: 40px;
      line-height: 40px;

      .ant-select {
        width: 100% !important;
      }
    }

    .drawer {
      // border: 1px solid #e9e9e9;
    }
  }
}

.cascader {
  .ant-cascader-menu {
    height: 320px;

    .ant-cascader-menu-item {
      height: 40px;
      line-height: 40px;
    }
  }
}

.editCalcModal {
  .ant-modal-content {
    border-radius: 6px;
    width: 800px;
    height: 560px;

    .ant-modal-body {
      padding: 0;
    }
  }

  .content {
    display: flex;

    .eventList {
      width: 240px;
      margin-right: 24px;
      border-right: 1px solid #F0F0F0;

      .title {
        font-size: 16px;
        font-weight: 600;
        margin: 16px 0 16px 24px;
        margin-top: 16px;
        margin-bottom: 16px;
      }

      .search {
        margin: 0 24px 8px 24px;

        .ant-input {
          border-radius: 6px;
        }
      }

      .eventContent {
        .eventItem {
          cursor: pointer;
          height: 32px;
          line-height: 32px;
          padding-left: 24px;

          &:hover {
            background: #FFF4E6;
            color: $primary_color;
          }
        }
      }
    }

    .calcFormula {
      margin: 16px 24px 12px 0;
      flex: 1;

      .title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;
      }

      .calcContent {
        margin-bottom: 16px;

        & .calcFormulaWrap {
          cursor: text;
          width: 500px;
          height: 200px;
          border-radius: 6px;
          border: 1px solid #D9D9D9;
        }

        .calcInput {
          height: 200px;
          border-radius: 6px;
        }

        .calcDiv {
          border-radius: 6px;
          height: 200px;
          padding: 12px;
          border: 1px solid #d9d9d9;
          -webkit-user-modify: read-write-plaintext-only !important;
        }
      }

      .desc {
        color: rgba(0, 0, 0, 0.45);

      }

      .btnGroup {
        margin-top: 144px;
        display: flex;
        justify-content: flex-end;

        .ant-btn {
          border-radius: 6px;
        }
      }
    }
  }
}