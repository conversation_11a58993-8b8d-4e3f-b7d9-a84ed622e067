/*
 * @Author: Wxw
 * @Date: 2022-07-26 11:04:29
 * @LastEditTime: 2023-04-21 16:58:31
 * @LastEditors: 卜广硕 <EMAIL>
 * @Description:事件指标部分
 * @FilePath: \Front\src\pages\home\analysisCenter\funnelAnalysis\steps\event.jsx
 */
import { Line } from '@ant-design/charts';
import { Input, Popover, Spin, Table } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useContext, useEffect, useRef, useState } from 'react';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { useDebounce } from 'utils/customhooks';
import './index.scss';
// import icon from '../icon';
import { t } from 'utils/translation';
import { eventAnalysisContext } from '../eventAnalysisContext';

// const { TabPane } = Tabs;
const { Search } = Input;
const columns = [
  {
    title: t('analysisCenter-zp3dv61CpIVt'),
    dataIndex: 'propertySchema'
  },
  {
    title: t('analysisCenter-rR3JdsdiFUs2'),
    dataIndex: 'displayName'
  },
  {
    title: t('analysisCenter-pDV7ATHQRsR0'),
    dataIndex: 'dataType'
  }
];
const nameAlias = {
  date: {
    alias: t('analysisCenter-Jd4WwoJKuPZJ')
  },
  count: {
    alias: t('analysisCenter-LL6wIVykkhDi')
  }
};

export default function Steps(props) {
  // eslint-disable-next-line no-unused-vars
  const { id, flag: isMultiStep, ind } = props;
  const { state, dispatch } = useContext(eventAnalysisContext);
  const [eventConfig, setEventConfig] = useState({
    page: 1,
    search: [
      // { operator: 'EQ', propertyName: 'eventType', value: 'BURIED_POINT_EVENT' }
    ],
    totalElements: null,
    size: 10,
    sorts: [{ propertyName: 'updateTime', direction: 'desc' }]
  });
  const [eventValue, setEventValue] = useState([]);
  const [loading, setLoading] = useState(false);
  const [flag, setFlag] = useState(false);
  const [lineList, setLineList] = useState([]);
  const searchRef = useRef();
  const { stepList, calcTypeList } = state;

  useEffect(() => {
    setLoading(true);
    const init = async () => {
      try {
        const eventValue = await FunnelAnalysis.queryEvent(eventConfig);
        const lineList = await FunnelAnalysis.getEventCountLogsByProjectId({
          projectId: localStorage.getItem('projectId')
        });
        setLineList(lineList);
        setEventValue(eventValue.content);
        setEventConfig({
          ...eventConfig,
          page: 1,
          totalElements: eventValue.totalElements
        });
        setLoading(false);
      } catch (err) {
        setLoading(false);
      }
    };
    init();
  }, []);

  // 监听eventBox触底
  const onEventScroll = async (e) => {
    if (eventConfig.totalElements > eventValue.length) {
      setLoading(true);
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      if (!flag && scrollTop + clientHeight >= scrollHeight) {
        const newData = { ...eventConfig, page: eventConfig.page + 1 };
        setEventConfig(newData);
        const newEventValue = await FunnelAnalysis.queryEvent(newData);
        setEventValue([...eventValue, ...newEventValue.content]);
      }
      setLoading(false);
    }
  };

  const onChange = useDebounce(() => {
    if (searchRef.current.input.value === '') {
      setEventConfig({ ...eventConfig, page: 1 });
    }
    const init = async () => {
      const data = {
        page: 1,
        search: [
          {
            operator: 'EQ',
            propertyName: 'eventType',
            value: 'BURIED_POINT_EVENT'
          },
          {
            operator: 'LIKE',
            propertyName: 'name',
            value: searchRef.current.input.value
          }
        ],
        size: 10,
        sorts: [{ propertyName: 'updateTime', direction: 'desc' }]
      };
      const eventValue = await FunnelAnalysis.queryEvent(data);

      await setEventValue(eventValue.content);
      setFlag(false); // 重置防止调用两次接口
    };
    init();
  }, 200);

  const renderEventInfo = (item) => {
    const findValue = _.find(lineList, (i) => i.eventId === item.id);
    if (
      _.some(findValue.eventCountLogList, (o) => {
        return !o;
      })
    ) {
      return;
    }
    const data = findValue.eventCountLogList.map((e) => {
      return {
        ...e,
        statTime: dayjs(e.statTime).format('MM-DD')
      };
    });
    const config = {
      data,
      padding: 'auto',
      xField: 'statTime',
      yField: 'count'
    };
    return (
      <div className="eventInfo">
        <div className="eventInfoTitle">{item.eventNameValue}</div>
        <div className="eventInfoContent">
          <div className="createInformation">
            {item.createUserName} {t('analysisCenter-t3uGhVX19sa0')} {dayjs(item.createTime).format('YYYY-MM-DD')}
          </div>
          <div>
            {t('analysisCenter-uPwVexn0OPir')}:
            {item.eventType === 'CUSTOM' ? t('analysisCenter-V7hnWw9TbvRl') : t('analysisCenter-E2kqbGQ9KCy0')}
          </div>
          <div className="eventTable">
            <Table
              size="small"
              rowKey="key"
              columns={columns}
              dataSource={_.map(item?.specialPropertyMappingList, (item, index) => {
                return { ...item, key: index };
              })}
              pagination={{
                defaultPageSize: 5,
                pageSizeOptions: ['5']
              }}
            />
          </div>
          <div className="line">
            <div className="lineTitle">{t('analysisCenter-BRCl1YnojTb5')}</div>
            <div className="lineChart">
              <Line {...config} meta={nameAlias} />
            </div>
          </div>
        </div>
      </div>
    );
  };

  /**
   * @description: 如果是多路径 就存储到当前步骤对应的ind下标中
   * @param {*} item
   * @return {*}
   */
  const clickEvent = async (item) => {
    const _steps = _.cloneDeep(stepList);
    const eventList = await FunnelAnalysis.getEventPropertyList({
      name: '',
      eventId: item?.id
    });

    const value = _steps.map((i) => {
      if (i.step === id) {
        return {
          step: i.step,
          calcType: undefined,
          eventProp: undefined,
          filters: [],
          eventList,
          // metricsType: i.metricsType,
          clsType: i.clsType,
          visibleSetp: false,
          type: 'BASE',
          event: item,
          name: item.name
        };
      }
      return i;
    });

    const value2 = calcTypeList.map((i) => {
      if (i.step === id) {
        return {
          step: i.step,
          calcType: undefined,
          eventProp: undefined,
          filters: [],
          eventList,
          // metricsType: i.metricsType,
          clsType: i.clsType,
          visibleSetp: false,
          type: 'BASE',
          event: item,
          name: item.name
        };
      }
      return i;
    });
    dispatch({ stepList: value, calcTypeList: value2 });
    dispatch({ dimensionGroup: [], globalFilters: [] });
  };

  return (
    <div className="renderEvent">
      <Spin spinning={loading}>
        <Search placeholder={t('analysisCenter-ir65iIgADiD3')} allowClear onChange={onChange} ref={searchRef} />
        <div className="eventBox" onScroll={onEventScroll}>
          {eventValue.map((item) => {
            return (
              <div className="popItem" key={item.id}>
                <Popover
                  placement="right"
                  autoAdjustOverflow
                  getPopupContainer={() => document.getElementsByClassName('content')[0]}
                  className="eventItemPopover"
                  content={() => renderEventInfo(item)}
                  trigger="hover"
                  overlayStyle={{ width: '512px' }}
                >
                  <div className="eventItem" onClick={() => clickEvent(item)}>
                    {item.name}
                  </div>
                </Popover>
              </div>
            );
          })}
        </div>
      </Spin>
    </div>
  );
}
