.calcWrap {
    .content {
        .divItem {
            display: flex;
            align-items: center;
            background: #F5F5F5;
            margin-right: 4px;
            border-radius: 4px;
            padding: 0 8px;
        }

        .divItem:last-of-type {
            margin-right: 0 !important;
        }

        .symbolItem {
            display: inline-block;
            padding-left: 4px;
            padding-right: 4px;
            text-align: center;
            cursor: text;
        }
    }
}

.filterPopWrap {
    .ant-popover-content {
        width: 560px;
    }

    .ant-popover-inner-content {
        padding: 12px 0 0 0;
    }

    .ant-popover-inner {
        border-radius: 6px;

        .ant-popover-title {
            min-height: 56px;
            padding: 16px 16px 4px;
        }
    }

    .popTitle {
        .title {
            font-size: 16px;
            font-weight: 600;
            margin-right: 16px;
        }

        .titleDesc {
            color: rgba(0, 0, 0, 0.45);
        }
    }

    .calcFilter {
        .calcContent {
            max-height: 400px;
            overflow-y: auto;
        }

        .wolf-static-component_filter_FilterGroupPanel {
            padding: 0 12px;
        }

        .Ctroller {
            .add {
                display: none;
            }
        }

        .FilterAdder {
            >span {
                display: none;
            }
        }

        .FilterGroupPanel {
            .FilterList.inner {
                background: none;
            }
        }

        .btn-group {
            display: flex;
            height: 56px;
            justify-content: flex-end;
            align-items: center;
            border-top: 1px solid #F0F0F0;
            margin-top: 16px;

            .ant-btn {
                border-radius: 6px;
            }

            .submit {
                margin: 0 12px 0 8px;
            }
        }
    }
}