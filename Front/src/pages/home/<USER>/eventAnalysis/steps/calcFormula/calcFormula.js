import React, { useEffect, useRef, useState, useContext } from 'react';
import _ from 'lodash';
import { Popover, Button } from 'antd';
import { FilterOutlined } from '@ant-design/icons';
import { Filter } from 'wolf-static-cpnt';
import { eventAnalysisContext } from '../../eventAnalysisContext';

import './calcFormula.scss';

export default function FormulaComponent(props) {
  const { state, dispatch } = useContext(eventAnalysisContext);
  const { formulaData } = props;
  const { formulaList } = state;
  // const [calcNodeList, setCalcNodeList] = useState(state.formulaList);

  const [lock, setLock] = useState(false);

  const inputRef = useRef(null);

  useEffect(() => {
    if (formulaData !== '') {
      //
    }
  }, [formulaData]);

  useEffect(() => {
    // 获取父级节点
    const parentNode = document.getElementById('container');
    // 获取所有子节点
    const childNodes = parentNode?.querySelectorAll('*');

    // 遍历子节点
    for (let i = 0; i < childNodes?.length; i++) {
      const childNode = childNodes[i];

      // 判断是否为 input 元素
      if (childNode.tagName === 'INPUT') {
        // 聚焦 input 元素
        childNode.focus();
      }
    }
  }, [formulaList]);

  const onInputChange = (e) => {
    if (!lock) {
      const _calcNodeList = _.cloneDeep(formulaList);
      const index = _calcNodeList.findIndex((item) => item.type === 'input');
      const inputList = ['+', '-', '*', '/', '(', ')'];
      if (inputList.find((item) => item === e.target.value)) {
        _calcNodeList.splice(index, 0, {
          type: 'symbol',
          value: e.target.value,
          key: new Date().valueOf()
        });
        dispatch({ formulaList: _calcNodeList });
      }
    }
    setLock(false);
  };

  const blockClick = (e, index) => {
    e.stopPropagation();
    const _calcNodeList = _.cloneDeep(formulaList);
    const newNodeIndex = formulaList.findIndex((item) => item.type === 'input');
    _calcNodeList.splice(newNodeIndex, 1);

    if (index === 0) {
      _calcNodeList.splice(index + 1, 0, {
        type: 'input',
        value: '',
        key: new Date().valueOf()
      });
    } else {
      _calcNodeList.splice(index - 1, 0, {
        type: 'input',
        value: '',
        key: new Date().valueOf()
      });
    }
    dispatch({ formulaList: _calcNodeList });
  };

  const onSymbolClick = (item, index, e) => {
    e.stopPropagation();
    const _calcNodeList = _.cloneDeep(formulaList);
    const newNodeIndex = _calcNodeList.findIndex((item) => item.type === 'input');
    _calcNodeList.splice(newNodeIndex, 1);

    const symbolIndex = _calcNodeList.findIndex((nodeItem) => nodeItem.key === item.key);

    _calcNodeList.splice(symbolIndex + 1, 0, {
      type: 'input',
      value: '',
      key: new Date().valueOf()
    });
    dispatch({ formulaList: _calcNodeList });
  };

  const onInputDelete = (e) => {
    if (e.keyCode === 8) {
      const _calcNodeList = _.cloneDeep(formulaList);
      const newNodeIndex = _calcNodeList.findIndex((item) => item.type === 'input');
      if (_calcNodeList[newNodeIndex - 1].type === 'span') {
        return;
      }
      _calcNodeList.splice(newNodeIndex - 1, 1);
      dispatch({ formulaList: _calcNodeList });
    } else if (e.keyCode === 37) {
      const _calcNodeList = _.cloneDeep(formulaList);
      const newNodeIndex = _calcNodeList.findIndex((item) => item.type === 'input');
      if (_calcNodeList[newNodeIndex - 1].type === 'span') {
        return;
      }
      _calcNodeList.splice(newNodeIndex, 1);
      _calcNodeList.splice(newNodeIndex - 1, 0, {
        type: 'input',
        value: '',
        key: new Date().valueOf()
      });
      dispatch({ formulaList: _calcNodeList });
    } else if (e.keyCode === 39) {
      const _calcNodeList = _.cloneDeep(formulaList);
      const newNodeIndex = _calcNodeList.findIndex((item) => item.type === 'input');
      if (_calcNodeList[newNodeIndex + 1].type === 'span') {
        return;
      }
      _calcNodeList.splice(newNodeIndex, 1);
      _calcNodeList.splice(newNodeIndex + 1, 0, {
        type: 'input',
        value: '',
        key: new Date().valueOf()
      });
      dispatch({ formulaList: _calcNodeList });
    }
  };

  const handleComposition = (e) => {
    if (e.type === 'compositionstart') {
      setLock(true);
    } else {
      setLock(true);
    }
  };

  const onCalcFocus = (e) => {
    blockClick(e, formulaList.length - 1);
  };

  // const onChange = (e) => {};
  const getFilterContent = () => {
    return (
      <div className="calcFilter">
        <div className="calcContent">
          <Filter
            mode="edit"
            dataProvider={{}}
            value={{}}
            // onChange={(v) => onChange({ ...{}, filter: v })}
          />
        </div>

        <div className="btn-group">
          <Button>验证</Button>
          <Button type="primary" className="submit">
            确定
          </Button>
        </div>
      </div>
    );
  };

  const nodeRender = (item, index) => {
    let resultNode;
    if (item.type === 'span') {
      resultNode = (
        <span
          style={{ width: 20, height: '100%' }}
          className={index === 0 ? 'startBlock' : 'endBlock'}
          onClick={(e) => blockClick(e, index)}
          key={item.key}
        />
      );
    } else if (item.type === 'input') {
      resultNode = (
        <input
          key={item.key}
          value=""
          style={{ width: 6, border: 'none', outline: 'none', height: '100%' }}
          onChange={(e) => onInputChange(e)}
          onKeyDown={(e) => onInputDelete(e)}
          onClick={(e) => e.stopPropagation()}
          onCompositionStart={handleComposition}
          onCompositionEnd={handleComposition}
        />
      );
    } else if (item.type === 'div') {
      resultNode = (
        <div className="divItem" key={item.key}>
          <div onClick={(e) => e.stopPropagation()}>{item.value}</div>
          <Popover
            content={() => getFilterContent(item)}
            title={
              <div className="popTitle">
                <span className="title">过滤条件</span>
                <span className="titleDesc">最多可选择10个过滤条件</span>
              </div>
            }
            overlayClassName="filterPopWrap"
            trigger="click"
            placement="bottomLeft"
          >
            <FilterOutlined
              style={{
                marginLeft: 6,
                color: 'rgb(0 0 0 / 60%)',
                cursor: 'pointer'
              }}
              onClick={(e) => e.stopPropagation()}
            />
          </Popover>
        </div>
      );
    } else if (item.type === 'symbol') {
      resultNode = (
        <div style={{ display: 'flex' }} className="symbolItem" key={index}>
          <span className="symbolItem" key={item.key} onClick={(e) => onSymbolClick(item, index, e)}>
            {item.value}
          </span>
        </div>
      );
    }

    return resultNode;
  };

  const renderInputs = () => {
    return (
      <div
        id="container"
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          height: '25px',
          marginTop: 12
        }}
      >
        {formulaList.map((item, index) => {
          return nodeRender(item, index);
        })}
      </div>
    );
  };

  return (
    <div className="calcFormulaWrap" onClick={onCalcFocus}>
      <div className="calcWrap">
        {/* <button onClick={handleAddInput}>Add Input</button>
          <button onClick={handleSubmit}>Confirm</button> */}
        <div ref={inputRef} className="content">
          {renderInputs()}
        </div>
      </div>
    </div>
  );
}
