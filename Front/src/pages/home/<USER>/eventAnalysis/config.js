import { t } from 'utils/translation';

export default {
  maxFilterCount: 20,
  operatorList: [
    {
      name: t('analysisCenter-FarZZNwJEotL'),
      operator: 'EQ'
    },
    {
      name: t('analysisCenter-vHrMOsiG9CLX'),
      operator: 'NE'
    },
    {
      name: t('analysisCenter-R6jbNm3rgHfX'),
      operator: 'GT'
    },
    {
      name: t('analysisCenter-sXGfMdryGNGv'),
      operator: 'GTE'
    },
    {
      name: t('analysisCenter-EVrD6ujJrtWP'),
      operator: 'LT'
    },
    {
      name: t('analysisCenter-q1s2veeEOzXl'),
      operator: 'LTE'
    },
    {
      name: t('analysisCenter-qIXPxlQwjAWJ'),
      operator: 'BETWEEN'
    },
    {
      name: t('analysisCenter-cv6Npw9aCzy9'),
      operator: 'ADVANCED_BETWEEN'
    },
    {
      name: t('analysisCenter-bk71JjuQzzur'),
      operator: 'IN'
    },
    {
      name: t('analysisCenter-dv1s0XHqko0S'),
      operator: 'NOT_IN'
    },
    {
      name: t('analysisCenter-WlDIqEc2P11Q'),
      operator: 'IS_NOT_NULL'
    },
    {
      name: t('analysisCenter-uoLh50HmFLHN'),
      operator: 'IS_NULL'
    },
    {
      name: t('analysisCenter-ANPZcZutt4nP'),
      operator: 'LIKE'
    },
    {
      name: t('analysisCenter-2eu05xG58ija'),
      operator: 'NOT_LIKE'
    },
    {
      name: t('analysisCenter-TNSBFFe2uUQa'),
      operator: 'START_WITH'
    },
    {
      name: t('analysisCenter-40CeqFf8ZXpn'),
      operator: 'NOT_START_WITH'
    },
    {
      name: t('analysisCenter-39v79y18hNtX'),
      operator: 'END_WITH'
    },
    {
      name: t('analysisCenter-upKXHw1YQpPw'),
      operator: 'NOT_END_WITH'
    },
    {
      name: t('analysisCenter-kZPuGFLAniZV'),
      operator: 'IS_TRUE'
    },
    {
      name: t('analysisCenter-lATIbUkc7lqI'),
      operator: 'IS_FALSE'
    }
  ],
  typeOperator: {
    INT: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    LONG: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    DOUBLE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    HIVE_DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
    DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
    DATETIME: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
    HIVE_TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
    TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
    STRING: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'LIKE',
      'NOT_LIKE',
      'START_WITH',
      'NOT_START_WITH',
      'END_WITH',
      'NOT_END_WITH',
      'IN',
      'NOT_IN'
    ],
    BOOL: ['IS_TRUE', 'IS_FALSE']
  },
  typeFormatter: {
    DATE: 'YYYY-MM-DD',
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    TIMESTAMP: 'YYYY-MM-DD HH:mm:ss',
    HIVE_DATE: 'YYYY-MM-DD',
    HIVE_TIMESTAMP: 'YYYY-MM-DD HH:mm:ss'
  },
  connector: [
    {
      name: t('analysisCenter-2ZaUZSdT7FkU'),
      value: 'AND'
    },
    {
      name: t('analysisCenter-WC6SRO5gm0Qy'),
      value: 'OR'
    }
  ],
  validator: {
    STRING: {
      option: {
        required: true,
        maxLen: 500
      },
      message: {
        required: t('analysisCenter-2hN7gzCaRDTo'),
        maxLen: t('analysisCenter-WdgJ6W6LrNc6')
      }
    },
    INT: {
      option: {
        required: true,
        maxLen: 11,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: t('analysisCenter-2hN7gzCaRDTo'),
        maxLen: t('analysisCenter-DRhiXUBdldLC'),
        regex: t('analysisCenter-8ucziCOoMPJ9')
      }
    },
    LONG: {
      option: {
        required: true,
        maxLen: 20,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: t('analysisCenter-2hN7gzCaRDTo'),
        maxLen: t('analysisCenter-j888IzjcXjSb'),
        regex: t('analysisCenter-8ucziCOoMPJ9')
      }
    },
    DOUBLE: {
      option: {
        required: true,
        // regex: '^\\d*[.]?\\d*$',
        regex: '^[+-]?\\d+(\\.\\d+)?$',
        maxLen: 20
      },
      message: {
        required: t('analysisCenter-2hN7gzCaRDTo'),
        maxLen: t('analysisCenter-j888IzjcXjSb'),
        regex: t('analysisCenter-ZhK1Uc4FTExC')
      }
    },
    DATETIME: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-DgyMenUNLSLj')
      }
    },
    TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-DgyMenUNLSLj')
      }
    },
    DATE: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-gIicE6E5VdR7')
      }
    },
    HIVE_TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-DgyMenUNLSLj')
      }
    },
    HIVE_DATE: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-gIicE6E5VdR7')
      }
    },
    BOOL: {
      option: {
        required: false
      },
      message: {
        required: t('analysisCenter-2hN7gzCaRDTo')
      }
    }
  }
};
