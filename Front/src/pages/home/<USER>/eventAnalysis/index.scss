.eventAnalysis {
  header {
    display: flex;
    height: 60px;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    margin: 0 -24px;
    padding: 0 24px;
    border-bottom: 1px solid #f0f0f0;

    .left {
      display: flex;
      align-items: center;
      margin: 0;

      .titleBack {
        display: flex;
        cursor: pointer;
      }

      .leftHandleWrapper {
        min-width: 400px;
        margin-left: 8px;

        .ant-input {
          font-size: 20px;
          font-weight: 700;
          box-sizing: border-box;
        }

        &:hover {
          border: 1px solid $primary_color;
          // box-shadow: 0px 0px 0px 2px rgba(255, 104, 0, 0.25);
          border-radius: 6px;
        }
      }
    }

    .right {
      .btnGroup {
        .ant-btn {
          border-radius: 6px;
        }
      }
    }
  }

  .content {
    height: calc(100vh - 120px);
    margin: 0 -24px;
    display: flex;
    overflow: hidden;
    border-right: 1px solid #f0f0f0;

    .ant-popover {
      .ant-popover-content {
        .ant-popover-arrow {
          display: none;
        }
      }
    }

    .ant-spin-nested-loading {
      width: 100%;

    }

    .anticon {
      //图标
      margin-right: 0;
    }

    .stepItem {
      .ant-tabs>.ant-tabs-nav {
        display: none;
      }

      .renderEvent {
        // position: relative;

        .eventBox {
          margin-top: 16px;
          height: 240px;
          overflow-y: auto;

          .popItem {
            .eventItem {
              position: relative;
              line-height: 32px;
              width: 100%;
              height: 32px;
              cursor: pointer;

              &:hover {
                background: $active_color;
              }
            }

          }
        }

        .eventTip {
          display: none;
          position: absolute;
          top: 0;
          right: -40px;
        }

        .eventInfo {
          .eventInfoTitle {
            font-weight: bold;
          }
        }
      }
    }

    .eventInfo {

      //子元素挂载到contend的样式
      .eventInfoTitle {
        font-size: 16px;
        font-weight: bold;
      }

      .eventInfoContent {
        .createInformation {
          color: rgba(0, 0, 0, 0.45);
        }

        .eventTable {
          margin-top: 16px;
          margin-bottom: 16px;
        }

        .line {
          .lineTitle {
            font-weight: bold;
            margin-bottom: 18px;
          }

          .lineChart {
            height: 280px;
          }
        }
      }
    }

    .left {
      height: calc(100vh - 120px);
      background-color: #fff;
      position: relative;

      .open {
        position: absolute;
        bottom: 20px;
        right: 20px;
        cursor: pointer;
      }

      .resize-save {
        position: absolute;
        top: 0;
        right: 5px;
        bottom: 0;
        left: 0;
        // padding: 24px;
        overflow-x: hidden;

        .screen {
          padding: 24px;
          border-bottom: 1px solid #f0f0f0;

          .ant-popover {

            .ant-popover-content>.ant-popover-inner {
              border-radius: 6px;

              .ant-popover-inner-content {
                padding: 12px 16px 3px 16px;

                .conversion-title {
                  font-size: 14px;
                  color: #8c8c8c;
                  margin-bottom: 24px;
                }

                .conversion-content {
                  display: flex;
                  justify-content: space-between;

                  .ant-input-number,
                  .ant-select {
                    width: 48%;
                    border-radius: 6px;

                    &>.ant-select-selector {
                      border-radius: 6px;
                    }
                  }
                }

                .tip {
                  font-size: 14px;
                  color: #FAAD14;
                }
              }
            }
          }

          .conversion {
            padding: 0 11px;
            cursor: pointer;
            color: #000;

            .svg {
              margin-left: 6px;
              font-size: 12px;
            }
          }

          &>div {
            color: #595959;
            height: 32px;
            line-height: 32px;

            .anticon-down {
              color: #000;
            }
          }
        }

        .process {
          padding: 24px;

          .title {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 16px;
          }

          .ant-tree {
            .ant-tree-node-content-wrapper {
              padding: 0;
              cursor: text;
            }

            .ant-tree-title {
              display: inline-block;
              width: 300px;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
          }

          .treeNoopNone {
            .ant-tree-switcher-noop {
              display: none;
            }
          }
        }
      }

      .resize-bar {
        height: inherit;
        resize: horizontal;
        opacity: 0;
        cursor: ew-resize;
        width: 440px;
        min-width: 320px;
        /* 最小宽度 320px */
        max-width: 640px;
        /* 最大宽度 640px */
        overflow: scroll;
      }

      .resize-line {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        border-right: 1px solid #f0f0f0;
        border-left: 1px solid #f0f0f0;
        pointer-events: none;
      }

      .resize-bar:hover~.resize-line,
      .resize-bar:active~.resize-line {
        border-left: 2px solid $primary_color;
      }

      .resize-bar::-webkit-scrollbar {
        width: 300px;
        height: inherit;
      }
    }

    .right {
      height: calc(100vh - 120px);
      padding: 16px;
      flex: 1 1 auto;
      background-color: #fff;
      box-sizing: border-box;
      // overflow: hidden;
      overflow-y: auto;

      .open {
        position: absolute;
        bottom: 20px;
        left: 10px;
        cursor: pointer;
      }

      .rightScreen {
        width: 100%;
        height: 48px;
        display: -webkit-inline-box;

        .site-input-group-wrapper1 {
          margin-right: -150px;

          .ant-input-group {
            .ant-input {
              &:first-child {
                width: 32% !important;
                // border-left: none;
                // border-radius: 6px 0 0 6px;
              }

              &:last-child {
                width: 32% !important;
                border-radius: 0 6px 6px 0;
              }
            }

          }

          .shortcutTime {
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;

            .ant-popover {
              width: 253px !important;

              .shortcutOptions-item {
                width: 100px;
                height: 32px;
                line-height: 32px;
                border-radius: 6px;
              }
            }
          }
        }

        // margin-bottom: 16px;
        &>.ant-select {
          margin-right: 8px;

          .ant-select-selector {
            border-radius: 6px;
          }
        }

        .selectTimeV2 .selectTimeV2-content {
          width: auto;

          .shortcut {
            .customTime {
              // padding: 0;
              // margin: 0 8px;
            }
          }

          .contentLeft {
            border-radius: 6px;
          }

          .shortcutOptions {
            .shortcutOptions-item {
              width: 90px;
            }
          }
        }
      }

      .chartType {
        // display: flex;
        // justify-content: flex-end;
        margin-left: auto;
        margin-top: 6px;

        .disableMask {
          position: absolute;
          display: inline-block;
          width: 24px;
          height: 24px;
          right: 40px;
        }

        .disableMask2 {
          position: absolute;
          display: inline-block;
          width: 24px;
          height: 24px;
          right: 5px;
        }

        .chartTypeIcon {
          margin-right: 4px;
          cursor: pointer;
          display: inline-block;
          border-radius: 4px;
          width: 32px;
          height: 32px;
          text-align: center;

          .anticon {
            position: relative;
            top: 5px;
            font-size: 16px !important;
          }

          :hover {
            transition: .2s all;
            color: $primary_color;
          }
        }

        .chartIconActive {
          background: $active_color;
          color: $primary_color;
        }
      }

      .chart {
        padding: 0 24px;

        .axisSetting {
          float: right;
          font-size: 12px;
          cursor: pointer;

          .anticon {
            margin-right: 6px;
            margin-bottom: 6px;
          }
        }
      }

      .table1 {
        padding: 0 24px;

        .eventHeader {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;

          .eventTableTitle {
            font-weight: 600;
            font-size: 16px;
          }
        }
      }

      .empty {
        &>.ant-empty {
          line-height: 300px;
        }
      }
    }
  }

  // .ant-btn {
  //   border-radius: 6px;
  // }

  .addButton {
    border-radius: 6px;

    &:nth-of-type(1) {
      margin-bottom: 40px
    }

    &:nth-of-type(2) {
      margin-bottom: 40px;
    }
  }

  .term {
    margin-left: 24px;
    display: flex;

    .title {
      line-height: 32px;
    }

    .ant-card>.ant-card-body {
      padding: 0;
      line-height: 32px;
      padding-left: 8px;

      .anticon-close {
        font-size: 12px;
        margin-left: 9px;
        cursor: pointer;
      }
    }
  }

  .ant-select-selection-overflow {
    padding-right: 33px;
  }
}

.axisSettinnPop {
  padding-top: 0;

  .ant-popover-arrow {
    display: none;
  }

  .ant-popover-inner {
    border-radius: 6px !important;

    .ant-popover-title {
      padding: 5px 24px 4px;
      height: 56px;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
    }

    .ant-popover-inner-content {
      width: 400px;
      padding: 0;

      .axisContent {
        padding: 24px;
        max-height: 314px;
        overflow-y: auto;

        .yAxisSetting {
          .yTitle {
            font-weight: 600;
            margin-bottom: 8px;
          }

          .yContent {
            .yItem {
              margin-bottom: 16px;
              display: flex;
              justify-content: space-between;

              .ystep {
                width: 22px;
                height: 22px;
                text-align: center;
                line-height: 22px;
                background: $primary_color;
                color: #fff;
                border-radius: 4px;
                font-size: 12px;
                margin-right: 8px;
              }
            }
          }
        }

        .xAxisSetting {
          .xTitle {
            font-weight: 600;
            margin-bottom: 8px;
          }

          .xContent {}
        }
      }

      .axisFooter {
        height: 56px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        border-top: 1px solid #f0f0f0;

        .ant-btn {
          border-radius: 6px;
        }

        .submit {
          margin: 0 8px;
        }
      }
    }
  }
}