import { t } from 'utils/translation';

export const colors = ['#FF7A45', '#40A9FF', '#5CDBD3', '#9254DE', '#FF4D4F', '#FFC53D'];
export const mockData = [
  {
    step: '1',
    name: t('analysisCenter-wEEQMahRdCUq'),
    dataList: [
      {
        date: '2022-07-01',
        type: t('analysisCenter-8wddayhdS7y5'),
        value: 12901,
        total: 100,
        ratio: 0.32
      },
      { date: '2022-07-01', type: '123', value: 12901, total: 100, ratio: 0.32 }
    ]
  },
  {
    step: '2',
    name: t('analysisCenter-ZnbvkBKRDMch'),
    dataList: [
      {
        date: '2022-07-01',
        type: t('analysisCenter-8wddayhdS7y5'),
        value: 11901,
        total: 100,
        ratio: 0.32
      }
      // { date: '2022-07-01', type: '12313', value: 12901, total: 100, ratio: 0.32 }
    ]
  },
  {
    step: '3',
    name: t('analysisCenter-6x8f0KbZu6ny'),
    dataList: [
      { date: '2022-07-01', type: t('analysisCenter-8wddayhdS7y5'), value: 5000, total: 100, ratio: 0.32 }
      // { date: '2022-07-01', type: '132', value: 12901, total: 100, ratio: 0.32 }
    ]
  }
];
