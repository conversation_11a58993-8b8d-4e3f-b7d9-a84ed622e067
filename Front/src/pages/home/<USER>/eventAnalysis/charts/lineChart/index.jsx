/*
 * @Author: 卜广硕 <EMAIL>
 * @Date: 2023-02-27 17:34:59
 * @LastEditors: 卜广硕 <EMAIL>
 * @LastEditTime: 2023-04-24 11:30:50
 * @FilePath: \Front\src\pages\home\analysisCenter\eventAnalysis\charts\column\index.jsx
 */
import { DualAxes, Line } from '@ant-design/charts';
import { Empty, Spin } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import './index.scss';

import { eventAnalysisContext } from '../../eventAnalysisContext';

/**
 * @description: 普通图表
 * @param {*} chartList 图表数据
 * @param {*} readOn 只读
 * @return {*}
 */
export default function LineChart({ loading, chartList, eventInfo }) {
  const [stepDataList, setStepDataList] = useState([]);
  const [stepLeftList, setStepLeftList] = useState([]);
  const [stepDataRightList, setStepDataRightList] = useState([]);
  const [isGroup, setIsGroup] = useState(false);
  const [chartEnum, setChartEnum] = useState({});

  const context = useContext(eventAnalysisContext);

  useEffect(() => {
    if (!_.isEmpty(chartList)) {
      const _chartList = _.cloneDeep(chartList);
      setChartEnum({
        nameType: _chartList[0].nameType,
        timeType: _chartList[0].timeType
      });
      const leftY = [];
      const rightY = [];

      const status = _chartList.find((item) => item.nameValue);
      setIsGroup(status);
      if (context) {
        if (!_.isEmpty(context.state.axisShowType.left)) {
          context.state.axisShowType.left.forEach((axisItem) => {
            const res = _chartList
              .filter((chartItem) => chartItem[chartItem.nameType] === axisItem)
              .map((mapItem) => {
                return {
                  ...mapItem,
                  value: mapItem.res
                };
              });
            leftY.push(...res);
          });
        }

        if (!_.isEmpty(context.state.axisShowType.right)) {
          context.state.axisShowType.right.forEach((axisItem) => {
            const res = _chartList
              .filter((chartItem) => chartItem[chartItem.nameType] === axisItem)
              .map((mapItem) => {
                return {
                  ...mapItem,
                  count: mapItem.res
                };
              });
            rightY.push(...res);
          });
        }
      } else {
        const _axisShowType = { left: [], right: [] };
        const _eventAnalysis = _.cloneDeep(eventInfo);

        if (!_eventAnalysis.axisConf) {
          _eventAnalysis.axisConf = _eventAnalysis.metric.map((item) => {
            return {
              eventName: item.name,
              step: item.step,
              metricName: item.metricName,
              side: 'LEFT'
            };
          });
        }

        _eventAnalysis.axisConf.forEach((item) => {
          if (item.side === 'LEFT') {
            _axisShowType.left.push(item.eventName);
          } else {
            _axisShowType.right.push(item.eventName);
          }
        });

        if (!_.isEmpty(_axisShowType.left)) {
          _axisShowType.left.forEach((axisItem) => {
            const res = _chartList
              .filter((chartItem) => chartItem[chartItem.nameType] === axisItem)
              .map((mapItem) => {
                return {
                  ...mapItem,
                  value: mapItem.res
                };
              });
            leftY.push(...res);
          });
        }

        if (!_.isEmpty(_axisShowType.right)) {
          _axisShowType.right.forEach((axisItem) => {
            const res = _chartList
              .filter((chartItem) => chartItem[chartItem.nameType] === axisItem)
              .map((mapItem) => {
                return {
                  ...mapItem,
                  count: mapItem.res
                };
              });
            rightY.push(...res);
          });
        }
      }

      setStepDataList(chartList);
      setStepLeftList(leftY);
      setStepDataRightList(rightY);
    } else {
      setStepDataList([]);
      setStepLeftList([]);
      setStepDataRightList([]);
    }
  }, [chartList, context?.state?.axisShowType]);

  const config = {
    data: [stepLeftList, stepDataRightList],
    xField: chartEnum.timeType,
    yField: ['value', 'count'],
    legend: {
      position: 'bottom',
      radio: false,
      offsetY: 12
    },
    slider: {
      padding: [12, 12, 0, 0]
    },
    tooltip: {
      enterable: true
    },
    yAxis: {
      value: {
        label: {
          formatter: (name) => {
            return parseFloat(Number(name).toFixed(2));
          }
        }
      },
      count: {
        label: {
          formatter: (name) => {
            return parseFloat(Number(name).toFixed(2));
          }
        }
      }
    },
    meta: {
      [chartEnum.timeType]: {
        sync: false
      }
    },
    // legend: false,
    geometryOptions: [
      {
        geometry: 'line',
        seriesField: `${isGroup ? 'displayName' : chartEnum.nameType}`
      },
      {
        geometry: 'line',
        seriesField: `${isGroup ? 'displayName' : chartEnum.nameType}`
      }
    ]
  };

  const configLeft = {
    data: stepLeftList,
    xField: chartEnum.timeType,
    yField: 'value',
    seriesField: `${isGroup ? 'displayName' : chartEnum.nameType}`,
    slider: {
      padding: [12, 12, 0, 0]
    },
    tooltip: {
      enterable: true
    },
    meta: {
      [chartEnum.timeType]: {
        sync: false
      }
    },
    yAxis: {
      position: 'left'
    },
    legend: {
      position: 'bottom',
      radio: false,
      offsetY: 12
    }
  };

  const configRight = {
    data: stepDataRightList,
    xField: chartEnum.timeType,
    yField: 'count',
    seriesField: `${isGroup ? 'displayName' : chartEnum.nameType}`,
    slider: {
      padding: [12, 12, 0, 0]
    },
    tooltip: {
      enterable: true
    },
    yAxis: {
      position: 'right'
    },
    meta: {
      [chartEnum.timeType]: {
        sync: false
      }
    },
    legend: {
      position: 'bottom',
      radio: false,
      offsetY: 12
    }
  };

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        padding: _.isEmpty(stepDataList) ? '100px' : '5px 5px',
        overflow: 'auto'
      }}
      className="lineChart"
    >
      {loading ? (
        <div className="mainChartSpin">
          <Spin />
        </div>
      ) : _.isEmpty(stepDataList) ? (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      ) : // : _.isEmpty(stepLeftList) && !_.isEmpty(stepDataRightList) ? <Line {...configRight} /> : <DualAxes {...config} />}
      !_.isEmpty(stepLeftList) && !_.isEmpty(stepDataRightList) ? (
        <DualAxes {...config} />
      ) : !_.isEmpty(stepLeftList) && _.isEmpty(stepDataRightList) ? (
        <Line {...configLeft} />
      ) : (
        <Line {...configRight} />
      )}
    </div>
  );
}
