.column {
  padding-top: 24px;

  .column-conversionRate {
    .topTitile {
      width: 100%;
      display: flex;
      font-size: 12px;
      font-weight: bold;
      margin-bottom: 16px;
      overflow: hidden;
      padding-right: 20%;

      &>div {
        width: 24%;
        display: flex;
        flex: 1;
        justify-content: center;
        text-align: center;
        align-items: center;
        white-space: nowrap;

        &:last-child {
          margin-right: 0;
        }
      }
    }

  }

  .columnContent {
    .columnContentItem {

      .columnContentItem-title {
        font-size: 14px;

        &>div {
          display: flex;
          justify-content: space-between;
        }
      }

      .cloumnCharts {
        width: 100%;
        margin-top: 8px;
        // margin-bottom: -20px;

        .cloumnChartsItem {
          display: flex;
          height: 20px;
          margin-bottom: 16px;

          .cloumnChartsItem-type {
            width: 10%;
            margin-right: 4px;
          }

          .cloumnChartsItem-value {
            width: 10%;
            margin-right: 4px;
          }

          .cloumnChartsItem-progress {
            width: 80%;
            background: #F5F5F5;

            .cloumnChartsItem-progress-bar {
              font-size: 14px;
              line-height: 20px;
              overflow: hidden;
              text-align: right;
              color: #fff;

              &:hover {
                animation: hover-border 1s ease-in-out infinite;
                border: 1px solid rgba(0, 0, 0, 0.8);
              }
            }
          }

          .cloumnChartsItem-total {
            width: 20%;
            text-align: center;
          }
        }

        .cloumnChartsFooter {
          display: flex;
          height: 45px;
          padding-right: 20%;
          position: relative;
          justify-content: center;

          .cloumnChartsFooterItem {
            height: 32px;
            margin-right: 12px;

            &:last-child {
              margin-right: 0;
            }

            .cloumnChartsFooter-arrow {
              width: 0px;
              height: 0px;
              border: 40px solid transparent;
              border-top: 6px solid transparent;
              border-bottom: 2px solid transparent;
              border-top-color: #F5F5F5;
            }

            .cloumnChartsFooter-inner {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 80px;
              height: 24px;
              background: #F5F5F5;
              border-top: 3px solid #FF7A45;
              border-top-left-radius: 6px;
              border-top-right-radius: 6px;
              cursor: pointer;
            }
          }
        }
      }
    }
  }

  .titleColors {
    display: inline-block;
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 2px;
  }
}

.lineChart {
  .g2-tooltip{
    max-height: 350px;
    overflow-y: auto;
  }
}

.funnelChartsPopover {
  .content {
    div {
      margin: 16px 0 8px 0;
    }
  }
}