/*
 * @Author: Wxw
 * @Date: 2022-08-18 17:45:23
 * @LastEditTime: 2023-04-24 18:30:09
 * @LastEditors: 卜广硕 <EMAIL>
 * @Description: 事件分析表格
 * @FilePath: \Front\src\pages\home\analysisCenter\funnelAnalysis\charts\renderTable.jsx
 */
import { Select, Table, Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import thousands from 'utils/thousands';
import { t } from 'utils/translation';
import { eventAnalysisContext } from '../eventAnalysisContext';
import './renderTable.scss';

const { Option } = Select;

export default () => {
  const { state } = useContext(eventAnalysisContext);
  const { isSwitch } = state;
  const [columns, setColumns] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [calcColumns, setCalcColumns] = useState('SUM');
  const { tableList, chartList, tableLoading, calcFinishData } = state;

  const stepStyle = {
    textAlign: 'center',
    position: 'relative',
    minWidth: 22,
    height: 22,
    borderRadius: 4,
    background: 'var(--ant-primary-color)',
    color: '#fff',
    fontSize: 12,
    lineHeight: '22px'
  };

  const onCalcSelect = (value) => {
    setCalcColumns(value);
  };

  const changeData = (data, field) => {
    let count = 0; // 重复项的第一项
    let indexCount = 1; // 下一项
    const _data = _.cloneDeep(data);
    while (indexCount < _data.length) {
      const item = _data.slice(count, count + 1)[0]; // 获取没有比较的第一个对象
      if (!item.rowSpan) {
        item.rowSpan = 1; // 初始化为1
      }
      if (item[field] === _data[indexCount][field]) {
        // 第一个对象与后面的对象相比，有相同项就累加，并且后面相同项设置为0
        item.rowSpan++;
        _data[indexCount].rowSpan = 0;
      } else {
        count = indexCount;
      }
      indexCount++;
    }
    return _data;
  };

  useEffect(() => {
    const getData = async () => {
      const tableChartList = chartList.map((item) => {
        return {
          valueText: item[item.nameType],
          customers: item.res,
          statDate: item[item.timeType],
          groupName: item.nameValue
        };
      });
      let newData = _.map(tableChartList, (item) => {
        return {
          ...item,
          valueText: `${item.valueText}`
        };
      });

      newData = _.sortBy(newData, (o) => {
        return o.statDate;
      });

      const list = Object.values(
        newData.reduce((acc, cur) => {
          const key = cur.groupName + cur.valueText;
          acc[key] = acc[key] || {};
          acc[key][cur.statDate] = {
            customers: cur.customers,
            groupName: cur.groupName,
            stateDate: cur.statDate,
            valueText: cur.valueText
          };
          acc[key].valueName = cur.valueText;
          acc[key].valueText = cur.valueText;
          acc[key].groupName = cur.groupName;
          return acc;
        }, {})
      );

      const list2 = Object.values(
        newData.reduce((acc, cur) => {
          const key = cur.groupName + cur.statDate;
          acc[key] = acc[key] || {};
          acc[key][cur.valueText] = {
            customers: cur.customers,
            groupName: cur.groupName,
            stateDate: cur.statDate,
            valueText: cur.valueText
          };
          acc[key].statDate = cur.statDate;
          acc[key].groupName = cur.groupName;
          return acc;
        }, {})
      );

      const columnsPackageData = [];

      _.forEach(newData, (item) => {
        columnsPackageData.push(item.statDate);
      });

      list.forEach((item) => {
        if (calcColumns === 'SUM') {
          let total = 0;
          for (const key in item) {
            if (typeof item[key] === 'object') {
              total += item[key].customers;
            }
          }
          item.total = Number(total.toFixed(2));
        } else if (calcColumns === 'AVG') {
          let total = 0;
          for (const key in item) {
            if (typeof item[key] === 'object') {
              total += item[key].customers;
            }
          }
          const dataLength = Object.values(item).filter((o) => typeof o === 'object').length;
          if (
            calcFinishData.metric.length &&
            calcFinishData.metric.every(
              (item) =>
                item.function === 'COUNT_DT_ID' ||
                item.function === 'UNI_DT_ID' ||
                item.function === 'COUNT_DT_ID_DIV_UNI_DT_ID'
            )
          ) {
            item.total = Math.round(total / dataLength);
          } else {
            item.total = Number((total / dataLength).toFixed(2));
          }
        } else if (calcColumns === 'MAX') {
          const customerList = [];
          for (const key in item) {
            if (typeof item[key] === 'object') {
              customerList.push(item[key].customers);
            }
          }
          item.total = _.max(customerList);
        } else {
          const customerList = [];
          for (const key in item) {
            if (typeof item[key] === 'object') {
              customerList.push(item[key].customers);
            }
          }
          item.total = _.min(customerList);
        }
      });

      const getColumns = (data) => {
        const columns = [];
        if (chartList[0].columnName) {
          columns.push({
            title: chartList[0].columnName,
            dataIndex: 'groupName',
            key: 'groupName',
            ellipsis: {
              showTitle: false
            },
            fixed: 'left',
            width: 200,
            onCell: (render) => {
              return { rowSpan: render.rowSpan };
            },
            render: (text) => (
              <Tooltip placement="topLeft" title={text}>
                {text}
              </Tooltip>
            )
          });
        }

        columns.push({
          title: t('analysisCenter-5ROPgpqUnGFH'),
          dataIndex: 'valueText',
          key: 'valueText',
          fixed: 'left',
          ellipsis: {
            showTitle: false
          },
          width: 200,
          render: (text) => {
            const valueArr = text.split('·');
            return (
              <span>
                <Tooltip title={text}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div style={stepStyle}>{valueArr[0]}</div>
                    <div
                      style={{
                        marginLeft: 8,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                      }}
                    >{`${valueArr[1]}·${valueArr[2]}`}</div>
                  </div>
                </Tooltip>
              </span>
            );
          }
        });

        columns.push({
          title: (
            <Select
              value={calcColumns}
              onChange={onCalcSelect}
              style={{ width: 108 }}
              onClick={(e) => e.stopPropagation()}
            >
              <Option value="SUM">{t('analysisCenter-6pJMHbkHx4uS')}</Option>
              <Option value="AVG">{t('analysisCenter-WcowdgHPWw0B')}</Option>
              <Option value="MAX">{t('analysisCenter-E2gSPPmepoNh')}</Option>
              <Option value="MIN">{t('analysisCenter-0CiAWF8EfwjF')}</Option>
            </Select>
          ),
          dataIndex: 'total',
          key: 'total',
          fixed: 'left',
          sorter: true,
          render: (text) => thousands(text),
          width: 150
        });
        Object.keys(data[0])
          .filter((key) => key !== 'valueName' && key !== 'valueText' && key !== 'total' && key !== 'groupName')
          .forEach((date) => {
            columns.push({
              title: date,
              dataIndex: date,
              key: date,
              width: 150,
              sorter: true,
              render: (text) => thousands(text)
            });
          });
        return columns;
      };

      const getColumns2 = (data) => {
        const columns = [];
        columns.push({
          title: t('analysisCenter-0vtAwvMYMZLk'),
          dataIndex: 'statDate',
          key: 'statDate',
          fixed: 'left',
          width: 150,
          sorter: true,
          onCell: (render) => {
            return { rowSpan: render.rowSpan };
          }
        });

        if (chartList[0].columnName) {
          columns.push({
            title: chartList[0].columnName,
            dataIndex: 'groupName',
            key: 'groupName',
            fixed: 'left',
            ellipsis: {
              showTitle: false
            },
            render: (text) => (
              <Tooltip placement="topLeft" title={text}>
                {text}
              </Tooltip>
            ),
            width: 200
          });
        }

        Object.keys(data[0])
          .filter((key) => key !== 'statDate' && key !== 'groupName')
          .forEach((valueText) => {
            const valueArr = valueText.split('·');

            columns.push({
              title: (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={stepStyle}>{valueArr[0]}</div>
                  <div style={{ marginLeft: 8 }}>{`${valueArr[1]}·${valueArr[2]}`}</div>
                </div>
              ),
              sorter: true,
              dataIndex: valueText,
              key: valueText,
              render: (text) => thousands(text),
              width: 150
            });
          });
        return columns;
      };

      const getDataSource = (data) => {
        return data.map((item) => {
          const record = {
            key: `${item.valueText}-${item.groupName}`,
            valueText: item.valueText,
            total: item.total,
            groupName: item.groupName
          };
          Object.keys(item)
            .filter((key) => key !== 'valueName' && key !== 'valueText' && key !== 'total' && key !== 'groupName')
            .forEach((date) => {
              record[date] = item[date]?.customers;
            });
          return record;
        });
      };

      const getDataSource2 = (data) => {
        const arr = [];
        return data.map((item) => {
          const record = {
            key: `${item.statDate}-${item.groupName}`,
            statDate: item.statDate,
            time: dayjs(item.statDate).valueOf(),
            groupName: item.groupName
          };

          Object.keys(item)
            .filter((key) => key !== 'statDate' && key !== 'groupName')
            .forEach((date) => {
              arr.push(date);
              record.metricName = Array.from(new Set(arr));
              record[date] = item[date]?.customers;
            });
          return record;
        });
      };

      const columns = isSwitch ? getColumns2(list2) : getColumns(list);

      const data = getDataSource(list);
      const data2 = getDataSource2(list2);

      const sortData = chartList[0].columnName
        ? data.sort((a, b) => {
            if (a.groupName === b.groupName) {
              return 0;
            } else {
              return a.groupName < b.groupName ? -1 : 1;
            }
          })
        : data;

      const finalListData = changeData(isSwitch ? data2 : sortData, isSwitch ? 'statDate' : 'groupName');

      setDataSource(finalListData);
      setColumns(columns);
    };
    getData();
  }, [tableList, calcColumns, chartList, isSwitch]);

  const onTableChange = (pagination, filters, sorter) => {
    const { field, order } = sorter;
    const { isSwitch } = state;

    const fieldVal = field === 'statDate' ? 'time' : field;

    const _dataSource = _.cloneDeep(dataSource);
    _dataSource.forEach((item) => {
      item.rowSpan = undefined;
    });

    if (order) {
      let sortRes = [];
      if (fieldVal === 'time') {
        sortRes = _dataSource.sort((a, b) =>
          order === 'ascend' ? a[fieldVal] - b[fieldVal] : b[fieldVal] - a[fieldVal]
        );
      } else {
        if (!isSwitch && calcFinishData.axisConf.length === 1) {
          sortRes = _dataSource.sort((a, b) =>
            order === 'ascend' ? a[fieldVal] - b[fieldVal] : b[fieldVal] - a[fieldVal]
          );
        } else {
          const groupedData = _dataSource.reduce((groups, item) => {
            const groupName = isSwitch ? item.statDate : item.groupName;
            if (!groups[groupName]) {
              groups[groupName] = [];
            }
            groups[groupName].push(item);
            return groups;
          }, {});

          for (const i in groupedData) {
            const groupValues = Object.values(groupedData[i]);
            const sortValues = groupValues.sort((a, b) =>
              order === 'ascend' ? a[fieldVal] - b[fieldVal] : b[fieldVal] - a[fieldVal]
            );
            groupedData[i] = sortValues;
          }

          for (const i in groupedData) {
            sortRes.push(...groupedData[i]);
          }
        }
      }

      const finalListData = changeData(sortRes, isSwitch ? 'statDate' : 'groupName');

      setDataSource(finalListData);
    }
  };

  return (
    <div className="eventTable">
      <Table
        className="virtual-table"
        columns={columns}
        dataSource={dataSource}
        bordered
        loading={tableLoading}
        scroll={{ x: 300, y: 500 }}
        onChange={onTableChange}
        pagination={false}
        summary={(pageData) => {
          const totalArr = [];
          if (pageData.length && pageData[0].metricName) {
            pageData[0].metricName.forEach((item) => {
              const totalCount = pageData.reduce((a, b) => {
                return a + b[item];
              }, 0);
              totalArr.push(thousands(totalCount));
            });
          }
          if (isSwitch) {
            return (
              <>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0}>{t('analysisCenter-6pJMHbkHx4uS')}</Table.Summary.Cell>
                  {pageData.length && pageData[0].groupName ? (
                    <Table.Summary.Cell index={1}> </Table.Summary.Cell>
                  ) : null}
                  {totalArr.map((totalItem, totalIndex) => (
                    <Table.Summary.Cell index={totalIndex + 2} key={totalIndex}>
                      {totalItem}
                    </Table.Summary.Cell>
                  ))}
                </Table.Summary.Row>
              </>
            );
          }
        }}
      />
      {/* <VirtualTable className="statisticTable" rowKey="valueText" dataSource={dataSource} columns={columns} scroll={{ x: 1500, y: 500 }} pagination={false} tableLoading={tableLoading} columnName={chartList[0].columnName} /> */}
    </div>
  );
};
