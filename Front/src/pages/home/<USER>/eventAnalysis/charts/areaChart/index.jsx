/*
 * @Author: 卜广硕 <EMAIL>
 * @Date: 2023-02-27 17:34:59
 * @LastEditors: 卜广硕 <EMAIL>
 * @LastEditTime: 2023-04-11 11:13:03
 * @FilePath: \Front\src\pages\home\analysisCenter\eventAnalysis\charts\column\index.jsx
 */
import { Area } from '@ant-design/charts';
import { Empty, Spin } from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import './index.scss';

/**
 * @description: 普通图表
 * @param {*} chartList 图表数据
 * @param {*} readOn 只读
 * @return {*}
 */
export default function AreaChart({ loading, chartList }) {
  const [stepDataList, setStepDataList] = useState([]);
  const [isGroup, setIsGroup] = useState(false);
  const [chartEnum, setChartEnum] = useState({});

  useEffect(() => {
    if (!_.isEmpty(chartList)) {
      const _chartList = _.cloneDeep(chartList);
      setChartEnum({
        nameType: _chartList[0].nameType,
        timeType: _chartList[0].timeType
      });
      const state = _chartList.find((item) => item.nameValue);
      setIsGroup(state);
      setStepDataList(_chartList);
    }
  }, [chartList]);

  const config = {
    data: stepDataList,
    xField: chartEnum.timeType,
    yField: 'res',
    legend: {
      position: 'bottom',
      radio: false,
      offsetY: 12
    },
    tooltip: {
      enterable: true
    },
    slider: {
      padding: [24, 12, 0, 0]
    },
    seriesField: isGroup ? 'displayName' : chartEnum.nameType
  };

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        padding: _.isEmpty(stepDataList) ? '100px' : '5px 5px',
        overflow: 'auto'
      }}
    >
      {loading ? (
        <div className="mainChartSpin">
          <Spin />
        </div>
      ) : _.isEmpty(stepDataList) ? (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      ) : (
        <Area {...config} />
      )}
    </div>
  );
}
