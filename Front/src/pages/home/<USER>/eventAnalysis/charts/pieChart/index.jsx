/*
 * @Author: 卜广硕 <EMAIL>
 * @Date: 2023-02-27 17:34:59
 * @LastEditors: 卜广硕 <EMAIL>
 * @LastEditTime: 2023-03-21 17:46:27
 * @FilePath: \Front\src\pages\home\analysisCenter\eventAnalysis\charts\column\index.jsx
 */
import { Pie } from '@ant-design/charts';
import { Empty, Spin } from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import './index.scss';

/**
 * @description: 普通图表
 * @param {*} chartList 图表数据
 * @param {*} readOn 只读
 * @return {*}
 */
export default function PieChart({ loading, chartList }) {
  const [stepDataList, setStepDataList] = useState([]);

  const stepStyle = {
    textAlign: 'center',
    position: 'relative',
    width: 22,
    height: 22,
    borderRadius: 4,
    background: 'var(--ant-primary-color)',
    color: '#fff',
    fontSize: 12,
    lineHeight: '22px'
  };

  /**
   * @description: 事件分组
   * @param {*} originalArr 图表数据
   * @param {*} field 事件Key
   * @param {*} Field 分组里维度Key
   * @param {*} Sumfield 要累加的key
   * @return {*}
   */

  const getArrWithKey = (originalArr, field, Field, Sumfield) => {
    const tempArr = [];
    const endData = [];

    for (let i = 0; i < originalArr.length; i++) {
      if (tempArr.indexOf(originalArr[i][field]) === -1) {
        endData.push({
          [field]: originalArr[i][field],
          label: originalArr[i][field],
          step: originalArr[i].step,
          metricName: originalArr[i].metricName,
          data: [originalArr[i]],
          eventValue: originalArr[i].eventValue
        });
        tempArr.push(originalArr[i][field]);
      } else {
        for (let j = 0; j < endData.length; j++) {
          if (endData[j][field] === originalArr[i][field]) {
            endData[j].data.push(originalArr[i]);
            break;
          }
        }
      }
    }

    endData.forEach((item) => {
      const _data = _.cloneDeep(item.data);
      const objValue = _data.reduce((obj, item) => {
        if (!obj[item[Field]]) {
          obj[item[Field]] = 0;
        }
        obj[item[Field]] += Number(item[Sumfield]);
        return obj;
      }, {});

      const newArray = Object.keys(objValue).map((key) => ({
        [Field]: key,
        [Sumfield]: objValue[key]
      }));

      item.data = newArray;
    });

    return endData;
  };

  useEffect(() => {
    if (!_.isEmpty(chartList)) {
      const _chartList = _.cloneDeep(chartList);

      const newChartList = getArrWithKey(_chartList, _chartList[0].nameType, 'nameValue', 'res');
      setStepDataList(newChartList);
    } else {
      setStepDataList([]);
    }
  }, [chartList]);

  const nameSplit = (text) => {
    if (text.length > 12) {
      return `${text.slice(0, 12)}...`;
    } else {
      return text;
    }
  };
  const config = {
    appendPadding: 10,
    angleField: 'res',
    colorField: 'nameValue',
    radius: 0.7,
    innerRadius: 0.7,
    legend: {
      position: 'bottom',
      offsetX: 10,
      radio: false
    }
    // tooltip: {
    //   fields: ['nameValue', 'res', 'percent'],
    //   customContent: (title, datum) => {
    //     console.log(datum);
    //     // return { name: datum.x, value: datum.y + '%' };
    //   }
    // }
  };

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        padding: _.isEmpty(stepDataList) ? '100px' : '0 5px',
        overflow: 'auto'
      }}
    >
      {loading ? (
        <div className="mainChartSpin">
          <Spin />
        </div>
      ) : _.isEmpty(stepDataList) ? (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      ) : (
        <div className="pieWrap" id="pie" style={stepDataList?.length > 1 ? {} : { justifyContent: 'center' }}>
          {stepDataList.map((item, index) => {
            return (
              <div
                className="pieItem"
                key={index}
                style={stepDataList?.length > 1 ? { minWidth: '50%' } : { minWidth: '100%' }}
              >
                <Pie
                  {...config}
                  data={item.data}
                  label={
                    item.data?.length >= 10
                      ? false
                      : {
                          layout: '',
                          formatter: (data) => {
                            return `${nameSplit(data.nameValue)}\n${parseFloat(data.percent * 100).toFixed(2)}%`;
                          },
                          offset: 24
                        }
                  }
                  tooltip={{
                    showTitle: true,
                    title: 'nameValue',
                    customContent: (title, data) => {
                      let sum = 0;
                      item.data.forEach((val) => (sum += val.res));
                      return `<div style="padding: 12px 4px;list-style:none;"><span style="background-color:${
                        data[0]?.color
                      };" class="g2-tooltip-marker"></span>${data[0]?.data.nameValue}<span style='margin-left: 24px'>${
                        data[0]?.data.res
                      }<span>（${
                        data[0]?.data.res ? parseFloat(((data[0]?.data.res / sum) * 100).toFixed(2)) : '0'
                      }%）</span></div>`;
                    }
                  }}
                  statistic={{
                    title: false,
                    content: {
                      style: {
                        whiteSpace: 'pre-wrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        lineHeight: 2,
                        fontSize: 16,
                        width: 200,
                        fontWeight: 400
                      },
                      offsetY: -170,
                      customHtml: () => (
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <div style={stepStyle}>{item.step}</div>
                          <div style={{ marginLeft: 8 }}>{item.eventValue}</div>
                        </div>
                      )
                      // content: <div>{}</div>
                    }
                  }}
                />
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
