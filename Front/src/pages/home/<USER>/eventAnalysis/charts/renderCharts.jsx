/*
 * @Author: Wxw
 * @Date: 2022-08-18 17:45:23
 * @LastEditTime: 2023-03-20 11:53:01
 * @LastEditors: 卜广硕 <EMAIL>
 * @Description:
 * @FilePath: \Front\src\pages\home\analysisCenter\funnelAnalysis\charts\renderCharts.jsx
 */
import React, { useContext } from 'react';
import { eventAnalysisContext } from '../eventAnalysisContext';
import Area from './areaChart';
import Column from './column';
import Line from './lineChart';
import Pie from './pieChart';

export default ({ chartList, loading }) => {
  const { state } = useContext(eventAnalysisContext);
  const { chartDisplayType, selectedRowKeys, tableColumn, lineChartList, tableList } = state;

  const renderAddCharts = () => {
    switch (chartDisplayType) {
      case 'COLUMN_CHART':
        return <Column chartList={chartList} selectedRowKeys={selectedRowKeys} readOn={false} loading={loading} />;
      case 'PERCENT_CHART':
        return <Pie chartList={chartList} selectedRowKeys={selectedRowKeys} loading={loading} readOn={false} />;
      case 'LINE_CHART':
        return (
          <Line
            lineChartList={lineChartList}
            tableColumn={tableColumn}
            chartList={chartList}
            selectedRowKeys={selectedRowKeys}
            readOn={false}
            loading={loading}
            tableList={tableList}
          />
        );
      case 'AREA_CHART':
        return (
          <Area
            lineChartList={lineChartList}
            tableColumn={tableColumn}
            chartList={chartList}
            selectedRowKeys={selectedRowKeys}
            readOn={false}
            loading={loading}
            tableList={tableList}
          />
        );
      default:
    }
  };

  return <div>{renderAddCharts()}</div>;
};
