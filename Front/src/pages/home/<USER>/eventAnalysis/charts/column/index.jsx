/*
 * @Author: 卜广硕 <EMAIL>
 * @Date: 2023-02-27 17:34:59
 * @LastEditors: 卜广硕 <EMAIL>
 * @LastEditTime: 2023-04-24 15:32:36
 * @FilePath: \Front\src\pages\home\analysisCenter\eventAnalysis\charts\column\index.jsx
 */
import { Column, DualAxes } from '@ant-design/charts';
import { Empty, Spin } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import './index.scss';

import { eventAnalysisContext } from '../../eventAnalysisContext';

/**
 * @description: 普通图表
 * @param {*} chartList 图表数据
 * @param {*} readOn 只读
 * @return {*}
 */
export default function ColumnCharts({ loading, chartList, readOn, eventInfo }) {
  const [stepDataList, setStepDataList] = useState([]);
  const [stepLeftList, setStepLeftList] = useState([]);
  const [stepDataRightList, setStepDataRightList] = useState([]);
  const [isGroup, setIsGroup] = useState(false);
  const [xAxis, setAxis] = useState('time');
  const [chartEnum, setChartEnum] = useState({});

  const context = useContext(eventAnalysisContext);

  const getArrWithKey = (originalArr, field, Field, Sumfield) => {
    const tempArr = [];
    const endData = [];
    const result = [];

    for (let i = 0; i < originalArr.length; i++) {
      if (tempArr.indexOf(originalArr[i][field]) === -1) {
        endData.push({
          [field]: originalArr[i][field],
          nameType: originalArr[i].nameType,
          data: [originalArr[i]]
        });
        tempArr.push(originalArr[i][field]);
      } else {
        for (let j = 0; j < endData.length; j++) {
          if (endData[j][field] === originalArr[i][field]) {
            endData[j].data.push(originalArr[i]);
            break;
          }
        }
      }
    }

    endData.forEach((item) => {
      const _data = _.cloneDeep(item.data);
      const objValue = _data.reduce((obj, item) => {
        if (!obj[item[Field]]) {
          obj[item[Field]] = 0;
        }
        obj[item[Field]] += Number(item[Sumfield]);
        return obj;
      }, {});

      const newArray = Object.keys(objValue).map((key) => ({
        [Field]: key,
        [Sumfield]: objValue[key]
      }));

      item.data = newArray;
    });

    endData.forEach((item) => {
      item.data.forEach((dataItem) => {
        result.push({
          [item.nameType]: item[item.nameType],
          nameValue: dataItem.nameValue,
          nameType: item.nameType,
          res: dataItem.res
        });
      });
    });

    return result;
  };

  useEffect(() => {
    if (!_.isEmpty(chartList)) {
      let _chartList = _.cloneDeep(chartList);
      setChartEnum({
        nameType: _chartList[0].nameType,
        timeType: _chartList[0].timeType
      });
      const leftY = [];
      const rightY = [];
      const status = _chartList.find((item) => item.nameValue);
      setIsGroup(status);
      if (!readOn) {
        setAxis(context.state.xAxisType);
        _chartList =
          context.state.xAxisType === 'group'
            ? getArrWithKey(_chartList, _chartList[0].nameType, 'nameValue', 'res')
            : _chartList;

        if (!_.isEmpty(context.state.axisShowType.left)) {
          context.state.axisShowType.left.forEach((axisItem) => {
            const res = _chartList
              .filter((chartItem) => chartItem[chartItem.nameType] === axisItem)
              .map((mapItem) => {
                return {
                  ...mapItem,
                  value: mapItem.res
                };
              });
            leftY.push(...res);
          });
        }

        if (!_.isEmpty(context.state.axisShowType.right)) {
          context.state.axisShowType.right.forEach((axisItem) => {
            const res = _chartList
              .filter((chartItem) => chartItem[chartItem.nameType] === axisItem)
              .map((mapItem) => {
                return {
                  ...mapItem,
                  value: mapItem.res
                };
              });
            rightY.push(...res);
          });
        }
      } else {
        const _axisShowType = { left: [], right: [] };
        const _eventAnalysis = _.cloneDeep(eventInfo);
        if (!_eventAnalysis.axisConf) {
          _eventAnalysis.axisConf = _eventAnalysis.metric.map((item) => {
            return {
              eventName: item.name,
              side: 'LEFT'
            };
          });
        }

        _eventAnalysis.axisConf.forEach((item) => {
          if (item.side === 'LEFT') {
            _axisShowType.left.push(item.eventName);
          } else {
            _axisShowType.right.push(item.eventName);
          }
        });

        if (!_.isEmpty(_axisShowType.left)) {
          _axisShowType.left.forEach((axisItem) => {
            const res = _chartList
              .filter((chartItem) => chartItem[chartItem.nameType] === axisItem)
              .map((mapItem) => {
                return {
                  ...mapItem,
                  value: mapItem.res
                };
              });
            leftY.push(...res);
          });
        }

        if (!_.isEmpty(_axisShowType.right)) {
          _axisShowType.right.forEach((axisItem) => {
            const res = _chartList
              .filter((chartItem) => chartItem[chartItem.nameType] === axisItem)
              .map((mapItem) => {
                return {
                  ...mapItem,
                  value: mapItem.res
                };
              });
            rightY.push(...res);
          });
        }
      }

      setStepDataList(chartList);
      setStepLeftList(leftY);
      setStepDataRightList(rightY);
    } else {
      setStepDataList([]);
      setStepLeftList([]);
      setStepDataRightList([]);
    }
  }, [chartList, context?.state?.axisShowType, context?.state?.xAxisType]);

  const config = {
    data: [stepLeftList, stepDataRightList],
    xField: xAxis === 'group' ? 'nameValue' : chartEnum.timeType,
    yField: ['value', 'value'],
    legend: {
      position: 'bottom',
      radio: false
    },
    slider: {
      padding: [24, 12, 8, 0]
    },
    tooltip: {
      enterable: true
    },
    meta: {
      [xAxis === 'group' ? 'nameValue' : chartEnum.timeType]: {
        sync: false
      }
    },
    yAxis: {
      value: {
        label: {
          formatter: (name) => {
            return parseFloat(Number(name).toFixed(2));
          }
        }
      }
    },
    maxColumnWidth: 30,
    geometryOptions: [
      {
        geometry: 'column',
        maxColumnWidth: 30,
        seriesField: `${isGroup ? (xAxis === 'group' ? chartEnum.nameType : 'displayName') : chartEnum.nameType}`,
        groupField: chartEnum.nameType,
        isGroup: true,
        isStack: xAxis === 'time'
        // interactions: [
        //   {
        //     type: 'element-highlight-by-color'
        //   }
        // ]
      },
      {
        geometry: 'column',
        isGroup: true,
        maxColumnWidth: 30,
        seriesField: `${isGroup ? (xAxis === 'group' ? chartEnum.nameType : 'displayName') : chartEnum.nameType}`,
        groupField: chartEnum.nameType,
        isStack: xAxis === 'time'
        // interactions: [
        //   {
        //     type: 'element-highlight-by-color'
        //   }
        // ]
      }
    ]
  };

  const configLeft = {
    data: stepLeftList,
    xField: xAxis === 'group' ? 'nameValue' : chartEnum.timeType,
    yField: 'value',
    legend: {
      position: 'bottom',
      radio: false,
      offsetY: 12
    },
    tooltip: {
      enterable: true,
      shared: false,
      formatter: (datum) => ({
        name: isGroup ? (xAxis === 'group' ? datum[chartEnum.nameType] : datum.displayName) : datum[chartEnum.nameType],
        value: datum.value
      })
    },
    slider: {
      padding: [24, 12, 0, 0]
    },
    yAxis: {
      position: 'left'
    },
    interactions: [
      {
        type: 'element-highlight-by-color'
      }
    ],
    meta: {
      [xAxis === 'group' ? 'nameValue' : chartEnum.timeType]: {
        sync: false
      }
    },
    maxColumnWidth: 30,
    seriesField: `${isGroup ? (xAxis === 'group' ? chartEnum.nameType : 'displayName') : chartEnum.nameType}`,
    groupField: chartEnum.nameType,
    isGroup: true,
    isStack: xAxis === 'time'
  };

  const configRight = {
    data: stepDataRightList,
    xField: xAxis === 'group' ? 'nameValue' : chartEnum.timeType,
    yField: 'value',
    legend: {
      position: 'bottom',
      radio: false,
      offset: 12
    },
    maxColumnWidth: 30,
    yAxis: {
      position: 'right'
    },
    tooltip: {
      enterable: true,
      shared: false,
      formatter: (datum) => ({
        name: isGroup ? (xAxis === 'group' ? datum[chartEnum.nameType] : datum.displayName) : datum[chartEnum.nameType],
        value: datum.value
      })
    },
    interactions: [
      {
        type: 'element-highlight-by-color'
      }
    ],
    slider: {
      padding: [24, 12, 0, 0]
    },
    meta: {
      [xAxis === 'group' ? 'nameValue' : chartEnum.timeType]: {
        sync: false
      }
    },
    seriesField: `${isGroup ? (xAxis === 'group' ? chartEnum.nameType : 'displayName') : chartEnum.nameType}`,
    groupField: chartEnum.nameType,
    isGroup: true,
    isStack: xAxis === 'time'
  };

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        padding: _.isEmpty(stepDataList) ? '100px' : '5px 5px',
        overflow: 'auto'
      }}
      className="columnChart"
    >
      {loading ? (
        <div className="mainChartSpin">
          <Spin />
        </div>
      ) : _.isEmpty(stepDataList) ? (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      ) : // : _.isEmpty(stepLeftList) && !_.isEmpty(stepDataRightList) ? <Column {...configRight} /> : <DualAxes {...config} />}
      !_.isEmpty(stepLeftList) && !_.isEmpty(stepDataRightList) ? (
        <DualAxes {...config} />
      ) : !_.isEmpty(stepLeftList) && _.isEmpty(stepDataRightList) ? (
        <Column {...configLeft} />
      ) : (
        <Column {...configRight} />
      )}
    </div>
  );
}
