.lineCharts {
  height: 100%;
  display: flex;
  // min-height: 375px;

  .lineChartsLeft {
    width: 35%;
    min-height: 375px;
    overflow: auto;

    .arrow-arrow {
      width: 0px;
      height: 0px;
      border: 75px solid transparent;
      border-top: 6px solid transparent;
      border-bottom: 2px solid transparent;
      border-top-color: #FAFAFA;
    }

    .arrow-inner {
      width: 150px;
      height: 24px;
      display: flex;
      white-space: nowrap;
      justify-content: center;
      align-items: center;
      color: rgba(0, 0, 0, 0.85);
      background: #FAFAFA;
      // border-top: 3px solid #FF7A45;
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
      cursor: pointer;
    }

    .arrow {
      height: 32px;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 13px 0;
      // justify-content: center;
      // margin-right: 16px;

      .stepName {
        width: 280px;
        height: 40px;
        margin-top: 13px;
        padding: 0 10px;
        display: flex;
        line-height: 40px;
        background: #f6f6f6;
        border-radius: 6px;

        &>div:last-child {
          margin-left: auto;
        }
      }
    }

    .stepItem {
      display: flex;
      flex-direction: column;
      align-items: center;
      // justify-content: center;
      // margin-right: 16px;

      .stepName {
        width: 280px;
        height: 40px;
        // margin-top: 13px;
        padding: 0 10px;
        display: flex;
        line-height: 40px;
        background: #f6f6f6;
        border-radius: 6px;

        &>div:last-child {
          margin-left: auto;
        }
      }
    }
  }

  .lineChartsRight {
    width: 65%;
    margin-left: 15px;
  }
}