import React, { useState, useRef } from 'react';
import { VariableSizeGrid as Grid } from 'react-window';
import ResizeObserver from 'rc-resize-observer';
import classNames from 'classnames';
import { Table, Tooltip } from 'antd';
import './index.scss';

function VirtualTable(props) {
  const { columns, scroll, tableLoading, columnName } = props;
  const [tableWidth, setTableWidth] = useState(0);
  const widthColumnCount = columns.filter(({ width }) => !width).length;
  const mergedColumns = columns.map((column) => {
    if (column.width) {
      return column;
    }

    return { ...column, width: Math.floor(tableWidth / widthColumnCount) };
  });
  const gridRef = useRef();
  const [connectObject] = useState(() => {
    const obj = {};
    Object.defineProperty(obj, 'scrollLeft', {
      get: () => null,
      set: (scrollLeft) => {
        if (gridRef.current) {
          gridRef.current.scrollTo({
            scrollLeft
          });
        }
      }
    });
    return obj;
  });

  const renderTableCell = ({ rawData, rowIndex, mergedColumns, columnIndex }) => {
    if (columnName) {
      if (columnIndex !== 0) {
        if (columnIndex === 1) {
          return rawData[rowIndex].valueName;
        }

        if (columnIndex === 2) {
          return rawData[rowIndex].total;
        }

        return `${rawData[rowIndex][mergedColumns[columnIndex].dataIndex]?.customers}`;
      }
    } else {
      if (columnIndex !== 0) {
        if (columnIndex === 1) {
          return rawData[rowIndex].total;
        }
        return `${rawData[rowIndex][mergedColumns[columnIndex].dataIndex]?.customers}`;
      }
      return rawData[rowIndex].valueName;
    }
    return rawData[rowIndex].groupName;
  };

  const renderVirtualList = (rawData, { scrollbarSize, ref, onScroll }) => {
    ref.current = connectObject;
    const totalHeight = rawData.length * 54;
    return (
      <Grid
        ref={gridRef}
        className="virtual-grid"
        columnCount={mergedColumns.length}
        columnWidth={(index) => {
          const { width } = mergedColumns[index];
          return totalHeight > scroll.y && index === mergedColumns.length - 1 ? width - scrollbarSize - 1 : width;
        }}
        height={scroll.y}
        rowCount={rawData.length}
        rowHeight={() => 54}
        width={tableWidth}
        onScroll={({ scrollLeft }) => {
          onScroll({
            scrollLeft
          });
        }}
      >
        {({ columnIndex, rowIndex, style }) => (
          <div
            className={classNames('virtual-table-cell', {
              'virtual-table-cell-last': columnIndex === mergedColumns.length - 1
            })}
            style={style}
          >
            {/* {rawData[rowIndex][mergedColumns[columnIndex].dataIndex]} */}
            <Tooltip
              title={renderTableCell({
                rawData,
                rowIndex,
                mergedColumns,
                columnIndex
              })}
            >
              {renderTableCell({
                rawData,
                rowIndex,
                mergedColumns,
                columnIndex
              })}
            </Tooltip>
          </div>
        )}
      </Grid>
    );
  };

  return (
    <ResizeObserver
      onResize={({ width }) => {
        setTableWidth(width);
      }}
    >
      <Table
        {...props}
        className="virtual-table"
        columns={columns}
        bordered
        loading={tableLoading}
        components={{
          body: renderVirtualList
        }}
      />
    </ResizeObserver>
  );
}

export default VirtualTable;
