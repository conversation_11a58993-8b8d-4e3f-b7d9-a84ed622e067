import { Alert, Form, Input, Radio, TreeSelect, message } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useImperativeHandle, useState } from 'react';
import { useHistory } from 'react-router-dom';
import DashboardService from 'service/dashboardService';

import { eventAnalysisContext } from '../eventAnalysisContext';

const layouts = [
  [{ x: 0, y: 1000, w: 4, h: 3, isResizable: true }],
  [
    { x: 0, y: 1000, w: 4, h: 3, isResizable: true },
    { x: 4, y: 1000, w: 4, h: 3, isResizable: true }
  ],
  [
    { x: 0, y: 1000, w: 4, h: 3, isResizable: true },
    { x: 4, y: 1000, w: 4, h: 3, isResizable: true },
    { x: 8, y: 1000, w: 4, h: 3, isResizable: true }
  ]
];

export default function SaveChart(props) {
  const { onRef, chartName, boardType, detailObj } = props;
  const history = useHistory();
  const [form] = Form.useForm();
  const [form2] = Form.useForm();
  const [addBoardType, setAddBoardType] = useState('不添加到数据看板');

  const [allChartBoard, setAllChartBoard] = useState([]); // 所有未分组的数据看板
  const [allChartBoardCategory, setAllChartBoardCategory] = useState([]); // 所有文件夹

  const [chartBoards, setChartBoards] = useState([]); // 选中的数据看板
  const [menuList, setMunuList] = useState([]); // 文件夹List
  const [boardList, setBoardList] = useState([]); // 数据看板List

  const { state, dispatch } = useContext(eventAnalysisContext);

  useEffect(() => {
    setBoardList([]);
    form2.resetFields();
  }, [addBoardType]);

  const saveCharts = async () => {
    const formValue = await form.validateFields();
    const formValue2 = await form2.validateFields();
    const { addBoard, chartName } = formValue;
    const { boardName, folder } = formValue2;
    let findBoard;
    if (addBoard === '添加到已存在的数据看板') {
      // 根据chartBoards里面的的id去filter allChartBoard
      findBoard = allChartBoard.filter((w) => chartBoards.includes(`${w.id}`));
      findBoard.map((item) => {
        item.layouts.xxs = [...item.layouts.xxs, ...layouts[chartBoards.length - 1]];
        item.widgets = [...item.widgets, ...layouts[chartBoards.length - 1]];
        return item;
      });
    } else if (addBoard === '添加到新的数据看板') {
      findBoard = [
        {
          boardName,
          category: allChartBoardCategory.find((w) => w.id === parseInt(folder.replace('folder.', ''))),
          layouts: { xxs: layouts[0] },
          widgets: layouts[0]
        }
      ];
    }
    const saveChartValue = {
      ...formValue,
      ...formValue2,
      chartBoards: findBoard,
      name: chartName
    };
    return _.omit(saveChartValue, ['addBoard', 'boardName', 'chartName']);
  };

  useImperativeHandle(onRef, () => ({
    saveCharts
  }));

  const onValuesChange = async (changedValues) => {
    dispatch({ displayType: changedValues.displayType });
    if (changedValues?.addBoard) setAddBoardType(changedValues.addBoard);
    if (
      changedValues.addBoard === '添加到已存在的数据看板' ||
      (changedValues.addBoard === '添加到新的数据看板' && _.isEmpty(boardList))
    ) {
      const allCategoryList = await DashboardService.getAllchartBoardCategory([
        {
          operator: 'EQ',
          propertyName: 'deptId',
          value: window.getDeptId()
        }
      ]);
      const chartBoardList = await DashboardService.getAllchartBoard([
        {
          operator: 'EQ',
          propertyName: 'deptId',
          value: window.getDeptId()
        }
      ]);
      setAllChartBoard(chartBoardList);
      setAllChartBoardCategory(allCategoryList);
      const menuList = getLevelData(chartBoardList, allCategoryList, 0);
      const boardList = getLevelData(chartBoardList, allCategoryList, 0, true);

      setMunuList(menuList);
      setBoardList(boardList);
    }
  };

  const getLevelData = (chartBoardList, data, parentId, isBoard) => {
    const itemArr = [];
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      if (node.parentId === parentId) {
        const newNode = {};
        newNode.value = `folder.${node.id}`;
        if (isBoard) newNode.selectable = false;
        newNode.title = node.name;
        newNode.isLeaf = false;
        const res = getLevelData(chartBoardList, data, node.id, isBoard);
        if (res.length > 0) {
          newNode.children = res;
        }
        if (isBoard) {
          const list = chartBoardList.filter((w) => parseInt(node.id) === w.category?.id);
          if (list.length > 0) {
            if (newNode.children) {
              list.forEach((n) => {
                newNode.children.push({
                  value: `${n.id}`,
                  title: n.boardName,
                  isLeaf: true
                });
              });
            } else {
              newNode.children = list.map((n) => ({
                value: `${n.id}`,
                title: n.boardName,
                isLeaf: true
              }));
            }
          }
        }
        itemArr.push(newNode);
      }
    }
    return itemArr;
  };

  const changeOne = (selectKeys) => {
    if (selectKeys.length > 3) {
      setChartBoards(selectKeys.slice(0, 3));
      return message.error('最多只能选择3个数据看板');
    } else {
      setChartBoards(selectKeys);
    }
  };

  const renderAddBoard = () => {
    if (addBoardType === '不添加到数据看板') return null;
    else if (addBoardType === '添加到已存在的数据看板') {
      return (
        <TreeSelect
          multiple
          style={{ width: '100%' }}
          value={chartBoards}
          onChange={changeOne}
          treeData={boardList}
          placeholder="请选择"
        />
      );
    } else if (addBoardType === '添加到新的数据看板') {
      return (
        <Form form={form2} layout="vertical">
          <Form.Item
            label="看板名称"
            name="boardName"
            rules={[
              { required: true, message: '请输入看板名称' },
              { max: 20, message: '看板名称最多20个字符' }
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item label="所属文件夹" name="folder" rules={[{ required: true, message: '请选择所属文件夹' }]}>
            <TreeSelect
              // multiple
              style={{ width: '100%' }}
              // value={chartBoards}
              // onChange={onChange}
              treeData={menuList}
            />
          </Form.Item>
        </Form>
      );
    }
  };

  return (
    <div>
      <Alert
        message={
          boardType
            ? '图表将保存至活动分析页面，数据将通过活动用户适配'
            : '图表将保存至分析图表页面，也可添加至数据看板'
        }
        showIcon
        type="info"
        className="mb-[16px] "
      />
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          chartName,
          displayType: 'LINE_CHART',
          addBoard: '不添加到数据看板'
        }}
        onValuesChange={onValuesChange}
      >
        <Form.Item
          label="图表名称"
          name="chartName"
          rules={[
            { required: true, message: '请输入图表名称' },
            { max: 32, message: '图表名称最多32个字符' }
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item label="展示方式" name="displayType" rules={[{ required: true, message: '请选择展示方式' }]}>
          <Radio.Group>
            <Radio value="LINE_CHART"> 折线图 </Radio>
            <Radio value="COLUMN_CHART"> 柱状图 </Radio>
            <Radio value="PERCENT_CHART" disabled={!state.isGroupType}>
              {' '}
              饼状图{' '}
            </Radio>
            <Radio value="AREA_CHART" disabled={!state.isAreaType}>
              {' '}
              堆叠图{' '}
            </Radio>
          </Radio.Group>
        </Form.Item>

        {boardType ? (
          <div className="mt-[16px]">
            <div className="mb-[8px]">活动信息</div>
            <div className="cursor-pointer">
              {boardType === 'campaigns' ? (
                <a
                  onClick={() => history.push(`/aimarketer/home/<USER>/detail/${detailObj.id}`)}
                >{`[${detailObj.id}]${detailObj.name}`}</a>
              ) : (
                <a
                  onClick={() => history.push(`/aimarketer/home/<USER>/detail?id=${detailObj.id}`)}
                >{`[${detailObj.id}]${detailObj.name}`}</a>
              )}
            </div>
          </div>
        ) : (
          <Form.Item label="是否将图表添加到数据看板？" name="addBoard" rules={[{ required: true, message: '请选择' }]}>
            <Radio.Group>
              <Radio value="不添加到数据看板"> 不添加到数据看板 </Radio>
              <Radio value="添加到已存在的数据看板"> 添加到已存在的数据看板 </Radio>
              <Radio value="添加到新的数据看板"> 添加到新的数据看板 </Radio>
            </Radio.Group>
          </Form.Item>
        )}
      </Form>
      {renderAddBoard()}
    </div>
  );
}
