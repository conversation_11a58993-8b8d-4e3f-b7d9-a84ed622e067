import { Alert, Form, Input, Radio, TreeSelect, message } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useImperativeHandle, useState } from 'react';
import { useHistory } from 'react-router-dom';
import DashboardService from 'service/dashboardService';

import { eventAnalysisContext } from '../eventAnalysisContext';

import { t } from 'utils/translation';

const layouts = [
  [{ x: 0, y: 1000, w: 4, h: 3, isResizable: true }],
  [
    { x: 0, y: 1000, w: 4, h: 3, isResizable: true },
    { x: 4, y: 1000, w: 4, h: 3, isResizable: true }
  ],
  [
    { x: 0, y: 1000, w: 4, h: 3, isResizable: true },
    { x: 4, y: 1000, w: 4, h: 3, isResizable: true },
    { x: 8, y: 1000, w: 4, h: 3, isResizable: true }
  ]
];

export default function SaveChart(props) {
  const { onRef, chartName, boardType, detailObj } = props;
  const history = useHistory();
  const [form] = Form.useForm();
  const [form2] = Form.useForm();
  const [addBoardType, setAddBoardType] = useState(t('analysisCenter-t8Tm54xb6WJf'));

  const [allChartBoard, setAllChartBoard] = useState([]); // 所有未分组的数据看板
  const [allChartBoardCategory, setAllChartBoardCategory] = useState([]); // 所有文件夹

  const [chartBoards, setChartBoards] = useState([]); // 选中的数据看板
  const [menuList, setMunuList] = useState([]); // 文件夹List
  const [boardList, setBoardList] = useState([]); // 数据看板List

  const { state, dispatch } = useContext(eventAnalysisContext);

  useEffect(() => {
    setBoardList([]);
    form2.resetFields();
  }, [addBoardType]);

  const saveCharts = async () => {
    const formValue = await form.validateFields();
    const formValue2 = await form2.validateFields();
    const { addBoard, chartName } = formValue;
    const { boardName, folder } = formValue2;
    let findBoard;
    if (addBoard === t('analysisCenter-o9LbFlyMWdua')) {
      // 根据chartBoards里面的的id去filter allChartBoard
      findBoard = allChartBoard.filter((w) => chartBoards.includes(`${w.id}`));
      findBoard.map((item) => {
        item.layouts.xxs = [...item.layouts.xxs, ...layouts[chartBoards.length - 1]];
        item.widgets = [...item.widgets, ...layouts[chartBoards.length - 1]];
        return item;
      });
    } else if (addBoard === t('analysisCenter-Lzx9nkOF9QOB')) {
      findBoard = [
        {
          boardName,
          category: allChartBoardCategory.find((w) => w.id === parseInt(folder.replace('folder.', ''))),
          layouts: { xxs: layouts[0] },
          widgets: layouts[0]
        }
      ];
    }
    const saveChartValue = {
      ...formValue,
      ...formValue2,
      chartBoards: findBoard,
      name: chartName
    };
    return _.omit(saveChartValue, ['addBoard', 'boardName', 'chartName']);
  };

  useImperativeHandle(onRef, () => ({
    saveCharts
  }));

  const onValuesChange = async (changedValues) => {
    dispatch({ displayType: changedValues.displayType });
    if (changedValues?.addBoard) setAddBoardType(changedValues.addBoard);
    if (
      changedValues.addBoard === t('analysisCenter-o9LbFlyMWdua') ||
      (changedValues.addBoard === t('analysisCenter-Lzx9nkOF9QOB') && _.isEmpty(boardList))
    ) {
      const allCategoryList = await DashboardService.getAllchartBoardCategory([
        {
          operator: 'EQ',
          propertyName: 'deptId',
          value: window.getDeptId()
        }
      ]);
      const chartBoardList = await DashboardService.getAllchartBoard([
        {
          operator: 'EQ',
          propertyName: 'deptId',
          value: window.getDeptId()
        }
      ]);
      setAllChartBoard(chartBoardList);
      setAllChartBoardCategory(allCategoryList);
      const menuList = getLevelData(chartBoardList, allCategoryList, 0);
      const boardList = getLevelData(chartBoardList, allCategoryList, 0, true);

      setMunuList(menuList);
      setBoardList(boardList);
    }
  };

  const getLevelData = (chartBoardList, data, parentId, isBoard) => {
    const itemArr = [];
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      if (node.parentId === parentId) {
        const newNode = {};
        newNode.value = `folder.${node.id}`;
        if (isBoard) newNode.selectable = false;
        newNode.title = node.name;
        newNode.isLeaf = false;
        const res = getLevelData(chartBoardList, data, node.id, isBoard);
        if (res.length > 0) {
          newNode.children = res;
        }
        if (isBoard) {
          const list = chartBoardList.filter((w) => parseInt(node.id) === w.category?.id);
          if (list.length > 0) {
            if (newNode.children) {
              list.forEach((n) => {
                newNode.children.push({
                  value: `${n.id}`,
                  title: n.boardName,
                  isLeaf: true
                });
              });
            } else {
              newNode.children = list.map((n) => ({
                value: `${n.id}`,
                title: n.boardName,
                isLeaf: true
              }));
            }
          }
        }
        itemArr.push(newNode);
      }
    }
    return itemArr;
  };

  const changeOne = (selectKeys) => {
    if (selectKeys.length > 3) {
      setChartBoards(selectKeys.slice(0, 3));
      return message.error(t('analysisCenter-vuy1LhMIhOEr'));
    } else {
      setChartBoards(selectKeys);
    }
  };

  const renderAddBoard = () => {
    if (addBoardType === t('analysisCenter-t8Tm54xb6WJf')) return null;
    else if (addBoardType === t('analysisCenter-o9LbFlyMWdua')) {
      return (
        <TreeSelect
          multiple
          style={{ width: '100%' }}
          value={chartBoards}
          onChange={changeOne}
          treeData={boardList}
          placeholder={t('analysisCenter-lTSwu2SGyvp7')}
        />
      );
    } else if (addBoardType === t('analysisCenter-Lzx9nkOF9QOB')) {
      return (
        <Form form={form2} layout="vertical">
          <Form.Item
            label={t('analysisCenter-t5BtRxjvZ3dc')}
            name="boardName"
            rules={[
              { required: true, message: t('analysisCenter-qsrV5LE9PPDJ') },
              { max: 20, message: t('analysisCenter-AdMjnh8gT3fp') }
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={t('analysisCenter-SpakKYHx3HvQ')}
            name="folder"
            rules={[{ required: true, message: t('analysisCenter-NWgzRd4N73dV') }]}
          >
            <TreeSelect
              // multiple
              style={{ width: '100%' }}
              // value={chartBoards}
              // onChange={onChange}
              treeData={menuList}
            />
          </Form.Item>
        </Form>
      );
    }
  };

  return (
    <div>
      <Alert
        message={boardType ? t('analysisCenter-jtLZmc21H9Ip') : t('analysisCenter-Z4MH0g5rAabl')}
        showIcon
        type="info"
        className="mb-[16px] "
      />
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          chartName,
          displayType: 'LINE_CHART',
          addBoard: t('analysisCenter-t8Tm54xb6WJf')
        }}
        onValuesChange={onValuesChange}
      >
        <Form.Item
          label={t('analysisCenter-iuc2YFHglLVU')}
          name="chartName"
          rules={[
            { required: true, message: t('analysisCenter-BpuMIeIWyfDt') },
            { max: 32, message: t('analysisCenter-wqewIuTY6yVf') }
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label={t('analysisCenter-sAYANklxNgLt')}
          name="displayType"
          rules={[{ required: true, message: t('analysisCenter-dokR2dZ8Tg7u') }]}
        >
          <Radio.Group>
            <Radio value="LINE_CHART"> {t('analysisCenter-gxg158wCc0Gn')} </Radio>
            <Radio value="COLUMN_CHART"> {t('analysisCenter-Gcf9TVeltitu')} </Radio>
            <Radio value="PERCENT_CHART" disabled={!state.isGroupType}>
              {' '}
              {t('analysisCenter-rHvjyT9SP6Zr')}
            </Radio>
            <Radio value="AREA_CHART" disabled={!state.isAreaType}>
              {' '}
              {t('analysisCenter-UTGl5qGDvl8U')}
            </Radio>
          </Radio.Group>
        </Form.Item>

        {boardType ? (
          <div className="mt-[16px]">
            <div className="mb-[8px]">{t('analysisCenter-N87Rj9q1LEYA')}</div>
            <div className="cursor-pointer">
              {boardType === 'campaigns' ? (
                <a
                  onClick={() => history.push(`/aimarketer/home/<USER>/detail/${detailObj.id}`)}
                >{`[${detailObj.id}]${detailObj.name}`}</a>
              ) : (
                <a
                  onClick={() => history.push(`/aimarketer/home/<USER>/detail?id=${detailObj.id}`)}
                >{`[${detailObj.id}]${detailObj.name}`}</a>
              )}
            </div>
          </div>
        ) : (
          <Form.Item
            label={t('analysisCenter-VbGdQIdsSpSm')}
            name="addBoard"
            rules={[{ required: true, message: t('analysisCenter-lTSwu2SGyvp7') }]}
          >
            <Radio.Group>
              <Radio value={t('analysisCenter-t8Tm54xb6WJf')}> {t('analysisCenter-t8Tm54xb6WJf')} </Radio>
              <Radio value={t('analysisCenter-o9LbFlyMWdua')}> {t('analysisCenter-o9LbFlyMWdua')} </Radio>
              <Radio value={t('analysisCenter-Lzx9nkOF9QOB')}> {t('analysisCenter-Lzx9nkOF9QOB')} </Radio>
            </Radio.Group>
          </Form.Item>
        )}
      </Form>
      {renderAddBoard()}
    </div>
  );
}
