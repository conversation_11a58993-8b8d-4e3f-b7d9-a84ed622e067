import {
  CloseOutlined,
  DownOutlined,
  ExclamationCircleOutlined,
  QuestionCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useRequest } from 'ahooks';
import {
  Button,
  Card,
  Checkbox,
  Divider,
  Drawer,
  Dropdown,
  Empty,
  Input,
  Modal,
  Popover,
  Radio,
  Select,
  Space,
  Spin,
  Tooltip,
  Tree,
  message
} from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { getDeptPath } from 'pages/home/<USER>/dataPermissions/config';
import React, { useEffect, useReducer, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import CampaignV2Service from 'service/CampaignV2Service';
import CampaignsService from 'service/CampaignsService';
import ScenarioService from 'service/ScenarioService';
import UserService from 'service/UserService';
import AnalysisCenterService from 'service/analysisCenterService';
import EventAnalysis from 'service/eventAnalysis.js';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { useDebounce, useQuery } from 'utils/customhooks';
import { SelectTime } from 'wolf-static-cpnt';
import { MyIconV2 } from '../../../../utils/myIcon';
import RenderChart from './charts/renderCharts';
import RenderTable from './charts/renderTable';
import Dimension from './dimension';
import { eventAnalysisContext } from './eventAnalysisContext';
import GlobalFilter from './filter';
import './index.scss';
import SaveChart from './saveChart/saveChart';
import Steps from './steps';
import UpdateChart from './updateChart/updateChart';

const querystring = require('querystring');

const { Option } = Select;

const reducer = (state, action) => ({ ...state, ...action });

const campaignV2Service = new CampaignV2Service();
const campaignsService = new CampaignsService();
const userService = new UserService();

// const shortcutOptions = {
//   今天: [dayjs().startOf('day'), dayjs().endOf('day')],
//   昨日: [dayjs().subtract(1, 'days').startOf('day'), dayjs().subtract(1, 'days').endOf('day')],
//   本周: [dayjs().startOf('week'), dayjs().endOf('week')],
//   本月: [dayjs().startOf('month'), dayjs().endOf('month')],
//   过去7天: [dayjs().subtract(7, 'days'), dayjs()],
//   过去30天: [dayjs().subtract(30, 'days'), dayjs()],
//   过去60天: [dayjs().subtract(60, 'days'), dayjs()]
// };

export default function FunnelAnalysisDom(props) {
  const parmas = querystring.parse(window.location.search.substr(1));
  const { id } = useParams();
  const queryParams = useQuery();
  const campaignId = queryParams.get('campaignId');
  const boardType = queryParams.get('boardType');
  const scenario = queryParams.get('scenarioId');
  const version = queryParams.get('version');
  const saveId = queryParams.get('saveId');
  const deptId = queryParams.get('deptId');

  const [state, dispatch] = useReducer(reducer, {
    name: '未命名事件分析',
    loading: false,
    scenarioId: null,
    dateRange2: [
      {
        type: 'RELATIVE',
        timeTerm: 'DAY',
        isPast: true,
        times: 7,
        truncateAsDay: true
      },
      { type: 'NOW', timeTerm: 'DAY', isPast: true, truncateAsDay: true }
    ],
    chartConfig: {
      eventAnalysis: {
        showStyle: 'LINE'
      }
    },
    timeTerm: 'DAY',
    tableColumn: null,
    tableList: null,
    filterTableList: [],
    chartList: null,
    lineChartList: null,
    stepList: [],
    dimensionGroup: [],
    globalFilters: [],
    scenarioList: [],
    chartDisplayType: 'LINE_CHART',
    chartLoading: false,
    tableLoading: false,
    isMultiStep: false, // 是否禁用维度分组
    selectedRowKeys: [],
    offSave: true,
    open: true,
    defaultFunnel: null,
    storageType: null, // CONVERT,LOSS   漏斗保存分群类型 转化/流失
    saveGroupValue: null, // 保存分群的值
    visibleDrawer: false,
    axisOpen: false,
    updateChart: false,
    displayType: 'LINE_CHART', // 最终图表展示的类型 COLUMN_CHART,PERCENT_COLUMN_CHART,LINE_CHART,
    calcTypeList: [],
    axisShowType: { left: [], right: [] },
    calcFinishData: {},
    isGroupType: false,
    isAreaType: false,
    xAxisType: 'time',
    isSwitch: false,
    formulaList: [
      {
        type: 'span',
        sort: 'first',
        value: '',
        key: 'first'
      },
      {
        type: 'input',
        value: '',
        key: 'input-0'
      },
      {
        type: 'span',
        sort: 'end',
        value: '',
        key: 'end'
      }
    ]
  });
  const [scenarioList, setScenarioList] = useState([]);
  const [xAxisRadio, setXaxisRadio] = useState('time');
  const [filterConfig, setFilterConfig] = useState({});
  const [chartConfigData, setChartConfigData] = useState([]);
  const [userId, setUserId] = useState(null);
  const [showChartType, setShowChartType] = useState('LINE_CHART');
  const [boardDetail, setBoardDetail] = useState({});
  const [campaignTreeData, setCampaignTreeData] = useState([]);
  const [deptPath, setDeptPath] = useState(getDeptPath());
  const [campaignList, setCampaignList] = useState([]);

  const timer = useRef(null);
  const onRef = useRef(null);

  useEffect(() => {
    dispatch({ offSave: true });
  }, [state.stepList, state.globalFilters, state.dimensionGroup, state.timeTerm, state.dateRange2]);

  // 过滤
  useEffect(() => {
    if (!_.isEmpty(state.tableList)) {
      const _filterTableList = [];
      state.tableList.forEach((item, index) => {
        // push进去前6个
        if (index < 6) {
          _filterTableList.push(item.key);
        }
      });
      dispatch({ filterTableList: _filterTableList });
    }
  }, [state.tableList]);

  // 遍历维度分组是否禁用
  useEffect(() => {
    const isFlag = state.stepList.some((item) => item.multiStep);
    if (isFlag) {
      dispatch({ isMultiStep: true });
    } else {
      dispatch({ isMultiStep: false });
    }
  }, [state.stepList]);

  const GetQueryString = (name) => {
    const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
    const r = window.location.search.substr(1).match(reg); // search,查询？后面的参数，并匹配正则
    if (r != null) return unescape(r[2]);
    return null;
  };

  const saveRecordInfo = async (id, userId) => {
    await campaignsService.saveUserOperationRecord({
      targetId: id,
      id: chartConfigData?.recentUserOperationRecord?.id,
      targetType: 'CHART',
      type: 'RECENT',
      createUserId: userId,
      updateUserId: userId,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });
  };

  useEffect(() => {
    init();
    // mockEditInit();
    return () => {
      timer.current && clearTimeout(timer.current);
      localStorage.removeItem('isActivityAnalysis');
      // localStorage.removeItem('timefilter');
    };
  }, []);

  useEffect(() => {
    const getBoardInfo = async () => {
      if (campaignId) {
        if (boardType === 'campaigns') {
          const result = await campaignsService.getCampaignsV2({
            id: Number(campaignId),
            deptId: window.getDeptId()
          });

          // const res = await campaignsService.findCampaigns([
          //   {
          //     operator: 'EQ',
          //     propertyName: 'campaignsId',
          //     value: Number(props.location.state?.campaignId)
          //   }
          // ]);

          if (!_.isNil(id)) {
            const treeResult = [
              {
                title: `营销活动：[${result.id}] ${result.name}`,
                key: result.id,
                children: state.chartConfig.campaignFilters
                  ? state.chartConfig.campaignFilters.map((item) => {
                      return {
                        title: `流程画布：[${item.id}] ${item.name}`,
                        key: item.id
                      };
                    })
                  : []
              }
            ];
            setCampaignTreeData(treeResult);
          } else {
            const res = await campaignsService.getCampaignFilter({
              type: 'CAMPAIGNS',
              id: Number(campaignId)
            });

            setCampaignList(res);

            const treeResult = [
              {
                title: `营销活动：[${result.id}] ${result.name}`,
                key: result.id,
                children:
                  res && res.length
                    ? res.map((item) => {
                        return {
                          title: `流程画布：[${item.id}] ${item.name}`,
                          key: item.id
                        };
                      })
                    : []
              }
            ];
            setCampaignTreeData(treeResult);
          }
          setBoardDetail(result);
        } else {
          if (!_.isNil(id)) {
            const treeResult = state.chartConfig.campaignFilters
              ? state.chartConfig.campaignFilters.map((item) => {
                  return {
                    title: `流程画布：[${item.id}] ${item.name}`,
                    key: item.id
                  };
                })
              : [];

            setBoardDetail(state.chartConfig.campaignFilters && state.chartConfig.campaignFilters[0]);
            setCampaignTreeData(treeResult);
          } else {
            const result = await campaignsService.getCampaignFilter({
              type: 'FLOW_CANVAS',
              id: Number(campaignId)
            });

            setCampaignList(result);
            const treeResult = [
              {
                title: `流程画布：[${result[0].id}] ${result[0].name}`,
                key: result[0].id
              }
            ];
            setBoardDetail(result[0]);
            setCampaignTreeData(treeResult);
          }
        }
      }
    };

    getBoardInfo();
  }, [state.chartConfig]);

  /**
   * @param {object} result 刷新传入
   */
  const init = async (result) => {
    const functionEnum = {
      COUNT_DT_ID: '总次数',
      UNI_DT_ID: '总人数',
      COUNT_DT_ID_DIV_UNI_DT_ID: '人均次数',
      SUM_COL: '求和',
      MAX_COL: '求最大值',
      MIN_COL: '求最小值',
      AVG_COL: '求平均值',
      COUNT_COL_DIV_UNI_DT_ID: '求人均值',
      UNI_COL: '去重'
    };

    if (campaignId) {
      localStorage.setItem('activityCache', true);
    }
    let _scenarioList = await ScenarioService.scenarioList([]);
    _scenarioList = _scenarioList.sort((a, b) => a.orderNum - b.orderNum);
    const userInfo = await userService.getCurrentUser();
    setUserId(userInfo.id);
    setScenarioList(_scenarioList);
    dispatch({
      scenarioList: _scenarioList
    });
    if (!_.isNil(id)) {
      dispatch({
        chartLoading: true,
        tableLoading: true
      });
      // let info = await AnalysisCenterService.getChartConfig(id);
      let info = await AnalysisCenterService.getChartConfigV2({
        id: Number(id),
        deptId: deptId || window.getDeptId()
      });
      if (info) {
        setDeptPath(getDeptPath(info?.deptId));
        setChartConfigData(info);
        // 事件指标数据结构适配
        const res = await Promise.all(
          info.chartConfig.eventAnalysis.metric.map(async (metricsItem, metricsIndex) => {
            const eventList = await FunnelAnalysis.getEventPropertyList({
              name: '',
              eventId: metricsItem.events[0]?.id
            });
            let metricName = '';
            if (
              metricsItem.function === 'COUNT_DT_ID' ||
              metricsItem.function === 'UNI_DT_ID' ||
              metricsItem.function === 'COUNT_DT_ID_DIV_UNI_DT_ID'
            ) {
              metricName = functionEnum[metricsItem.function];
            } else {
              metricName = `按${metricsItem.eventProp.fieldName}${functionEnum[metricsItem.function]}`;
            }

            return {
              step: `${metricsIndex + 1}`,
              metricName,
              name: metricsItem.name,
              visibleSetp: false,
              event: metricsItem.events[0],
              eventList,
              type: metricsItem.type,
              clsType: metricsItem.clsType,
              // metricsType: metricsItem.metricsType,
              calcType: metricsItem.eventProp
                ? [metricsItem.function, metricsItem.eventProp.fieldName]
                : [metricsItem.function],
              eventProp: metricsItem.eventProp,
              filters: _.isEmpty(metricsItem.filter)
                ? []
                : // eslint-disable-next-line array-callback-return
                  metricsItem.filter.filter.map((filterItem) => {
                    if (filterItem.type === 'EVENT_PROP') {
                      return {
                        connector: metricsItem.filter.connector,
                        eventGroup: {
                          connector: metricsItem.filter.connector,
                          filters: [
                            {
                              connector: metricsItem.filter.connector,
                              filters: [
                                {
                                  eventFilterProperty: {
                                    connector: metricsItem.filter.connector,
                                    filters: [
                                      {
                                        connector: metricsItem.filter.connector,
                                        filters: [
                                          {
                                            ...filterItem.eventProp,
                                            value: filterItem.eventProp.value
                                          }
                                        ]
                                      }
                                    ]
                                  }
                                }
                              ]
                            }
                          ]
                        }
                      };
                    } else if (filterItem.type === 'USER_LABEL') {
                      return {
                        connector: metricsItem.filter.connector,
                        userLabel: {
                          connector: metricsItem.filter.connector,
                          filters: [
                            {
                              connector: metricsItem.filter.connector,
                              filters: [
                                {
                                  ...filterItem.userLabel,
                                  fieldType: filterItem.userLabel.userLabel.dataType,
                                  displayName: filterItem.userLabel.userLabel.displayName,
                                  value: filterItem.userLabel.value
                                }
                              ]
                            }
                          ]
                        }
                      };
                    } else if (filterItem.type === 'USER_SEGMENT') {
                      return {
                        connector: metricsItem.filter.connector,
                        segment: {
                          connector: metricsItem.filter.connector,
                          filters: [
                            {
                              connector: metricsItem.filter.connector,
                              filters: [
                                {
                                  segment: {
                                    customerCount: filterItem.segments.segments[0].customerCount,
                                    id: filterItem.segments.segments[0].id,
                                    lastCalcTime: filterItem.segments.segments[0].lastCalcTime,
                                    name: filterItem.segments.segments[0].name
                                  }
                                }
                              ]
                            }
                          ]
                        },
                        userGroupList: filterItem.segments.segments[0]
                      };
                    } else if (filterItem.type === 'FLOW_BATCH') {
                      return {
                        connector: metricsItem.filter.connector,
                        campaignGroup: {
                          connector: metricsItem.filter.connector,
                          filterType: 'CAMPAIGN_BATCH',
                          filters: filterItem.flowCalcLogs.flowCalcLogs
                        }
                      };
                    } else if (filterItem.type === 'FLOW_NODES') {
                      return {
                        connector: metricsItem.filter.connector,
                        campaignGroup: {
                          connector: metricsItem.filter.connector,
                          filterType: 'CAMPAIGN_NODE',
                          filters: filterItem.flowNodeLogs.flowCalcLogs
                        }
                      };
                    }
                  })
            };
          })
        );

        const _res = _.cloneDeep(res);
        _res.forEach((item) => {
          const metricRes = item.filters.find((item) => _.find(Object.keys(item), (o) => o === 'campaignGroup'));
          if (metricRes) {
            metricRes.campaignGroup.filters.forEach(async (item, index) => {
              const campaign = await FunnelAnalysis.query2({
                search: [
                  {
                    propertyName: 'id',
                    operator: 'EQ',
                    value: item.campaignId
                  },
                  {
                    operator: 'EQ',
                    propertyName: 'deptId',
                    value: window.getDeptId()
                  }
                ]
              });

              metricRes.campaignGroup.filters.splice(index, 1, {
                ...campaign.content[0],
                logList: [item]
              });
            });
          }
        });

        // let metricRes = _res.find(item => _.find(Object.keys(item), (o) => o === 'campaignGroup'));
        // if (metricRes) {
        //   metricRes.campaignGroup.filters.forEach(async (item, index) => {
        //     const campaign = await FunnelAnalysis.query2({
        //       search: [{ propertyName: 'id', operator: 'EQ', value: item.campaignId }]
        //     });

        //     metricRes.campaignGroup.filters.splice(index, 1, { ...campaign.content[0], logList: [item] });
        //   });
        // }
        // 全局过滤数据结构适配
        const globalRes = _.isEmpty(info.chartConfig.eventAnalysis.filter)
          ? []
          : // eslint-disable-next-line array-callback-return
            info.chartConfig.eventAnalysis.filter.filter.map((filterItem) => {
              if (filterItem.type === 'EVENT_PROP') {
                return {
                  connector: info.chartConfig.eventAnalysis.filter.connector,
                  eventFilterProperty: {
                    connector: info.chartConfig.eventAnalysis.filter.connector,
                    filters: [
                      {
                        ...filterItem.eventProp,
                        value: filterItem.eventProp.value
                      }
                    ]
                  }
                };
              } else if (filterItem.type === 'USER_LABEL') {
                return {
                  connector: info.chartConfig.eventAnalysis.filter.connector,
                  userLabel: {
                    connector: info.chartConfig.eventAnalysis.filter.connector,
                    filters: [
                      {
                        connector: info.chartConfig.eventAnalysis.filter.connector,
                        filters: [
                          {
                            ...filterItem.userLabel,
                            fieldType: filterItem.userLabel.userLabel.dataType,
                            displayName: filterItem.userLabel.userLabel.displayName,
                            value: filterItem.userLabel.value
                          }
                        ]
                      }
                    ]
                  }
                };
              } else if (filterItem.type === 'USER_SEGMENT') {
                return {
                  connector: info.chartConfig.eventAnalysis.filter.connector,
                  segment: {
                    connector: info.chartConfig.eventAnalysis.filter.connector,
                    filters: [
                      {
                        connector: info.chartConfig.eventAnalysis.filter.connector,
                        filters: [
                          {
                            segment: {
                              customerCount: filterItem.segments.segments[0].customerCount,
                              id: filterItem.segments.segments[0].id,
                              lastCalcTime: filterItem.segments.segments[0].lastCalcTime,
                              name: filterItem.segments.segments[0].name
                            }
                          }
                        ]
                      }
                    ]
                  },
                  userGroupList: filterItem.segments.segments[0]
                };
              } else if (filterItem.type === 'FLOW_BATCH') {
                return {
                  connector: info.chartConfig.eventAnalysis.filter.connector,
                  campaignGroup: {
                    connector: info.chartConfig.eventAnalysis.filter.connector,
                    filterType: 'CAMPAIGN_BATCH',
                    filters: filterItem.flowCalcLogs.flowCalcLogs
                  }
                };
              } else if (filterItem.type === 'FLOW_NODES') {
                return {
                  connector: info.chartConfig.eventAnalysis.filter.connector,
                  campaignGroup: {
                    connector: info.chartConfig.eventAnalysis.filter.connector,
                    filterType: 'CAMPAIGN_NODE',
                    filters: filterItem.flowNodeLogs.flowCalcLogs
                  }
                };
              }
            });

        const _globalRes = _.cloneDeep(globalRes);
        const findRes = _globalRes.find((item) => _.find(Object.keys(item), (o) => o === 'campaignGroup'));
        if (findRes) {
          findRes.campaignGroup.filters.forEach(async (item, index) => {
            const campaign = await FunnelAnalysis.query2({
              search: [
                { propertyName: 'id', operator: 'EQ', value: item.campaignId },
                {
                  operator: 'EQ',
                  propertyName: 'deptId',
                  value: window.getDeptId()
                }
              ]
            });

            findRes.campaignGroup.filters.splice(index, 1, {
              ...campaign.content[0],
              logList: [item]
            });
          });
        }

        // _globalRes.forEach(item => {
        //   let findIndex = _.findIndex(Object.keys(item), (o) => o === 'campaignGroup');
        //   if (findIndex !== -1) {
        //     _globalRes.splice(findIndex, 1, findRes);
        //   }
        // });
        // 维度分组数据结构适配
        const dimensionRes = _.isEmpty(info.chartConfig.eventAnalysis.group)
          ? []
          : // eslint-disable-next-line array-callback-return
            info.chartConfig.eventAnalysis.group.filter.map((demensionItem) => {
              if (demensionItem.type === 'EVENT_PROP') {
                return {
                  eventType: 'EVENT_PROP',
                  connector: 'AND',
                  type: 'USER_PROPERTIES',
                  name: demensionItem.eventProp.fieldName,
                  filters: demensionItem.eventProp.value.map((valueItem) => {
                    return {
                      eventFilterProperty: {
                        connector: 'AND',
                        filters: [
                          {
                            ...demensionItem.eventProp,
                            value: valueItem
                          }
                        ],
                        empty: false
                      },
                      groupName: valueItem
                    };
                  }),
                  filterValue: demensionItem.eventProp.value
                };
              } else if (demensionItem.type === 'USER_LABEL') {
                return {
                  eventType: 'USER_LABEL',
                  connector: 'AND',
                  type: 'USER_LABEL',
                  name: demensionItem.userLabel.userLabel.displayName,
                  filters: demensionItem.userLabel.value.map((valueItem) => {
                    return {
                      userLabel: {
                        connector: 'AND',
                        filters: [
                          {
                            connector: 'AND',
                            filters: [
                              {
                                ...demensionItem.userLabel,
                                userLabel: demensionItem.userLabel.userLabel,
                                value: valueItem
                              }
                            ],
                            empty: false
                          }
                        ],
                        empty: false
                      },
                      groupName: valueItem
                    };
                  }),
                  filterValue: demensionItem.userLabel.value
                };
              } else if (demensionItem.type === 'USER_SEGMENT') {
                return {
                  eventType: 'SEGMENT',
                  connector: 'AND',
                  type: 'SEGMENT',
                  filters: demensionItem.segments.segments.map((segmentsItem) => {
                    return {
                      segment: {
                        connector: 'AND',
                        filters: [
                          {
                            connector: 'AND',
                            filters: [
                              {
                                segment: segmentsItem
                              }
                            ]
                          }
                        ]
                      },
                      userGroupList: segmentsItem
                    };
                  }),
                  filterValue: demensionItem.segments.segments.map((item) => item.name)
                };
              } else if (demensionItem.type === 'FLOW_BATCH') {
                return {
                  eventType: 'CAMPAIGN',
                  type: 'CAMPAIGN',
                  filters: [
                    {
                      campaignGroup: {
                        connector: 'AND',
                        filters: demensionItem.flowCalcLogs.flowCalcLogs,
                        filterType: 'CAMPAIGN_BATCH'
                      }
                    }
                  ],
                  filterValue: [null]
                };
              } else if (demensionItem.type === 'FLOW_NODES') {
                return {
                  eventType: 'CAMPAIGN',
                  type: 'CAMPAIGN',
                  filters: [
                    {
                      campaignGroup: {
                        connector: 'AND',
                        filters: demensionItem.flowNodeLogs.flowCalcLogs,
                        filterType: 'CAMPAIGN_NODE'
                      }
                    }
                  ],
                  filterValue: [null]
                };
              }
            });

        let _dimensionGroup = _.cloneDeep(dimensionRes);

        if (_.isEmpty(_dimensionGroup)) {
          _dimensionGroup = [];
        } else {
          if (_dimensionGroup[0].type === 'CAMPAIGN') {
            _dimensionGroup[0].filters[0].campaignGroup.filters.forEach(async (filterItem, filterIndex) => {
              const campaign = await FunnelAnalysis.query2({
                search: [
                  {
                    propertyName: 'id',
                    operator: 'EQ',
                    value: filterItem.campaignId
                  },
                  {
                    operator: 'EQ',
                    propertyName: 'deptId',
                    value: window.getDeptId()
                  }
                ]
              });
              _dimensionGroup[0].filters[0].campaignGroup.filters.splice(filterIndex, 1, {
                ...campaign.content[0],
                logList: [filterItem]
              });
            });
          }

          _dimensionGroup.map((item) => {
            const arr = [];
            item.filters &&
              item.filters.forEach((i) => {
                arr.push(i?.groupName);
              });
            item.filterValue = arr;
            return item;
          });
        }

        const timeFilter = GetQueryString('timeFilter');
        // 设置状态来判定是否第一次进入页面
        if (timeFilter && !localStorage.getItem('timeFilter')) {
          const timeFilterArr = timeFilter.split(',');
          localStorage.setItem('timeFilter', JSON.stringify(timeFilter));
          info = {
            ...info,
            dateRange2: [
              {
                type: 'ABSOLUTE',
                timestamp: parseInt(timeFilterArr[0]),
                times: 30,
                timeTerm: 'DAY',
                isPast: true
              },
              {
                type: 'ABSOLUTE',
                timestamp: parseInt(timeFilterArr[1]),
                times: 0,
                timeTerm: 'DAY',
                isPast: true
              }
            ]
          };
          setFilterConfig({
            startTime: timeFilterArr[0],
            endTime: timeFilterArr[1]
          });
        }

        const calcTime = parmas.calcState ? JSON.parse(localStorage.getItem('dateRange')) : info.dateRange2;

        const styleEnum = {
          LINE: 'LINE_CHART',
          PIE: 'PERCENT_CHART',
          COLUMN: 'COLUMN_CHART',
          AREA: 'AREA_CHART'
        };
        setShowChartType(styleEnum[info.chartConfig.eventAnalysis.showStyle]);

        await dispatch({
          name: result?.name || info.name,
          chartConfig: info.chartConfig,
          // dateRange2: info.dateRange2,
          dateRange2: calcTime,
          stepList: _res,
          scenarioId: info.scenario.id,
          globalFilters: _globalRes,
          dimensionGroup: _dimensionGroup,
          chartType: 'EVENT_ANALYSIS',
          // displayType: info.displayType,
          timeTerm: info.chartConfig.eventAnalysis.timeTerm,
          displayType: styleEnum[info.chartConfig.eventAnalysis.showStyle],
          chartDisplayType: styleEnum[info.chartConfig.eventAnalysis.showStyle],
          calcTypeList: res
          // defaultFunnel: info
        });
        // let tableColumn = await FunnelAnalysis.queryFunnelTableColumn(info.chartConfig.eventAnalysis);
        // dispatch({ tableColumn: tableColumn.columns });
        info.isBusinessTable = 0;
        await doRequest(info, 'TABLE', calcTime);
      }
    } else {
      const _stepList = _.cloneDeep(state.stepList);
      // _stepList.push({
      //   step: '1',
      //   visibleSetp: false,
      //   name: '请选择事件',
      //   // metricsType: 'BASE',
      //   clsType: 'BaseMetric',
      //   type: 'BASE',
      //   event: {}
      // }, {
      //   step: '2',
      //   visibleSetp: false,
      //   clsType: 'BaseMetric',
      //   name: '请选择事件',
      //   // metricsType: 'BASE',
      //   type: 'BASE',
      //   event: {}
      // });
      _stepList.push({
        step: '1',
        visibleSetp: false,
        name: '请选择事件',
        // metricsType: 'BASE',
        clsType: 'BaseMetric',
        type: 'BASE',
        event: {}
      });
      dispatch({
        stepList: _stepList,
        scenarioId:
          campaignId && boardType === 'flowCanvas'
            ? Number(scenario)
            : _scenarioList.find((item) => item.isDefault)?.id || _scenarioList[0].id
      });
    }
  };

  const exitQuit = () => {
    props.history.go(-1);
  };

  const changeName = (e) => {
    let name;
    if (!_.isEmpty(e)) {
      if (e.target.value.length > 32) {
        tip();
      }
      name = e.target.value.substring(0, 32);
      dispatch({ name });
    }
  };

  const tip = useDebounce(() => {
    message.error('名称不能超过32个字符');
  }, 500);

  const change = (value) => {
    dispatch({
      dateRange2: value
    });
  };

  const changeScenario = (value) => {
    const oldValue = _.cloneDeep(state.scenarioId);
    const _stepList = _.cloneDeep(state.stepList);
    const flag = _.every(_stepList, (item) => item.name === '请选择事件');

    if (!flag) {
      Modal.confirm({
        icon: <ExclamationCircleOutlined />,
        content: '切换ID类型将清空当前事件指标，是否切换？',
        okText: '是',
        cancelText: '否',
        onOk() {
          dispatch({
            scenarioId: value,
            stepList: [],
            dimensionGroup: [],
            globalFilters: []
          });
        },
        onCancel() {
          dispatch({ scenarioId: oldValue });
        }
      });
    } else {
      dispatch({
        scenarioId: value,
        stepList: [],
        dimensionGroup: [],
        globalFilters: []
      });
    }
  };

  const renderFilter = (filterArr, step) => {
    const filterEnum = {
      eventGroup: { type: 'EVENT_PROP', key: 'eventProp' },
      userLabel: { type: 'USER_LABEL', key: 'userLabel' },
      segment: { type: 'USER_SEGMENT', key: 'segments' },
      campaignGroup: { type: 'CAMPAIGN_GROUP', key: 'CAMPAIGN_GROUP' }
    };

    const campaignEnum = {
      CAMPAIGN_BATCH: { type: 'FLOW_BATCH', key: 'flowCalcLogs' },
      CAMPAIGN_NODE: { type: 'FLOW_NODES', key: 'flowNodeLogs' }
    };
    const res = filterArr.map((filterItem) => {
      const obj = {
        type:
          _.filter(Object.keys(filterItem), (v) => v !== 'connector')[0] === 'campaignGroup'
            ? campaignEnum[filterItem.campaignGroup.filterType].type
            : filterEnum[_.filter(Object.keys(filterItem), (v) => v !== 'connector')[0]].type
      };

      const objectKey = _.filter(Object.keys(filterItem), (v) => v !== 'connector')[0];

      if (objectKey === 'eventGroup') {
        obj.eventProp = {
          event: step.event,
          ...filterItem.eventGroup.filters[0].filters[0].eventFilterProperty.filters[0].filters[0],
          operator: filterItem.eventGroup.filters[0].filters[0].eventFilterProperty.filters[0].filters[0].operator,
          value: filterItem.eventGroup.filters[0].filters[0].eventFilterProperty.filters[0].filters[0].value
        };
      } else if (objectKey === 'userLabel') {
        obj.userLabel = {
          userLabel: filterItem.userLabelList,
          ...filterItem.userLabel.filters[0].filters[0],
          operator: filterItem.userLabel.filters[0].filters[0].operator,
          value: filterItem.userLabel.filters[0].filters[0].value
        };
      } else if (objectKey === 'segment') {
        obj.segments = { segments: [filterItem.userGroupList] };
      } else if (objectKey === 'campaignGroup') {
        const flowCalcList = [];
        filterItem.campaignGroup.filters.forEach((item2) => {
          flowCalcList.push(...item2.logList);
        });
        if (filterItem.campaignGroup.filterType === 'CAMPAIGN_BATCH') {
          obj.flowCalcLogs = { flowCalcLogs: flowCalcList };
        } else {
          obj.flowNodeLogs = { flowCalcLogs: flowCalcList };
        }
      }

      return obj;
    });
    return res;
  };

  const renderGlobalFilter = (globalArr) => {
    if (_.isEmpty(globalArr)) {
      return {};
    }
    const filterEnum = {
      eventFilterProperty: { type: 'EVENT_PROP', key: 'eventProp' },
      userLabel: { type: 'USER_LABEL', key: 'userLabel' },
      segment: { type: 'USER_SEGMENT', key: 'segments' },
      campaignGroup: { type: 'CAMPAIGN_GROUP', key: 'CAMPAIGN_GROUP' }
    };

    const campaignEnum = {
      CAMPAIGN_BATCH: { type: 'FLOW_BATCH', key: 'flowCalcLogs' },
      CAMPAIGN_NODE: { type: 'FLOW_NODES', key: 'flowNodeLogs' }
    };

    const res = globalArr.map((item) => {
      const objectKey = _.filter(Object.keys(item), (v) => v !== 'connector')[0];
      const obj = {
        type:
          objectKey === 'campaignGroup' ? campaignEnum[item.campaignGroup.filterType].type : filterEnum[objectKey].type
      };
      if (objectKey === 'eventFilterProperty') {
        obj.eventProp = {
          ...item.eventFilterProperty.filters[0],
          operator: item.eventFilterProperty.filters[0].operator,
          value: item.eventFilterProperty.filters[0].value
        };
      } else if (objectKey === 'userLabel') {
        obj.userLabel = {
          userLabel: item.userLabelList,
          ...item.userLabel.filters[0].filters[0],
          operator: item.userLabel.filters[0].filters[0].operator,
          value: item.userLabel.filters[0].filters[0].value
        };
      } else if (objectKey === 'segment') {
        obj.segments = { segments: [item.userGroupList] };
      } else if (objectKey === 'campaignGroup') {
        const flowCalcList = [];

        if (item.campaignGroup.filters.find((o) => o.logList)) {
          item.campaignGroup.filters.forEach((item2) => {
            flowCalcList.push(...item2.logList);
          });
        } else {
          item.campaignGroup.filters.forEach((item2) => {
            flowCalcList.push(item2);
          });
        }

        if (item.campaignGroup.filterType === 'CAMPAIGN_BATCH') {
          obj.flowCalcLogs = { flowCalcLogs: flowCalcList };
        } else {
          obj.flowNodeLogs = { flowCalcLogs: flowCalcList };
        }
      }
      return obj;
    });
    return {
      connector: globalArr[0].connector,
      filter: res
    };
  };

  const renderDimensFilter = (dimensArr) => {
    if (_.isEmpty(dimensArr)) {
      return {};
    }
    const res = dimensArr.map((item) => {
      const obj = {};
      if (item.eventType === 'EVENT_PROP') {
        obj.type = 'EVENT_PROP';
        obj.eventProp = {
          ...item.filters[0].eventFilterProperty.filters[0],
          operator: 'IN',
          value: item.filterValue
        };
      } else if (item.type === 'USER_LABEL') {
        obj.type = 'USER_LABEL';
        obj.userLabel = {
          ...item.filters[0].userLabel.filters[0].filters[0],
          userLabel: item.filters[0].userLabelList || item.filters[0].userLabel.filters[0].filters[0].userLabel,
          operator: 'IN',
          value: item.filterValue
        };
      } else if (item.type === 'SEGMENT') {
        obj.type = 'USER_SEGMENT';
        obj.segments = {
          segments: item.filters.map((item) => item.userGroupList)
        };
      } else if (item.type === 'CAMPAIGN') {
        const campaignType = item.filters[0].campaignGroup.filterType;
        const flowCalcList = [];
        item.filters[0].campaignGroup.filters.forEach((item2) => {
          flowCalcList.push(...item2.logList);
        });
        if (campaignType === 'CAMPAIGN_BATCH') {
          obj.type = 'FLOW_BATCH';
          obj.flowCalcLogs = { flowCalcLogs: flowCalcList };
        } else {
          obj.type = 'FLOW_NODES';
          obj.flowNodeLogs = { flowCalcLogs: flowCalcList };
        }
      }

      return obj;
    });

    return {
      connector: 'AND',
      filter: res
    };
  };

  const saveFunnel = async (obj) => {
    const { type, forceCalc } = obj;
    if (type === 'newChart') {
      return dispatch({ updateChart: false, visibleDrawer: true });
    }
    const childValue = await onRef?.current?.saveCharts();
    dispatch({
      chartLoading: true,
      tableLoading: true,
      selectedRowKeys: []
    });
    try {
      const { name, chartConfig, dateRange2, stepList, scenarioId, calcTypeList, globalFilters } = state;

      if (stepList.length < 1) {
        dispatch({ chartList: [] });
        throw new Error('最少添加一个事件指标');
      }
      stepList.forEach((item) => {
        if (item.name === '请选择事件') {
          throw new Error('请设置事件指标');
        }

        if (!item.calcType) {
          dispatch({ chartList: [] });
          throw new Error('请设置指标计算方式');
        }

        if (!calcTypeList.length) {
          dispatch({ chartList: [] });
          throw new Error('请设置指标计算方式');
        }
      });

      setShowChartType('LINE_CHART');

      const findScenarioId = scenarioList.find((item) => item.id === scenarioId);
      const _dimensionGroup = _.cloneDeep(state.dimensionGroup);

      const format = {
        USER_PROPERTIES: 'eventFilterProperty', // 事件
        USER_LABEL: 'userLabel', // 用户标签
        SEGMENT: 'segment', // 用户分群
        TABLE_FIELD: 'userProperty', // 表字段
        CAMPAIGN: 'campaign' // 活动
      };

      const hasConnector = stepList.filter((v) => v.filters.length);

      stepList.forEach((item) => {
        if (!item.filters.every((filterItem) => filterItem.connector)) {
          throw new Error('请选择指标过滤条件');
        }
      });

      const functionEnum = {
        COUNT_DT_ID: '总次数',
        UNI_DT_ID: '总人数',
        COUNT_DT_ID_DIV_UNI_DT_ID: '人均次数',
        SUM_COL: '求和',
        MAX_COL: '求最大值',
        MIN_COL: '求最小值',
        AVG_COL: '求平均值',
        COUNT_COL_DIV_UNI_DT_ID: '求人均值',
        UNI_COL: '去重'
      };

      const metric = stepList.map((item) => {
        const newFilter = {
          connector: hasConnector.length ? hasConnector[0].filters[0].connector : undefined,
          filter: _.isEmpty(renderFilter(item.filters, item)) ? {} : renderFilter(item.filters, item)
        };

        let metricName = '';

        if (
          item.calcType[0] === 'COUNT_DT_ID' ||
          item.calcType[0] === 'UNI_DT_ID' ||
          item.calcType[0] === 'COUNT_DT_ID_DIV_UNI_DT_ID'
        ) {
          metricName = functionEnum[item.calcType[0]];
        } else {
          metricName = `按${item.calcType[1]}${functionEnum[item.calcType[0]]}`;
        }

        return {
          type: item.type,
          // metricsType: item.metricsType,
          metricName,
          step: String.fromCharCode(64 + parseInt(item.step)),
          clsType: item.clsType,
          displayName: item.displayName,
          name: item.name,
          events: [item.event],
          filter: _.isEmpty(item.filters) ? {} : newFilter,
          function: item.calcType
            ? item.calcType[0]
            : calcTypeList.find((filterItem) => filterItem.name === item.name)?.function,
          eventProp: item.eventProp || calcTypeList.find((filterItem) => filterItem.step === item.step)?.eventProp
        };
      });

      if (!globalFilters.every((item) => item.connector)) {
        throw new Error('请选择全局过滤条件');
      }

      const filterEnum = { EVENT_PROP: 'eventProp', USER_LABEL: 'userLabel' };

      metric.forEach((item) => {
        if (!_.isEmpty(item.filter)) {
          item.filter.filter.forEach((filterItem) => {
            if (filterEnum[filterItem.type]) {
              if (
                (filterItem[filterEnum[filterItem.type]].value === '' ||
                  !filterItem[filterEnum[filterItem.type]].value ||
                  (_.isArray(filterItem[filterEnum[filterItem.type]].value) &&
                    !filterItem[filterEnum[filterItem.type]].value.length)) &&
                filterItem[filterEnum[filterItem.type]].operator !== 'IS_NOT_NULL' &&
                filterItem[filterEnum[filterItem.type]].operator !== 'IS_NULL'
              ) {
                throw new Error(
                  `事件指标【${
                    filterItem[filterEnum[filterItem.type]].fieldName ||
                    filterItem[filterEnum[filterItem.type]].displayName
                  }】值不能为空`
                );
              }

              if (filterItem[filterEnum[filterItem.type]].fieldType === 'INT') {
                if (
                  filterItem[filterEnum[filterItem.type]].value &&
                  !_.isArray(filterItem[filterEnum[filterItem.type]].value) &&
                  filterItem[filterEnum[filterItem.type]].value.length > 10
                ) {
                  throw new Error(
                    `事件指标【${
                      filterItem[filterEnum[filterItem.type]].fieldName ||
                      filterItem[filterEnum[filterItem.type]].displayName
                    }】值长度不能超过10字符`
                  );
                }
              }

              if (filterItem[filterEnum[filterItem.type]].fieldType === 'LONG') {
                if (
                  filterItem[filterEnum[filterItem.type]].value &&
                  !_.isArray(filterItem[filterEnum[filterItem.type]].value) &&
                  filterItem[filterEnum[filterItem.type]].value.length > 20
                ) {
                  throw new Error(
                    `事件指标【${
                      filterItem[filterEnum[filterItem.type]].fieldName ||
                      filterItem[filterEnum[filterItem.type]].displayName
                    }】值长度不能超过20字符`
                  );
                }
              }

              if (filterItem.type === 'USER_LABEL') {
                if (
                  filterItem[filterEnum[filterItem.type]].dateType !== 'LATEST' &&
                  (filterItem[filterEnum[filterItem.type]].times === null ||
                    filterItem[filterEnum[filterItem.type]].times === undefined)
                ) {
                  throw new Error(
                    `事件指标【${
                      filterItem[filterEnum[filterItem.type]].fieldName ||
                      filterItem[filterEnum[filterItem.type]].displayName
                    }】标签值更新时间不能为空`
                  );
                }

                const _itemValue = _.cloneDeep(filterItem.userLabel.value);
                if (_.isArray(_itemValue) && typeof _itemValue[0] === 'string') {
                  const filterArrayRes = _itemValue.map((item3) => {
                    const itemRes = item3.match(/\[(.+?)\]/g);
                    item3 = itemRes ? RegExp.$1 : item3;
                    return item3;
                  });
                  filterItem.userLabel.showValue = filterItem.userLabel.showValue || filterItem.userLabel.value;
                  filterItem.userLabel.value = filterArrayRes;
                } else {
                  const result = _itemValue && !_.isArray(_itemValue) && _itemValue.toString().match(/\[(.+?)\]/g);

                  filterItem.userLabel.showValue = filterItem.userLabel.showValue || filterItem.userLabel.value;
                  filterItem.userLabel.value = result ? RegExp.$1 : filterItem.userLabel.value;
                }
              }
            }
          });
        }
      });

      if (!_.isEmpty(globalFilters)) {
        globalFilters.forEach((item) => {
          const keyList = Object.keys(item);
          const key = keyList.find((item) => item === 'eventFilterProperty' || item === 'userLabel');
          if (key === 'eventFilterProperty') {
            item[key].filters.forEach((filterItem) => {
              if (
                (filterItem.value === '' ||
                  !filterItem.value ||
                  (_.isArray(filterItem.value) && !filterItem.value.length)) &&
                filterItem.operator !== 'IS_NOT_NULL' &&
                filterItem.operator !== 'IS_NULL'
              ) {
                throw new Error(`全局过滤【${filterItem.fieldName}】值不能为空`);
              }

              if (filterItem.fieldType === 'INT') {
                if (filterItem.value && !_.isArray(filterItem.value) && filterItem.value.length > 10) {
                  throw new Error(`全局过滤【${filterItem.fieldName}】值长度不能超过10字符`);
                }
              }

              if (filterItem.fieldType === 'LONG') {
                if (filterItem.value && !_.isArray(filterItem.value) && filterItem.value.length > 20) {
                  throw new Error(`全局过滤【${filterItem.fieldName}】值长度不能超过20字符`);
                }
              }
            });
          } else if (key === 'userLabel') {
            item[key].filters[0].filters.forEach((filterItem) => {
              if (
                (filterItem.value === '' ||
                  !filterItem.value ||
                  (_.isArray(filterItem.value) && !filterItem.value.length)) &&
                filterItem.operator !== 'IS_NOT_NULL' &&
                filterItem.operator !== 'IS_NULL'
              ) {
                throw new Error(`全局过滤【${filterItem.displayName}】值不能为空`);
              }
              if (filterItem.fieldType === 'INT') {
                if (filterItem.value && !_.isArray(filterItem.value) && filterItem.value.length > 10) {
                  throw new Error(`全局过滤【${filterItem.displayName}】值长度不能超过10字符`);
                }
              }

              if (filterItem.fieldType === 'LONG') {
                if (filterItem.value && !_.isArray(filterItem.value) && filterItem.value.length > 20) {
                  throw new Error(`全局过滤【${filterItem.displayName}】值长度不能超过20字符`);
                }
              }

              if (filterItem.dateType !== 'LATEST' && (filterItem.times === null || filterItem.times === undefined)) {
                throw new Error(`全局过滤【${filterItem.fieldName || filterItem.displayName}】标签值更新时间不能为空`);
              }

              const _itemValue = _.cloneDeep(filterItem.value);
              if (_.isArray(_itemValue) && typeof _itemValue[0] === 'string') {
                const filterArrayRes = _itemValue.map((item3) => {
                  const itemRes = item3.match(/\[(.+?)\]/g);
                  item3 = itemRes ? RegExp.$1 : item3;
                  return item3;
                });
                filterItem.showValue = filterItem.showValue || filterItem.value;
                filterItem.value = filterArrayRes;
              } else {
                const result = _itemValue && !_.isArray(_itemValue) && _itemValue.toString().match(/\[(.+?)\]/g);

                filterItem.showValue = filterItem.showValue || filterItem.value;
                filterItem.value = result ? RegExp.$1 : filterItem.value;
              }
            });
          }
        });
      }

      _dimensionGroup.map((item) => {
        const { type, filterValue, filters, name } = item;
        if (_.isEmpty(item)) {
          throw new Error('请选择维度分组');
        }
        if (type === 'USER_PROPERTIES') {
          if (!filterValue.length) {
            throw new Error(`维度分组【${name}】值不能为空`);
          }
          const arr = [];
          filterValue.forEach((i) => {
            const _filters = _.cloneDeep(item.filters[0]);
            if (_filters[format[type]].filters[0].fieldType === 'INT') {
              if (i.length > 10) {
                throw new Error(`维度分组【${name}】值长度不能超过10字符`);
              }
            }

            if (_filters[format[type]].filters[0].fieldType === 'LONG') {
              if (i.length > 20) {
                throw new Error(`维度分组【${name}】值长度不能超过20字符`);
              }
            }
            _filters.groupName = i;
            _filters[format[type]].filters[0].value = i;
            arr.push(_filters);
          });
          item.filters = arr;
        } else if (type === 'USER_LABEL' || type === 'TABLE_FIELD') {
          if (!filterValue.length) {
            throw new Error(`维度分组【${name}】值不能为空`);
          }

          const arr = [];
          filterValue.forEach((i) => {
            const _filters = _.cloneDeep(item.filters[0]);
            if (_filters[format[type]].filters[0].filters[0].fieldType === 'INT') {
              if (i.length > 10) {
                throw new Error('值长度不能超过10字符');
              }
            }

            if (_filters[format[type]].filters[0].filters[0].fieldType === 'LONG') {
              if (i.length > 20) {
                throw new Error('值长度不能超过20字符');
              }
            }
            _filters.groupName = i;
            _filters[format[type]].filters[0].filters[0].value = i;
            _filters[format[type]].filters[0].filters[0].showValue =
              item.showValue || _filters[format[type]].filters[0].filters[0].showValue;
            arr.push(_filters);
          });
          item.filters = arr;
        } else if (type === 'CAMPAIGN') {
          if (!filters[0].campaignGroup.filters.length) {
            throw new Error('筛选值不能为空');
          }
        }
        return item;
      });

      // if (nullStatus) {
      //   throw new Error('筛选值不能为空');
      // }

      let data = {
        name,
        extendChartType: boardType || localStorage.getItem('isActivityAnalysis') ? 'CREATE_CAMPAIGN_CHART' : undefined,
        chartConfig: {
          ...chartConfig,
          eventAnalysis: {
            ...chartConfig.eventAnalysis,
            metric,
            filter: renderGlobalFilter(state.globalFilters),
            timeTerm: state.timeTerm,
            // stepList,
            // timeConfig: {
            //   timeTerm: state.timeTerm
            // },
            // globalFilters: state.globalFilters,
            group: renderDimensFilter(state.dimensionGroup),
            // group: _dimensionGroup
            chartDisplayType: null
            // displayGroupNames: state.selectedRowKeys,
            // uniqueId: state.uuid
          }
        },
        scenario: findScenarioId,
        dateRange2: dateRange2.map((item) => {
          return {
            ...item,
            truncateAsDay: true
          };
        }),
        chartType: 'EVENT_ANALYSIS',
        projectId: localStorage.getItem('projectId'),
        id: !_.isNil(id) ? id : null,
        deptId: state.defaultFunnel?.deptId || window.getDeptId(),
        ...(forceCalc ? { argChange: true } : {}),
        isBusinessTable: 0
      };

      if (!data.chartConfig.campaignFilters && campaignId) {
        data.chartConfig = {
          ...data.chartConfig,
          campaignFilters: campaignList
        };
      }

      if (forceCalc) {
        // 如果手动计算就把table表格选项清空
        data = {
          ...data,
          ...obj
        };
        dispatch({ displayType: 'LINE_CHART', chartDisplayType: 'LINE_CHART' });
        // data.chartConfig.eventAnalysis.displayGroupNames = [];
        await doRequest(_.cloneDeep(data), 'TABLE');
        // let tableColumn = await FunnelAnalysis.queryFunnelTableColumn(data.chartConfig.eventAnalysis);
        // dispatch({ tableColumn: tableColumn.columns });
      } else {
        const chartEnum = {
          LINE_CHART: 'LINE',
          COLUMN_CHART: 'COLUMN',
          PERCENT_CHART: 'PIE',
          AREA_CHART: 'AREA'
        };

        data = {
          ...data,
          ...childValue,
          isBusinessTable: 0,
          displayType: undefined,
          chartConfig: {
            ...data.chartConfig,
            eventAnalysis: {
              ...data.chartConfig.eventAnalysis,
              showStyle: childValue ? chartEnum[childValue.displayType] : chartEnum[state.displayType],
              axisConf:
                _.isEmpty(state.chartList) && state.displayType !== 'LINE_CHART' && state.displayType !== 'COLUMN_CHART'
                  ? undefined
                  : state.calcFinishData.axisConf
            }
          },
          id: type === 'updateChart' ? id : null,
          deptId: type === 'updateChart' ? data?.deptId : window.getDeptId()
        };

        if (!data.chartConfig.campaignFilters && campaignId) {
          data.chartConfig = {
            ...data.chartConfig,
            campaignFilters: campaignList
          };
        }
        if (boardType === 'flowCanvas') {
          if (type === 'updateChart') {
            if (scenarioId !== chartConfigData.scenario.id) {
              throw new Error('图表ID类型与活动不一致，不能保存到活动分析');
            }
          } else {
            if (scenarioId !== Number(scenario)) {
              throw new Error('图表ID类型与活动不一致，不能保存到活动分析');
            }
          }
        }

        const result = await AnalysisCenterService.saveChartConfig(data);
        if (campaignId) {
          if (_.isNil(id)) {
            const selectValue = await campaignV2Service.campaignV2ChartBoardListBy([
              {
                operator: 'EQ',
                propertyName: 'campaignId',
                value: campaignId
              }
            ]);
            const widgets = selectValue.length ? selectValue[0].widgets : [];
            // const version = props.location.state.version;

            const _widgets = _.cloneDeep(widgets);

            _widgets.push({
              x: (_widgets.length * 4) % 12,
              y: widgets[widgets?.length - 1]?.y + 100 || 0,
              w: 4,
              h: 3,
              isResizable: true,
              i: `${result.id}`
            });

            await campaignV2Service.saveCampaignV2ChartBoard({
              widgets: _widgets,
              campaignId,
              version: Number(version),
              type: boardType === 'campaigns' ? 'CAMPAIGNS' : 'FLOW_CANVAS',
              id: Number(saveId)
            });
          }

          messageDom(type, data.chartBoards, result, campaignId, boardDetail);
        } else {
          messageDom(type, data.chartBoards, result);
        }
        saveRecordInfo(result.id, userId);
        // props.history.push('/aimarketer/home/<USER>/database');
      }
    } catch (err) {
      console.error(err);
      err.message && message.error(err.message);
      dispatch({
        chartLoading: false,
        tableLoading: false
      });
    }
  };

  const { refresh } = useRequest(() => init(), {
    manual: true
  });

  const messageDom = async (type, chartBoards, result, state, detailObj) => {
    if (parseInt(result?.id) === parseInt(id)) {
      dispatch({ name: result.name });
    }
    dispatch({
      chartLoading: false,
      tableLoading: false,
      visibleDrawer: false
    });
    if (type === 'updateChart') {
      dispatch({ updateChart: false });
      if (state) {
        if (boardType === 'campaigns') {
          message.success(
            <div style={{ display: 'inline-block' }}>
              <span>【{result.name}】</span>已更新至
              <span
                style={{
                  color: 'var(--ant-primary-color)',
                  cursor: 'pointer'
                }}
                onClick={() => props.history.push(`/aimarketer/home/<USER>/detail/${campaignId}`)}
              >
                【{detailObj.name}】
              </span>
              营销活动
            </div>
          );
        } else {
          message.success(
            <div style={{ display: 'inline-block' }}>
              <span>【{result.name}】</span>已更新至
              <span
                style={{
                  color: 'var(--ant-primary-color)',
                  cursor: 'pointer'
                }}
                onClick={() => props.history.push(`/aimarketer/home/<USER>/detail?id=${campaignId}`)}
              >
                【{detailObj.name}】
              </span>
              流程画布
            </div>
          );
        }
      } else {
        return message.success(`【${result.name}】已更新至分析图表`);
      }
    } else {
      if (!_.isEmpty(chartBoards)) {
        message.success(
          <div style={{ display: 'inline-block' }}>
            【{result.name}】已保存至分析图表，并添加至
            {result.chartBoards.map((item) => {
              return (
                <a onClick={() => window.open(`/aimarketer/home/<USER>/dataDashboard/${item.id}`)}>
                  【{item.boardName}】
                </a>
              );
            })}
            看板
          </div>
        );
        if (!id) props.history.push(`/aimarketer/home/<USER>/analysisCenter/database/eventAnalysis/${result.id}`);
        dispatch({ updateChart: false });
        refresh();
      } else {
        if (state) {
          if (boardType === 'campaigns') {
            message.success(
              <div style={{ display: 'inline-block' }}>
                <span>【{result.name}】</span>已保存至
                <span
                  style={{
                    color: 'var(--ant-primary-color)',
                    cursor: 'pointer'
                  }}
                  onClick={() => props.history.push(`/aimarketer/home/<USER>/detail/${campaignId}`)}
                >
                  【{detailObj.name}】
                </span>
                营销活动
              </div>
            );
          } else {
            message.success(
              <div style={{ display: 'inline-block' }}>
                <span>【{result.name}】</span>已保存至
                <span
                  style={{
                    color: 'var(--ant-primary-color)',
                    cursor: 'pointer'
                  }}
                  onClick={() => props.history.push(`/aimarketer/home/<USER>/detail?id=${campaignId}`)}
                >
                  【{detailObj.name}】
                </span>
                流程画布
              </div>
            );
          }
        } else {
          message.success(`【${result.name}】已保存至分析图表`);
        }

        if (id) {
          init(parseInt(result?.id) === parseInt(id) ? result : null);
          dispatch({ updateChart: false });
          return false;
        } else {
          if (state) {
            localStorage.setItem('isActivityAnalysis', true);
            props.history.push(
              `/aimarketer/home/<USER>/analysisCenter/database/eventAnalysis/${result.id}?campaignId=${campaignId}&boardType=${boardType}&scenarioId=${scenario}&version=${version}&saveId=${saveId}`
            );
          } else {
            props.history.push(`/aimarketer/home/<USER>/analysisCenter/database/eventAnalysis/${result.id}`);
          }

          init();
          // dispatch({ name: result.name });
          refresh();
        }
      }
    }

    dispatch({ updateChart: false });
  };

  /**
   * @description: 异步计算接口
   * @param {*} data 需要深克隆data
   * @param {*} isTable 是否是表格
   * @return {*}
   */
  const doRequest = async (data, type, calcTime) => {
    try {
      // if (type) data.chartConfig.eventAnalysis.queryType = type;
      if (calcTime) data.dateRange2 = calcTime;
      const res = await EventAnalysis.calcEventChartResult(data);
      if (!res) {
        dispatch({
          chartLoading: false,
          tableLoading: false
        });
      } else if (res.header.code === 0) {
        // debugger;
        if (!_.isEmpty(res.body.chartResult)) {
          dispatch({ chartResult: res.body.chartResult });
        }

        // res.body.chartResult.tableList.map((item, index) => {
        //   return item.key = index;
        // });

        if (type === 'TABLE') {
          const _dataMap = _.cloneDeep(res.body.chartResult.dataMap);
          const groupStatus = _dataMap.every((item) => Object.keys(item).length === 6);

          const { config } = await EventAnalysis.getBusiness({
            businessType: 'SegmentBusinessConfig'
          });
          const eventData = config.eventBusiTable.config;

          _dataMap.forEach((item) => {
            const index = _.findIndex(
              Object.keys(item),
              (o) => o !== eventData.times && o !== eventData.event_name && o !== 'res'
            );
            item.nameType = eventData.event_name;
            item.eventValue = `${item[eventData.event_name]}·${item.metricName}`;
            item[eventData.event_name] = `${item.step}·${item[eventData.event_name]}·${item.metricName}`;
            item.timeType = eventData.times;
            item.res = Number(item.res.toFixed(2));
            item.displayName = groupStatus
              ? `${item[eventData.event_name]}-${item[Object.keys(item)[index]]}`
              : `${item[eventData.event_name]}`;
            item.nameValue = groupStatus ? item[Object.keys(item)[index]] : undefined;
            if (_.isEmpty(data.chartConfig.eventAnalysis.group)) {
              item.columnName = undefined;
            } else {
              const groupFilter = data.chartConfig.eventAnalysis.group.filter[0];
              if (groupFilter.type === 'EVENT_PROP') {
                item.columnName = groupFilter.eventProp.fieldName;
              } else if (groupFilter.type === 'USER_LABEL') {
                item.columnName = groupFilter.userLabel.label;
              } else {
                item.columnName = '分组';
              }

              // else if (groupFilter.type === 'FLOW_NODES') {
              //   const batchId = item[Object.keys(item)[index]].split('-')[0];
              //   const nodeId = item[Object.keys(item)[index]].split('-')[1];
              //   const targetFlow = groupFilter.flowNodeLogs.flowCalcLogs.find(item => item.id === Number(batchId));
              //   const targetNodeName = _.find(targetFlow.flows, (nodeItem) => nodeItem.nodeId === Number(nodeId)).name;
              //   item.columnName = '分组';
              //   item.displayName = `${item[eventData.event_name]}-${targetNodeName}`;
              // }
            }
          });

          const _axisShowType = _.cloneDeep(state.axisShowType);
          const _eventAnalysis = _.cloneDeep(data.chartConfig.eventAnalysis);

          _axisShowType.left = [];
          _axisShowType.right = [];

          if (!_eventAnalysis.axisConf) {
            _eventAnalysis.axisConf = _eventAnalysis.metric.map((item) => {
              return {
                step: item.step,
                metricName: item.metricName,
                eventName: `${item.step}·${item.name}·${item.metricName}`,
                side: 'LEFT'
              };
            });
          }
          _eventAnalysis.metric.forEach((obj) => {
            if (_eventAnalysis.metric.length === _eventAnalysis.axisConf.length) {
              _eventAnalysis.axisConf = _eventAnalysis.metric.map((m, mIndex) => {
                return {
                  eventName: `${m.step}·${m.name}·${m.metricName}`,
                  step: m.step,
                  metricName: m.metricName,
                  side: _eventAnalysis.axisConf[mIndex].side
                };
              });
            } else if (
              !_eventAnalysis.axisConf.some((existingObj) => {
                return existingObj.eventName === obj.name;
              })
            ) {
              _eventAnalysis.axisConf.push({
                eventName: `${obj.step}·${obj.name}·${obj.metricName}`,
                step: obj.step,
                metricName: obj.metricName,
                side: 'LEFT'
              });
            }
          });

          _eventAnalysis.axisConf.forEach((item) => {
            if (item.side === 'LEFT') {
              _axisShowType.left.push(item.eventName);
            } else {
              _axisShowType.right.push(item.eventName);
            }
          });

          const _axisConf = _.cloneDeep(_eventAnalysis.axisConf);
          _axisConf.forEach((item, index) => {
            item.eventName = `${_eventAnalysis.metric[index].step}·${_eventAnalysis.metric[index].name}·${_eventAnalysis.metric[index].metricName}`;
            item.step = _eventAnalysis.metric[index].step;
            item.metricName = _eventAnalysis.metric[index].metricName;
          });

          dispatch({
            // chartList: res.body.chartResult.chartList,
            chartList: _dataMap,
            // chartList: _mockData,
            isGroupType: groupStatus,
            calcFinishData: { ..._eventAnalysis, axisConf: _axisConf },
            axisShowType: _axisShowType,
            isAreaType: _eventAnalysis.metric.every(
              (item) => item.function === 'COUNT_DT_ID' || item.function === 'SUM_COL'
            ),
            // tableList: mockTableData,
            // lineChartList: res.body.chartResult.lineChartList,
            tableLoading: false,
            chartLoading: false,
            offSave: !!state.chartLoading,
            defaultFunnel: data
          });
          // dispatch({
          //   // chartList: res.body.chartResult.chartList,
          //   chartList: res.body.dataMap,
          //   // chartList: mockData,
          //   calcFinishData: data.chartConfig.eventAnalysis,
          //   // tableList: res.body.chartResult.tableList,
          //   // lineChartList: res.body.chartResult.lineChartList,
          //   tableLoading: false,
          //   chartLoading: false,
          //   offSave: !!state.chartLoading,
          //   defaultFunnel: data
          // });
        }
        // if (res.body.chartResult) {
        //   dispatch({ loading: false });
        // }
      } else if (res.header.code === 210) {
        // dispatch({loading: false});
        timer.current = setTimeout(async () => {
          data.forceCalc = false;
          await doRequest(data, type);
        }, 3000);
      } else if (res.header.code === 1) {
        if (res.header.message) {
          message.info(res.header.message);
        }

        dispatch({
          chartLoading: false,
          tableLoading: false
        });
      }
    } catch (error) {
      // responseData[selectedChart] = {rowList: [], columnList: []};
      dispatch({
        chartLoading: false,
        tableLoading: false
      });
    }
  };

  const inputRef = useRef();

  const onBlurName = () => {
    if (inputRef.current.input.value === '') {
      const name = '未命名事件分析';
      dispatch({ name });
    }
  };

  const setChartType = (type) => {
    dispatch({ chartDisplayType: type });
  };

  const clickIcon = () => {
    setFilterConfig({});
    init();
  };

  const clickOpen = (flag) => {
    dispatch({ open: flag });
  };

  const onShowTypeClick = (type) => {
    setChartType(type);
    setShowChartType(type);
    dispatch({ displayType: type });
  };

  const onYRadioChange = (e, value) => {
    const _axisConf = _.cloneDeep(state.calcFinishData.axisConf);
    const metricIndex = _.findIndex(_axisConf, (o) => o.eventName === value.eventName);
    _axisConf[metricIndex].side = e.target.value;

    dispatch({
      calcFinishData: { ...state.calcFinishData, axisConf: _axisConf }
    });
  };

  const onXRadioChange = (e) => {
    setXaxisRadio(e.target.value);
  };

  const onAxisClick = () => {
    const resConf = state.calcFinishData.axisConf;
    const _axisShowType = { left: [], right: [] };

    dispatch({ offSave: false });

    resConf.forEach((item) => {
      if (item.side === 'LEFT') {
        _axisShowType.left.push(item.eventName);
      } else {
        _axisShowType.right.push(item.eventName);
      }
    });

    dispatch({
      axisShowType: _axisShowType,
      xAxisType: xAxisRadio,
      axisOpen: false
    });
  };

  const axisSettingContent = () => {
    return (
      <div>
        <div className="axisContent">
          {state.calcFinishData.metric.length > 1 ? (
            <div className="yAxisSetting">
              <div className="yTitle">纵坐标</div>
              <div className="yContent">
                {state.calcFinishData.axisConf.map((item, index) => {
                  return (
                    <div className="yItem" key={index}>
                      <div style={{ display: 'flex' }}>
                        <div className="ystep">{String.fromCharCode(64 + parseInt(index + 1))}</div>
                        <div>{item.eventName}</div>
                      </div>
                      <div>
                        <Radio.Group value={item.side} onChange={(e) => onYRadioChange(e, item)}>
                          <Radio value="LEFT">左</Radio>
                          <Radio value="RIGHT">右</Radio>
                        </Radio.Group>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ) : null}

          {state.isGroupType && showChartType === 'COLUMN_CHART' ? (
            <div className="xAxisSetting">
              <div className="xTitle">横坐标</div>
              <div className="xContent">
                <div>
                  <Radio.Group value={xAxisRadio} onChange={(e) => onXRadioChange(e)}>
                    <Radio value="time">以时间为横坐标</Radio>
                    <Radio value="group">以维度分组为横坐标</Radio>
                  </Radio.Group>
                  <span>
                    <Tooltip title="将按照维度分组组合展示所选时间范围内的汇总数">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </span>
                </div>
              </div>
            </div>
          ) : null}
        </div>
        <div className="axisFooter">
          <div className="btnGroup">
            <Button onClick={() => onAxisOpenChange(false)}>取消</Button>
            <Button type="primary" className="submit" onClick={onAxisClick}>
              确定
            </Button>
          </div>
        </div>
      </div>
    );
  };

  const onAxisOpenChange = (open) => {
    dispatch({ axisOpen: open });
  };

  const onCheckChange = (e) => {
    dispatch({ isSwitch: e.target.checked });
  };

  return (
    <div className="eventAnalysis">
      <Spin spinning={false}>
        <header>
          <div className="left">
            <span className="titleBack" onClick={exitQuit}>
              <MyIconV2 type="icon-arrowleft" style={{ fontSize: 20 }} />
            </span>
            <div className="leftHandleWrapper">
              <Input bordered={false} value={state.name} onChange={changeName} ref={inputRef} onBlur={onBlurName} />
            </div>
          </div>
          <div className="right">
            <Space className="btnGroup">
              <Button
                type="primary"
                loading={
                  // 如果chartLoading和tableLoading都为false
                  state.chartLoading
                }
                onClick={() => saveFunnel({ forceCalc: true })}
              >
                计算
              </Button>
              {boardType || localStorage.getItem('isActivityAnalysis') ? (
                <Button
                  onClick={() => (!_.isNil(id) ? dispatch({ updateChart: true }) : dispatch({ visibleDrawer: true }))}
                  disabled={state.offSave || state.chartLoading}
                  // loading={state.initLoading}
                  buttonsRender={([leftButton, rightButton]) => [
                    React.cloneElement(leftButton, {
                      loading: state.initLoading,
                      key: '11'
                    }),
                    React.cloneElement(rightButton, {
                      loading: state.initLoading,
                      key: '22'
                    })
                  ]}
                  type="primary"
                >
                  {!_.isNil(id) ? '更新图表' : '保存图表'}
                </Button>
              ) : (
                <Dropdown.Button
                  icon={<DownOutlined />}
                  onClick={() => (!_.isNil(id) ? dispatch({ updateChart: true }) : dispatch({ visibleDrawer: true }))}
                  disabled={state.offSave || state.chartLoading}
                  // loading={state.initLoading}
                  buttonsRender={([leftButton, rightButton]) => [
                    React.cloneElement(leftButton, {
                      loading: state.initLoading,
                      key: '11'
                    }),
                    React.cloneElement(rightButton, {
                      loading: state.initLoading,
                      key: '22'
                    })
                  ]}
                  type="primary"
                  menu={{
                    items: [
                      {
                        key: '1',
                        label: '另存为副本',
                        onClick: () => dispatch({ visibleDrawer: true })
                      }
                    ]
                  }}
                >
                  {!_.isNil(id) ? '更新图表' : '保存图表'}
                </Dropdown.Button>
              )}
            </Space>
          </div>
        </header>
        <eventAnalysisContext.Provider value={{ state, dispatch, scenarioList }}>
          <div className="content">
            <div className="left" style={{ display: !state.open && 'none' }}>
              <div className="resize-bar" />
              <div className="resize-line" />
              <div className="resize-save">
                <div className="screen">
                  <div>
                    ID类型：
                    <Select
                      style={{ minWidth: '100px' }}
                      value={state.scenarioId}
                      bordered={false}
                      placeholder="请选择"
                      onChange={changeScenario}
                    >
                      {state.scenarioList.map((n) => (
                        <Option key={n.id} value={n.id}>
                          {n.name}
                        </Option>
                      ))}
                    </Select>
                  </div>

                  <div className="overflow-hidden whitespace-nowrap text-ellipsis">
                    归属部门：
                    <Tooltip title={deptPath} placement="topLeft">
                      <span>{deptPath}</span>
                    </Tooltip>
                  </div>
                </div>
                <div className="process">
                  <div className="setUpSteps">
                    <div className="title">事件指标</div>
                    <Steps />
                  </div>
                  <div className="setGlobalFilter">
                    <div className="title">全局过滤</div>
                    <GlobalFilter />
                  </div>
                  <div className="setDimension">
                    <div className="title">维度分组</div>
                    <Dimension />
                  </div>
                  {campaignId && (
                    <div className="campaignDataWrap">
                      <div className="title">活动数据</div>
                      <Tree
                        treeData={campaignTreeData}
                        selectable={false}
                        className={boardType === 'campaigns' ? '' : 'treeNoopNone'}
                      />
                    </div>
                  )}
                </div>
              </div>
              <div className="open" onClick={() => clickOpen(false)}>
                <MyIconV2 type="icon-icon-page" />
              </div>
            </div>
            <div className="right">
              {!state.open && (
                <div className="open" onClick={() => clickOpen(true)}>
                  <MyIconV2 type="icon-icon-page" />
                </div>
              )}
              <div className="rightScreen" style={{ paddingLeft: !state.open && '80px' }}>
                <Select value={state.timeTerm} onChange={(e) => dispatch({ timeTerm: e })}>
                  <Option value="DAY">按天</Option>
                  <Option value="HOUR">按小时</Option>
                  <Option value="MONTH">按月</Option>
                </Select>
                <SelectTime
                  data={state.dateRange2}
                  showTime
                  isAnalysis
                  style={{
                    maxWidth: 'auto'
                  }}
                  onChange={change}
                />

                {/* <Select defaultValue="contrast">
                    <Option value="contrast">对比</Option>
                  </Select> */}
                <div className="chartType">
                  {/* <Button type={state.chartDisplayType === 'COLUMN_CHART' && 'primary'} onClick={() => setChartType('COLUMN_CHART')}>柱图</Button>
                  <Button type={state.chartDisplayType === 'PERCENT_COLUMN_CHART' && 'primary'} onClick={() => setChartType('PERCENT_COLUMN_CHART')}>%柱图</Button>
                  <Button type={state.chartDisplayType === 'LINE_CHART' && 'primary'} onClick={() => setChartType('LINE_CHART')}>趋势</Button> */}
                  <span
                    className={`chartTypeIcon ${showChartType === 'LINE_CHART' ? 'chartIconActive' : ''}`}
                    onClick={() => onShowTypeClick('LINE_CHART')}
                    style={{}}
                  >
                    <MyIconV2 type="icon-icon-chart-line" style={{ fontSize: 20 }} />
                  </span>
                  <span
                    className={`chartTypeIcon ${showChartType === 'COLUMN_CHART' ? 'chartIconActive' : ''}`}
                    onClick={() => onShowTypeClick('COLUMN_CHART')}
                  >
                    <MyIconV2 type="icon-icon-chart-column" style={{ fontSize: 20 }} />
                  </span>
                  <span
                    className={`chartTypeIcon ${showChartType === 'PERCENT_CHART' ? 'chartIconActive' : ''}`}
                    onClick={() => onShowTypeClick('PERCENT_CHART')}
                    style={
                      state.isGroupType
                        ? null
                        : {
                            color: '#ccc',
                            cursor: 'not-allowed',
                            pointerEvents: 'none'
                          }
                    }
                  >
                    <MyIconV2 type="icon-icon-chart-pie" style={{ fontSize: 20 }} />
                  </span>
                  {state.isGroupType ? null : (
                    <Tooltip title="饼图不支持无分组">
                      <span className="disableMask" placement="topRight" />
                    </Tooltip>
                  )}
                  <span
                    className={`chartTypeIcon ${showChartType === 'AREA_CHART' ? 'chartIconActive' : ''}`}
                    onClick={() => onShowTypeClick('AREA_CHART')}
                    style={
                      state.isAreaType
                        ? null
                        : {
                            color: '#ccc',
                            cursor: 'not-allowed',
                            pointerEvents: 'none'
                          }
                    }
                  >
                    <MyIconV2 type="icon-icon-chart-area" style={{ fontSize: 20 }} />
                  </span>
                  {state.isAreaType ? null : (
                    <Tooltip title="堆叠图不支持多指标">
                      <span className="disableMask2" placement="topRight" />
                    </Tooltip>
                  )}
                </div>
              </div>
              {!_.isEmpty(filterConfig) && (
                <div className="term">
                  <span className="title">外部筛选条件：</span>
                  <Card style={{ width: '348px', height: '32px' }}>
                    <p>
                      {dayjs(parseInt(filterConfig.startTime)).format('YYYY-MM-DD HH:mm:ss')} ~{' '}
                      {dayjs(parseInt(filterConfig.endTime)).format('YYYY-MM-DD HH:mm:ss')}{' '}
                      <CloseOutlined onClick={clickIcon} />
                    </p>
                  </Card>
                </div>
              )}

              {!_.isEmpty(state.chartList) ? (
                <>
                  <div className="chart">
                    {!_.isEmpty(state.calcFinishData) &&
                    (showChartType === 'LINE_CHART' || showChartType === 'COLUMN_CHART') ? (
                      (showChartType === 'LINE_CHART' && state.calcFinishData.metric.length === 1) ||
                      (!state.isGroupType && state.calcFinishData.metric.length === 1) ? null : (
                        <Popover
                          content={axisSettingContent}
                          trigger="click"
                          title="坐标轴设置"
                          placement="bottomRight"
                          overlayClassName="axisSettinnPop"
                          open={state.axisOpen}
                          onOpenChange={onAxisOpenChange}
                        >
                          <div className="axisSetting">
                            <SettingOutlined />
                            坐标轴设置
                          </div>
                        </Popover>
                      )
                    ) : null}

                    <RenderChart chartList={state.chartList} loading={state.chartLoading} />
                    {/* <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无数据，请选择至少2个漏斗步骤"
                /> */}
                  </div>
                  <div style={{ padding: '0 24px' }}>
                    <Divider />
                  </div>
                  <div className="table1">
                    <div className="eventHeader">
                      <div className="eventTableTitle">事件分析表格</div>
                      <div className="columnsSwitch">
                        <Checkbox checked={state.isSwitch} onChange={onCheckChange}>
                          行列转置
                        </Checkbox>
                      </div>
                    </div>
                    <RenderTable loading={state.chartLoading} />
                  </div>
                </>
              ) : (
                <div className="empty">
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                </div>
              )}
            </div>
          </div>
          <Drawer
            title="保存图表"
            placement="right"
            width={600}
            onClose={() => dispatch({ visibleDrawer: false })}
            destroyOnClose
            footer={
              <div className="buttomFooter">
                <Button onClick={() => dispatch({ visibleDrawer: false })}>取消</Button>
                <Button type="primary" onClick={saveFunnel}>
                  保存
                </Button>
              </div>
            }
            open={state.visibleDrawer}
          >
            <SaveChart
              onRef={onRef}
              chartName={state.name}
              boardType={boardType}
              campaignId={campaignId}
              detailObj={boardDetail}
            />
          </Drawer>
          <Drawer
            open={state.updateChart}
            title="更新图表"
            width={560}
            destroyOnClose
            onCancel={() => dispatch({ updateChart: false })}
            onClose={() => dispatch({ updateChart: false })}
            footer={
              <div className="buttomFooter">
                <Button onClick={() => dispatch({ updateChart: false })} key="1">
                  取消
                </Button>
                <Button type="primary" key="3" onClick={() => saveFunnel({ type: 'updateChart' })}>
                  更新图表
                </Button>
              </div>
            }
          >
            <UpdateChart boardType={boardType} detailObj={boardDetail} />
          </Drawer>
        </eventAnalysisContext.Provider>
      </Spin>
    </div>
  );
}
