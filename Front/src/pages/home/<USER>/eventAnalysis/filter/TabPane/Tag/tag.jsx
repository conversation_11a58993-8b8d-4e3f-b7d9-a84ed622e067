import { SearchOutlined } from '@ant-design/icons';
import { Button, Empty, Input, Spin, Tree, message } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import User360 from 'service/User360';
import userLabelService from 'service/userlabelservice';
import { t } from 'utils/translation';
import { eventAnalysisContext } from '../../../eventAnalysisContext';
import './tag.scss';
import TagHover from './tagHover';

const user360 = new User360();
const { DirectoryTree } = Tree;

const findObj = (data, key) => {
  let info = {};
  data.forEach((n) => {
    if (n.key === key) {
      info = n;
    }
    n.children &&
      n.children.forEach((w) => {
        if (w.key === key) {
          info = w;
        }
        w.children &&
          w.children.forEach((h) => {
            if (h.key === key) {
              info = h;
            }
          });
      });
  });
  return info;
};

export default ({ setFlagVisible }) => {
  const {
    state,
    dispatch,
    setVisible,
    flag,
    isDimension,
    index,
    ind,
    isMulti,
    filterIndex,
    filterConnetor,
    dimensionIndex
  } = useContext(eventAnalysisContext);
  const { stepList, scenarioId, scenarioList, globalFilters, dimensionGroup } = state;
  const scenario = scenarioList.find((item) => item.id === scenarioId);
  const [treeData, setTreeData] = useState([]);
  const [labelHistoryList, setLabelHistoryList] = useState([]);
  const [selectValue, setSelectValue] = useState(null);
  const [userLabels, setUserLabels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [objData, setObjData] = useState({});
  const [loadingTagValue, setLoadingTagValue] = useState(false);
  const [displayName, setDisplayName] = useState('');
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [loadedKeys, setLoadedKeys] = useState([]);
  const [flags, setFlags] = useState(true);

  useEffect(() => {
    const init = async () => {
      const data = await getAsyncData('key.0');
      setTreeData(data);
    };
    init();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getAsyncData = async (key) => {
    setLoading(true);
    const result = await user360.findAllCategory({
      categoryId: parseInt(key.split('.')[1]),
      scenario
    });
    const data = [];
    const obj = {};
    result.categoryList &&
      result.categoryList.forEach((n) => {
        data.push({
          title: n.name,
          key: `key.${n.id}`
        });
      });
    result.userLabels &&
      result.userLabels.forEach((n) => {
        data.push({
          title: n.displayName,
          key: `${n.id}`,
          name: n.name,
          scenarioName: n.scenario.name,
          scenarioCode: n.scenario.code,
          isLeaf: true,
          type: n.type,
          validDate: n.validDate,
          tagValue: n,
          icon: <></>
        });
        obj[`${n.id}`] = n;
      });
    setObjData({ ...objData, ...obj });
    result.userLabels && setUserLabels([...userLabels, ...result.userLabels]);
    setLoading(false);
    return data;
  };

  useEffect(() => {
    const getData = async () => {
      setLoadingTagValue(true);
      let data = await userLabelService.getTagValuesById({
        // beginDate: timeValue[0].valueOf(),
        labelId: parseInt(selectValue.id)
        // endDate: timeValue[1].valueOf()
      });
      // if (selectValue.key !== parseInt(selectValue.key)) return setLoadingTagValue(false);
      setLoadingTagValue(false);
      data = _.map(data, (item) => {
        return {
          ...item,
          valueAndDisplayValue: `${item.valueAndDisplayValue}`
        };
      });
      data = _.sortBy(data, (o) => {
        return o.createTime;
      });
      setLabelHistoryList(data);
    };

    selectValue && getData();
  }, [selectValue]);

  useEffect(() => {
    const getLabelSearch = async () => {
      setLoading(true);
      if (displayName) {
        const _list = await userLabelService.getTagList({ displayName, scenario });
        const _categoryList = await userLabelService.findCategoryByProjectId({});
        const result = getLevelList(_list, _categoryList);
        setLoadedKeys([]);
        setTreeData(result._treeData);
        setExpandedKeys(result._expandedKeys);
        setFlags(true);
        const data = getObjData(_list);
        setObjData({ ...objData, ...data });
      } else if (flags) {
        setFlags(false);
        const data = await getAsyncData('key.0');
        setLoadedKeys([]);
        setExpandedKeys([]);
        setTreeData(data);
      }
      setLoading(false);
    };
    // if (visible) {
    getLabelSearch();
  }, [displayName]);

  const getLevelList = (_list, _categoryList) => {
    const _treeData = [];
    let _expandedKeys = [];
    const categoryObj = {};
    _categoryList.forEach((n) => {
      categoryObj[`key.${n.id}`] = n;
    });
    _list.forEach((n) => {
      const info = categoryObj[`key.${n.categoryId}`];
      if (!info) {
        _treeData.push({ title: n.displayName, key: `${n.id}`, isLeaf: true });
      } else {
        const codeArr = `${info.path}${info.id}`
          .split(',')
          .filter((x) => x !== '0')
          .map((h) => `key.${h}`);
        _expandedKeys = _expandedKeys.concat(codeArr);
        if (codeArr[0] && !_treeData.find((item) => item.key === codeArr[0])) {
          _treeData.push({
            title: categoryObj[codeArr[0]].name,
            key: codeArr[0]
          });
        }
        if (codeArr[1]) {
          const category1Info = findObj(_treeData, codeArr[0]);
          if (!category1Info.children) {
            category1Info.children = [{ title: categoryObj[codeArr[1]].name, key: codeArr[1] }];
          } else if (category1Info.children.findIndex((w) => w.key === codeArr[1]) === -1) {
            category1Info.children.push({
              title: categoryObj[codeArr[1]].name,
              key: codeArr[1]
            });
          }
        }
        if (codeArr[2]) {
          const category2Info = findObj(_treeData, codeArr[1]);
          if (!category2Info.children) {
            category2Info.children = [{ title: categoryObj[codeArr[2]].name, key: codeArr[2] }];
          } else if (category2Info.children.findIndex((w) => w.key === codeArr[2]) === -1) {
            category2Info.children.push({
              title: categoryObj[codeArr[2]].name,
              key: codeArr[2]
            });
          }
        }
        const categoryInfo = findObj(_treeData, `key.${n.categoryId}`);
        if (!categoryInfo.children) {
          categoryInfo.children = [{ title: n.displayName, key: `${n.id}`, isLeaf: true }];
        } else {
          categoryInfo.children.push({
            title: n.displayName,
            key: `${n.id}`,
            isLeaf: true
          });
        }
      }
    });
    _treeData.sort((a, b) => (a.isLeaf ? 1 : 0) - (b.isLeaf ? 1 : 0));
    return { _treeData, _expandedKeys };
  };

  const getObjData = (_list) => {
    const data = {};
    _list.forEach((n) => {
      data[n.id] = n;
    });
    return data;
  };

  const onSelect = (keys, info) => {
    // 判定 是否是叶子节点
    if (info.node.isLeaf) {
      const labelInfo = objData[keys[0]];
      setSelectValue(labelInfo);
    }
  };

  const onLoadData = async ({ key, children }) => {
    if (children) {
      return;
    }
    const data = await getAsyncData(key);
    setTreeData((origin) => updateTreeData(origin, key, data));
  };

  const updateTreeData = (list, key, children) => {
    // debugger;
    return list.map((node) => {
      if (node.key === key) {
        return { ...node, children };
      } else if (node.children) {
        return {
          ...node,
          children: updateTreeData(node.children, key, children)
        };
      }
      return node;
    });
  };

  const save = () => {
    if (!_.isEmpty(selectValue)) {
      const findValue = _.cloneDeep(selectValue);

      const data = {
        connector: filterConnetor,
        userLabel: {
          connector: filterConnetor,
          filters: [
            {
              connector: filterConnetor,
              filters: [
                {
                  dataType: findValue.dataType,
                  displayName: findValue.displayName,
                  id: findValue.id,
                  fieldType: findValue.dataType,
                  label: findValue.name,
                  lastCalcTime: findValue?.lastCalcTime,
                  operator: 'EQ',
                  value: null,
                  dateType: findValue?.busiDate ? 'LATEST' : findValue?.lastCalcTime ? 'ABSOLUTE' : 'RELATIVE',
                  times: findValue?.busiDate ? null : findValue?.lastCalcTime ? findValue.lastCalcTime : 0,
                  isEnum: findValue.whetherEnum
                }
              ]
            }
          ]
        }
      };
      if (flag) {
        const _globalFilters = _.cloneDeep(globalFilters);
        _globalFilters[filterIndex] = data;
        _globalFilters[filterIndex].userLabelList = findValue;
        dispatch({ globalFilters: _globalFilters });
      } else if (isDimension) {
        const _dimensionGroup = _.cloneDeep(dimensionGroup);
        _dimensionGroup[dimensionIndex] = {
          type: 'USER_LABEL',
          name: findValue.displayName,
          filterValue: [],
          group: dimensionIndex,
          filters: [
            {
              groupName: null,
              userLabelList: findValue,
              userLabel: {
                connector: 'AND',
                filters: [
                  {
                    connector: 'AND',
                    filters: [
                      {
                        dataType: findValue.dataType,
                        displayName: findValue.displayName,
                        id: findValue.id,
                        fieldType: findValue.dataType,
                        label: findValue.name,
                        operator: 'EQ',
                        value: null,
                        dateType: findValue?.busiDate ? 'LATEST' : findValue?.lastCalcTime ? 'ABSOLUTE' : 'RELATIVE',
                        times: findValue?.busiDate ? null : findValue?.lastCalcTime ? findValue.lastCalcTime : 0
                      }
                    ]
                  }
                ]
              }
            }
          ]
        };
        setFlagVisible(false);
        dispatch({ dimensionGroup: _dimensionGroup });
      } else {
        const _stepList = _.cloneDeep(stepList);
        if (isMulti) {
          _stepList[index].multiStepList[ind].filters[filterIndex] = data;
        } else {
          _stepList[index].filters[filterIndex] = data;
          _stepList[index].filters[filterIndex].userLabelList = findValue;
        }
        dispatch({ stepList: _stepList });
      }
      setVisible(false);
    } else {
      return message.error(t('analysisCenter-0SoBRXggB5Cu'));
    }
  };

  const onLabelSearch = _.debounce((val) => {
    setDisplayName(val);
  }, 500);

  return (
    <>
      <Input
        placeholder={t('analysisCenter-ubC2qqM4JBbr')}
        className="w-[40%]"
        onKeyDown={loading ? (e) => e.preventDefault() : null}
        suffix={<SearchOutlined className="text-[rgba(0,0,0,.65)]" />}
        onChange={(e) => onLabelSearch(e.target.value)}
      />
      <div className="Tag">
        <div className="treeContent">
          <div className="leftTree">
            <Spin spinning={loading}>
              <DirectoryTree
                className="fieldDirectoryTree"
                onSelect={onSelect}
                loadData={onLoadData}
                treeData={treeData}
                loadedKeys={loadedKeys}
                expandedKeys={expandedKeys}
                onExpand={setExpandedKeys}
                onLoad={(data) => setLoadedKeys(data)}
              />
            </Spin>
          </div>

          <div className="info">
            {selectValue ? (
              <Spin spinning={loading} className="relative top-[24px]">
                {' '}
                <TagHover
                  selectValue={selectValue}
                  labelHistoryList={labelHistoryList}
                  loading={loadingTagValue}
                />{' '}
              </Spin>
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )}
          </div>
        </div>
        <footer>
          <Button type="primary" onClick={save}>
            {t('analysisCenter-I0mK00tLqRSb')}
          </Button>
        </footer>
      </div>
    </>
  );
};
