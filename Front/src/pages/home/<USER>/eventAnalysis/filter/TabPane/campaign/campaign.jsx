/*
 * @Author: 卜广硕 <EMAIL>
 * @Date: 2023-03-29 18:41:54
 * @LastEditors: 卜广硕 <EMAIL>
 * @LastEditTime: 2023-04-11 10:01:59
 * @FilePath: \Front\src\pages\home\analysisCenter\eventAnalysis\filter\TabPane\campaign\campaign.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import _ from 'lodash';
import React, { useContext } from 'react';
import './campaign.scss';

import { t } from 'utils/translation';
import { eventAnalysisContext } from '../../../eventAnalysisContext';

/**
 * @description:
 * @param {*} flag 是否是步骤过滤
 * @param {*} index 步骤下标
 * @param {*} ind 过滤的下标
 * @param {*} value 当前步骤过滤条件
 * @param {*} isMulti 是否是多路径
 * @return {*}
 */
export default ({ setFlagVisible }) => {
  const contextValue = useContext(eventAnalysisContext);
  const {
    state,
    dispatch,
    flag,
    isDimension,
    index,
    ind,
    isMulti,
    filterIndex,
    setVisible,
    filterConnetor,
    dimensionIndex
  } = contextValue;
  const { stepList, globalFilters, dimensionGroup } = state;

  const save = (value) => {
    const data = {
      connector: filterConnetor,
      campaignGroup: {
        connector: filterConnetor,
        filterType: value,
        filters: []
      }
    };
    if (flag) {
      const _globalFilters = _.cloneDeep(globalFilters);
      _globalFilters[filterIndex] = data;
      dispatch({ globalFilters: _globalFilters });
    } else if (isDimension) {
      const _dimensionGroup = _.cloneDeep(dimensionGroup);
      _dimensionGroup[dimensionIndex] = {
        type: 'CAMPAIGN',
        filterValue: [],
        group: dimensionIndex,
        filters: [
          {
            groupName: null,
            campaignGroup: {
              connector: 'AND',
              filterType: value,
              filters: []
            }
          }
        ]
      };
      setFlagVisible(false);
      dispatch({ dimensionGroup: _dimensionGroup });
    } else {
      const _stepList = _.cloneDeep(stepList);
      if (isMulti) {
        _stepList[index].multiStepList[ind].filters[filterIndex] = data;
      } else {
        _stepList[index].filters[filterIndex] = data;
      }
      dispatch({ stepList: _stepList });
    }
    setVisible(false);
  };

  return (
    <div className="TabPaneCampaign">
      {/* <div className="node" onClick={() => save('CAMPAIGN')}>按营销活动</div> */}
      <div className="node" onClick={() => save('CAMPAIGN_BATCH')}>
        {t('analysisCenter-KpWrkAiXtxS7')}
      </div>
      <div className="node" onClick={() => save('CAMPAIGN_NODE')}>
        {t('analysisCenter-P8AxeWWZEo3n')}
      </div>
    </div>
  );
};
