import { Divider, Empty, Input, Popover, Spin, Typography } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useContext, useEffect, useRef, useState } from 'react';
import UserGroupService from 'service/UserGroupService';
import DataEngineService from 'service/dataEngineService';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { useDebounce } from 'utils/customhooks';
import { t } from 'utils/translation';
import { ActionCollective, Complex } from 'wolf-static-cpnt';
import CampaignDetail from '../../../../../portraitCenter/userGroup/create/campaign/selectCampaign/detail';
import MyFilter from '../../../../../portraitCenter/userGroup/create/conditional/filterCondition/myFilter/MyFilter';
import { eventAnalysisContext } from '../../../eventAnalysisContext';
import { calcStatusList } from './config';
import './userGroup.scss';

const { Search } = Input;
const { Title } = Typography;
const userGroupService = new UserGroupService();
const dataEngineService = new DataEngineService();
const operatorMap = {
  EQ: t('analysisCenter-ySF4RpYDzEkU'),
  GT: t('analysisCenter-TqIyzXF4Ep5Q'),
  GTE: t('analysisCenter-yBjv6OsLWu0Q'),
  LT: t('analysisCenter-uqjTJKASOxcZ'),
  LTE: t('analysisCenter-ao8aE5dxmZdX')
};

export default () => {
  const { state, dispatch, setVisible, flag, index, ind, isMulti, filterIndex, filterConnetor } =
    useContext(eventAnalysisContext);
  const { stepList, scenarioId, globalFilters } = state;
  const searchRef = useRef();
  const [userGroupQuery, setUserGroupQuery] = useState(null);
  const [loading, setLoading] = useState(false);
  const [groupList, setGroupList] = useState([]);
  const searchConfig = {
    size: 20,
    page: 1,
    search: [
      { operator: 'LIKE', propertyName: 'name', value: '' },
      { operator: 'EQ', propertyName: 'status', value: 'NORMAL' },
      { operator: 'EQ', propertyName: 'calcStatus', value: 'SUC' },
      { operator: 'EQ', propertyName: 'scenario.id', value: scenarioId },
      {
        operator: 'IN',
        propertyName: 'approvalStatus',
        value: 'NONE,PASS,CANCEL'
      },
      {
        operator: 'EQ',
        propertyName: 'deptId',
        value: window.getDeptId()
      }
    ],
    sorts: [{ direction: 'desc', propertyName: 'updateTime' }]
  };

  useEffect(() => {
    init();
  }, []);

  const init = async () => {
    setLoading(true);
    try {
      const userGroupQuery = await FunnelAnalysis.getEventQuery(searchConfig);
      setUserGroupQuery(userGroupQuery);
      const groupList = await userGroupService.list([
        {
          operator: 'EQ',
          propertyName: 'status',
          value: 'NORMAL'
        },
        {
          operator: 'EQ',
          propertyName: 'deptId',
          value: window.getDeptId()
        }
      ]);
      setGroupList(groupList);
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  };

  const onChange = useDebounce(() => {
    if (searchRef.current.input.value === '') {
      init();
    }
    const getSearch = async () => {
      setLoading(true);
      const _searchConfig = _.cloneDeep(searchConfig);
      _searchConfig.search[0].value = searchRef.current.input.value;
      const userGroupQuery = await FunnelAnalysis.getEventQuery(_searchConfig);
      setUserGroupQuery(userGroupQuery);
      setLoading(false);
    };
    getSearch();
  }, 500);

  const clickUserGroup = (item) => {
    const data = {
      connector: filterConnetor,
      segment: {
        connector: filterConnetor,
        filters: [
          {
            connector: filterConnetor,
            filters: [
              {
                segment: {
                  customerCount: item.customerCount,
                  id: item.id,
                  lastCalcTime: item.lastCalcTime,
                  name: item.name
                },
                type: 'INCLUDE'
              }
            ]
          }
        ]
      }
    };

    if (flag) {
      const _globalFilters = _.cloneDeep(globalFilters);
      _globalFilters[filterIndex] = data;
      _globalFilters[filterIndex].userGroupList = item;
      dispatch({ globalFilters: _globalFilters });
    } else {
      const _stepList = _.cloneDeep(stepList);
      if (isMulti) {
        _stepList[index].multiStepList[ind].filters[filterIndex] = data;
      } else {
        _stepList[index].filters[filterIndex] = data;
        _stepList[index].filters[filterIndex].userGroupList = item;
      }
      dispatch({ stepList: _stepList });
    }
    setVisible(false);
  };

  const renderUserGroupInfo = (data) => {
    const rule = {
      ONCE: t('analysisCenter-zGuzZ1LgU0Os'),
      SCHEDULE: t('analysisCenter-WbMW4Z0onZPc'),
      DAY: t('analysisCenter-9VebW87i18my'),
      WEEK: t('analysisCenter-6U3LV8xVTaZF'),
      MONTH: t('analysisCenter-jC3ZxLHNpcNG')
    };
    const { name, calcStatus, scheduleConf, customerCount, lastCalcTime, calcRule } = data;
    // const { calcRule, schedule } = scheduleConf;
    return (
      <div className="renderUserGroupInfo">
        <div className="title">{name}</div>
        <div className="content">
          <div>
            {t('analysisCenter-8ofjDQtiMgsh')}{' '}
            {calcStatus && _.filter(calcStatusList, (v) => v.value === calcStatus)[0].name}
          </div>
          <div>
            {t('analysisCenter-XZuHUjlYVoC3')}
            {rule[calcRule]}
          </div>
          {calcRule === 'SCHEDULE' && (
            <div>
              {t('analysisCenter-Qy0kjJJRKaVp')}
              {scheduleConf ? rule[scheduleConf.schedule.scheduleRate] : '-'}
            </div>
          )}
          {/* <div>下次计算时间：2022-07-14 00:00:00</div> */}
        </div>
        {data.type !== 'CAMPAIGN' && (
          <>
            <div className="count">
              {customerCount}
              {t('analysisCenter-MLabopFEK4Be')}
            </div>
            <div className="lastCalcTime">{dayjs(lastCalcTime).format('YYYY-MM-DD HH:mm:ss')}</div>
          </>
        )}
        {ruleRender(data)}
      </div>
    );
  };

  const ruleRender = (data) => {
    if (data.type === 'CONDITIONAL') {
      return (
        <MyFilter
          mode="detail"
          value={{
            filterInfo: data.filterInfo || {},
            label: data.label || {},
            connector: data.connector || 'AND'
          }}
        />
      );
    } else if (data.type === 'COMPLEX') {
      return (
        <>
          <Title level={4}>{t('analysisCenter-thoKS4eripKV')}</Title>
          <Complex
            dataProvider={dataProvider}
            value={data.includeSegments || {}}
            selectList={groupList}
            mode="detail"
          />
          <div hidden={data.excludeSegments.filters.length === 0}>
            <Divider />
            <Title level={4}>{t('analysisCenter-iPnKZuFJfd9H')}</Title>
            <Complex
              dataProvider={dataProvider}
              value={data.excludeSegments || {}}
              selectList={groupList}
              mode="detail"
            />
          </div>
        </>
      );
    } else if (data.type === 'CAMPAIGN') {
      return <CampaignDetail info={data} />;
    } else if (data.type === 'UPLOAD') {
      return (
        <Empty
          description={
            <span style={{ fontSize: 14, color: 'var(--ant-primary-color)' }}>{t('analysisCenter-k8c1xAG0eLDn')}</span>
          }
        />
      );
    } else if (data.type === 'CONDITION_AGGREGATE') {
      return (
        <>
          <Title level={4}>{t('analysisCenter-thoKS4eripKV')}</Title>
          <ActionCollective dataProvider={dataProvider} value={data.includeConditionAggregate || {}} mode="detail" />
          <div hidden={data.excludeConditionAggregate.filters.length === 0}>
            <Divider />
            <Title level={4}>{t('analysisCenter-iPnKZuFJfd9H')}</Title>
            <ActionCollective dataProvider={dataProvider} value={data.excludeConditionAggregate || {}} mode="detail" />
          </div>
        </>
      );
    } else if (data.type === 'SHORT_LINK') {
      return (
        <div className="short-link-rules">
          {_.map(data?.shortLinkInfo?.filters, (item) => {
            if (item.function === 'COUNT') {
              return (
                <div
                  key={`${item.name}${item.function}`}
                  className="short-link-rule"
                >{`${t('analysisCenter-xl9Jsx622xkI')}${operatorMap[item.operator]} ${item.value}`}</div>
              );
            }
            return (
              <div
                key={`${item.name}${item.function}`}
                className="short-link-rule"
              >{`${item.name}: ${operatorMap[item.operator]} ${item.value}`}</div>
            );
          })}
        </div>
      );
    }
  };

  const dataProvider = {
    getPropertyList: async (name, eventId) => {
      const propertyList = await dataEngineService.propertyList({
        name,
        eventId: eventId || 0
      });
      return propertyList;
    },
    getPropertyEnumList: async (tableId, schemaId) => {
      const propertyItem = await dataEngineService.findFilterEnum({
        tableId,
        schemaId
      });
      return propertyItem;
    },

    getEventList: async (name) =>
      userGroupService.getEventList({
        size: 20,
        page: 1,
        search: [{ operator: 'LIKE', propertyName: 'name', value: name }],
        sorts: [{ direction: 'desc', propertyName: 'updateTime' }]
      }),
    getEventPropertyList: async (name, eventId) =>
      userGroupService.getEventPropertyList({
        name,
        eventId
      }),
    getEventCountLogsByProjectId: async () =>
      FunnelAnalysis.getEventCountLogsByProjectId({
        projectId: localStorage.getItem('projectId')
      }),
    getGroupList: async () =>
      userGroupService.list([
        {
          operator: 'EQ',
          propertyName: 'status',
          value: 'NORMAL'
        },
        {
          operator: 'EQ',
          propertyName: 'deptId',
          value: window.getDeptId()
        }
      ])
  };

  return (
    <div className="TabPaneUserGroup">
      <Spin spinning={loading}>
        <Search placeholder={t('analysisCenter-4FCVb9IYwfVr')} allowClear onChange={onChange} ref={searchRef} />
        <div className="tip">
          {t('analysisCenter-Cq7iHwjdSP69')}
          {userGroupQuery?.totalElements}
          {t('analysisCenter-VrJhWy7W0mHZ')}
        </div>
        <div className="userGroupBox">
          {userGroupQuery &&
            userGroupQuery.content.map((item) => {
              return (
                <div className="popItem" key={item.id}>
                  <Popover
                    placement="right"
                    overlayClassName="userGroupItemPopover"
                    content={() => renderUserGroupInfo(item)}
                    trigger="hover"
                  >
                    <div className="userGroupItem" onClick={() => clickUserGroup(item)}>
                      {item.name}
                    </div>
                  </Popover>
                </div>
              );
            })}
        </div>
      </Spin>
    </div>
  );
};
