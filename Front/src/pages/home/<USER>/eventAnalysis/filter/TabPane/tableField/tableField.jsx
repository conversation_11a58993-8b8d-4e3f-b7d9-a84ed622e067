import { Popover, Spin, Tree } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { t } from 'utils/translation';
import { eventAnalysisContext } from '../../../eventAnalysisContext';
import './tableField.scss';

const { DirectoryTree } = Tree;

/**
 * @description:
 * @param {*} flag 是否是步骤过滤
 * @param {*} index 步骤下标
 * @param {*} ind 过滤的下标
 * @param {*} value 当前步骤过滤条件
 * @param {*} isMulti 是否是多路径
 * @return {*}
 */
export default ({ setFlagVisible }) => {
  const contextValue = useContext(eventAnalysisContext);
  const {
    state,
    dispatch,
    flag,
    index,
    ind,
    isMulti,
    filterIndex,
    setVisible,
    filterConnetor,
    isDimension,
    dimensionIndex
  } = contextValue;
  const { stepList, globalFilters, dimensionGroup } = state;
  const [treeData, setTreeData] = useState([]);
  const [allTableField, setAllTableField] = useState([]);
  const [loading, setLoading] = useState(false);
  const [objData, setObjData] = useState({});

  useEffect(() => {
    const init = async () => {
      const data = await getAsyncData('key.0');
      setTreeData(data);
    };
    init();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getAsyncData = async () => {
    setLoading(true);
    const tableData = await FunnelAnalysis.getTableListBy([
      {
        operator: 'EQ',
        propertyName: 'table.id',
        value: isMulti ? stepList[index]?.multiStepList[ind]?.bizTable?.table?.id : stepList[index]?.bizTable?.table?.id
      }
    ]);
    const data = [];
    const obj = {};
    tableData &&
      tableData.forEach((n) => {
        data.push({
          title: n.table.displayName,
          key: `key.${n.table.id}`
        });
      });
    setObjData({ ...objData, ...obj });
    setLoading(false);
    return data;
  };

  const onSelect = (keys, info) => {
    // 判定 是否是叶子节点
    if (info.node.isLeaf) {
      const field = allTableField.find((item) => item.id === keys[0]);
      const data = {
        connector: filterConnetor,
        userProperty: {
          connector: filterConnetor,
          filters: [
            {
              connector: filterConnetor,
              filters: [
                {
                  field: field.name,
                  fieldName: field.displayName,
                  fieldType: field.dataType,
                  operator: 'EQ',
                  schemaId: field.id,
                  tableId: field.table.id,
                  value: null
                }
              ]
            }
          ]
        }
      };

      if (flag) {
        const _globalFilters = _.cloneDeep(globalFilters);
        _globalFilters[filterIndex] = data;
        dispatch({ globalFilters: _globalFilters });
      } else {
        const _stepList = _.cloneDeep(stepList);
        if (isMulti) {
          _stepList[index].multiStepList[ind].filters[filterIndex] = data;
        } else if (isDimension) {
          const _dimensionGroup = _.cloneDeep(dimensionGroup);
          _dimensionGroup[dimensionIndex] = {
            type: 'TABLE_FIELD',
            name: field.displayName,
            filterValue: [],
            group: dimensionIndex,
            filters: [
              {
                groupName: null,
                userProperty: {
                  connector: 'AND',
                  filters: [
                    {
                      connector: 'AND',
                      filters: [
                        {
                          field: field.name,
                          fieldName: field.displayName,
                          fieldType: 'DOUBLE',
                          operator: 'EQ',
                          schemaId: field.id,
                          tableId: field.table.id,
                          value: null
                        }
                      ]
                    }
                  ]
                }
              }
            ]
          };
          setFlagVisible(false);
          dispatch({ dimensionGroup: _dimensionGroup });
        } else {
          _stepList[index].filters[filterIndex] = data;
        }
        dispatch({ stepList: _stepList });
      }
      setVisible(false);
    }
  };

  const onLoadData = async ({ key, children }) => {
    if (children) {
      return;
    }
    setLoading(true);
    const id = key.substring(4);
    const search = [
      {
        operator: 'EQ',
        propertyName: 'projectId',
        value: localStorage.getItem('projectId')
      },
      {
        operator: 'EQ',
        propertyName: 'table.id',
        value: parseInt(id)
      }
    ];

    const tableData = await FunnelAnalysis.tableSchemaListBy(search);
    const data = [];
    tableData.forEach((n) => {
      data.push({
        title: (
          <Popover overlayClassName="tableField-popover" trigger="hover" placement="right" content={renderPopover(n)}>
            {n.displayName}
          </Popover>
        ),
        key: n.id,
        isLeaf: true
      });
    });
    const treedata = updateTreeData(treeData, key, data);
    setAllTableField([...allTableField, ...tableData]);
    setTreeData(treedata);
    setLoading(false);
  };

  const renderPopover = (n) => {
    return (
      <div className="renderPopover">
        <div>
          {n.displayName}[{n.name}]
        </div>
        <div>
          {t('analysisCenter-otjQO3L6vXRk')}
          <span>{n.dataType}</span>
        </div>
        <div>
          {t('analysisCenter-QGsD12KaVrSo')}
          <span>{n?.memo ? n?.memo : ''}</span>
        </div>
      </div>
    );
  };

  const updateTreeData = (list, key, children) => {
    return list.map((node) => {
      if (node.key === key) {
        return { ...node, children };
      } else if (node.children) {
        return {
          ...node,
          children: updateTreeData(node.children, key, children)
        };
      }
      return node;
    });
  };

  return (
    <div className="TabPaneTableField">
      <Spin spinning={loading}>
        <DirectoryTree className="fieldDirectoryTree" onSelect={onSelect} loadData={onLoadData} treeData={treeData} />
      </Spin>
    </div>
  );
};
