/*
 * @Author: Wxw
 * @Date: 2022-08-22 14:13:29
 * @LastEditTime: 2023-04-23 18:08:48
 * @LastEditors: 卜广硕 <EMAIL>
 * @Description: 过滤组件每一项
 * @FilePath: \Front\src\pages\home\analysisCenter\funnelAnalysis\filter\filterItem.jsx
 */
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Button, DatePicker, Input, InputNumber, Modal, Popover, Select, Tabs, Tooltip, message } from 'antd';
import dayjs from 'dayjs';
import React, { useContext, useEffect, useState } from 'react';
import { MyIconV2 } from 'utils/myIcon';
import './filterItem.scss';
// import { useDebounce } from 'utils/customhooks';
import _ from 'lodash';
import { t } from 'utils/translation';
import config from '../config';
import { eventAnalysisContext } from '../eventAnalysisContext';
import FilterValue from './FilterValue';
import Tag from './TabPane/Tag/tag';
import Campaign from './TabPane/campaign/campaign';
import Event from './TabPane/event/event';
import TableField from './TabPane/tableField/tableField';
import UserGroup from './TabPane/userGroup/userGroup';
import CampaignFilterValue from './campaignFilterVaule/campaignFIlterValue';

const { Option } = Select;

const relativeTimeObj = {
  0: t('analysisCenter-OJ6DWEnfJocy'),
  1: t('analysisCenter-Kqz1p5S0A9Fs'),
  2: t('analysisCenter-JBieS9i00vga')
};

const labelTimeOptionList = [
  { label: t('analysisCenter-851ezhnfIBhV'), value: 'LATEST' },
  { label: t('analysisCenter-EbvohF81fvTx'), value: 'ABSOLUTE' },
  { label: t('analysisCenter-5BALsCqmOFKH'), value: 'RELATIVE' }
];

/**
 * @description:
 * @param {*} flag 是否是步骤过滤
 * @param {*} index 步骤下标
 * @param {*} ind 过滤的下标
 * @param {*} value 当前步骤过滤条件
 * @param {*} isMulti 是否是多路径
 * @return {*}
 */
export default function FilterItem({ flag, index, ind, value, isMulti, filterIndex, isFilter }) {
  const { connector, typeOperator } = config;
  const [operatorList, setOperatorList] = useState(config.operatorList);
  const { state, dispatch } = useContext(eventAnalysisContext);
  const { stepList, globalFilters } = state;
  const [filterValue, setFilterValue] = useState('');
  const [visible, setVisible] = useState(false);
  const [defaltSelectOperatorList, setDefaltSelectOperatorList] = useState(null);
  const [filterConnetor, setFilterConnetor] = useState(
    flag
      ? globalFilters[0].connector
        ? globalFilters[0].connector
        : 'AND'
      : isMulti
        ? stepList[index].multiStepList[ind].filters[filterIndex]?.connector || 'AND'
        : stepList[index]?.filters[filterIndex]?.connector || 'AND'
  );
  const [fieldValue, setFieldValue] = useState({});
  const [dateType, setDateType] = useState('');
  const [lastCalcTime, setLastCalcTime] = useState();

  const [relativeOpen, setRelativeOpen] = useState(false);
  const [times, setTime] = useState(null);
  const [relativeValue, setRelativeValue] = useState(times);
  // 操作符map
  const OPERATOR_MAP =
    config.operatorList &&
    config.operatorList.reduce((map, obj) => {
      map[obj.operator] = obj;
      return map;
    }, {});

  useEffect(() => {
    setRelativeValue(times);
  }, [times]);

  useEffect(() => {
    setDefaltSelectOperatorList('EQ');
    setOperatorList(config.operatorList);
    if (flag) {
      const _globalFilters = _.cloneDeep(globalFilters);
      const keys = Object.keys(_globalFilters[filterIndex]);
      const eventValue =
        _globalFilters[filterIndex]?.eventGroup?.filters[0]?.filters[0]?.eventFilterProperty?.filters[0]?.filters[0];
      const userLabel = _globalFilters[filterIndex]?.userLabel?.filters[0]?.filters[0];
      const userProperty = _globalFilters[filterIndex]?.userProperty?.filters[0]?.filters[0];
      const eventFilterProperty = _globalFilters[filterIndex]?.eventFilterProperty?.filters[0];

      if (keys.includes('eventGroup')) {
        const { fieldType, operator, value } = eventValue;
        showValue('eventGroup', fieldType, operator, value, _globalFilters);
      } else if (keys.includes('userLabel')) {
        const { fieldType, operator, value } = userLabel;
        showValue('userLabel', fieldType, operator, value, _globalFilters);
      } else if (keys.includes('userProperty')) {
        const { fieldType, operator, value } = userProperty;
        showValue('userProperty', fieldType, operator, value, _globalFilters);
      } else if (keys.includes('eventFilterProperty')) {
        const { fieldType, operator, value } = eventFilterProperty;
        showValue('eventFilterProperty', fieldType, operator, value, _globalFilters);
      } else {
        setDefaltSelectOperatorList('EQ');
        setOperatorList(config.operatorList);
      }
    } else {
      const _stepList = _.cloneDeep(stepList);
      // 遍历键名是否有叫eventGroup的
      const keys = Object.keys(_stepList[index].filters[filterIndex]);
      const eventValue =
        _stepList[index]?.filters[filterIndex]?.eventGroup?.filters[0]?.filters[0]?.eventFilterProperty?.filters[0]
          ?.filters[0];
      const userLabel = _stepList[index]?.filters[filterIndex]?.userLabel?.filters[0]?.filters[0];
      const userProperty = _stepList[index]?.filters[filterIndex]?.userProperty?.filters[0]?.filters[0];
      if (keys.includes('eventGroup')) {
        const { fieldType, operator, value } = eventValue;
        showValue('eventGroup', fieldType, operator, value, _stepList);
      } else if (keys.includes('userLabel')) {
        const { fieldType, operator, value } = userLabel;
        showValue('userLabel', fieldType, operator, value, _stepList);
      } else if (keys.includes('userProperty')) {
        const { fieldType, operator, value } = userProperty;
        showValue('userProperty', fieldType, operator, value, _stepList);
      } else {
        setDefaltSelectOperatorList('EQ');
        setOperatorList(config.operatorList);
      }
    }
  }, [stepList, globalFilters]);

  /**
   * @description: 回显操作符
   * @param {*} type 过滤类型
   * @param {*} fieldType 字段类型
   * @param {*} operator 操作符
   * @param {*} value 值
   * @param {*} _stepList 步骤列表
   * @param {*} isMulti 是否是多步骤
   * @return {*}
   */
  const showValue = (type, fieldType, operator, value, _stepList) => {
    const typeList = typeOperator[fieldType];
    const _operatorList = [];
    typeList.forEach((item) => _operatorList.push(OPERATOR_MAP[item]));
    if (flag) {
      setFieldValue(() => {
        if (type && type === 'eventGroup') {
          return globalFilters[filterIndex][type].filters[0].filters[0].eventFilterProperty.filters[0].filters[0];
        } else if (type && type === 'userLabel') {
          return globalFilters[filterIndex][type].filters[0].filters[0];
        } else if (type && type === 'userProperty') {
          return globalFilters[filterIndex][type].filters[0].filters[0];
        } else if (type && type === 'eventFilterProperty') {
          return globalFilters[filterIndex][type].filters[0];
        }
      });

      setDateType(() => {
        if (type && type === 'userLabel') {
          return globalFilters[filterIndex][type].filters[0].filters[0].dateType;
        }
      });

      setTime(() => {
        if (type && type === 'userLabel') {
          return globalFilters[filterIndex][type].filters[0].filters[0].times;
        }
      });

      setLastCalcTime(() => {
        if (type && type === 'userLabel') {
          if (globalFilters[filterIndex][type].filters[0].filters[0].userLabel) {
            return globalFilters[filterIndex][type].filters[0].filters[0].userLabel.lastCalcTime;
          } else {
            return globalFilters[filterIndex][type].filters[0].filters[0].lastCalcTime;
          }
        }
      });

      setOperatorList(_operatorList);
      setFilterValue(value);
      setDefaltSelectOperatorList(operator || _operatorList[0].operator || 'EQ');
    } else {
      setFieldValue(() => {
        if (type === 'eventGroup') {
          return _stepList[index].filters[filterIndex][type].filters[0].filters[0].eventFilterProperty.filters[0]
            .filters[0];
        } else if (type === 'userLabel') {
          return _stepList[index].filters[filterIndex][type].filters[0].filters[0];
        } else if (type === 'userProperty') {
          return _stepList[index].filters[filterIndex][type].filters[0].filters[0];
        }
      });

      setDateType(() => {
        if (type && type === 'userLabel') {
          return _stepList[index].filters[filterIndex][type].filters[0].filters[0].dateType;
        }
      });

      setTime(() => {
        if (type && type === 'userLabel') {
          return _stepList[index].filters[filterIndex][type].filters[0].filters[0].times;
        }
      });

      setLastCalcTime(() => {
        if (type && type === 'userLabel') {
          if (_stepList[index].filters[filterIndex][type].filters[0].filters[0].userLabel) {
            return _stepList[index].filters[filterIndex][type].filters[0].filters[0].userLabel.lastCalcTime;
          } else {
            return _stepList[index].filters[filterIndex][type].filters[0].filters[0].lastCalcTime;
          }
        }
      });

      setOperatorList(_operatorList);
      setFilterValue(value);
      setDefaltSelectOperatorList(operator || _operatorList[0].operator || 'EQ');
    }
  };

  /**
   * @description: 操作符交互，等于/不等于
   * @param {*} e
   * @return {*}
   */
  const changeOperatorList = (e) => {
    if (flag) {
      const _globalFilters = _.cloneDeep(globalFilters);
      const keys = Object.keys(_globalFilters[filterIndex]);
      if (keys.includes('eventGroup')) {
        const value =
          _globalFilters[filterIndex].eventGroup.filters[0].filters[0].eventFilterProperty.filters[0].filters[0];
        _globalFilters[filterIndex].eventGroup.filters[0].filters[0].eventFilterProperty.filters[0].filters[0] = {
          ...value,
          operator: e,
          value: null
        };
        dispatch({ globalFilters: _globalFilters });
        setDefaltSelectOperatorList(e);
      } else if (keys.includes('userLabel')) {
        const value = _globalFilters[filterIndex].userLabel.filters[0].filters[0];
        _globalFilters[filterIndex].userLabel.filters[0].filters[0] = {
          ...value,
          operator: e,
          value: null
        };
        dispatch({ globalFilters: _globalFilters });
        setDefaltSelectOperatorList(e);
      } else if (keys.includes('userProperty')) {
        const value = _globalFilters[filterIndex].userProperty.filters[0].filters[0];
        _globalFilters[filterIndex].userProperty.filters[0].filters[0] = {
          ...value,
          operator: e,
          value: null
        };
        dispatch({ globalFilters: _globalFilters });
        setDefaltSelectOperatorList(e);
      } else if (keys.includes('eventFilterProperty')) {
        const value = _globalFilters[filterIndex].eventFilterProperty.filters[0];
        _globalFilters[filterIndex].eventFilterProperty.filters[0] = {
          ...value,
          operator: e,
          value: null
        };
        dispatch({ globalFilters: _globalFilters });
        setDefaltSelectOperatorList(e);
      } else {
        setDefaltSelectOperatorList(e);
        setOperatorList(config.operatorList);
      }
    } else {
      const _stepList = _.cloneDeep(stepList);
      if (isMulti) {
        const keys = Object.keys(_stepList[index].multiStepList[ind].filters[filterIndex]);
        if (keys.includes('eventGroup')) {
          const value =
            _stepList[index].multiStepList[ind].filters[filterIndex].eventGroup.filters[0].filters[0]
              .eventFilterProperty.filters[0].filters[0];
          _stepList[index].multiStepList[ind].filters[
            filterIndex
          ].eventGroup.filters[0].filters[0].eventFilterProperty.filters[0].filters[0] = {
            ...value,
            operator: e,
            value: null
          };
          dispatch({ stepList: _stepList });
          setDefaltSelectOperatorList(e);
        } else if (keys.includes('userLabel')) {
          const value = _stepList[index].multiStepList[ind].filters[filterIndex].userLabel.filters[0].filters[0];
          _stepList[index].multiStepList[ind].filters[filterIndex].userLabel.filters[0].filters[0] = {
            ...value,
            operator: e,
            value: null
          };
          dispatch({ stepList: _stepList });
          setDefaltSelectOperatorList(e);
        } else if (keys.includes('userProperty')) {
          const value = _stepList[index].multiStepList[ind].filters[filterIndex].userProperty.filters[0].filters[0];
          _stepList[index].multiStepList[ind].filters[filterIndex].userProperty.filters[0].filters[0] = {
            ...value,
            operator: e,
            value: null
          };
          dispatch({ stepList: _stepList });
          setDefaltSelectOperatorList(e);
        } else {
          setDefaltSelectOperatorList(e);
          setOperatorList(config.operatorList);
        }
      } else {
        // 遍历键名是否有叫eventGroup的
        const keys = Object.keys(_stepList[index].filters[filterIndex]);
        if (keys.includes('eventGroup')) {
          const value =
            _stepList[index].filters[filterIndex].eventGroup.filters[0].filters[0].eventFilterProperty.filters[0]
              .filters[0];
          _stepList[index].filters[
            filterIndex
          ].eventGroup.filters[0].filters[0].eventFilterProperty.filters[0].filters[0] = {
            ...value,
            operator: e,
            value: null
          };
          dispatch({ stepList: _stepList });
          setDefaltSelectOperatorList(e);
        } else if (keys.includes('userLabel')) {
          const value = _stepList[index].filters[filterIndex].userLabel.filters[0].filters[0];
          _stepList[index].filters[filterIndex].userLabel.filters[0].filters[0] = {
            ...value,
            operator: e,
            value: null
          };
          dispatch({ stepList: _stepList });
          setDefaltSelectOperatorList(e);
        } else if (keys.includes('userProperty')) {
          const value = _stepList[index].filters[filterIndex].userProperty.filters[0].filters[0];
          _stepList[index].filters[filterIndex].userProperty.filters[0].filters[0] = {
            ...value,
            operator: e,
            value: null
          };
          dispatch({ stepList: _stepList });
          setDefaltSelectOperatorList(e);
        } else {
          setDefaltSelectOperatorList(e);
          setOperatorList(config.operatorList);
        }
      }
    }
  };

  // 递归遍历每一层操作符
  useEffect(() => {
    const changeConnector = (obj) => {
      if (obj?.connector) {
        obj.connector = filterConnetor;
      }
      for (const key in obj) {
        if (typeof obj[key] === 'object') {
          changeConnector(obj[key]);
        }
      }
    };
    if (flag) {
      // let _globalFilters = _.cloneDeep(globalFilters);
      changeConnector(globalFilters);
      dispatch({ globalFilters });
    } else {
      // let _stepList = _.cloneDeep(stepList);
      changeConnector(stepList[index].filters);
      dispatch({ stepList });
    }
  }, [filterConnetor]);

  const onLabelFilterChange = (val) => {
    if (flag) {
      const _globalFilters = _.cloneDeep(globalFilters);

      const value = _globalFilters[filterIndex].userLabel.filters[0].filters[0];
      _globalFilters[filterIndex].userLabel.filters[0].filters[0] = {
        ...value,
        dateType: val,
        times: val === 'RELATIVE' ? 0 : null
      };
      dispatch({ globalFilters: _globalFilters });
    } else {
      const _stepList = _.cloneDeep(stepList);
      const value = _stepList[index].filters[filterIndex].userLabel.filters[0].filters[0];
      _stepList[index].filters[filterIndex].userLabel.filters[0].filters[0] = {
        ...value,
        dateType: val,
        times: val === 'RELATIVE' ? 0 : null
      };
      dispatch({ stepList: _stepList });
    }
  };

  /**
   * @description:
   * @param {*} type 类型
   * @return {*}
   */
  const renderIcon = () => {
    let type;
    // 遍历键名是否有叫eventGroup的
    const keys = flag
      ? Object.keys(globalFilters[filterIndex])
      : isMulti
        ? Object.keys(stepList[index].multiStepList[ind].filters[filterIndex])
        : Object.keys(stepList[index].filters[filterIndex]);
    if (keys.includes('eventGroup') || keys.includes('eventFilterProperty')) {
      type = 'eventGroup';
    } else if (keys.includes('userLabel')) {
      // 标签
      type = 'userLabel';
    } else if (keys.includes('segment')) {
      // 分群
      type = 'segment';
    } else if (keys.includes('userProperty')) {
      // 表字段
      type = 'userProperty';
    } else if (keys.includes('campaignGroup')) {
      type = 'CAMPAIGN';
    } else {
      type = 'CAMPAIGN';
    }

    if (type === 'eventGroup') {
      return <MyIconV2 type="icon-icon-event" />;
    } else if (type === 'userLabel') {
      return <MyIconV2 type="icon-icon-tag" />;
    } else if (type === 'CAMPAIGN') {
      return <MyIconV2 type="icon-icon-camping" />;
    } else if (type === 'userProperty') {
      return <MyIconV2 type="icon-icon-text" />;
    } else if (type === 'segment') {
      return <MyIconV2 type="icon-icon-users" />;
    }
  };

  const delFilter = () => {
    if (flag) {
      const _globalFilters = _.cloneDeep(globalFilters);
      _globalFilters.splice(filterIndex, 1);
      dispatch({ globalFilters: _globalFilters });
    } else {
      const _stepList = _.cloneDeep(stepList);
      if (isMulti) {
        const filterList = _stepList[index].multiStepList[ind].filters;
        filterList.splice(filterIndex, 1);
        filterList.forEach((item, index) => {
          item.id = index + 1;
        });
        _stepList[index].multiStepList[ind].filters = filterList;
        dispatch({ stepList: _stepList });
      } else {
        const filterList = _stepList[index].filters;
        filterList.splice(filterIndex, 1);
        filterList.forEach((item, index) => {
          item.id = index + 1;
        });
        _stepList[index].filters = filterList;
        dispatch({ stepList: _stepList });
      }
    }
  };

  const RenderFilter = () => {
    const style = {
      marginRight: 0,
      fontSize: 16
    };
    const showTabPane = {
      event: [
        t('analysisCenter-NcyILORFWK67'),
        t('analysisCenter-qwl9fu3wfIq2'),
        t('analysisCenter-FkKg37QMaFKe'),
        t('analysisCenter-YchDLvQihyy1')
      ],
      CUSTOM: [
        t('analysisCenter-NcyILORFWK67'),
        t('analysisCenter-qwl9fu3wfIq2'),
        t('analysisCenter-FkKg37QMaFKe'),
        t('analysisCenter-YchDLvQihyy1')
      ],
      bizTable: [
        t('analysisCenter-xa7uMP9jSPi3'),
        t('analysisCenter-qwl9fu3wfIq2'),
        t('analysisCenter-FkKg37QMaFKe'),
        t('analysisCenter-YchDLvQihyy1')
      ], // 过滤表字段
      campaignList: [t('analysisCenter-qwl9fu3wfIq2'), t('analysisCenter-FkKg37QMaFKe')],
      all: [
        t('analysisCenter-NcyILORFWK67'),
        t('analysisCenter-qwl9fu3wfIq2'),
        t('analysisCenter-FkKg37QMaFKe'),
        t('analysisCenter-xa7uMP9jSPi3'),
        t('analysisCenter-YchDLvQihyy1')
      ],
      BASE: [
        t('analysisCenter-NcyILORFWK67'),
        t('analysisCenter-qwl9fu3wfIq2'),
        t('analysisCenter-FkKg37QMaFKe'),
        t('analysisCenter-YchDLvQihyy1')
      ], // 大写的代表全局过滤
      BIZ_TABLE: [t('analysisCenter-qwl9fu3wfIq2'), t('analysisCenter-FkKg37QMaFKe'), t('analysisCenter-YchDLvQihyy1')],
      CAMPAIGN: [t('analysisCenter-qwl9fu3wfIq2'), t('analysisCenter-FkKg37QMaFKe')]
    };
    if (!flag) showTabPane.all.unshift(t('analysisCenter-xa7uMP9jSPi3'));
    // 在这里处理格式, 判定是否是空数组或者是空对象的给过滤掉
    let typeArr = [];
    let keys;
    let newObj;

    if (flag) {
      stepList.forEach((item) => {
        if (item.multiStep) {
          item.multiStepList.forEach((i) => {
            typeArr.push(i.type);
          });
        } else {
          typeArr.push(item.type);
        }
      });
      typeArr = [...new Set(typeArr)];
    } else {
      const obj = flag ? stepList[index] : isMulti ? stepList[index].multiStepList[ind] : stepList[index];
      newObj = Object.keys(obj).reduce((acc, cur) => {
        if (obj[cur] && obj[cur].length !== 0) {
          acc[cur] = obj[cur];
        }
        return acc;
      }, {});
    }

    let showType = null;
    let defaultActiveKey = null;
    if (flag) {
      // CAMPAIGN,'EVENT', 'BIZ_TABLE'
      // 取typeArr里面 交集的key
      const type = [];
      typeArr.forEach((item) => {
        showTabPane[item].forEach((i) => {
          type.push(i);
        });
      });
      // 找到type里面出现了typeArr.length次的key
      const obj = {};
      type.forEach((item) => {
        if (obj[item]) {
          obj[item]++;
        } else {
          obj[item] = 1;
        }
      });
      const arr = [];
      for (const key in obj) {
        if (obj[key] === typeArr.length) {
          arr.push(key);
        }
      }
      showType = arr;
    } else {
      keys = Object.keys(newObj);
      if (keys.includes('event')) {
        showType = 'event';
      } else if (keys.includes('bizTable')) {
        showType = 'bizTable';
      } else if (keys.includes('campaignList')) {
        showType = 'campaignList';
      } else if (newObj.type === 'CUSTOM') {
        showType = 'custom';
      } else {
        showType = 'all';
      }
    }

    const defaultTypeKeys = flag ? Object.keys(globalFilters[filterIndex]) : Object.keys(newObj.filters[filterIndex]);
    if (defaultTypeKeys.includes('userProperty')) {
      defaultActiveKey = t('analysisCenter-xa7uMP9jSPi3');
    } else if (defaultTypeKeys.includes('userLabel')) {
      defaultActiveKey = t('analysisCenter-qwl9fu3wfIq2');
    } else if (defaultTypeKeys.includes('segment')) {
      defaultActiveKey = t('analysisCenter-FkKg37QMaFKe');
    } else if (defaultTypeKeys.includes('campaignGroup')) {
      defaultActiveKey = t('analysisCenter-YchDLvQihyy1');
    } else if (defaultTypeKeys.includes('eventFilterProperty')) {
      defaultActiveKey = t('analysisCenter-NcyILORFWK67');
    }

    return (
      <div>
        <Tabs
          defaultActiveKey={defaultActiveKey}
          items={(flag ? showType : showTabPane[showType]).map((item) => {
            if (item === t('analysisCenter-NcyILORFWK67')) {
              return {
                label: (
                  <span>
                    {' '}
                    <MyIconV2 type="icon-icon-event" style={style} /> {t('analysisCenter-NcyILORFWK67')}
                  </span>
                ),
                key: t('analysisCenter-NcyILORFWK67'),
                children: <Event setFlagVisible={setVisible} />
              };
            } else if (item === t('analysisCenter-qwl9fu3wfIq2')) {
              return {
                label: (
                  <span>
                    <MyIconV2 type="icon-icon-tag" style={style} />
                    <span style={{ marginRight: 5 }}> {t('analysisCenter-qwl9fu3wfIq2')}</span>
                    <Tooltip
                      overlayClassName="activity-board-remark-tooltip"
                      placement="top"
                      title={t('analysisCenter-zYlTa6KMsR1W')}
                    >
                      <QuestionCircleOutlined className="remarkTooltip-icon" />
                    </Tooltip>
                  </span>
                ),
                key: t('analysisCenter-qwl9fu3wfIq2'),
                children: <Tag />
              };
            } else if (item === t('analysisCenter-FkKg37QMaFKe')) {
              return {
                label: (
                  <span>
                    <MyIconV2 type="icon-icon-users" style={style} />
                    <span style={{ marginRight: 5 }}> {t('analysisCenter-FkKg37QMaFKe')}</span>
                    <Tooltip
                      overlayClassName="activity-board-remark-tooltip"
                      placement="top"
                      title={t('analysisCenter-f4jeAiLerY8V')}
                    >
                      <QuestionCircleOutlined className="remarkTooltip-icon" />
                    </Tooltip>
                  </span>
                ),
                key: t('analysisCenter-FkKg37QMaFKe'),
                children: <UserGroup />
              };
            } else if (item === t('analysisCenter-xa7uMP9jSPi3')) {
              return {
                label: (
                  <span>
                    {' '}
                    <MyIconV2 type="icon-icon-text" style={style} /> {t('analysisCenter-xa7uMP9jSPi3')}
                  </span>
                ),
                key: t('analysisCenter-xa7uMP9jSPi3'),
                children: <TableField />
              };
            } else if (item === t('analysisCenter-YchDLvQihyy1')) {
              return {
                label: (
                  <span>
                    {' '}
                    <MyIconV2 type="icon-icon-camping" style={style} /> {t('analysisCenter-YchDLvQihyy1')}
                  </span>
                ),
                key: t('analysisCenter-YchDLvQihyy1'),
                children: <Campaign />
              };
            }
            return null;
          })}
        />
      </div>
    );
  };

  /**
   * @description: 渲染过滤名称
   * @return {*}
   */
  const RenderTitle = () => {
    let title = '';
    const filterTypeName = {
      // CAMPAIGN: '按营销活动',
      CAMPAIGN_BATCH: t('analysisCenter-KpWrkAiXtxS7'),
      CAMPAIGN_NODE: t('analysisCenter-P8AxeWWZEo3n')
    };
    if (flag) {
      // 全局
      const _globalFilters = globalFilters;
      const keys = Object.keys(_globalFilters[filterIndex]);
      if (keys.includes('eventGroup')) {
        title =
          _globalFilters[filterIndex].eventGroup.filters[0].filters[0].eventFilterProperty.filters[0].filters[0]
            .fieldName;
      } else if (keys.includes('userLabel')) {
        title = _globalFilters[filterIndex].userLabel.filters[0].filters[0].displayName;
      } else if (keys.includes('segment')) {
        title = _globalFilters[filterIndex].segment.filters[0].filters[0].segment.name;
      } else if (keys.includes('userProperty')) {
        title = _globalFilters[filterIndex].userProperty.filters[0].filters[0].fieldName;
      } else if (keys.includes('campaignGroup')) {
        title = filterTypeName[_globalFilters[filterIndex].campaignGroup.filterType];
      } else if (keys.includes('eventFilterProperty')) {
        title = _globalFilters[filterIndex].eventFilterProperty.filters[0].fieldName;
      }
    } else {
      const _stepList = _.cloneDeep(stepList);
      if (isMulti) {
        // 多步骤
        const keys = Object.keys(_stepList[index].multiStepList[ind].filters[filterIndex]);
        if (keys.includes('eventGroup')) {
          const eventValue =
            _stepList[index].multiStepList[ind].filters[filterIndex].eventGroup.filters[0].filters[0]
              .eventFilterProperty.filters[0].filters[0];
          title = eventValue.fieldName;
        } else if (keys.includes('userLabel')) {
          title = _stepList[index].multiStepList[ind].filters[filterIndex].userLabel.filters[0].filters[0].displayName;
        } else if (keys.includes('segment')) {
          title = _stepList[index].multiStepList[ind].filters[filterIndex].segment.filters[0].filters[0].segment.name;
        } else if (keys.includes('userProperty')) {
          title = _stepList[index].multiStepList[ind].filters[filterIndex].userProperty.filters[0].filters[0].fieldName;
        } else if (keys.includes('campaignGroup')) {
          title = filterTypeName[_stepList[index].multiStepList[ind].filters[filterIndex].campaignGroup.filterType];
        }
      } else {
        // 遍历键名是否有叫eventGroup的
        const keys = Object.keys(_stepList[index].filters[filterIndex]);
        if (keys.includes('eventGroup')) {
          const eventValue =
            _stepList[index].filters[filterIndex].eventGroup.filters[0].filters[0].eventFilterProperty.filters[0]
              .filters[0];
          title = eventValue.fieldName;
        } else if (keys.includes('userLabel')) {
          title = _stepList[index].filters[filterIndex].userLabel.filters[0].filters[0].displayName;
        } else if (keys.includes('segment')) {
          title = _stepList[index].filters[filterIndex].segment.filters[0].filters[0].segment.name;
        } else if (keys.includes('userProperty')) {
          title = _stepList[index].filters[filterIndex].userProperty.filters[0].filters[0].fieldName;
        } else if (keys.includes('campaignGroup')) {
          title = filterTypeName[_stepList[index].filters[filterIndex].campaignGroup.filterType];
        }
      }
    }

    return (
      <div className="filter-title" style={{ opacity: title === '' ? 0.25 : 1 }}>
        {title === '' ? t('analysisCenter-qlSkSwNfOxIu') : title}
      </div>
    );
  };

  // const changeFilterValue = (e) => {
  //   console.log(e);
  //   let _stepList = _.cloneDeep(stepList);
  //   if (isMulti) {
  //     console.log('多路径操作符回显');
  //   } else {
  //     // 遍历键名是否有叫eventGroup的
  //     let keys = Object.keys(_stepList[index].filters[filterIndex]);
  //     if (keys.includes('eventGroup')) {
  //       _stepList[index].filters[filterIndex].eventGroup.filters[0].filters[0].eventFilterProperty.filters[0].filters[0].value = e;
  //       dispatch({ stepList: _stepList });
  //       setFilterValue(e);
  //     } else {
  //       setFilterValue(e);
  //     }
  //   }
  // };
  /* 触发防抖函数 */
  const changeFilterValue = (e) => {
    setFilterValue(e);
    uploadFilterValue(flag, isMulti, index, ind, e);
  };

  /**
   * @description: 过滤值回显
   * @param {*} flag 是否是步骤过滤
   * @param {*} index 步骤下标
   * @param {*} ind 过滤的下标
   * @param {*} value 过滤值
   * @param {*} isMulti 是否是多路径
   * @return {*}
   */
  const uploadFilterValue = (flag, isMulti, index, ind, e) => {
    // 判定是否是正在输入,如果正在输入则不执行
    if (_.isString(e) && e.includes("'")) return;
    if (flag) {
      const _globalFilters = _.cloneDeep(globalFilters);
      const keys = Object.keys(_globalFilters[filterIndex]);
      if (keys.includes('eventGroup')) {
        _globalFilters[filterIndex].eventGroup.filters[0].filters[0].eventFilterProperty.filters[0].filters[0].value =
          e;
        dispatch({ globalFilters: _globalFilters });
        setFilterValue(e);
      } else if (keys.includes('userLabel')) {
        _globalFilters[filterIndex].userLabel.filters[0].filters[0].value = e;
        dispatch({ globalFilters: _globalFilters });
        setFilterValue(e);
      } else if (keys.includes('userProperty')) {
        _globalFilters[filterIndex].userProperty.filters[0].filters[0].value = e;
        dispatch({ globalFilters: _globalFilters });
        setFilterValue(e);
      } else if (keys.includes('eventFilterProperty')) {
        _globalFilters[filterIndex].eventFilterProperty.filters[0].value = e;
        dispatch({ globalFilters: _globalFilters });
        setFilterValue(e);
      } else {
        setFilterValue(e);
      }
    } else {
      const _stepList = _.cloneDeep(stepList);
      // 遍历键名是否有叫eventGroup的
      const keys = Object.keys(_stepList[index].filters[filterIndex]);
      if (keys.includes('eventGroup')) {
        _stepList[index].filters[
          filterIndex
        ].eventGroup.filters[0].filters[0].eventFilterProperty.filters[0].filters[0].value = e;
        dispatch({ stepList: _stepList });
        setFilterValue(e);
      } else if (keys.includes('userLabel')) {
        _stepList[index].filters[filterIndex].userLabel.filters[0].filters[0].value = e;
        dispatch({ stepList: _stepList });
        setFilterValue(e);
      } else if (keys.includes('userProperty')) {
        _stepList[index].filters[filterIndex].userProperty.filters[0].filters[0].value = e;
        dispatch({ stepList: _stepList });
        setFilterValue(e);
      } else {
        setFilterValue(e);
      }
    }
  };

  /**
   * @description: 渲染且/或
   * @return {*}
   */
  const renderConnector = () => {
    if (filterIndex === 0 && value.length !== 1) {
      return (
        <div className="connector" style={{ paddingLeft: '5px' }}>
          <Select
            className="condition"
            bordered={false}
            showArrow={false}
            onChange={setFilterConnetor}
            value={filterConnetor}
            dropdownMatchSelectWidth={false}
            // dropdownStyle={{
            //   width: '50px'
            // }}
          >
            {connector.map((item, index) => {
              return (
                <Option key={index} value={item.value}>
                  {item.name}
                </Option>
              );
            })}
          </Select>
        </div>
      );
    }
  };

  const renderFilterConfig = () => {
    let filterKey;
    if (flag) {
      const _globalFilters = _.cloneDeep(globalFilters);
      const keys = Object.keys(_globalFilters[filterIndex]);
      filterKey = keys;
      if (keys.includes('segment')) {
        return null;
      } else if (keys.includes('campaignGroup')) {
        return <CampaignFilterValue isMulti={isMulti} index={index} ind={ind} filterIndex={filterIndex} />;
      }
    } else {
      const _stepList = _.cloneDeep(stepList);
      const keys = Object.keys(_stepList[index].filters[filterIndex]);
      filterKey = keys;
      if (keys.includes('segment')) {
        return null;
      } else if (keys.includes('campaignGroup')) {
        return <CampaignFilterValue isMulti={isMulti} index={index} ind={ind} filterIndex={filterIndex} />;
      }
    }

    return (
      <div className="flex flex-col w-[100%]">
        <div className="flex items-center">
          <div className="operatorSelect">
            <Select
              bordered={false}
              showArrow={false}
              value={defaltSelectOperatorList}
              dropdownMatchSelectWidth={false}
              onChange={changeOperatorList}
            >
              {operatorList.map((item, index) => {
                return (
                  <Option key={index} value={item.operator}>
                    {item.name}
                  </Option>
                );
              })}
            </Select>
          </div>

          <div className="filerValue" style={{ width: '90%' }}>
            {!_.isEmpty(fieldValue) && <FilterValue value={fieldValue} onChange={changeFilterValue} />}
          </div>
        </div>

        {filterKey.includes('userLabel') ? (
          <div className="pl-[5px]">
            <div className="flex mb-[10px] mt-[5px] items-center">
              <div className="mr-[8px]">{t('analysisCenter-OQWY6nHbjYtf')}</div>
              <div className="labelTimeWrap">
                <Select
                  value={dateType}
                  onChange={onLabelFilterChange}
                  bordered={false}
                  suffixIcon={null}
                  className="w-[85px] labelTimeSelect"
                >
                  {lastCalcTime
                    ? labelTimeOptionList.map((item) => (
                        <Option key={item.value} value={item.value}>
                          {item.label}
                        </Option>
                      ))
                    : labelTimeOptionList
                        .filter((filterItem) => filterItem.value !== 'LATEST')
                        .map((item) => (
                          <Option key={item.value} value={item.value}>
                            {item.label}
                          </Option>
                        ))}
                </Select>
              </div>
              <div className="flex-1">{dateType === 'LATEST' ? null : labelValueRender()}</div>
            </div>
            <div className="flex">
              <div className="mr-[8px] text-[rgba(0,0,0,.45)]">{t('analysisCenter-U5Z81CJfg1jd')}</div>
              <div className="mr-[8px] text-[rgba(0,0,0,.45)]">
                {lastCalcTime ? dayjs(lastCalcTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
              </div>
            </div>
          </div>
        ) : null}
      </div>
    );
  };

  const onDatePickerChange = (e) => {
    const val = e ? dayjs(e).startOf('days').valueOf() : null;
    if (flag) {
      const _globalFilters = _.cloneDeep(globalFilters);

      const value = _globalFilters[filterIndex].userLabel.filters[0].filters[0];
      _globalFilters[filterIndex].userLabel.filters[0].filters[0] = {
        ...value,
        times: val
      };
      dispatch({ globalFilters: _globalFilters });
    } else {
      const _stepList = _.cloneDeep(stepList);
      const value = _stepList[index].filters[filterIndex].userLabel.filters[0].filters[0];
      _stepList[index].filters[filterIndex].userLabel.filters[0].filters[0] = {
        ...value,
        times: val
      };
      dispatch({ stepList: _stepList });
    }
  };

  const handleOk = () => {
    if (relativeValue === undefined || relativeValue === null) {
      message.error(t('analysisCenter-owjnMhAKbYOz'));
      return;
    }
    // setContent(relativeTimeObj[relativeValue] || `${relativeValue} 天前`);;
    if (flag) {
      const _globalFilters = _.cloneDeep(globalFilters);

      const value = _globalFilters[filterIndex].userLabel.filters[0].filters[0];
      _globalFilters[filterIndex].userLabel.filters[0].filters[0] = {
        ...value,
        times: relativeValue
      };
      dispatch({ globalFilters: _globalFilters });
    } else {
      const _stepList = _.cloneDeep(stepList);
      const value = _stepList[index].filters[filterIndex].userLabel.filters[0].filters[0];
      _stepList[index].filters[filterIndex].userLabel.filters[0].filters[0] = {
        ...value,
        times: relativeValue
      };
      dispatch({ stepList: _stepList });
    }
    setRelativeOpen(false);
  };

  const labelValueRender = () => {
    if (dateType === 'ABSOLUTE') {
      return (
        <div className="datePickerWrap">
          <DatePicker
            onChange={onDatePickerChange}
            value={times ? dayjs(times) : null}
            bordered={false}
            className="w-[100%]"
          />
        </div>
      );
    } else if (dateType === 'RELATIVE') {
      return (
        <div className="relativeTimeWrap">
          <Input
            value={times === null || times === undefined ? '' : relativeTimeObj[times] || `${times} 天前`}
            placeholder={t('analysisCenter-owjnMhAKbYOz')}
            readOnly
            className="w-[120px]"
            bordered={false}
            onClick={() => setRelativeOpen(true)}
          />
          <Modal
            title={t('analysisCenter-e95vNnkVSXAF')}
            open={relativeOpen}
            onOk={handleOk}
            okText={t('analysisCenter-7dAhp9nja9lO')}
            cancelText={t('analysisCenter-bWLU14enPpM5')}
            onCancel={() => setRelativeOpen(false)}
          >
            <div style={{ marginBottom: 20 }}>
              {Object.entries(relativeTimeObj).map((n) => (
                <Button
                  onClick={() => setRelativeValue(parseInt(n[0]))}
                  type={parseInt(n[0]) === relativeValue ? 'primary' : 'default'}
                  style={{ marginRight: 10 }}
                  key={n[0]}
                >
                  {n[1]}
                </Button>
              ))}
            </div>
            <div>
              {t('analysisCenter-qpgFLG3FGR4M')}{' '}
              <InputNumber min={0} precision={0} value={relativeValue} onChange={setRelativeValue} />
            </div>
          </Modal>
        </div>
      );
    }
  };

  const renderContent = () => {
    if (!flag) {
      const value = isMulti ? stepList[index].multiStepList[ind] : stepList[index];
      if (!(!_.isEmpty(value?.campaignList) || !_.isEmpty(value?.event) || !_.isEmpty(value?.bizTable))) {
        return <div className="stepTitle disableTitle">{RenderTitle()}</div>;
      }
    }
    return (
      <Popover
        getPopupContainer={() => document.getElementsByClassName('content')[0]}
        // content={RenderSetStep(item, index)}
        content={RenderFilter(flag, index, ind, value)}
        trigger="click"
        destroyTooltipOnHide
        overlayStyle={{ minWidth: '320px' }}
        autoAdjustOverflow
        open={visible}
        onOpenChange={setVisible}
        placement="bottomRight"
        className="conversion"
      >
        <div className="stepTitle">{RenderTitle()}</div>
      </Popover>
    );
  };

  // const renderCustomContent = () => {
  //   return (
  //     <Popover
  //       getPopupContainer={() => document.getElementsByClassName('content')[0]}
  //       // content={RenderSetStep(item, index)}
  //       content={RenderFilter(flag, index, ind, value)}
  //       trigger="click"
  //       destroyTooltipOnHide
  //       overlayStyle={{ minWidth: '320px' }}
  //       autoAdjustOverflow
  //       open={visible}
  //       onOpenChange={setVisible}
  //       placement="bottomRight"
  //       className="conversion"
  //     >
  //       <div className="stepTitle">{RenderTitle()}</div>
  //     </Popover>
  //   );
  // };

  return (
    <eventAnalysisContext.Provider
      value={{
        state,
        dispatch,
        setVisible,
        flag,
        index,
        ind,
        value,
        isMulti,
        filterIndex,
        filterConnetor,
        defaltSelectOperatorList,
        filterValue,
        isFilter
      }}
    >
      <div className="filterItem">
        <div className="filterContent">
          <div className="mouse">{renderIcon()}</div>
          <div className="stepContent">{renderContent()}</div>
          <div className="icons">
            <div className="stepIcon">
              <span onClick={delFilter}>
                <MyIconV2 type="icon-icon-close" />
              </span>
            </div>
          </div>
        </div>
        <div className="filterConfig">{renderFilterConfig()}</div>
        {renderConnector()}
      </div>
    </eventAnalysisContext.Provider>
  );
}
