.filterItem {
  padding: 4px;

  //且或
  .ant-select {
    .ant-select-selector {
      padding: 0 4px;
      display: flex;
      align-items: center;
      // line-height: 22px;

      // &>.ant-select-selection-item {
      //   line-height: 22px;
      // }
    }
  }

  .labelTimeWrap,
  .datePickerWrap,
  .relativeTimeWrap,
  .operatorSelect {
    border-radius: 6px;
  }

  .datePickerWrap:hover {
    background: $active_color;
  }

  .relativeTimeWrap:hover {
    background: $active_color;
  }

  .labelTimeWrap:hover {
    background: $active_color;
  }

  .operatorSelect:hover {
    background: $active_color;
  }

  .connector {
    .ant-select-selector {
      font-size: 12px;
      height: 25px;
      display: flex;
      align-items: center;
    }

    .condition {
      position: relative;
      height: 22px !important;
      line-height: 22px !important;

      .ant-select-selection-item {
        position: relative;
        top: -1px;
      }
    }
  }

  .filterContent {
    height: 36px;
    display: flex;
    margin-bottom: 6px;

    .mouse {
      margin: 6px 8px;
      font-size: 16px;
    }

    .stepContent {
      width: calc(100% - 60px);
      // min-width: 46%;
      // margin: -8px 0;
      align-items: center;


      &:hover {
        background-color: $active_color;
        border-radius: 6px;
      }

      .stepTitle {
        width: 100%;
        line-height: 36px;
        padding-left: 8px;
        color: rgba(0, 0, 0, 0.85);
        //定一个宽度,不换行 超出自动省略
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
      }

      .disableTitle {
        cursor: not-allowed !important;
      }
    }

    .icons {
      display: flex;
      margin-left: auto;

      .stepIcon {
        font-size: 16px;
        line-height: 36px;
        display: none;
        color: rgba(0, 0, 0, 0.45);

        span {
          margin-right: 4px;
          cursor: pointer;
        }
      }

      .more {
        padding-top: 2px;
        cursor: pointer;
      }
    }

    &:hover {
      .stepIcon {
        display: flex;
      }
    }
  }

  .filterConfig {
    cursor: pointer;
    display: flex;
    // height: 40px;
    // line-height: 40px;
    margin-bottom: 8px;


    .filerValue {

      .ant-input {
        border: none;
        border-radius: 6px;

        &:hover {
          background-color: $active_color;
        }
      }

      //时间选择器
      .ant-picker {
        border: none;
        width: 100%;
      }

      //下拉框
      .ant-select-selector {
        border: none;
      }

      //数字输入框
      .ant-input-number {
        border: none;
      }

      //group
      .ant-input-group {
        &>input {
          border: none;
        }
      }
    }

  }

  .condition {
    background: $primary_color;
    color: #fff;
    border-radius: 4px;
  }

  .ant-select-selection-overflow {
    flex-wrap: nowrap;

    .ant-select-selection-overflow-item {
      flex: none
    }
  }

  .ant-select-selection-search,
  .ant-select-selection-search-input {
    width: 90% !important;
    // padding-right: 30px;
  }
}