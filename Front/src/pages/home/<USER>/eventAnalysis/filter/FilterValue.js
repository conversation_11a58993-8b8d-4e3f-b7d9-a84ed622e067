import { AutoComplete, DatePicker, Input, InputNumber, Select, message } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useContext, useEffect, useRef, useState } from 'react';
import FunnelAnalysis from 'service/funnelAnalysis';
import TagAndTagValueService from 'service/tagAndTagValueService';
import { SelectTime } from 'wolf-static-cpnt';
import useDebounce from './useDebounce';
// import SelectTime from '../selectTime/index';
import { t } from 'utils/translation';

const { Option } = AutoComplete;
const { RangePicker } = DatePicker;

const FilterValueContext = React.createContext();

/**
 * 输入一个值的情况
 */
function TextInput(props) {
  let { fieldShowValue, items, onChange, value, fieldValue } = props;
  const { setMenuVisible } = useContext(FilterValueContext);
  const inputEl = useRef(null);

  if (_.isArray(items)) {
    items.filter((n) => !!n.value);
  } else {
    items = [];
  }

  const itemsDs = items
    .filter((n) => !!n.value)
    .map((v) => ({
      value: v?.priorityShow === 2 ? (v.displayValue ? v.valueAndDisplayValue : v.value) : v.value,
      label: v?.priorityShow === 2 ? (v.displayValue ? v.valueAndDisplayValue : v.value) : v.value
    }));

  return (
    <AutoComplete
      // dataSource={itemsDs}
      placeholder={t('analysisCenter-CX95MLuTCx2L')}
      style={{
        width: '100%'
      }}
      maxLength={60}
      options={itemsDs}
      showSearch={items.length > 0}
      onChange={onChange}
      value={value.label ? fieldShowValue : fieldValue}
      filterOption={(inputValue, option) =>
        option.value.toUpperCase().indexOf(typeof inputValue === 'string' ? inputValue.toUpperCase() : '') !== -1
      }
      allowClear
      // optionLabelProp="label"
      ref={inputEl}
      onDropdownVisibleChange={(v) => setMenuVisible(v)}
    />
  );
  // return 'OneInput';
}

function DateInput(props) {
  const { fieldType, onChange } = props;

  const showTime =
    fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP'
      ? { format: 'HH:mm:ss' }
      : null;
  const format =
    fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP'
      ? 'YYYY-MM-DD HH:mm:ss'
      : 'YYYY-MM-DD';
  const unit =
    fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP' ? 'second' : 'day';

  const onValueChange = (m) => {
    onChange(m.startOf(unit).valueOf());
  };

  return (
    <DatePicker
      allowClear={false}
      placeholder={t('analysisCenter-3PKwzWQtaYDO')}
      showTime={showTime}
      format={format}
      // allowClear
      value={props.fieldValue && dayjs(props.fieldValue)}
      // getCalendarContainer={triggerNode => triggerNode.parentNode}
      onChange={onValueChange}
    />
  );
}

/**
 * 单一输入的输入框，要根据fieldType决定显示那种输入框，可能是TextInput, 也可能是DateInput
 * @param {FilterModel} value
 * @param {function} onChange
 */
function SingleInput(props) {
  const { fieldType } = props;

  if (
    fieldType === 'DATE' ||
    fieldType === 'DATETIME' ||
    fieldType === 'TIMESTAMP' ||
    fieldType === 'HIVE_DATE' ||
    fieldType === 'HIVE_TIMESTAMP'
  ) {
    return DateInput(props);
  }

  return TextInput(props);
}

const longTodayjs = (fv) => {
  return _.isArray(fv) ? fv.map((v) => dayjs(v)) : undefined;
};

/**
 * 范围日历输入框
 */
function DateBetweenInput(props) {
  const { fieldValue, fieldType, onChange } = props;
  const [value, setValue] = useState(longTodayjs(fieldValue));
  const showTime = !!(fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP');
  const format =
    fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP'
      ? 'YYYY-MM-DD HH:mm:ss'
      : 'YYYY-MM-DD';
  const unit =
    fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP' ? 'second' : 'day';
  const onValueChange = (m) => {
    setValue(m);
    onChange(m && m[0] && m[1] && [m[0].startOf(unit).valueOf(), m[1].startOf(unit).valueOf()]);
  };

  useEffect(() => {
    setValue(longTodayjs(fieldValue));
  }, [fieldValue]);

  // const onValueOk = m => {
  //   onChange(m && m[0] && m[1] && [m[0].valueOf(), m[1].valueOf()]);
  // };
  return (
    <RangePicker
      allowClear={false}
      showTime={showTime}
      format={format}
      placeholder={[t('analysisCenter-DFcBD19Ez8rp'), t('analysisCenter-xYFw6unMHAAI')]}
      // onOk={onValueOk}
      onChange={onValueChange}
      value={value}
    />
  );
}

function NumberBetweenInput(props) {
  let { onChange, fieldValue } = props;
  const inputEl = useRef(null);

  if (!_.isArray(fieldValue)) {
    fieldValue = [];
  }
  return (
    <>
      <InputNumber
        style={{ width: '40%', marginRight: '5px' }}
        placeholder={t('analysisCenter-D38FerRj1cvH')}
        value={fieldValue[0]}
        ref={inputEl}
        onChange={(value) => !isNaN(value) && onChange([value, fieldValue[1]])}
      />
      {t('analysisCenter-SqDIdfG2sqJV')}
      <InputNumber
        style={{ width: '40%', marginLeft: '5px' }}
        placeholder={t('analysisCenter-rWhC6fyfQRIY')}
        value={fieldValue[1]}
        onChange={(value) => !isNaN(value) && onChange([fieldValue[0], value])}
      />
    </>
  );
}

function TwoInput(props) {
  const { fieldType, operator, onChange, fieldValue, items } = props;
  if (
    fieldType === 'DATE' ||
    fieldType === 'DATETIME' ||
    fieldType === 'TIMESTAMP' ||
    fieldType === 'HIVE_DATE' ||
    fieldType === 'HIVE_TIMESTAMP'
  ) {
    return (
      <DateBetweenInput
        fieldType={fieldType}
        operator={operator}
        fieldValue={fieldValue}
        items={items}
        onChange={onChange}
      />
    );
  }
  return (
    <NumberBetweenInput
      fieldType={fieldType}
      operator={operator}
      fieldValue={fieldValue}
      items={items}
      onChange={onChange}
    />
  );
}

function AdvancedBetween(props) {
  const { onChange, fieldValue } = props;
  return <SelectTime showTime data={fieldValue} onChange={onChange} />;
}

/**
 * 枚举类型的输入框
 * @param {object} props
 */
function EnumInput(props) {
  let { onChange, fieldShowValue, items, fieldValue, value } = props;
  const inputEl = useRef(null);
  const { setMenuVisible } = useContext(FilterValueContext);

  if (!_.isArray(fieldShowValue)) {
    fieldShowValue = [];
  }
  const changeSelect = (value) => {
    if (!_.isEmpty(value) && value[value.length - 1].length > 60) {
      return message.error(t('analysisCenter-yo0vQsgLwNAU'));
    } else {
      onChange(value);
    }
  };
  return (
    <Select
      mode="tags"
      placeholder={t('analysisCenter-Uho0U0QHmmBc')}
      onChange={changeSelect}
      allowClear
      maxTagCount={4}
      maxTagTextLength={20}
      value={value.label ? fieldShowValue : fieldValue}
      ref={inputEl}
      optionLabelProp="label"
      onDropdownVisibleChange={(v) => setMenuVisible(v)}
    >
      {items
        .filter((n) => !!n.value)
        .map((item, i) => (
          <Option
            value={item?.priorityShow === 2 && item.displayValue ? item.valueAndDisplayValue : item.value}
            label={item?.priorityShow === 2 && item.displayValue ? item.valueAndDisplayValue : item.value}
            key={i.id}
          >
            {item?.priorityShow === 2 && item.displayValue ? item.valueAndDisplayValue : item.value ? item.value : item}
          </Option>
        ))}
    </Select>
  );
}

const dataProvider = {
  getTagValuesById: async (id) => TagAndTagValueService.getTagValuesById({ labelId: id }),
  getEventCountLogsByProjectId: async () =>
    FunnelAnalysis.getEventCountLogsByProjectId({
      projectId: localStorage.getItem('projectId')
    })
};

/**
 * *****************************************
 * 主组件
 * *****************************************
 */
export default function FilterValue({ value, onChange }) {
  // const { logProvider, dataProvider } = useContext(FilterContext);
  const [items, setItems] = useState([]);
  // const [log] = useState(logProvider.getLogger('FilterValue'));
  const [menuVisible, setMenuVisible] = useState(false);
  const [filterValueContext] = useState({
    setMenuVisible
  });

  const { userLabel, field, fieldType, operator, tableId, schemaId } = value;
  const [fieldValue, setFieldValue] = useState(value.value);
  const [fieldShowValue, setFieldShowValue] = useState(value.showValue);
  const debounceFieldValue = useDebounce(fieldValue, 500);

  useEffect(() => {
    const fetchItem = async () => {
      setItems([]);
      // if (!isEnum) {
      //   return;
      // }
      const _items = await dataProvider.getTagValuesById(value?.id || userLabel?.id);
      // log.debug('fetchItem', _items);
      setItems(_items);
    };
    if (menuVisible) {
      fetchItem();
    }
  }, [field, tableId, schemaId, menuVisible]);
  // debounceFieldValue

  useEffect(() => {
    setFieldValue(value.value);
    setFieldShowValue(value.showValue);
  }, [JSON.stringify(value.value)]);

  useEffect(() => {
    // log.debug('debounceFieldValue changed call onChange', debounceFieldValue);
    if (debounceFieldValue === null || debounceFieldValue === undefined) return;
    onChange(debounceFieldValue);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceFieldValue]);

  const onChangeFieldValue = (v) => {
    // log.debug('onChangeFieldValue', v, JSON.stringify(value));
    value.value = v;
    value.showValue = v;

    // // 如果value是个枚举类型的属性，并且枚举是keyvalue形式的，需要向filterModel赋值一个showValue，方便用于回显
    // if (value.isEnum && _.isArray(items) && items.length > 0 && items[0].name) {
    //   const itemValueMap = items.reduce((a, b) => {
    //     a[b.value] = b.name;
    //     return a;
    //   }, {});
    //   if (_.isArray(v)) {
    //     value.showValue = v.map((_v) => itemValueMap[_v] || _v);
    //   } else {
    //     value.showValue = itemValueMap[v] || v;
    //   }
    // }
    setFieldValue(v);
    setFieldShowValue(value.showValue);
  };

  /**
   * 主方法
   * 返回渲染组件
   */
  const filterValue = () => {
    switch (operator) {
      case 'EQ':
      case 'NE':
      case 'GT':
      case 'GTE':
      case 'LT':
      case 'LTE':
      case 'LIKE':
      case 'NOT_LIKE':
      case 'START_WITH':
      case 'NOT_START_WITH':
      case 'END_WITH':
      case 'NOT_END_WITH':
        return (
          <SingleInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            value={value}
            fieldShowValue={fieldShowValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'BETWEEN':
        return (
          <TwoInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'ADVANCED_BETWEEN':
        return (
          <AdvancedBetween
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'IN':
      case 'NOT_IN':
        return (
          <EnumInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            value={value}
            fieldShowValue={fieldShowValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'IS_NOT_NULL':
      case 'IS_NULL':
      case 'IS_TRUE':
      case 'IS_FALSE':
        return <span />;
      default:
        return <Input placeholder={t('analysisCenter-Djb0BKnYmxue')} disabled={!operator} />;
    }
  };

  return (
    <FilterValueContext.Provider value={filterValueContext}>
      <div>{filterValue()}</div>
    </FilterValueContext.Provider>
  );
}
