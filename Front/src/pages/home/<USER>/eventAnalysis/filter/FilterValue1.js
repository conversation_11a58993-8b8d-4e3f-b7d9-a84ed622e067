/* eslint-disable */
import { useDebounce } from '@umijs/hooks';
import { AutoComplete, Input, InputNumber } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { t } from 'utils/translation';

const FilterValueContext = React.createContext();

/**
 * 输入一个值的情况
 */
function TextInput(props) {
  let { fieldValue, items, onChange } = props;
  const { setMenuVisible } = useContext(FilterValueContext);
  const inputEl = useRef(null);

  // if (fieldValue === '') {
  //   fieldValue = Text;
  //   value.changeValue(fieldValue);
  // }

  if (_.isArray(items)) {
    items = items.filter((n) => !!n.value);
  } else {
    items = [];
  }

  const itemsDs = items
    .filter((n) => !!n.value)
    .map((v) =>
      // <Option label={v.value || v} value={v.value || v} key={v.id}>{v.value || v}</Option>
      ({ value: v.value, label: v.value })
    );

  return (
    <>
      <AutoComplete
        // dataSource={itemsDs}
        style={{
          width: 100
        }}
        placeholder={t('analysisCenter-8swk9UfMSUpY')}
        showSearch={items.length > 0}
        onChange={onChange}
        value={fieldValue}
        maxLength={36}
        options={itemsDs}
        filterOption={(inputValue, option) =>
          option.value.toUpperCase().indexOf(typeof inputValue === 'string' ? inputValue.toUpperCase() : '') !== -1
        }
        // allowClear
        // optionLabelProp="label"
        ref={inputEl}
        onDropdownVisibleChange={(v) => setMenuVisible(v)}
      />
      <span> --------</span>
    </>
  );
  // return 'OneInput';
}

/**
 * 单一输入的输入框，要根据fieldType决定显示那种输入框，可能是TextInput, 也可能是DateInput
 * @param {FilterModel} value
 * @param {function} onChange
 */
function SingleTextInput(props) {
  return TextInput(props);
}

function NumberBetweenInput(props) {
  let { onChange, fieldValue } = props;
  const inputEl = useRef(null);

  if (!_.isArray(fieldValue)) {
    fieldValue = [];
  }
  return (
    <>
      <InputNumber
        style={{ width: 100, marginRight: '5px' }}
        placeholder={t('analysisCenter-D38FerRj1cvH')}
        value={fieldValue[0]}
        ref={inputEl}
        onChange={(value) => !isNaN(value) && onChange([value, fieldValue[1]])}
      />
      {t('analysisCenter-SqDIdfG2sqJV')}
      <InputNumber
        style={{ width: 100, marginLeft: '5px' }}
        placeholder={t('analysisCenter-rWhC6fyfQRIY')}
        value={fieldValue[1]}
        onChange={(value) => !isNaN(value) && onChange([fieldValue[0], value])}
      />
    </>
  );
}

/**
 * 范围日历输入框
 */
// function DateBetweenInput(props) {
//   let { fieldValue, fieldType, onChange } = props;
//   const [value, setValue] = useState(longTodayjs(fieldValue));
//   const showTime = !!(
//     fieldType === 'DATETIME' ||
//     fieldType === 'TIMESTAMP' ||
//     fieldType === 'HIVE_TIMESTAMP'
//   );
//   const format =
//     fieldType === 'DATETIME' ||
//     fieldType === 'TIMESTAMP' ||
//     fieldType === 'HIVE_TIMESTAMP'
//       ? 'YYYY-MM-DD HH:mm:ss'
//       : 'YYYY-MM-DD';
//   let unit =
//     fieldType === 'DATETIME' ||
//     fieldType === 'TIMESTAMP' ||
//     fieldType === 'HIVE_TIMESTAMP'
//       ? 'second'
//       : 'day';
//   const onValueChange = (m) => {
//     setValue(m);
//     onChange(
//       m &&
//         m[0] &&
//         m[1] && [m[0].startOf(unit).valueOf(), m[1].startOf(unit).valueOf()]
//     );
//   };

//   // const onValueOk = m => {
//   //   onChange(m && m[0] && m[1] && [m[0].valueOf(), m[1].valueOf()]);
//   // };

//   return (
//     <RangePicker
//       allowClear={false}
//       showTime={showTime}
//       format={format}
//       placeholder={['开始时间', '结束时间']}
//       // onOk={onValueOk}
//       onChange={onValueChange}
//       value={value}
//     />
//   );
// }

function TwoInput(props) {
  const { fieldType, operator, onChange, fieldValue, items } = props;
  return (
    <NumberBetweenInput
      fieldType={fieldType}
      operator={operator}
      fieldValue={fieldValue}
      items={items}
      onChange={onChange}
    />
  );
}

// function DateInput(props) {
//   let { fieldType, onChange } = props;

//   const showTime =
//     fieldType === 'DATETIME' ||
//     fieldType === 'TIMESTAMP' ||
//     fieldType === 'HIVE_TIMESTAMP'
//       ? { format: 'HH:mm:ss' }
//       : null;
//   const format =
//     fieldType === 'DATETIME' ||
//     fieldType === 'TIMESTAMP' ||
//     fieldType === 'HIVE_TIMESTAMP'
//       ? 'YYYY-MM-DD HH:mm:ss'
//       : 'YYYY-MM-DD';
//   let unit =
//     fieldType === 'DATETIME' ||
//     fieldType === 'TIMESTAMP' ||
//     fieldType === 'HIVE_TIMESTAMP'
//       ? 'second'
//       : 'day';
//   const onValueChange = (m) => {
//     onChange(m.startOf(unit).valueOf());
//   };

//   return (
//     <DatePicker
//       placeholder="请输入日期"
//       showTime={showTime}
//       format={format}
//       // allowClear
//       allowClear={false}
//       value={props.fieldValue && dayjs(props.fieldValue)}
//       // getCalendarContainer={triggerNode => triggerNode.parentNode}
//       onChange={onValueChange}
//     />
//   );
// }

/**
 * 返回
 * @param {FilterModel} value
 * @param {function} onChange
 */
function SingleInput(props) {
  let { fieldValue, onChange } = props;
  const inputEl = useRef(null);
  return (
    <InputNumber
      style={{ width: 100, marginRight: '5px' }}
      placeholder={t('analysisCenter-Djb0BKnYmxue')}
      value={fieldValue}
      ref={inputEl}
      min={0}
      onChange={onChange}
    />
  );
}

/**
 * *****************************************
 * 主组件
 * *****************************************
 */
export default function FilterValue({ value: values, onChange }) {
  const { operator, fieldType, value } = values;
  const [items] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [menuVisible, setMenuVisible] = useState(false);
  // const [inputVal, setInputVal] = useState('');
  const [filterValueContext] = useState({
    setMenuVisible
  });
  // const { operator, propertyType } = eventAggregateProperty;

  // let fieldType = 'STRING';
  // let fieldType = fieldType;

  // if (propertyType === 'TIMES') {
  //   fieldType = 'INT';
  // } else {
  //   fieldType = eventAggregateProperty?.property?.fieldType || 'STRING';
  // }
  const [fieldValue, setFieldValue] = useState(value);
  const debounceFieldValue = useDebounce(fieldValue, 500);

  useEffect(() => {
    setFieldValue(value);
  }, [value]);

  useEffect(() => {
    if (debounceFieldValue === null || debounceFieldValue === undefined) return;
    onChange(debounceFieldValue);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceFieldValue]);

  const onChangeFieldValue = (v) => {
    if (v === '') {
      Text = '';
    }
    Text = v;
    value.changeValue(v);
    setFieldValue(v);
  };

  const onTextChange = (e) => {
    if (e === '') {
      Text = '';
    } else {
      Text = e.target.value;
    }

    // value.changeEventAggregateProperty({
    //   ...value.eventAggregateProperty,
    //   value: Text
    // });
    onChange(value);
  };
  /**
   * 主方法
   * 返回渲染组件
   */
  const filterValue = () => {
    switch (operator) {
      case 'EQ':
      case 'NE':
      case 'GT':
      case 'GTE':
      case 'LT':
      case 'LTE':
      case 'LIKE':
      case 'NOT_LIKE':
      case 'START_WITH':
      case 'NOT_START_WITH':
      case 'END_WITH':
      case 'NOT_END_WITH':
        return (
          <SingleInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case '':
        return (
          <SingleTextInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
            // value={value}
          />
        );
      case 'BETWEEN':
        return (
          <TwoInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'IS_NOT_NULL':
      case 'IS_NULL':
      case 'IS_TRUE':
      case 'IS_FALSE':
      case 'ALL':
        return <span />;
      default:
        return (
          <>
            <Input
              onChange={onTextChange}
              style={{ width: '100px' }}
              placeholder={t('analysisCenter-8swk9UfMSUpY')}
              maxLength={20}
            />
          </>
        );
    }
  };

  return (
    <FilterValueContext.Provider value={filterValueContext}>
      <div>{filterValue()}</div>
    </FilterValueContext.Provider>
  );
}
