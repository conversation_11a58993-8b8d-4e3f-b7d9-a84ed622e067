/*
 * @Author: 卜广硕 <EMAIL>
 * @Date: 2023-02-27 17:34:59
 * @LastEditors: 卜广硕 <EMAIL>
 * @LastEditTime: 2023-04-13 15:11:50
 * @FilePath: \Front\src\pages\home\analysisCenter\eventAnalysis\updateChart\updateChart.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Alert, Radio } from 'antd';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import FunnelAnalysis from 'service/funnelAnalysis.js';
import { eventAnalysisContext } from '../eventAnalysisContext';
import './updateChart.scss';

export default function UpdateChart(props) {
  const { state, dispatch } = useContext(eventAnalysisContext);
  const { boardType, detailObj } = props;
  const history = useHistory();
  const { displayType } = state;
  const [boardList, setBoardList] = useState([]);
  const [campaignList, setCampaignList] = useState([]);
  const { id } = useParams();

  useEffect(() => {
    const init = async () => {
      dispatch({ displayType: 'LINE_CHART' });
      const getChartsFromBoard = await FunnelAnalysis.getChartsFromBoard({ id });
      const getChartsFromCampaign = await FunnelAnalysis.getChartsFromCampaign({
        id
      });
      setBoardList(getChartsFromBoard);
      setCampaignList(getChartsFromCampaign);
    };
    init();
  }, []);

  const clickBoards = (clickId, type, campaignType) => {
    if (type === 'board') {
      window.open(`/aimarketer/home/<USER>/dataDashboard/${clickId}`);
    } else {
      if (campaignType === 'CAMPAIGNS') {
        window.open(`/aimarketer/home/<USER>/detail/${clickId}`);
      } else {
        window.open(`/aimarketer/home/<USER>/detail?id=${clickId}`);
      }
    }
  };

  const onChartTypeChange = (e) => {
    dispatch({ displayType: e.target.value });
  };

  return (
    <div className="funnelUpdateChart">
      <div className="content">
        <Alert
          message={
            localStorage.getItem('isActivityAnalysis')
              ? '更新活动分析图表，数据将重新适配'
              : '更新图表，包含此图表的数据看板也将更新'
          }
          type="info"
          showIcon
        />
        <div className="displayType">
          <div>展示方式</div>
          <div>
            <Radio.Group onChange={onChartTypeChange} value={displayType}>
              <Radio value="LINE_CHART"> 折线图 </Radio>
              <Radio value="COLUMN_CHART"> 柱状图 </Radio>
              <Radio value="PERCENT_CHART" disabled={!state.isGroupType}>
                {' '}
                饼状图{' '}
              </Radio>
              <Radio value="AREA_CHART" disabled={!state.isAreaType}>
                {' '}
                堆叠图{' '}
              </Radio>
            </Radio.Group>
          </div>
        </div>

        {!_.isEmpty(boardList) && (
          <div className="showBoard">
            <div className="title">数据看板</div>
            <div className="boards">
              {boardList.map((item, index) => {
                return (
                  <a className="boardsItem" key={index} onClick={() => clickBoards(item?.id, 'board')}>
                    {item?.boardName}
                  </a>
                );
              })}
            </div>
          </div>
        )}

        {localStorage.getItem('isActivityAnalysis') ? (
          <div className="mt-[16px]">
            <div className="mb-[8px]">活动信息</div>
            <div className="cursor-pointer">
              {boardType === 'campaigns' ? (
                <a
                  onClick={() => history.push(`/aimarketer/home/<USER>/detail/${detailObj.id}`)}
                >{`[${detailObj.id}]${detailObj.name}`}</a>
              ) : (
                <a
                  onClick={() => history.push(`/aimarketer/home/<USER>/detail?id=${detailObj.id}`)}
                >{`[${detailObj.id}]${detailObj.name}`}</a>
              )}
            </div>
          </div>
        ) : (
          !_.isEmpty(campaignList) && (
            <div className="showBoard">
              <div className="title">活动分析</div>
              <div className="boards">
                {campaignList.map((item, index) => {
                  return (
                    <a
                      className="boardsItem"
                      key={index}
                      onClick={() => clickBoards(item?.campaignId, 'campaign', item?.type)}
                    >
                      {item?.type === 'CAMPAIGNS' ? item?.campaigns?.name : item?.campaign?.name}
                    </a>
                  );
                })}
              </div>
            </div>
          )
        )}
      </div>
    </div>
  );
}
