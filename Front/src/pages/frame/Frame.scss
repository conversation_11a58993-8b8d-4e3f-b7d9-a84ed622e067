// @import 'assets/css/variable.scss';

@font-face {
  font-family: 'DIA';
  src:
    url('../../assets/font/DINAlternate.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

.initLoading {
  position: absolute;
  margin-left: -100px;
  margin-top: -46px;
  left: 50%;
  top: 50%;
}

.noLicense {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;

  .content {
    width: 362px;
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: center;

    img {
      width: 80px;
      height: 80px;
    }

    span {
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      letter-spacing: 0em;
      text-align: center;
    }
  }
}

#datatist-wolf-app {
  .ant-layout {
    background: #F6F8FA;
  }

  .ant-layout-header {
    height: $app_header;
    line-height: $app_header;
  }

  .frame-header {
    color: #fff;
    background-color: $white;
    border-bottom: 1px solid $border_color;
    box-shadow: 0 2px 10px rgba(0, 0, 0, .4);
    padding: 0;
    position: fixed;
    top: 0;
    width: 100%;
    min-width: 1200px;
    z-index: 1000;
  }

  @media screen and (max-width: 900px) {
    .frame-header {
      width: 1500px;
      position: absolute;
    }
  }

  section {
    // z-index: 1;
  }

  .app-bottom-layout {
    // background: url('../../assets/images/background.png') ;
    background-attachment: fixed;
  }

  .frame-content {
    // background: #f6f6f6;
    background: linear-gradient(180deg, rgba(193, 224, 255, 0.05) 0%, rgba(246, 248, 250, 0.00) 100%), radial-gradient(24.35% 16.06% at 84.20% -0.00%, rgba(240, 220, 255, 0.35) 0%, rgba(221, 205, 255, 0.35) 49.32%, rgba(244, 245, 251, 0.35) 100%), linear-gradient(180deg, #E8F3FE 0%, #F7F8FA 25.48%);
    ;
    margin-top: $menu_height;
    padding: 0 24px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    // min-height: 100vh;
  }

  @media screen and (max-width: 900px) {
    .frame-content {
      width: 1500px;
    }
  }

  .frame-content-no-margin-top {
    margin-top: 0;
  }
}

.app-full {
  .ant-layout-header {
    display: none;
  }
}

.analysisGroupType::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}

::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  /**/
}

::-webkit-scrollbar-track {
  background: rgb(239, 239, 239);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #969696;
}

.loadingPlace {
  width: 100%;
  height: calc(100vh - 60px);
}

// .ant-select-selector {
//   border-radius: 6px !important
// }

// .ant-input-affix-wrapper {
//   border-radius: 6px !important
// }

// button {
//   border-radius: 6px !important;
//   border-color: #eee !important
// }

//快捷时间选项
.shortcutOptions-item {
  box-sizing: initial !important;
}

.shortcutTime {
  height: 32px !important;
  line-height: 32px !important;
}

// .ant-select-dropdown,
// //btn
// .ant-btn,
// //input
// // .ant-input,
// .ant-input-affix-wrapper,
// //select
// .ant-select-selector,
// //picker
// .ant-picker,
// //picker drop
// .ant-picker-panel-container,
// //menu
// .ant-menu,
// .ant-popover-inner {
//   border-radius: 6px !important;
// }

.ant-message {
  z-index: 99999 !important;
}

.ant-menu-vertical {
  a {
    color: #333 !important;
  }
}

h1 {
  font-size: 24px !important;
  font-weight: 600 !important;
}

.userGroupPicker {
  width: 75%;
  // margin-top: 12px;
  margin-left: 12px;
  display: inline-flex;
}

.buttomFooter {
  display: flex;
  justify-content: flex-end;

  button {
    margin-left: 12px;
  }
}

//封装时间选择器
#userGroupForm_validDateType,
.userGroups,
#validDateType {
  width: 100%;
  height: 22px;
  line-height: 22px;
  display: inline-flex;

  .ant-radio-wrapper {
    margin-right: 0;
    line-height: 32px;
  }

  &>.ant-radio-wrapper:last-child {
    width: 80%;

    &>span:last-child {
      display: flex;
      width: 100%;

      &>span:last-child {
        width: 70%;

        &>.userGroupPicker {
          .ant-picker {
            width: 100%;
          }
        }
      }
    }
  }
}

// 流程节点的名称高度
.editable-cell-value-wrap-name {
  min-height: 20px !important;
}

.deptStyle {
  margin-bottom: 16px;

  &>:first-child {
    width: 90px;
  }

  &>:last-child {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

//react-grid-layout 禁止调整大小
.disableResize {
  .react-resizable-handle {
    display: none;
  }
}

.checkEventDetail {
  .ant-timeline-item {
    margin: 0;
    padding: 0;
    padding-bottom: 20px;
  }
}