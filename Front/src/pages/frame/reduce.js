// 自动登录请求前，code为0，调用登录接口后，code为1
// login代表自动登录是否成功，true标识自动登录成功，false则标识自动登录失败，之后应该跳转到登录页面

const autologin = (state = { code: 0, login: false }, action) => {
  switch (action.type) {
    case 'autologin':
      if (action.autologin.userId) {
        return { code: 1, login: true };
      } else {
        return { code: 1, login: false };
      }
    case 'logout':
      return { code: 0, login: false };
    default:
      return state;
  }
};

const defaultState = {};
const currentUser = (state = defaultState, action) => {
  switch (action.type) {
    case 'currentUser':
      return action.currentUser;
    default:
      return state;
  }
};

const meunData = (state = '', action) => {
  switch (action.type) {
    case 'meunData':
      return action.meunData;
    default:
      return state;
  }
};

const userDepartment = (state = '', action) => {
  switch (action.type) {
    case 'userDepartment':
      return action.userDepartment;
    default:
      return state;
  }
};

const userCenterMenuData = (state = '', action) => {
  switch (action.type) {
    case 'userCenterMenuData':
      return action.userCenterMenuData;
    default:
      return state;
  }
};

const loginUser = (state = {}, action) => {
  switch (action.type) {
    case 'loginUser':
      return action.loginUser;
    default:
      return state;
  }
};

// 默认初始化数据时未加载完成，loading图标出现
const initLoading = (state = true, action) => {
  switch (action.type) {
    case 'initLoading':
      return action.initLoading;
    default:
      return state;
  }
};

const userList = (state = [], action) => {
  switch (action.type) {
    case 'userList':
      return action.userList;
    default:
      return state;
  }
};

const projectList = (state = [], action) => {
  switch (action.type) {
    case 'projectList':
      return action.projectList;
    default:
      return state;
  }
};

const defaultAuthInfo = { superAdmin: false, codeList: [], urlList: [] };
const authInfo = (state = defaultAuthInfo, action) => {
  switch (action.type) {
    case 'authInfo':
      return { ...state, ...action.payload };
    default:
      return state;
  }
};

const messageInfo = (state = [], action) => {
  switch (action.type) {
    case 'messageInfo':
      return action.messageInfo;
    default:
      return state;
  }
};

export {
  authInfo,
  autologin,
  currentUser,
  initLoading,
  loginUser,
  messageInfo,
  meunData,
  projectList,
  userCenterMenuData,
  userDepartment,
  userList
};
