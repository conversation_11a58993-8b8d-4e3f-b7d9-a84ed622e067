// @import 'assets/css/variable.scss';

.headerBox {
  width: 100%;
  display: flex;
  padding: 0 24px;
  align-items: center;
  background: $menu_bg_color;

  .logoBox {
    display: flex;
    align-items: center;
    width: 180px;
    cursor: pointer;

    .logo {
      height: 60px;
      width: 180px;

      &+.trigger {
        font-size: 18px;
        vertical-align: middle;
        padding: 0 0 0 24px;
        cursor: pointer;
        transition: color 0.3s;

        &:hover {
          color: #1890ff;
        }
      }
    }

    .productLogo {
      display: flex;
      width: 40px;
      height: 40px;
    }

    .productName {
      margin-left: 8px;
      font-size: 18px;
      font-weight: 500;
      // color: black;
      color: #fff;
      max-width: 130px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  .W190 {
    width: 190px
  }

  .headNav {
    flex: 1;
    align-items: center;
    padding-left: 15px;
    height: $menu_height;
  }

  .navChange {
    margin: 0 10px;
  }

  .navProject {
    padding-right: 24px;
    display: flex;
    align-items: center;

    >a,
    >a:hover {
      max-width: 200px;
      overflow: hidden;
      display: inline-block;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: $menu_hover_color
    }

    .ant-divider {
      background: #ccc;
      margin: 0 12px;
    }

    .setting,
    .help {
      cursor: pointer;

      span {
        margin: 0 5px 0 0px;
      }
    }

    .exit {
      display: block;
      margin: -5px -12px;
      padding: 5px 12px;
    }

    .ant-dropdown-menu-title-content {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  .userAndSetting {
    // min-width: 150px;
    // padding-right: 20px;
    display: flex;
    align-items: center;

    .ant-badge-count {
      font-size: 10px !important;
      padding: 0 4px !important;
    }

    .submenu-title {
      font-size: 16px;
      color: #FFF;
    }

    .userInfo {

      .ant-dropdown-menu-title-content {
        display: ruby;
      }

      .ant-dropdown-menu {
        border-radius: 8px;
      }

      .loginUserNameWrapper {
        display: flex;
        align-items: center;

        .head-portrait {
          width: 32px;
          height: 32px;
          border-radius: 20px;
          background-color: $avater_bg_color;
          line-height: 32px;
          text-align: center;
          color: $avater_color;
          margin-right: 8px;
        }

        .ant-badge-dot {
          box-shadow: none;
        }
      }

      .header {
        width: 320px;
        height: 72px;
        display: flex;
        gap: 8px;
        margin: 14px 12px 0 12px;
        align-items: center;

        .head-portrait {
          width: 40px;
          height: 40px;
          border-radius: 20px;
          background-color: $primary_color;
          line-height: 40px;
          text-align: center;
          color: var(--avatar-menu-color);
          margin-bottom: 8px;
        }

        .userConfig {
          height: 44px;
          display: flex;
          flex-direction: column;

          .loginusername {
            max-width: 200px;
          }
        }
      }

      .org-name {
        .ant-tag {
          color: var(--ant-primary-color);
          background: var(--primary-hover-bg);
          margin-left: 4px;
          border: none;
          border-radius: 6px;
        }
      }

      .org {
        // display: inline-block;
        position: relative;

        .role {
          position: absolute;
          max-width: 220px;
          top: 0;
          right: -250px;
          color: rgba(0, 0, 0, 0.45);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .ant-divider {
      background: #ccc;
      margin: 0 12px;
    }

    .loginusername>:first-child {
      max-width: 110px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .loginusername {
      height: 44px;

      span {
        height: 22px;
        line-height: 22px;
      }
    }

    .setting,
    .help {
      cursor: pointer;

      span {
        margin: 0 5px 0 10px;
      }
    }

    .exit {
      display: block;
      margin: -5px -12px;
      padding: 5px 12px;
    }

    .navName {
      cursor: pointer;
    }

    // .ant-menu-horizontal:not(.ant-menu-dark)>.ant-menu-submenu {
    //   padding: 0 10px !important;
    // }
  }

  .menuStyle {
    .ant-menu-submenu-title {
      padding: 0 10px !important;
    }
  }

  .ant-dropdown {
    top: 50px !important;
  }

  .ant-dropdown:before {
    top: auto !important;
  }

  .ant-menu-horizontal {
    flex: 1;
    color: $menu_color;
    border: none;
    background: transparent !important;
    height: 100%;
    padding-left: 10px;
    margin-right: 24px;

    & .ant-menu-submenu-horizontal {
      text-align: center;
      // width: 100px;
    }

    // .ant-menu-item {
    //   height: 100%;

    //   // line-height: $menu_item_lineHeight_horizontal;
    //   a {
    //     color: #666;
    //     line-height: 40px;
    //     // opacity: 0.8;
    //   }
    // }

    // .ant-menu-item-selected {

    //   // background: #232226;
    //   a {
    //     color: #fff;
    //   }
    // }

    // .ant-menu-item-active {
    //   a {
    //     color: $primary_color;
    //   }
    // }

    // .ant-menu-submenu-horizontal {
    //   height: 100%;
    //   // line-height: $menu_item_lineHeight_horizontal;
    // }
  }

  .ant-menu-submenu-selected {
    //默认选中背景颜色
    background: $menu_active_color;
    //默认选中颜色
    color: $menu_title_active !important;
    //选中底部
    border-bottom: 2px solid $menu_buttom_color !important;

    a {
      color: var(--menu-hover-color) !important;
    }
  }

  .ant-menu-submenu-popup {
    top: 60px !important;
  }
}

.headerMenu {
  &>ul {
    min-width: 130px;
  }
}

.ant-menu-horizontal {
  .ant-menu-item:hover {
    a {
      color: var(--menu-hover-color) !important;
    }
  }
}

.ant-menu-vertical {
  .ant-menu-item:hover {
    a {
      color: var(--primary-color) !important;
    }
  }
}

.ant-menu-item-selected {
  a {
    color: var(--primary-color) !important;
  }
}

.ant-menu-horizontal {

  //选中子菜单 颜色
  .ant-menu-submenu-title:hover,
  .ant-menu-submenu-active {
    color: $menu_hover_color !important;

    a {
      color: var(--menu-hover-color) !important;
    }
  }
}

.ant-menu-vertical {

  //选中子菜单 颜色
  .ant-menu-submenu-title:hover,
  .ant-menu-submenu-active {
    color: var(--primary-color) !important;

    a {
      color: var(--primary-color) !important;
    }
  }
}

.custom-menu-item-wrapper {
  .ant-dropdown-menu-item {
    a {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .active {
      // color: $primary_color;

      .anticon {
        margin-left: 24px;
      }
    }
  }
}

.userPopover {
  .ant-popover-inner {
    border-radius: 6px;

    .ant-popover-inner-content {
      padding: 4px 0 !important;
    }
  }

  .ant-popover-arrow {
    display: none;
  }

  .ant-tree-title {
    display: inline-block;
    // width: calc(100vh - 250px);
    min-width: 200px;
    max-width: 300px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.subBadge {
  .ant-badge-count {
    font-size: 10px !important;
    padding: 0 4px !important;
  }
}

.login-timeout-notification {
  border-radius: 6px;
  padding: 16px;
  width: 421px;
}