import { checkLang, langMap } from '@/constant';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { CheckOutlined, CloseOutlined, DownOutlined, SettingOutlined } from '@ant-design/icons';
import { Badge, Dropdown, Menu, message, notification, Popover, Switch, Tag } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { idToName } from 'pages/home/<USER>/dataPermissions/config';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { Link, withRouter } from 'react-router-dom';
import UserService from 'service/UserService';
import _organizationManageService from 'service/organizationmanageService';
import { setDeptId } from 'utils/commonUtils';
import { t } from 'utils/translation';
import { MyIconV2 } from 'utils/myIcon';
import { transformUrl } from 'utils/universal';
import HeadeNav from '../frameNav/headeNav/HeadeNav';
import './FrameHeade.scss';

const mapStateToProps = (state) => {
  return {
    currentUser: state.loginUser,
    projectList: state.projectList,
    systemInfo: state.systemInfo,
    messageInfo: state.messageInfo
  };
};

const userConfig = [
  {
    route: '/aimarketer/home/<USER>',
    name: '站内信',
    icon: 'pie-chart'
  },
  {
    route: '/aimarketer/usercenter/productcenter',
    name: '产品中心',
    icon: 'pie-chart'
  },
  {
    route: '/aimarketer/home/<USER>/personSetting',
    name: '个人设置',
    icon: 'pie-chart'
  },
  // { route: '/aimarketer/home/<USER>/person', query: 'toLogin', name: '个人设置', icon: 'pie-chart' },
  {
    route: { pathname: '/aimarketer/login', query: 'toLogin' },
    name: '安全退出',
    icon: 'pie-chart'
  }
];

class FrameHeade extends Component {
  constructor(props) {
    super(props);
    this.userService = new UserService();
    this.state = {
      selectedNav: '',
      organizationList: []
    };
    this.logoutClickHandle = this.logoutClickHandle.bind(this);
  }

  static getDerivedStateFromProps(props) {
    return { selectedNav: props.location.pathname };
  }

  componentDidMount() {
    // 监听窗口变化，动态给文档设置高度
    const height = document.documentElement.clientHeight;
    const box = document.querySelector('#datatist-wolf-app');
    const mainContent = document.querySelector('.app-bottom-layout');
    box.style.minHeight = `${height}px`;
    mainContent.style.minHeight = `${height - 60}px`;
    window.addEventListener('resize', resizeHandle);
    function resizeHandle() {
      const height2 = document.documentElement.clientHeight;
      box.style.minHeight = `${height2}px`;
      mainContent.style.minHeight = `${height2 - 60}px`;
    }

    (async () => {
      if (localStorage.getItem('passwordTip')) notification.destroy(localStorage.getItem('passwordTip'));
      localStorage.removeItem('passwordTip');
      const passwordTip = await this.userService.getUserPasswordExpireDateTips();
      if (passwordTip) {
        notification.warning({
          message: '密码过期提醒',
          description: passwordTip,
          duration: null,
          key: passwordTip
        });
        localStorage.setItem('passwordTip', passwordTip);
      }
    })();

    // 每隔5分钟调用一下自动登陆
    this.refresh = setInterval(async () => {
      const autoLoginData = await this.userService.autoLogin();
      if (!autoLoginData.userId) {
        this.refresh && clearInterval(this.refresh);
        message.warn('登陆已过期，请重新登陆', 1);
        // this.props.history.push('/aimarketer/login');
        global.__LOGOUT__();
      }
    }, 300000);
    this.refresh1 = setInterval(async () => {
      const data = await this.userService.findWebsiteMessage();
      if (!_.isEmpty(data)) {
        try {
          const ids = data.map((item) => item.msgRecordId);
          // const messageList = data.map((item) => item.content);
          data.forEach((item) => {
            const key = `login-success${new Date().getTime()}`;
            if (item.messageType !== 'TOKEN_EXPIRE' && item.content) {
              const tip = (
                <span>
                  {item.content}
                  <CloseOutlined
                    style={{ cursor: 'pointer', color: '#000', marginLeft: 8 }}
                    onClick={() => message.destroy(key)}
                  />
                </span>
              );
              message.success({
                content: tip,
                duration: 3,
                key
              });
              // message.success(item.content);
            }
          });

          const result = data.find((item) => item.messageType === 'TOKEN_EXPIRE');

          if (result) {
            const currentTime = dayjs();
            const expiryTime = dayjs(result.content);
            const fiveMinutesInMillis = 5 * 60 * 1000;

            if (expiryTime.diff(currentTime) <= fiveMinutesInMillis) {
              notification.warn({
                message: '登录即将超时',
                description: '您将在 5 分钟后被自动退出登录，请尽快完成操作。',
                className: 'login-timeout-notification',
                duration: 0
              });
            }
          }

          await this.userService.modifyWebsiteMessage({
            status: 'READ',
            ids
          });
        } catch (err) {
          console.error(err.message);
        }
      }
    }, 10000);

    // 获取组织列表x
    this.getOrganizationList();
    // this.props.getDepartment();
  }

  componentWillUnmount() {
    this.refresh && clearInterval(this.refresh);
    this.refresh1 && clearInterval(this.refresh1);
  }

  // 退出登录,退出登录清除登陆成功信息，并将初始化loading和自动登录状态重新初始化。
  logoutClickHandle() {
    global.__LOGOUT__();
  }

  backInit = () => {
    this.props.dispatch({ type: 'logout' });
    this.props.dispatch({ type: 'initLoading', initLoading: true });
  };

  getOrganizationList = async () => {
    const data = await _organizationManageService.findAllByUserId();
    this.setState({
      organizationList: data
    });
  };

  onSwitch = (status) => {
    status ? localStorage.setItem('deptShowType', 'all') : localStorage.removeItem('deptShowType');
    window.location.reload();
  };

  configToMenu(arr) {
    const { organizationList } = this.state;
    const { messageInfo } = this.props;

    const totalMessage = Object.values(messageInfo).filter((item) => item);

    if (!arr || arr.length === 0) return null;
    const orgId = localStorage.getItem('organizationId');
    const orgInfo = organizationList.find((k) => `${k.id}` === orgId);
    const _departmentListAll = JSON.parse(localStorage.getItem('departmentListAll'));

    const value = _.find(_departmentListAll, {
      id: parseInt(window.getDeptId())
    });

    const content = (
      <div className="w-400 h-332 overflow-auto" onClick={(e) => e.stopPropagation()}>
        {value?.level !== 1 && (
          <div className="flex justify-between items-center px-14 py-8">
            <div className="flex flex-col justify-center">
              <div>显示所有内容</div>
              <div className=" text-[rgba(0,0,0,.45)] text-[12px]">显示当前用户所有部门的全部内容</div>
            </div>
            <div>
              <Switch size="small" onClick={this.onSwitch} checked={localStorage.getItem('deptShowType') === 'all'} />
            </div>
          </div>
        )}

        {this.props.departmentList.map((item, index) => {
          const value = _.find(_departmentListAll, {
            id: parseInt(item?.deptId)
          });
          const deptKeyValue = JSON.parse(localStorage.getItem('deptKeyValue'));
          return (
            <div
              key={index}
              className={`h-56 flex  py-8 px-12 hover:bg-[#f5f5f5] hover:cursor-pointer ${
                window.getDeptId() === `${value.id}` && 'bg-[#f5f5f5]'
              }`}
              onClick={() => {
                setDeptId(value?.id);
                // if (value?.level === 1) {
                //   localStorage.removeItem('deptShowType');
                // }
                // 找到产品的落地页，如果有跳转到落地页，如果没有跳转到菜单第一个
                const targetMenu = this.getDefaultMenu();
                this.props.history.replace(targetMenu?.route || this.props.meunData?.main[0]?.children[0]?.route);
                window.location.reload();
              }}
            >
              <div className="w-350 flex flex-col">
                <div className="my-text-overflow" title={value?.name}>
                  {value?.name}
                </div>
                <div
                  className="text-[rgba(0,0,0,.45)] my-text-overflow"
                  title={idToName(deptKeyValue, `${value.routes}${value.id}`).join(' / ')}
                >
                  {idToName(deptKeyValue, `${value.routes}${value.id}`).join(' / ')}
                </div>
              </div>
              {window.getDeptId() === parseInt(value.id) && (
                <span className="leading-[40px] ml-[auto]">
                  <CheckOutlined />
                </span>
              )}
            </div>
          );
        })}
      </div>
    );
    return [
      {
        label: (
          <span className="org">
            组织切换 <span className="role">{orgInfo?.name}</span>
          </span>
        ),
        key: 'sub1',
        popupClassName: 'headerMenu',
        children: this.state.organizationList.map((item) => ({
          label: (
            <a
              rel="noopener noreferrer"
              className={orgId === `${item.id}` ? 'active' : ''}
              onClick={() => this.changeOrganization(item.id)}
            >
              {item.name} {orgId === `${item.id}` ? <CheckOutlined /> : null}
            </a>
          ),
          key: `sub1-${item.id}`
        }))
      },
      {
        label: (
          <Popover content={content} placement="leftTop" overlayClassName="userPopover">
            <span className="org inline-block w-[100%]">
              部门切换{' '}
              <span className="role !right-0" title={this.props.parentPath}>
                {this.props.parentPath.length > 16 ? `...${this.props.parentPath.slice(-16)}` : this.props.parentPath}
              </span>
            </span>
          </Popover>
        ),
        key: 'department',
        children: []
      },
      {
        label: (
          <div>
            <span className="mr-[4px]">我的</span>
            <span>
              <Badge count={_.isEmpty(totalMessage) ? 0 : _.sum(totalMessage)} />
            </span>
          </div>
        ),
        key: 'sub2',
        popupClassName: 'headerMenu',
        children: [
          {
            label: (
              <div onClick={() => this.props.history.push('/aimarketer/home/<USER>')}>
                <span className="mr-[4px]">我的待办</span>
                <span>
                  <Badge count={messageInfo.toDoList} className="subBadge" />
                </span>
              </div>
            ),
            key: 'sub2-1'
          },
          (this.props.systemInfo['sys.approval.v2.switch'] === 'ALL' ||
            this.props.systemInfo['sys.approval.v2.switch'] === 'APPROVAL') && {
            label: (
              <div onClick={() => this.props.history.push('/aimarketer/home/<USER>')}>
                <span className="mr-[4px]">我的待办V2</span>
                <span>
                  <Badge count={messageInfo.toToListV2} className="subBadge" />
                </span>
              </div>
            ),
            key: 'sub2-2'
          }
        ]
      },
      {
        label: '切换语言',
        key: 'switchLang',
        popupClassName: 'headerMenu',
        children: [
          ...(localStorage.getItem('switchLang') === 'ON'
            ? checkLang.map((item) => {
                return {
                  label: (
                    <div onClick={() => this.onLanguageChange(item.key)}>
                      <span className="mr-[4px]">{item.label}</span>
                      <span>{localStorage.getItem('lang') === item.key ? <CheckOutlined /> : null}</span>
                    </div>
                  ),
                  key: item.key
                };
              })
            : [])
        ]
      },
      ...arr.map((item) => {
        return {
          label:
            typeof item.route === 'object' && item.route.pathname === '/aimarketer/login' ? (
              <span className="exit" onClick={this.logoutClickHandle}>
                {item.name}
              </span>
            ) : (
              <Link
                // className={`${this.state.selectedNav === item.route && 'active'} routeLink`}
                to={item.route}
              >
                {item.name}
              </Link>
            ),
          key: typeof item.route === 'object' ? item.route.pathname : item.route
        };
      })
    ];
  }

  changeOrganization = (id) => {
    localStorage.setItem('organizationId', id);
    localStorage.removeItem('currentProduct');
    localStorage.removeItem('deptId');
    localStorage.removeItem('deptShowType');
    window.location.reload();
    // this.props.history.replace('/aimarketer/usercenter/productcenter');
  };

  changeProject = (id) => {
    const projectId = localStorage.getItem('projectId');
    if (id === projectId) return;
    localStorage.setItem('projectId', id);
    // 找到产品的落地页，如果有跳转到落地页，如果没有跳转到菜单第一个
    const targetMenu = this.getDefaultMenu();
    this.props.history.replace(targetMenu?.route || this.props.meunData?.main[0]?.children[0]?.route);
    // this.props.history.replace('/aimarketer/usercenter/productcenter');
    setTimeout(() => {
      window.location.reload();
    }, 4);
  };

  getDefaultMenu = () => {
    const defaultPage = this.props.defaultPage;
    let targetMenu = {};
    if (!_.isNil(defaultPage)) {
      // 在当前菜单下找到是否有落地页
      let allMenus = _.concat(this.props.meunData?.main, this.props.meunData?.right);
      allMenus = _.filter(allMenus, (item) => !_.isEmpty(item));
      _.forEach(allMenus, (item) => {
        if (item.id === defaultPage) {
          targetMenu = item;
          return false;
        } else {
          if (item.children) {
            _.forEach(item.children, (child) => {
              if (child.id === defaultPage) {
                targetMenu = child;
                return false;
              }
            });
          }
        }
      });
    }
    return targetMenu;
  };

  toHome = () => {
    this.props.history.push('/aimarketer/usercenter/productcenter');
  };

  toTargetUrl = () => {
    const targetMenu = this.getDefaultMenu();
    this.props.history.replace(targetMenu?.route || this.props.meunData?.main[0]?.children[0]?.route);
  };

  handleClick = (e) => {
    this.props.history.push(e.key);
  };

  onLanguageChange = (e) => {
    window.location.reload();
    localStorage.setItem('lang', e);
    dayjs.locale(langMap[e]);
    document.documentElement.lang = langMap[e];
    setTimeout(() => {
      this.props.i18n.changeLanguage(e);
    }, 100);
  };

  render() {
    const { collapsed, toggle, navType, meunData, projectList, messageInfo } = this.props;

    const hasMessage = Object.values(messageInfo).some((value) => value);

    const { selectedNav } = this.state;

    const projectId = localStorage.getItem('projectId');
    const projectInfo = projectList.find((k) => k.id === projectId);
    const menuStyle = {
      boxShadow: 'none'
    };

    const recursion = (menuConfig, right) => {
      menuConfig &&
        menuConfig.forEach((item) => {
          const hasRoute = !!item.route;
          item.label =
            window.location.hostname === 'localhost' ||
            localStorage.getItem('env') === 'DEFAULT' ||
            localStorage.getItem('env') === 'NS' ? (
              <Link
                to={hasRoute ? item.route : '#'}
                className="text-[var(--menu-color)]"
                onClick={(event) => !hasRoute && event.preventDefault()}
                onContextMenu={(event) => !hasRoute && event.preventDefault()}
              >
                {item.name}
              </Link>
            ) : (
              <Link
                to={hasRoute ? item.route : '#'}
                target="_blank"
                className="text-[var(--menu-color)]"
                onClick={(event) => !hasRoute && event.preventDefault()}
                onContextMenu={(event) => !hasRoute && event.preventDefault()}
              >
                <span>{item.name}</span>
              </Link>
            );
          item.key = item.route || item.name;
          item.icon = item?.icon ? (
            right ? (
              <SettingOutlined />
            ) : _.isString(item.icon) ? (
              item.icon.indexOf('icon-') !== -1 ? (
                <MyIconV2 type={item.icon} />
              ) : (
                item?.icon
              )
            ) : (
              item.icon
            )
          ) : null;
          if (!_.isEmpty(item.children)) {
            recursion(item.children);
          } else {
            item.children = null;
          }
          delete item.orderNum;
          delete item.parentId;
        });
      return menuConfig;
    };
    const RightItems = recursion(meunData?.right, 'right');
    const MainItems = recursion(meunData?.main);

    const totalMessage = Object.values(messageInfo).filter((item) => item);

    return (
      <div className="headerBox">
        <div onClick={this.toTargetUrl} className={`logoBox ${navType && 'W190'}`}>
          {this.props.iconUrl && <img className="logo" src={transformUrl(this.props.iconUrl)} alt="logo" />}
          {navType && (
            <LegacyIcon className="trigger" type={collapsed ? 'menu-unfold' : 'menu-fold'} onClick={toggle} />
          )}
        </div>
        <div className="headNav">{!navType && <HeadeNav menuConfig={MainItems} />}</div>
        {/* {localStorage.getItem('switchLang') === 'ON' && (
          <Dropdown
            menu={{
              items: checkLang.map((item) => {
                return {
                  key: item.key,
                  label: (
                    <span>
                      {item.label} {localStorage.getItem('lang') === item.key ? <CheckOutlined /> : null}
                    </span>
                  ),
                  onClick: () => this.onLanguageChange(item.key)
                };
              })
            }}
          >
            <GlobalOutlined style={{ margin: '0 20px' }} />
          </Dropdown>
        )} */}
        <div className="menuStyle flex">
          <Menu
            selectedKeys={[selectedNav]}
            mode="horizontal"
            inlineIndent={0}
            style={{ flex: 1 }}
            onClick={this.handleClick}
            items={RightItems}
          />
          <div className="navProject">
            <Dropdown
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
              overlayStyle={{
                width: 160,
                maxHeight: 300,
                overflow: 'auto',
                WebkitBoxShadow:
                  '0 3px 6px -4px rgb(0 0 0 / 12%), 0 6px 16px 0 rgb(0 0 0 / 8%), 0 9px 28px 8px rgb(0 0 0 / 5%)'
              }}
              menu={{
                items: projectList.map((item) => {
                  return {
                    label: (
                      <a title={item.name} rel="noopener noreferrer" onClick={() => this.changeProject(item.id)}>
                        {item.name}
                      </a>
                    ),
                    key: item.id
                  };
                })
              }}
            >
              <a className="ant-dropdown-link" onClick={(e) => e.preventDefault()}>
                {/* 当前项目： */}
                {projectInfo && projectInfo.name} <DownOutlined />
              </a>
            </Dropdown>
          </div>
        </div>
        <div className="userAndSetting">
          <div className="userInfo">
            <Dropdown
              trigger={['hover']}
              menu={{ items: this.configToMenu(userConfig) }}
              dropdownRender={(menu) => {
                return (
                  <div
                    style={{
                      background: '#fff',
                      borderRadius: '8px',
                      boxShadow:
                        '0 6px 16px 0 rgba(0,0,0,.08),0 3px 6px -4px rgba(0,0,0,.12),0 9px 28px 8px rgba(0,0,0,.05)'
                    }}
                  >
                    <div className="header">
                      <span className="head-portrait">
                        {<span className="head-portrait">{this.props?.loginUser?.name.substring(0, 1)}</span>}
                      </span>

                      <div className="userConfig">
                        <span title={this.props?.loginUser?.name} className="loginusername">
                          {this.props?.loginUser?.name}
                        </span>
                        <span className="org-name">
                          <Tag>{projectInfo?.roleName}</Tag>
                        </span>
                      </div>
                    </div>
                    {React.cloneElement(menu, {
                      style: menuStyle
                    })}
                  </div>
                );
              }}
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
            >
              <span
                style={{ display: 'flex' }}
                className={`${
                  ~selectedNav.indexOf('/home/<USER>') && 'active'
                } routerLink navName loginUserNameWrapper`}
              >
                {/* 欢迎您： */}
                <Badge offset={[-8, 0]} count={hasMessage ? _.sum(totalMessage) : 0} className="subBadge">
                  <div className="head-portrait">{this.props?.loginUser?.name?.substring(0, 1)}</div>
                </Badge>
                <div
                  className="loginusername flex flex-col"
                  style={{ height: _.isEmpty(this.props.parentPath) && 'auto' }}
                >
                  <span title={this.props?.loginUser?.name}>{this.props?.loginUser?.name}</span>
                  {!_.isEmpty(this.props.parentPath) && (
                    <span title={this.props.parentPath}>
                      {this.props.parentPath.length > 10
                        ? `...${this.props.parentPath.slice(-10)}`
                        : this.props.parentPath}
                    </span>
                  )}
                </div>
              </span>
            </Dropdown>
          </div>
        </div>
      </div>
    );
  }
}

export default connect(mapStateToProps)(withRouter(withTranslation()(FrameHeade)));

FrameHeade.propTypes = {
  collapsed: PropTypes.bool.isRequired,
  toggle: PropTypes.func.isRequired
};
