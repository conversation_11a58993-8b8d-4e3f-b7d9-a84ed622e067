//内边距
.ant-menu-horizontal:not(.ant-menu-dark)>.ant-menu-submenu {
  padding: 0 !important;
}

//hover选中样式
.ant-menu-horizontal:not(.ant-menu-dark)>.ant-menu-submenu-selected::after {
  border-bottom: 0 !important;
}

//组织切换
.ant-dropdown-menu-title-content {
  .anticon-check {
    margin-left: 24px;
  }
}

//table表头before
.ant-table-thead>tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
  position: static;
}

//g2 list
.g2-tooltip-list {
  max-height: 220px;
  padding-right: 20px !important;
  overflow: auto;
}