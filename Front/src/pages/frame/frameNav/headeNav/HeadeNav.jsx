import { Menu } from 'antd';
import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import './HeadeNav.scss';

class HeadeNav extends Component {
  handleClick = (e) => {
    this.props.history.push(e.key);
  };

  render() {
    const { menuConfig } = this.props;
    let pathName = this.props.location.pathname;
    if (this.props.location.search.indexOf('?src=') !== -1) {
      pathName = `/aimarketer/home/<USER>
    }
    return (
      <div className="headeNavInner">
        <Menu
          selectedKeys={[pathName]}
          mode="horizontal"
          // onClick={this.handleClick}
          items={menuConfig}
        />
      </div>
    );
  }
}

export default withRouter(HeadeNav);
