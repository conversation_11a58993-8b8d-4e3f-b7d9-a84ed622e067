// @import 'assets/css/variable.scss';

.headeNavInner {
  height: 100%;
  width: calc(100% - 40px);

  .navName {
    margin-right: 20px;
    cursor: pointer;
  }

  .ant-menu-horizontal {
    color: #fff;
    border: none;
    background: transparent !important;
    height: 100%;

    .ant-menu-item {
      height: 100%;

      // line-height: $menu_item_lineHeight_horizontal;
      // border:none;
      a {
        color: var(--menu-color);
        line-height: 40px;
        // opacity: 0.8;
      }
    }

    .ant-menu-submenu {
      margin: 0;
    }

    .ant-menu-submenu-title {
      padding: 0 10px;
    }

    .ant-menu-item-selected {

      // background: #232226;
      a {
        color: $primary_color;
      }

      // border:none;
    }

    .ant-menu-item-active {
      a {
        color: $primary_color;
      }
    }

    .ant-menu-submenu-horizontal {
      height: 100%;
      // line-height: $menu_item_lineHeight_horizontal;
    }
  }

  .ant-menu-submenu-selected {
    background: #232226;
    color: #fff;
  }

  .ant-menu-submenu-popup {
    top: 60px !important;
  }
}