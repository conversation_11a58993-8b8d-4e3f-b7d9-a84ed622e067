import React, { Component } from 'react';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Menu } from 'antd';
import { <PERSON>, withRouter } from 'react-router-dom';
import { menuConfig } from '../menuConfig';
import './SideNav.scss';

const { SubMenu } = Menu;

class SideNav extends Component {
  state = {
    selectedNav: ''
  };

  static getDerivedStateFromProps(props) {
    const pathName = props.location.pathname;
    const selectedNav = pathName.split('/').slice(0, 4).join('/');
    return { selectedNav };
  }

  render() {
    const { selectedNav } = this.state;
    return (
      <Menu className="left-menu" mode="inline" theme="dark" onClick={this.selectMenu} selectedKeys={[selectedNav]}>
        {menuConfig.map((item) => {
          if (item.children && item.children.length > 0) {
            return (
              <SubMenu
                key={item.name}
                title={
                  <span className="submenu-title">
                    <LegacyIcon type={item.icon} />
                    {item.name}
                  </span>
                }
              >
                {item.children.map((subItem) => {
                  return (
                    <Menu.Item key={subItem.route.split('/').slice(0, 4).join('/')}>
                      <Link to={subItem.route}>{subItem.name}</Link>
                    </Menu.Item>
                  );
                })}
              </SubMenu>
            );
          } else {
            return (
              <Menu.Item key={item.name}>
                <Link to={item.route}>
                  {item.icon && <LegacyIcon type={item.icon} />}
                  <span>{item.name}</span>
                </Link>
              </Menu.Item>
            );
          }
        })}
      </Menu>
    );
  }
}

export default withRouter(SideNav);
