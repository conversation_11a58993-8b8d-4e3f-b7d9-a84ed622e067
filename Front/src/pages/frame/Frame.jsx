import department from '@/service/department';
import AntPathMatcher from 'ant-path-matcher';
import { Button, Form, Input, Layout, Modal, Result, Spin, message, notification } from 'antd';
import img403 from 'assets/images/403.svg';
import warning from 'assets/images/warning.png';
import classnames from 'classnames';
import dayjs from 'dayjs';
import _ from 'lodash';
import Home from 'pages/home/<USER>';
import { detpToKeyValue, idToName } from 'pages/home/<USER>/dataPermissions/config';
import React, { Component, createRef } from 'react';
import { connect } from 'react-redux';
import { Redirect, Route, Switch } from 'react-router-dom';
import ConfigService from 'service/ConfigService';
import UserService from 'service/UserService';
import MyToDoListService from 'service/myToDoListService';
import _organizationmanageService from 'service/organizationmanageService';
import PermissionService from 'service/permissionService';
import SystemInfoService from 'service/systemInfoService';
import { getDeptId, setDeptId } from 'utils/commonUtils';
import { useStore } from '../../store/globalStore';
import NoProject from '../noProject/index';
import './Frame.scss';
import './antd.scss';
import CooperateManageModal from './cooperateManageModal';
import FrameHeade from './frameHeade/FrameHeade';
import SideNav from './frameNav/sideNav/SideNav';
import ShareModal from './shareModal/index';

const querystring = require('querystring');

const matcher = new AntPathMatcher();

const { Header, Sider, Content } = Layout;
const userService = new UserService();
const configService = new ConfigService();
const permissionService = new PermissionService();

const mapState = (state) => {
  return {
    autologin: state.autologin,
    initLoading: state.initLoading,
    meunData: state.meunData,
    loginUser: state.loginUser,
    authInfo: state.authInfo,
    systemInfo: state.systemInfo
  };
};

// const mapDispatch = (dispatch) => {
//   return {
//     initData: () => {
//       dispatch(init());
//     },
//     backAutolog: () => {
//       dispatch({ type: 'logout' });
//     }
//   };
// };

const firstMenu = '/aimarketer/usercenter/productcenter';
class Frame extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isMenuCollasped: false,
      navType: false, // 导航栏按钮控制，只有两种状态，用bool值控制。true:侧边导航栏，false：头部导航栏
      fullScreen: false, // 局部是否显示头部导航栏
      allFullScreen: false,
      licenseState: 'NORMAL',
      init: false,
      defaultPage: '',
      iconUrl: '',
      name: '',
      pwdModalVisible: false,
      vaildData: {},
      loading: false,
      noFrame: false, // 是否显示头部导航栏
      departmentList: [],
      parentPath: ''
    };
    this.toggle = this.toggle.bind(this);
    this.changeNav = this.changeNav.bind(this);
    this.inUserDepartment = this.inUserDepartment.bind(this);
  }

  componentDidMount() {
    this.init();
    this.getDepartment();
    if (sessionStorage.getItem('loginByToken')) {
      this.setState({ allFullScreen: true });
    }
    const params1 = querystring.parse(window.location.search.substr(1));
    if (params1.fullScreen) {
      this.setState({
        fullScreen: true
      });
    } else if (this.state.fullScreen) {
      this.setState({
        fullScreen: false
      });
    }
    if (params1.noFrame) {
      this.setState({
        noFrame: true
      });
    } else if (this.state.noFrame) {
      this.setState({
        noFrame: false
      });
    }
    this.props.history.listen((history) => {
      // 获取history中的参数，判断是否隐藏header
      const params = querystring.parse(history.search.substr(1));
      if (params.fullScreen) {
        this.setState({
          fullScreen: true
        });
      } else if (this.state.fullScreen) {
        this.setState({
          fullScreen: false
        });
      }
      if (params.noFrame) {
        this.setState({
          noFrame: true
        });
      } else if (this.state.noFrame) {
        this.setState({
          noFrame: false
        });
      }
    });
  }

  componentDidUpdate() {
    const { autologin } = this.props;
    // 发现自动登录不成功，保存当前登录地址，并跳转到登录页面
    if (autologin.code === 1 && !autologin.login && !_.includes(this.props.history.href, '/aimarketer/login')) {
      // this.props.dispatch({ type: 'logout' });
      localStorage.setItem('url', window.location.href);
      // // if (localStorage.getItem('isAutoMessage')) message.error('自动登录失败，请重新登陆', 1);
      // this.props.history.push('/aimarketer/ssologin' || '/aimarketer/login');
      global.__LOGOUT__();
      return null;
    }
    const currentPath = this.props.location.pathname;
    // 自动登录成功之后，查看是否有登录前地址，如果有登录前地址，跳转到登录前地址，并清除
    if (autologin.code === 1 && autologin.login) {
      if (localStorage.getItem('url') && localStorage.getItem('url').includes('/aimarketer')) {
        // 一般记住的都是项目里面的地址，如果用户不存在productId页面会报错，所以需要判断一下
        if (!_.isEmpty(localStorage.getItem('productId'))) {
          window.location.href = localStorage.getItem('url');
        } else if (!_.isEmpty(localStorage.getItem('loginByToken'))) {
          // 海尔判断
          this.props.history.push('/aimarketer/login/loginByToken/tokenLogin');
        } else {
          window.location.href = '/aimarketer/usercenter/productcenter';
        }
        localStorage.removeItem('url');

        return null;
      } else if (
        /^\/aimarketer\/home[/]?$/.test(currentPath) &&
        firstMenu &&
        this.props.location.pathname !== firstMenu
      ) {
        // 当前地址是home且有当前菜单，且当前地址不是第一个菜单地址，就跳转至第一个菜单
        if (!_.isEmpty(localStorage.getItem('loginByToken'))) {
          // 海尔判断
          this.props.history.push('/aimarketer/login/loginByToken/tokenLogin');
        } else if (_.isEmpty(global.__SSOCONFIG__) && !global.__SSOCONFIG__.redirectFlag) {
          this.props.history.push(firstMenu);
        }
        // else if (_.isEmpty(global.__SSOCONFIG__) && _.isEmpty(global.__SSOCONFIG__.requestUrl)) {
        //   this.props.history.push(firstMenu);
        // }
        return null;
      }
    }
  }

  pwdForm = createRef();

  inUserDepartment = (id, userDepartment) => {
    return _.find(userDepartment, { deptId: parseInt(id) });
  };

  getDepartment = async () => {
    if (!localStorage.getItem('organizationId')) return;
    const params = {
      operator: 'EQ',
      propertyName: 'companyId',
      value: localStorage.getItem('organizationId')
    };

    const _departmentList = await department.listBy([params]);
    if (_.isEmpty(_departmentList)) {
      this.props.history.replace(firstMenu);
    }
    // 获取自己所有列表 然后转换成keyValue形式
    const deptKeyValue = detpToKeyValue(_departmentList);
    const userDepartment = await department.userDepartment([
      params,
      {
        operator: 'EQ',
        propertyName: 'userId',
        value: localStorage.getItem('userId')
      }
    ]);

    if (_.isEmpty(userDepartment)) {
      return this.props.history.replace(firstMenu);
    }
    // 这个是所有的部门列表，不是用户自己的部门
    _departmentList && localStorage.setItem('departmentListAll', JSON.stringify(_departmentList));
    window.userDepartment = userDepartment;
    const userId = localStorage.getItem('userId');
    const currentDeptId = getDeptId();
    let defaultId;

    // 获取上次登录的部门ID
    const lastLoginDeptId = userId ? useStore.getState().lastLoginDeptMap[userId] : null;
    const isLastLoginDeptIdValid = this.inUserDepartment(lastLoginDeptId, userDepartment);
    const isCurrentDeptIdValid = this.inUserDepartment(currentDeptId, userDepartment);

    /** 部门ID选择逻辑优先级：
     * 1. 当前部门ID（如果在用户部门列表中）
     * 2. 上次登录的部门ID（如果在用户部门列表中）
     * 3. 默认使用用户部门列表中的第一个部门
     */
    if (currentDeptId && isCurrentDeptIdValid) {
      defaultId = currentDeptId;
    } else if (lastLoginDeptId && isLastLoginDeptIdValid) {
      defaultId = lastLoginDeptId;
    } else {
      defaultId = userDepartment[0]?.deptId;
    }

    // 设置选定的部门ID
    setDeptId(defaultId);

    const nodeValue = _.find(_departmentList, { id: parseInt(defaultId) });
    const routes = `${nodeValue.routes}${nodeValue.id}`;
    if (nodeValue?.id) {
      setDeptId(nodeValue?.id);
    }
    const path = idToName(deptKeyValue, routes);
    localStorage.setItem('deptKeyValue', JSON.stringify(deptKeyValue));
    localStorage.setItem('userDepartment', JSON.stringify(userDepartment));
    this.setState({
      departmentList: userDepartment,
      parentPath: path.join(' / ')
    });

    this.props.dispatch({ type: 'userDepartment', userDepartment });
    // console.log('触发了');
    localStorage.removeItem('ssoDeptId');
  };

  init = async () => {
    let { systemInfo } = this.props;
    if (localStorage.getItem('modifyPassword')) {
      const res = await userService.loginReferenceListBy([
        { operator: 'EQ', propertyName: 'type', value: 'PASSWORD_SAFETY' },
        { operator: 'EQ', propertyName: 'code', value: 'safety.verify' }
      ]);

      this.setState({ vaildData: res[0], pwdModalVisible: true });
    }

    // props中systemInfo为undefined时重新请求系统信息接口
    if (!localStorage.getItem('login')) {
      if (
        systemInfo['login.redirect'] &&
        systemInfo['login.redirect'] !== 'OFF' &&
        !_.isEmpty(global.__SSOCONFIG__) &&
        global.__SSOCONFIG__.redirectFlag
      ) {
        const systemInfos = null;
        if (!systemInfo['login.redirect']) {
          const { dispatch } = this.props;
          const list = await SystemInfoService.getSystemConfig([]);
          systemInfo = list.reduce((obj, n) => {
            obj[n.name] = n.value;
            return obj;
          }, {});
          dispatch({ type: 'systemInfo', systemInfos });
        }
        let { productId, projectId } = systemInfo['login.redirect']
          ? JSON.parse(systemInfo['login.redirect'])
          : JSON.parse(systemInfos['login.redirect']);
        const data = await _organizationmanageService.findAllByUserId();
        let currentOrganization = localStorage.getItem('organizationId');
        if (_.isEmpty(currentOrganization) || !_.find(data, (item) => `${item.id}` === currentOrganization)) {
          currentOrganization = data[0]?.id || '';
          localStorage.setItem('organizationId', currentOrganization);
        }
        let productParam = 0;
        if (projectId === 'lastProject') {
          if (`${productId}` === localStorage.getItem('productId')) {
            projectId = localStorage.getItem('projectId');
            productParam = localStorage.getItem('productId');
          } else {
            productParam = productId;
          }
        } else {
          productParam = productId;
        }

        const reData = await _organizationmanageService.getBizProductProject({
          testStatus: false,
          companyId: localStorage.getItem('organizationId'),
          bizProductId: Number(productParam)
        });

        // 获取产品是否有效
        await _organizationmanageService.getProductValid({
          testStatus: false,
          companyId: localStorage.getItem('organizationId'),
          bizProductId: Number(productParam)
        });

        const { defaultPageAuthMenu, projectList } = reData;
        localStorage.setItem('defaultUrl', defaultPageAuthMenu.route);

        if (projectId === 'lastProject' && `${productId}` !== localStorage.getItem('productId')) {
          projectList ? (projectId = projectList[0]?.id) : (projectId = '');
        }

        setTimeout(() => {
          if (_.find(projectList, (item) => `${item.id}` === `${projectId}`)) {
            localStorage.setItem('productId', productId);
            localStorage.setItem('projectId', projectId);
            localStorage.setItem('login', true);
            this.props.history.push(defaultPageAuthMenu.route);
          }
        }, 1000);
      }
    }

    // 检查授权是否到期
    try {
      const licenseInfo = await userService.getLicense();
      if (!_.isEmpty(licenseInfo) && licenseInfo.dueTime) {
        const dueTime = dayjs(licenseInfo.dueTime);
        const currentTime = dayjs(licenseInfo.currentTime);
        if (dueTime.diff(currentTime, 'days') <= 0) {
          // 如果返回结果是过期
          this.setState({
            licenseState: 'EXPIRE'
          });
          return;
        } else if (dueTime.diff(currentTime, 'days') <= 30) {
          // 如果是即将过期
          this.setState({
            licenseState: 'ABOUTTOEXPIRE'
          });

          const args = {
            message: '授权即将过期提醒',
            description: `Datatist智能营销云系统使用授权将于${dueTime.format(
              'YYYY-MM-DD'
            )}到期，授权到期将影响系统正常使用，请联系客户经理及时续期`,
            duration: 0
          };
          notification.warn(args);
        }
      }

      // 如果是即将过期
    } catch (error) {
      console.error(error);
    }
    if (!this.props.autologin.login) {
      let autologin;
      try {
        autologin = await userService.autoLogin(); // 自动登录，并将数据保存在store中
        this.props.dispatch({ type: 'autologin', autologin });
        if (!autologin.userId) return; // 自动登录失败后，退出初始化
      } catch (error) {
        this.props.dispatch({ type: 'autologin', autologin: {} });
        return;
      }
    }

    // 根据产品id获取产品信息
    // let projectId = await configService.listMyProjects();
    let systemNullInfos = null;
    if (!systemInfo['login.redirect'] || !systemInfo['token.login.defaultCompanyId']) {
      const list = await SystemInfoService.getSystemConfig([]);
      systemNullInfos = list.reduce((obj, n) => {
        obj[n.name] = n.value;
        return obj;
      }, {});
    }
    // let { productId, projectId } = systemInfo['login.redirect'] ? JSON.parse(systemInfo['login.redirect']) : JSON.parse(systemNullInfos['login.redirect']);
    let projectId;
    let productId;
    const info = systemInfo['login.redirect']
      ? systemInfo['login.redirect'] === 'OFF'
        ? systemInfo['login.redirect']
        : JSON.parse(systemInfo['login.redirect'])
      : JSON.parse(systemNullInfos['login.redirect']);
    if (info !== 'OFF') {
      projectId = info.projectId;
      productId = info.productId;
    }
    const loginType = JSON.parse(sessionStorage.getItem('loginByToken'));
    const productInfo = await _organizationmanageService.getBizProductProject({
      bizProductId: loginType ? productId : Number(localStorage.getItem('productId')) || productId,
      // bizProductId: productId,
      companyId: loginType
        ? systemInfo['token.login.defaultCompanyId']
          ? JSON.parse(systemInfo['token.login.defaultCompanyId'])
          : JSON.parse(systemNullInfos['token.login.defaultCompanyId'])
        : localStorage.getItem('organizationId'),
      testStatus: false
    });

    // 获取产品是否有效
    await _organizationmanageService.getProductValid({
      bizProductId: loginType ? productId : Number(localStorage.getItem('productId')) || productId,
      // bizProductId: productId,
      companyId: loginType
        ? systemInfo['token.login.defaultCompanyId']
          ? JSON.parse(systemInfo['token.login.defaultCompanyId'])
          : JSON.parse(systemNullInfos['token.login.defaultCompanyId'])
        : localStorage.getItem('organizationId'),
      testStatus: false
    });
    // let projectId = [];
    const { projectList = [], defaultPage, iconUrl, name } = productInfo;
    let loginUser = {};
    let meunData = [];
    let superAdmin = false;
    let action = { actionUrls: [] };
    let userList = [];

    if (projectList?.length > 0) {
      // 首先获取url的state里是否有项目id，如果有以state里的为准
      // const { state = {} } = this.props.location;
      // const _projectId = state.projectId || localStorage.getItem('projectId');
      // if (!_projectId || projectList.findIndex(n => n.id === _projectId) === -1) {
      //   localStorage.setItem('projectId', projectList[0].id);
      // } else {
      //   localStorage.setItem('projectId', _projectId);
      // }
      const targetProject = localStorage.getItem('projectId');
      if (loginType) {
        if (_.find(projectList, (item) => `${item.id}` === `${projectId}`)) {
          localStorage.setItem('projectId', _.find(projectList, (item) => `${item.id}` === `${projectId}`).id);
        } else {
          localStorage.setItem('projectId', projectList[0].id);
        }
      } else {
        if (!_.find(projectList, (item) => `${item.id}` === `${targetProject}`)) {
          localStorage.setItem('projectId', projectList[0].id);
        }
      }

      const reData = await Promise.all([
        userService.getCurrentUser(),
        configService.getMenuConfig({
          bizProductId: localStorage.getItem('productId')
        }),
        permissionService.reqIsSuperAdmin(),
        permissionService.getActionGroups(),
        userService.findAllName('')
      ]);

      const approvalTypeRes = await MyToDoListService.getProcessAuthority({
        projectId: localStorage.getItem('projectId'),
        loginId: localStorage.getItem('userId')
      });

      const processType = [];
      approvalTypeRes.forEach((item) => {
        if (item.flag) {
          processType.push(item.type);
        }
      });

      const param = {
        page: 1,
        size: 10,
        search: [
          {
            operator: 'EQ',
            propertyName: 'projectId',
            value: localStorage.getItem('projectId')
          },
          { operator: 'NE', propertyName: 'promoterId', value: localStorage.getItem('userId') },
          { operator: 'EQ', propertyName: 'status', value: 'RUNNING' },
          {
            operator: 'IN',
            propertyName: 'contentType',
            value: processType.join(',')
          },
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: window.getDeptId()
          },
          {
            operator: 'NE',
            propertyName: 'approvalType',
            value: 'ACTIVITI'
          }
        ],
        sorts: [{ propertyName: 'createTime', direction: 'desc' }]
      };

      if (systemInfo['sys.approval.v2.switch'] === 'ALL' || systemInfo['sys.approval.v2.switch'] === 'APPROVAL') {
        const result = await Promise.all([
          await MyToDoListService.acitvityQuery({
            companyId: Number(localStorage.getItem('organizationId')),
            isSuperAdmin: reData[2]
          }),
          await MyToDoListService.query2(param)
        ]);

        this.props.dispatch({
          type: 'messageInfo',
          messageInfo: {
            ...this.props.messageInfo,
            toToListV2: result[0].dataList.length,
            toDoList: _.isEmpty(processType) ? 0 : result[1].totalElements
          }
        });
      } else {
        const result = await Promise.all([await MyToDoListService.query2(param)]);

        this.props.dispatch({
          type: 'messageInfo',
          messageInfo: {
            ...this.props.messageInfo,
            toToListV2: 0,
            toDoList: _.isEmpty(processType) ? 0 : result[0].totalElements
          }
        });
      }

      loginUser = reData[0];
      meunData = reData[1]; // 初始化菜单
      // debugger;
      superAdmin = reData[2];
      action = reData[3];
      userList = reData[4];
    } else {
      localStorage.setItem('projectId', '');
      if (
        !_.isEmpty(global.__SSOCONFIG__) &&
        global.__SSOCONFIG__.redirectFlag &&
        localStorage.getItem('env') === 'SW'
      ) {
        localStorage.removeItem('aim_authorization');
        localStorage.removeItem('productId');
      } else if (
        !global.__SSOCONFIG__.autoJoinCompany ||
        !global.__SSOCONFIG__.autoAccessProject ||
        !global.__SSOCONFIG__.autoJoinProject
      ) {
        this.props.history.push(firstMenu);
      } else {
        message.error('您当前没有项目，请新建项目');
        throw new Error('您当前没有项目，请新建项目');
      }
    }

    global.__AUTHINFO__ = { superAdmin, codeList: action.actionGroups };

    if (localStorage.getItem('projectId') && localStorage.getItem('projectId') !== '') {
      const roleIdRes = await userService.getRoleByProjectId({
        projectId: localStorage.getItem('projectId')
      });

      localStorage.setItem('superAdmin', superAdmin);
      localStorage.setItem('roleId', JSON.stringify(roleIdRes.id));
    }

    this.props.dispatch({ type: 'meunData', meunData });
    this.props.dispatch({ type: 'loginUser', loginUser });
    this.props.dispatch({ type: 'userList', userList });
    this.props.dispatch({ type: 'projectList', projectList });
    this.props.dispatch({
      type: 'authInfo',
      payload: {
        superAdmin,
        codeList: action.actionGroups,
        urlList: ['/aimarketer/home/<USER>/personSetting', ...action.actionUrls]
      }
    });
    this.props.dispatch({ type: 'initLoading', initLoading: false }); // 必须放最后一行
    this.setState({ init: true, defaultPage, iconUrl, name });
  };

  handleOk = async () => {
    try {
      const baseParams = await this.pwdForm.current.validateFields();

      await _organizationmanageService.modifyPassWord(baseParams);
      localStorage.removeItem('modifyPassWord');
      message.success('修改密码成功');
      global.__LOGOUT__();
    } catch (e) {
      console.error(e);
    }
  };

  compareToFirstPassword = (rule, value, callback) => {
    if (value && value !== this.pwdForm.current.getFieldValue('pwd')) {
      callback('两次输入的密码不相同!');
    } else {
      callback();
    }
  };

  toggle() {
    const { isMenuCollasped } = this.state;
    this.setState({ isMenuCollasped: !isMenuCollasped });
  }

  changeNav() {
    const { navType } = this.state;
    this.setState({ navType: !navType });
  }

  render() {
    const { isMenuCollasped, navType, licenseState, parentPath, departmentList } = this.state;

    const checkAuth = () => {
      const {
        authInfo: { urlList, superAdmin },
        location
      } = this.props;

      // todo 路由权限三种情况：1.是超级管理员; 2.路由不匹配; 3.后端是否放开权限
      const result =
        superAdmin ||
        urlList.some(
          (n) => matcher.match(n, _.trimEnd(location.pathname, '/')) || n === `${location.pathname}${location.search}`
        ) ||
        urlList.some((n) => n === '__ALL__');
      if (!localStorage.getItem('projectId')) {
        return <NoProject />;
      }
      if ((window.getDeptId() && superAdmin) || result) {
        return <Home />;
      }
      return (
        <div
          style={{
            height: 'calc(100vh - 60px)',
            backgroundColor: '#fff',
            marginLeft: -24,
            marginRight: -24
          }}
        >
          <Result
            status="info"
            title="403"
            icon={<img src={img403} alt="403" />}
            subTitle="对不起, 您无权限访问当前页面，请联系管理员."
            extra={[
              <Button
                key="back"
                onClick={() => {
                  this.props.history.replace('/aimarketer/usercenter/productcenter');
                }}
              >
                返回首页
              </Button>
            ]}
          />
        </div>
      );
    };
    if (licenseState === 'EXPIRE') {
      return (
        <div className="noLicense">
          <div className="content">
            <img src={warning} alt="无授权警告" />
            <span>Datatist智能营销云系统使用授权已经到期，授权到期将影响系统正常使用，请联系客户经理及时续期</span>
          </div>
        </div>
      );
    }
    const { autologin, initLoading, meunData, loginUser } = this.props;

    if (autologin.code === 0) return <Spin className="initLoading" tip="正在检验是否登录" />; // 首次获取登录信息，为发送完请求时，增加loading
    // const firstMenu = localStorage.getItem('projectId') ? meunData?.menus?.main[0]?.children[0]?.route : '/aimarketer/home/<USER>';
    // const firstMenu = '/aimarketer/home/<USER>';

    // 初始化数据完成，取消初始化数据loading
    if (initLoading) return <Spin className="initLoading" tip="正在初始化数据" />;

    // if (autologin.code === 1 && autologin.login && localStorage.getItem('url')) {
    //   this.props.history.push(localStorage.getItem('url'));
    //   localStorage.removeItem('url');
    //   return null;
    // }

    return (
      <Layout
        id="datatist-wolf-app"
        className={classnames({
          // 这里可以根据各属性动态添加，如果属性值为true则为其添加该类名，如果值为false，则不添加。这样达到了动态添加class的目的
          app: true,
          'app-full': process.env.IS_FULL || this.state.noFrame
        })}
      >
        {!this.state.allFullScreen ? (
          this.state.fullScreen ? null : (
            <Header className="frame-header">
              <FrameHeade
                collapsed={isMenuCollasped}
                toggle={this.toggle}
                navType={navType}
                changeNav={this.changeNav}
                meunData={meunData.menus}
                loginUser={loginUser}
                defaultPage={this.state.defaultPage}
                iconUrl={this.state.iconUrl}
                name={this.state.name}
                departmentList={departmentList}
                parentPath={parentPath}
                getDepartment={this.getDepartment}
              />
            </Header>
          )
        ) : null}

        <Layout className="app-bottom-layout">
          {navType && (
            <Sider trigger={null} collapsible collapsed={isMenuCollasped} width="180">
              <SideNav toggle={this.toggle} />
            </Sider>
          )}

          <Content
            className={`frame-content ${
              this.state.fullScreen || this.state.allFullScreen ? 'frame-content-no-margin-top' : ''
            }`}
          >
            {this.state.init ? (
              <Switch>
                {!_.isEmpty(global.__SSOCONFIG__) && global.__SSOCONFIG__.redirectFlag ? (
                  <Redirect
                    exact
                    from="/aimarketer/home"
                    to={localStorage.getItem('defaultUrl') || '/aimarketer/ssologin' || '/aimarketer/login'}
                  />
                ) : (
                  <Redirect
                    exact
                    from="/aimarketer/home"
                    to={firstMenu || '/aimarketer/ssologin' || '/aimarketer/login'}
                  />
                )}
                {/* <Redirect exact from="/aimarketer/home" to={firstMenu || '/aimarketer/ssologin' || '/aimarketer/login'} /> */}
                <Route path="/aimarketer/home" render={checkAuth} />
              </Switch>
            ) : (
              <Spin tip="Loading...">
                <div className="loadingPlace" />{' '}
              </Spin>
            )}
            {/* <Home /> */}
          </Content>
        </Layout>
        <Modal
          title="修改密码"
          open={this.state.pwdModalVisible}
          maskClosable={null}
          keyboard={false}
          closable={false}
          footer={
            <Button type="primary" onClick={this.handleOk}>
              确定
            </Button>
          }
          cancelButtonProps={false}
          confirmLoading={this.state.loading}
        >
          <Form labelCol={{ span: 6 }} wrapperCol={{ span: 14 }} ref={this.pwdForm}>
            <Form.Item label="原密码" name="originalPwd" rules={[{ required: true, message: '请输入原密码' }]}>
              <Input.Password placeholder="请输入原密码" />
            </Form.Item>
            <Form.Item
              label="新密码"
              name="pwd"
              rules={[
                { required: true, message: '请输入新密码' },
                {
                  pattern:
                    this.state.vaildData?.value === 'ON'
                      ? new RegExp(
                          `^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*()\\-_=+{};:,<.>])(?!.*[\u4E00-\u9FA5])(?!.*\\s).{${this.state.vaildData?.config.minLength},24}$`
                        )
                      : /^[0-9A-Za-z\\W_=?.。~!@#$%^&*()-=]{6,32}$/,
                  message:
                    this.state.vaildData?.value === 'ON'
                      ? `密码必须包含大写字母、小写字母、数字和特殊字符，长度大于${this.state.vaildData?.config.minLength}个字符`
                      : '必须为6-32位的数字字母或特殊字符'
                },
                this.state.vaildData?.value === 'ON' && {
                  validator: async (_, inputValue) => {
                    try {
                      if (
                        inputValue === loginUser.email ||
                        inputValue === loginUser.mobile ||
                        inputValue === loginUser.jobNo
                      ) {
                        return Promise.reject(new Error('密码不能和登录账号（邮箱/手机号/员工号）相同'));
                      } else {
                        return Promise.resolve();
                      }
                    } catch (error) {
                      return Promise.reject(new Error('请求错误'));
                    }
                  }
                }
              ]}
            >
              <Input.Password placeholder="请输入新密码" />
            </Form.Item>
            <Form.Item
              label="确认新密码"
              name="confirmPwd"
              rules={[{ required: true, message: '请输入确认新密码' }, { validator: this.compareToFirstPassword }]}
            >
              <Input.Password placeholder="请输入确认新密码" />
            </Form.Item>
          </Form>
        </Modal>

        <ShareModal />
        <CooperateManageModal />
      </Layout>
    );
  }
}

export default connect(mapState)(Frame);
