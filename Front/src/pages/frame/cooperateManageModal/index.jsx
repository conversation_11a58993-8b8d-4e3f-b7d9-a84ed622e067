import { useStore } from '@/store/share';
import { Button, Dropdown, Form, Input, Modal, Popover, Select, Space, Table, Tag, Tooltip, message } from 'antd';
import React, { useEffect, useState } from 'react';

import shareService from '@/service/shareService';

import { getDeptPath } from '@/pages/home/<USER>/dataPermissions/config';
import { calcPageNo, getCurrentPageRolesList } from '@/utils/universal';
import { DownOutlined, SearchOutlined } from '@ant-design/icons';
import _ from 'lodash';
import './index.scss';

const { Option } = Select;
const mapArr = ['aim_segment_edit', 'aim_campaignV2_edit'];

const pagination = {
  showTotal: (totals) => `共 ${totals} 条`,
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['5', '10', '20', '50', '100']
};

const CooperateManageModal = () => {
  const { cooperateOpen, dispatchShare, shareInfo, shareOpen, reflash } = useStore();

  const [userList, setUserList] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectRowInfo, setSelectRowInfo] = useState([]);
  const [editOpen, setEditOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [rolesList, setRolesList] = useState([]);
  const [editType, setEditType] = useState('');
  const [editUserInfo, setEditUserInfo] = useState({});
  const [param, setParam] = useState({
    page: 1,
    search: [],
    size: 10,
    sorts: [{ propertyName: 'createTime', direction: 'desc' }]
  });

  const [form] = Form.useForm();

  useEffect(() => {
    const init = async () => {
      try {
        const rolesRes = await shareService.getActionGroupByRole({
          id: Number(localStorage.getItem('roleId'))
        });

        let rolesResult = getCurrentPageRolesList(shareInfo.type, rolesRes) || [];

        rolesResult = _.map(rolesResult, (item) => {
          if (_.includes(mapArr, item.code)) {
            return {
              ...item,
              name: '复制'
            };
          }
          return item;
        });

        // todo 本次只做查看
        rolesResult = rolesResult.filter(
          (item) =>
            item.code === 'aim_segment_view' ||
            item.code === 'aim_campaignV2_view' ||
            item.code === 'aim_campaigns_view' ||
            item.code === 'aim_segment_edit' ||
            item.code === 'aim_campaignV2_edit'
        );
        setRolesList(rolesResult);
      } catch (error) {
        console.error(error);
      }
    };
    cooperateOpen && init();
  }, [cooperateOpen]);

  useEffect(() => {
    const getData = async () => {
      setLoading(true);
      try {
        const _param = _.cloneDeep(param);
        _param.search = [
          ...param.search,
          {
            propertyName: 'shareContentId',
            operator: 'EQ',
            value: shareInfo?.id
          },
          {
            propertyName: 'type',
            operator: 'EQ',
            value: shareInfo?.type
          }
        ];

        const res = await shareService.query(_param);

        pagination.total = res.totalElements;
        pagination.current = param.page;
        pagination.pageSize = param.size;

        setUserList(res.content);

        setLoading(false);
      } catch (error) {
        setLoading(false);
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    cooperateOpen && getData();
  }, [cooperateOpen, param, reflash]);

  const columns = [
    {
      title: '协作者',
      dataIndex: 'grantShareUserName',
      key: 'grantShareUserName',
      width: 284,
      render: (text, render) => (
        <div>
          <Tooltip title={text} placement="topLeft">
            <div className="overflow-hidden text-ellipsis whitespace-nowrap">{text}</div>
          </Tooltip>

          <Tooltip title={getDeptPath(render.deptId)} placement="topLeft">
            <div className="text-[rgba(0,0,0,.45)] overflow-hidden text-ellipsis whitespace-nowrap text-[12px]">
              {getDeptPath(render.deptId)}
            </div>
          </Tooltip>
        </div>
      )
    },
    {
      title: '操作权限',
      dataIndex: 'shareRoleAuthVoList',
      key: 'shareRoleAuthVoList',
      width: 284,
      render: (text, record) => {
        if (text) {
          const notNullData = _.map(
            record.shareRoleAuthVoList.filter((item) => item),
            (item) => {
              if (_.includes(mapArr, item.authId)) {
                return {
                  ...item,
                  authName: '复制'
                };
              }
              return item;
            }
          );
          if (notNullData.length > 3) {
            const threeOnes = _.slice(notNullData, 0, 3);
            threeOnes.push({ authId: 'x', authName: '...' });
            return (
              <Popover
                overlayStyle={{ maxWidth: 640 }}
                trigger="click"
                overlayClassName="cooperateMorePop"
                placement="top"
                title="操作权限"
                content={_.map(notNullData, (item) => (
                  <Tag key={item.authId} className="mb-[8px]">
                    {item.authName}
                  </Tag>
                ))}
              >
                <div>
                  {_.map(threeOnes, (item) => (
                    <Tag key={item.authId}>{item.authName}</Tag>
                  ))}
                </div>
              </Popover>
            );
          }
          return (
            <div>
              {_.map(notNullData, (item) => (
                <Tag key={item.authId}>{item.authName}</Tag>
              ))}
            </div>
          );
        }
      }
    },
    {
      title: '操作',
      dataIndex: 'operter',
      key: 'operter',
      width: 132,
      render: (text, render) => (
        <div>
          <a className="mr-[16px]" onClick={() => onMultiEditChange('option', render)}>
            编辑权限
          </a>
          <a onClick={() => onMultiDeleteChange('option', render)}>移除</a>
        </div>
      )
    }
  ];

  const handleTableChange = (lastpagination, filtersArg, sorter) => {
    param.page = lastpagination.current;
    param.size = lastpagination.pageSize;
    if (sorter.field) {
      param.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setParam({ ...param });
  };

  const onCancel = () => {
    setSelectedRowKeys([]);
    setSelectRowInfo([]);
    setParam({ ...param, search: [] });
    dispatchShare({ cooperateOpen: false });
  };

  const onMultiEditChange = (type, render) => {
    if (type === 'multi' && _.isEmpty(selectedRowKeys)) {
      message.warning('请先选择要批量操作的协作者');
      return;
    }

    if (type === 'multi' && selectedRowKeys.length === 1) {
      form.setFieldsValue({
        roles: selectRowInfo[0].shareRoleAuthVoList.map((item) => item.authId)
      });
    } else if (type === 'option') {
      setEditUserInfo(render);
      form.setFieldsValue({
        roles: render.shareRoleAuthVoList.map((item) => item.authId)
      });
    }

    setEditType(type);
    setEditOpen(true);
  };

  const onEditCancel = () => {
    form.setFieldsValue({ roles: undefined });
    setEditOpen(false);
  };

  const onMultiEditOk = async () => {
    try {
      const res = await form.validateFields();

      let saveParams = [];

      if (editType === 'multi') {
        saveParams = selectRowInfo.map((item) => {
          return {
            authIds: res.roles.join(','),
            id: item.id
          };
        });
      } else {
        saveParams = [
          {
            id: editUserInfo.id,
            authIds: res.roles.join(',')
          }
        ];
      }

      await shareService.editSave(saveParams);

      message.success('编辑成功');
      setParam({ ...param });
      setSelectedRowKeys([]);
      setSelectRowInfo([]);
      setEditUserInfo({});

      onEditCancel();
    } catch (error) {
      console.error(error);
    }
  };

  const onMultiDeleteChange = (type, render) => {
    if (type === 'multi' && _.isEmpty(selectedRowKeys)) {
      message.warning('请先选择要批量操作的协作者');
      return;
    }

    setEditUserInfo(render);

    Modal.confirm({
      title: '移除',
      className: 'shareDelModal',
      width: 480,
      centered: true,
      content: (
        <div className="text-[rgba(0,0,0,.65)]">
          <div>
            您将移除：
            {type === 'multi'
              ? selectRowInfo.map((item) => item.grantShareUserName).join('、')
              : render.grantShareUserName}
          </div>
          <div className="mt-[8px]">移除后，成员将不能继续协作管理内容。</div>
        </div>
      ),
      okText: '确认移除',
      okType: 'primary',
      cancelText: '取消',
      async onOk() {
        try {
          type === 'multi' ? await shareService.deleteByIds(selectedRowKeys) : await shareService.deleteById(render.id);

          let page;
          if (type === 'multi' && selectedRowKeys.length !== 1) {
            let total = _.cloneDeep(pagination.total);
            selectedRowKeys.forEach(() => {
              total -= 1;
              page = calcPageNo(total, pagination.current, pagination.pageSize);
            });
          } else {
            page = calcPageNo(pagination.total, pagination.current, pagination.pageSize);
          }

          setParam({ ...param, page });
          setSelectedRowKeys([]);
          setSelectRowInfo([]);
          setEditUserInfo({});
          message.success('移除成功');
        } catch {
          message.success('移除失败');
        }
      },
      onCancel() {}
    });
  };

  const items = [
    {
      label: '批量编辑权限',
      onClick: (e) => onMultiEditChange('multi', e),
      key: 'multiEdit'
    },
    {
      label: '批量移除',
      onClick: (e) => onMultiDeleteChange('multi', e),
      key: 'multiDelete'
    }
  ];

  const addCooperateUser = () => {
    if (shareOpen) {
      dispatchShare({
        cooperateOpen: false,
        shareInfo: { type: shareInfo.type, id: shareInfo.id }
      });
    } else {
      dispatchShare({
        shareOpen: true,
        shareInfo: { type: shareInfo.type, id: shareInfo.id }
      });
    }
  };

  const onTableSelectChange = (newSelectedRowKeys, node) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectRowInfo(node);
  };

  const rowSelection = {
    selectedRowKeys,
    preserveSelectedRowKeys: true,
    onChange: onTableSelectChange
  };

  const onCooperateSearch = (val) => {
    setParam({
      ...param,
      page: 1,
      search: [
        {
          propertyName: 'user.name',
          operator: 'LIKE',
          value: val
        }
      ]
    });
  };

  return (
    <>
      <Modal
        confirmLoading={loading}
        open={cooperateOpen}
        onCancel={onCancel}
        footer={null}
        className="cooperateOpenModal"
        destroyOnClose
        width={800}
        title="协作管理"
      >
        <div>
          <div className="flex items-center justify-between mb-[16px]">
            <div className="flex items-center">
              <span className="text-[16px] font-[600] mr-[16px]">协作者列表</span>
              {selectedRowKeys.length ? (
                <span className="text-[rgba(0,0,0,.65)]">已选择 {selectedRowKeys.length} 项</span>
              ) : null}
            </div>

            <div className="flex">
              <Input
                placeholder="搜索协作者"
                className="mr-[16px] w-[264px]"
                onChange={(e) => onCooperateSearch(e.target.value)}
                suffix={<SearchOutlined className="text-[rgba(0,0,0,.65)]" />}
              />
              <Dropdown menu={{ items }}>
                <Button>
                  <Space>
                    批量操作 <DownOutlined />
                  </Space>
                </Button>
              </Dropdown>
              <Button type="primary" className="ml-[8px]" onClick={addCooperateUser}>
                添加协作者
              </Button>
            </div>
          </div>

          <div>
            <Table
              loading={loading}
              rowKey="id"
              columns={columns}
              pagination={pagination}
              dataSource={userList}
              rowSelection={rowSelection}
              onChange={handleTableChange}
              scroll={{
                y: 'calc(100vh - 431px)'
              }}
            />
          </div>
        </div>
      </Modal>

      <Modal
        open={editOpen}
        className="cooperateOpenModal"
        title="编辑权限"
        zIndex={99999}
        centered
        width={560}
        onOk={onMultiEditOk}
        onCancel={onEditCancel}
      >
        <Form form={form} layout="vertical">
          <Form.Item label="操作权限" rules={[{ required: true }]} name="roles">
            <Select
              mode="multiple"
              placeholder="请选择"
              allowClear
              dropdownStyle={{ zIndex: 99999 }}
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().includes(input.toLowerCase())}
            >
              {rolesList.map((item) => (
                <Option key={item.id} value={item.code}>
                  {item.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default CooperateManageModal;
