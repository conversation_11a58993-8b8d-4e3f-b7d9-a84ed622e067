// @ts-nocheck
import { transformUrl } from '@/utils/universal';
import { Button } from 'antd';
import { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import OrganizationManageService from 'service/organizationmanageService';

const ExpiredPage = () => {
  const history = useHistory();

  const [productInfo, setProductInfo] = useState({});

  useEffect(() => {
    const init = async () => {
      const reData = await OrganizationManageService.getBizProductProject({
        testStatus: false,
        companyId: localStorage.getItem('organizationId'),
        bizProductId: localStorage.getItem('productId')
      });

      setProductInfo(reData);
    };
    init();
  }, []);
  return (
    <div className="flex justify-center items-center h-screen">
      <div className="flex flex-col items-center">
        <img src={transformUrl(productInfo?.pictureUrl) || ''} alt="" className="w-[200px] h-[200px] mb-20" />
        <div className="text-24 mb-8">{productInfo.name}已经到期</div>
        <div className="text-[rgba(0,0,0,0.45)] mb-24">您没有权限访问该页面，请联系管理员</div>
        <Button
          type="primary"
          className="w-[116px] rounded-[6px]"
          onClick={() => {
            history.push('/aimarketer/usercenter/productcenter');
          }}
        >
          返回产品中心
        </Button>
      </div>
    </div>
  );
};

export default ExpiredPage;
