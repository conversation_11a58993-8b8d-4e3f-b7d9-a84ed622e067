/*** color config ***/
/* common*/
$primary_color: #3F7DE5;
$menu_bg_color: #3F7DE5;
$avater_bg_color: #fff;
$avater_color: #3F7DE5;
$active_color: #f0f8ff;
$menu_active_color: #2c5dbf;
$menu_title_active: #fff;
$menu_title_hover: #fff;
$menu_color: #a9c5f8;
$menu_hover_color: #fff;
$menu_buttom_color: #0632a1;
$white: #fff;
$border_color: #e4e4e4;
$table_head_bg: #ECF7FE;
$border_hover: #4B8EFA;
$tag_bg: #F0F8FF;
$tag_border: #9ECAFF;

/*nav*/
$app_header: 60px;

/*menu垂直菜单变量*/
$menu_bg: #69737F;
// $menu_color: #a7b1c2;
$menu_w: 180px;
$menu_mini_w: 80px;

%menu_hover {
    background-color: #212e3a;
    color: #fff;
}

// %menu_selected {
//     background: $white;
//     color: $primary_color;
// }

%menu_selected {
    background: $menu_active_color;
    color: $white;
}

%menu_item_height {
    height: 46px;
    line-height: 46px;
}

// 水平菜单变量
$menu_height: 60px;
$menu_item_lineHeight_horizontal: 60px;

/**content*/
$content_bg: #f4f4f4;

%color_gray {
    color: rgba(0, 0, 0, 0.45);
}