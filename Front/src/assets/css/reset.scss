html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Noto Sans" , "Noto Sans CJK SC" , "Microsoft YaHei" , "微软雅黑", "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans",
    "Droid Sans", "Helvetica Neue", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
  // overflow: hidden;
}
// 修改滚动条样式 IE
// body {
//     scrollbar-arrow-color:#f2f2f3;  /*上下箭头*/
//     scrollbar-track-color:#1589ce;  /*底层背景色*/
//     scrollbar-face-color:#27aeff;   /*滚动条前景色*/
//     scrollbar-Shadow-color:#1589ce; /*滚动条边线色*/
// }
// // 修改滚动条样式 Google
// body::-webkit-scrollbar {width:10px; height:10px; background-color:transparent;} /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
// body::-webkit-scrollbar-track {background-color:#ccc; border-radius:10px; -webkit-box-shadow:inset 0 0 6px rgba(0,0,0,0.3);} /*定义滚动条轨道 内阴影+圆角*/
// body::-webkit-scrollbar-thumb {background-color:#555; border-radius:10px; -webkit-box-shadow:inset 0 0 6px rgba(0,0,0,.3);} /*定义滑块 内阴影+圆角*/

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New", monospace;
}

body > #root {
  width: 100%;
}

.ant-calendar-picker-icon {
  z-index: 0;
}

// .ant-dropdown-menu-item:hover,.ant-dropdown-menu-submenu-title:hover,.ant-select-item-option-active {
//   background-color: $active_color !important;
// }

.ant-tree.ant-tree-directory {
  .ant-tree-treenode:not(.ant-tree-treenode-selected):hover::before {
    background-color: $active_color !important;
  }
}

.ant-drawer-title {
  font-weight: 600 !important;
}
.ant-drawer-close {
  position: absolute;
  right: 0;
}

.icon {
  width: 1em; height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
