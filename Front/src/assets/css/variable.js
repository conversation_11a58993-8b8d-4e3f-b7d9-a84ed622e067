const $primary_color = process.env.IS_GF ? '#3F7DE5' : process.env.IS_SW ? '#2E78EA' : '#ff6800';
const $white = '#fff';
const $border_color = '#e4e4e4';
const $table_head_bg = '#ECF7FE';
const $border_hover = process.env.IS_GF ? '#4B8EFA' : process.env.IS_SW ? '#599EF7' : '#FF8629';
const $active_color = process.env.IS_GF ? '#f0f8ff' : process.env.IS_SW ? '#F0FAFF' : '#fff4e6';

/* nav */
const $app_header = '60px';

/* menu */
const $menu_bg = '#69737F';
const $menu_color = '#a7b1c2';
const $menu_w = '180px';
const $menu_mini_w = '80px';
const menu_hover = {
  background_color: '#212e3a',
  color: '#fff'
};
const menu_selected = {
  background: $white,
  color: $primary_color
};
const menu_item_height = {
  height: '46px',
  line_height: '46px'
};

/** content */
const $content_bg = '#f4f4f4';

export {
  $primary_color,
  $white,
  $border_color,
  $table_head_bg,
  $app_header,
  $menu_bg,
  $menu_color,
  $menu_w,
  $menu_mini_w,
  menu_hover,
  menu_selected,
  menu_item_height,
  $content_bg,
  $border_hover,
  $active_color
};
