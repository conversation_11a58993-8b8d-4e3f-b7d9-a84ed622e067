/*** color config ***/

/* common*/
$primary_color: var(--ant-primary-color);
$menu_bg_color: var(--header-bg-color);
$avater_bg_color: var(--avatar-bg-color);
$avater_color: var(--avatar-color);
$active_color: var(--ant-primary-1);
$menu_active_color: var(--menu-hover-bg);
$menu_title_active: var(--menu-hover-color);
$menu_hover_color: var(--menu-hover-color);
$menu_buttom_color: var(--ant-primary-color);
$white: #fff;
$border_color: #e4e4e4;
$table_head_bg: #ECF7FE;
$border_hover: var(--ant-primary-color-hover);
$tag_bg: var(--ant-primary-1);
$tag_border: var(--ant-primary-2);

/*nav*/
$app_header: 60px;

/*menu垂直菜单变量*/
$menu_bg: #69737F;
$menu_color: var(--menu-color);
$menu_w: 180px;
$menu_mini_w: 80px;

%menu_hover {
    background-color: #212e3a;
    color: #fff;
}

%menu_selected {
    background: $white;
    color: $primary_color;
}

%menu_item_height {
    height: 46px;
    line-height: 46px;
}

// 水平菜单变量
$menu_height: 60px;
$menu_item_lineHeight_horizontal: 60px;

/**content*/
$content_bg: #f4f4f4;

%color_gray {
    color: rgba(0, 0, 0, 0.45);
}