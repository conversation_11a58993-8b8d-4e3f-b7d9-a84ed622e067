.ant-drawer-content-wrapper{

}
.drawer {
  .ant-drawer-wrapper-body{
    height: calc(100% - 55px);
  }
  .ant-drawer-content{
    overflow-y: hidden;
  }
  .ant-drawer-body {
    padding-bottom: 55px;
    padding-top: 8px;
    height: calc(100% - 55px)!important;
    overflow-y: auto;
    .ant-alert {
      padding-top: 3px;
      padding-bottom: 3px;
      margin-bottom: 16px;
      span {
        font-size: 12px;
      }
      i.ant-alert-icon {
        top: 7px;
        left: 12px;
      }
    }

    footer {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      padding: 0 24px;
      box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.12);
      background-color: #fff;
      z-index: 1000;
      &>div {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        height: 50px;

        button {
          margin-left: 20px;
        }
      }
    }
  }
}
