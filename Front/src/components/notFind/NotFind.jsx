import React, { Component } from 'react';
import { Result, Button } from 'antd';
import { Link } from 'react-router-dom';
import img404 from 'assets/images/404.svg';

class notFind extends Component {
  render() {
    return (
      <div
        style={{
          height: 'calc(100vh - 60px)',
          backgroundColor: '#fff',
          marginLeft: -24,
          marginRight: -24
        }}
      >
        <Result
          status="info"
          title="404"
          icon={<img src={img404} alt="403" />}
          subTitle="抱歉，您访问的页面不存在"
          extra={
            <Link to="/aimarketer/home">
              <Button type="primary">返回首页</Button>
            </Link>
          }
        />
      </div>
    );
  }
}

export default notFind;
