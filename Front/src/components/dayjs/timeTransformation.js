import dayjs from 'dayjs';

/**
 * 获取当前时间
 * @returns {dayjs.dayjs}
 */
export const getNowDate = () => {
  return dayjs(new Date(), 'YYYY-MM-DD');
};

/**
 * 当前时间之前的几天
 * @param number
 * @returns {dayjs.dayjs}
 */
export const getPreDayDate = (number) => {
  const curTime = new Date().getTime();
  const date2 = curTime - number * 3600 * 24 * 1000;
  const nDate = new Date(date2);
  return dayjs(nDate);
};

/**
 * 当前时间之前的一个月
 * @returns {dayjs.dayjs}
 */
export const getBeforeDate = () => {
  const date = new Date();
  let time = date.getTime();
  time -= 30 * 86400000;
  const nDate = new Date(time);
  return dayjs(nDate.toLocaleDateString());
};

/**
 * 当前时间之前的几周
 * @param number
 * @returns {*|dayjs.dayjs}
 */
export const getPreWeekDate = (number) => {
  const nowDate = new Date().getTime();
  const date2 = nowDate - number * 7 * 24 * 60 * 60 * 1000;
  const nDate = new Date(date2);
  return dayjs(nDate);
};

/**
 * 当前时间之前的几年
 * @param number
 * @returns {*|dayjs.dayjs}
 */
export const getBeforeYearDate = (number) => {
  const date = new Date();
  date.setFullYear(date.getFullYear() - number);
  return dayjs(new Date(date));
};
/**
 * 获取指定时间那天的 00:00:00开始的时间
 * @param data
 * @returns {string}
 */
export const getStartDate = (data) => {
  if (!data) {
    return '';
  }
  return `${data.format('YYYY-MM-DD')} 00:00:00`;
};

/**
 * 获取指定时间的 23:59:59结束的时间
 * @param data
 * @returns {string}
 */
export const getEndDate = (data) => {
  if (!data) {
    return '';
  }
  return `${data.format('YYYY-MM-DD')} 23:59:59`;
};

/**
 * 格式化时间
 * @param data
 * @param format
 */
export const formatDate = (data, format) => {
  if (!data) {
    return '';
  }
  return data.format(format);
};

/**
 * 计算相差多少
 * @param data
 * @param type day second minute
 */
export const computationTime = (data, type) => {
  if (!data || !type) {
    return '';
  }
  const m1 = dayjs();
  const m2 = dayjs(data);
  return m1.diff(m2, type);
};

/**
 * @description: 因为dayjs不支持dayjs('01:02','HH:mm')这种格式，所以需要自己封装一个方法，用于创建时间，返回moment对象
 * @param {*} hour
 * @param {*} minute
 * @returns moment()
 */
export const createTime = (str) => {
  const [hour, minute] = str.split(':').map(Number);
  const currentTime = dayjs().set('hour', hour).set('minute', minute);
  return currentTime;
};
