import { Select } from 'antd';
import React, { useState } from 'react';
import { tableSchemaList } from './config';
import './index.scss';

type TableSchemaListType = typeof tableSchemaList;

interface TableInputProps {
  options: TableSchemaListType;
  value?: number;
  tableName?: string;
  onChange?: (value: number) => void;
}

const { Option } = Select;

const TableInput: React.FC<TableInputProps> = ({ value = null, tableName, options, onChange }) => {
  const [selectValue, setSelectValue] = useState<null | number>(null);

  const onSelectChange = (e: number) => {
    setSelectValue(e);
    onChange && onChange(e);
  };
  return (
    <span className="tableInput">
      <span className="table">{tableName || '客户表 (tb_user)'}</span>
      <Select
        allowClear
        showSearch
        placeholder="请选择"
        optionFilterProp="children"
        value={value || selectValue}
        style={{ width: '80%' }}
        onChange={onSelectChange}
      >
        {options.map((item, index) => (
          <Option key={index} value={item.id}>
            {item.name}
          </Option>
        ))}
      </Select>
    </span>
  );
};

export default TableInput;
