import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Input, Popover } from 'antd';
import { SketchPicker } from 'react-color';
import _ from 'lodash';

import './index.scss';

class SketchColorCom extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: this.props.value || '#FFFFFF',
      visible: false
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (!_.isEqual(this.props.value, nextProps.value)) {
      this.setState({ value: nextProps.value });
    }
  }

  hide = () => {
    this.setState({
      visible: false
    });
  };

  handleVisibleChange = (visible) => {
    if (this.props.disabled) {
      return;
    }
    this.setState({ visible });
  };

  /**
   * 选择颜色
   *
   * @memberof SketchColorCom
   */
  handleChange = (color) => {
    const _color = _.toUpper(_.has(color, 'hex') && color.hex ? color.hex : '#FFFFFF');
    if (this.state.color !== _color) {
      setTimeout(() => {
        this.setState({ value: _color });
        if (this.props.onChange) {
          this.props.onChange(_color);
        }
      }, 150);
    }
  };

  render() {
    return (
      <Popover
        content={<SketchPicker disableAlpha color={this.state.value} onChange={this.handleChange} />}
        title="选择颜色"
        trigger="click"
        className="sketchcolorcom"
        open={this.state.visible}
        onOpenChange={this.handleVisibleChange}
      >
        <Input maxLength={7} readOnly disabled={this.props.disabled} value={this.state.value} />
        <div className="preview" style={{ background: this.state.value }} />
      </Popover>
    );
  }
}

SketchColorCom.propTypes = {
  // 未绑定,已绑定,[true,false]
  value: PropTypes.string,
  // 选择颜色事件
  onChange: PropTypes.func
};

export default SketchColorCom;
