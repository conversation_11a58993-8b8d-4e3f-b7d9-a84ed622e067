import { Icon as LegacyIcon } from '@ant-design/compatible';
import { MoreOutlined } from '@ant-design/icons';
import { Divider, Dropdown } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React from 'react';
import { t } from 'utils/translation';
import './index.scss';

const ColumnActionCom = (props) => {
  const { featureData, onClick, record } = props;
  let _featureData = _.cloneDeep(featureData);
  const needDeleteData = [];
  const needDeleteDropData = [];

  if (!global.__AUTHINFO__.superAdmin) {
    _.forEach(_featureData, (value, key) => {
      if (key !== 'dropdownData' && value.code && !_.includes(global.__AUTHINFO__.codeList, value.code)) {
        needDeleteData.push(key);
      } else {
        _.forEach(value, (dropValue, dropKey) => {
          if (dropValue.code && !_.includes(global.__AUTHINFO__.codeList, dropValue.code)) {
            needDeleteDropData.push(dropKey);
          }
        });
      }
    });
  }

  if (!_.isEmpty(needDeleteData)) {
    _featureData = _.omit(_featureData, needDeleteData);
  }
  if (!_.isEmpty(needDeleteDropData)) {
    _featureData.dropdownData = _.omit(_featureData.dropdownData, needDeleteDropData);
  }

  const menu =
    !_.isEmpty(_featureData.dropdownData) &&
    _.map(_featureData.dropdownData, (item, key) => {
      return {
        key,
        label: <span title={item.text}>{item.text}</span>,
        onClick: () => {
          onClick(key, item, record);
        }
      };
    });

  return (
    <div className="table-action">
      {_.map(_.omit(_featureData, 'dropdownData'), (item, key) => {
        if (key === 'Divider' && _.keys(_.omit(_featureData, 'dropdownData')).length > 1) {
          return <Divider key={item.key} type="vertical" />;
        }
        return (
          <span
            key={key}
            className="action-button"
            title={item.text}
            onClick={() => {
              onClick(key, item, record);
            }}
          >
            {key === 'disable' ? (
              <span style={{ color: 'rgba(0, 0, 0, 0.45)', cursor: 'not-allowed' }}>{item.text}</span>
            ) : key === 'edit' && item.iconType ? (
              <LegacyIcon type={item.iconType} />
            ) : (
              <a>{item.text}</a>
            )}
          </span>
        );
      })}
      {menu && (
        <Dropdown className="action-dropdown" key="drop-down" menu={{ items: menu }} trigger={['hover']}>
          <span>{props.closeDropdown ? <a>{t('dataCenter-CBmDQtBEZhBc')}</a> : <MoreOutlined title={t('dataCenter-CBmDQtBEZhBc')} />}</span>
        </Dropdown>
      )}
    </div>
  );
};

// 此组件是用于生成table操作列的组件，会根据传入的配置数据生成操作按钮，点击返回当前按钮信息和传入的data
ColumnActionCom.propTypes = {
  // 元素配置
  featureData: PropTypes.object.isRequired,
  // 点击处理函数
  onClick: PropTypes.func.isRequired,
  // 行数据
  record: PropTypes.object,
  closeDropdown: PropTypes.bool
};

export default React.memo(ColumnActionCom);
