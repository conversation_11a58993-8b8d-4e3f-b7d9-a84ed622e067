import { InfoCircleOutlined } from '@ant-design/icons';
import { DatePicker, Switch, Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { t } from 'utils/translation';

import './index.scss';

const RangePicker = forwardRef((props, ref) => {
  const {
    showTime = { format: 'HH:mm' },
    value = [null, null],
    format = 'YYYY-MM-DD HH:mm',
    startPlaceHolder = t('components-BpmkgdvKny'),
    endPlaceHolder = t('components-odSTAo5tba'),
    closeBefore = false,
    timeDiff = 0,
    unit = 'minutes',
    disabled,
    minDate,
    maxDate,
    style,
    disableStartTime,
    limitTime,
    createVisble,
    withEndTime = true,
    visibleFlag = false
  } = props;
  const [startValue, setStartValue] = useState(value[0]);
  const [endValue, setEndValue] = useState(value[1]);
  const [endOpen, setEndOpen] = useState(false);
  const [endTimeVisible, setEndTimeVisible] = useState(withEndTime);
  // 通过 useImperativeHandle 暴露子组件的状态和方法
  useImperativeHandle(ref, () => ({
    getEndTimeVisible: () => endTimeVisible
  }));
  useEffect(() => {
    let isMount = true;
    if (isMount) {
      setStartValue(value[0] ? value[0] : disableStartTime ? dayjs() : value[0]);
      setEndValue(value[1]);
    }
    return () => {
      isMount = false;
    };
  }, [value]);

  const disabledStartDate = (currentDate) => {
    // 判断是否开启禁用当前以前时间，
    if (closeBefore) {
      return currentDate && dayjs(currentDate).startOf('day').isBefore(dayjs().startOf('day'));
    }
    if (minDate) {
      return currentDate && dayjs(currentDate).startOf('day').isBefore(dayjs(minDate).startOf('day'));
    }
  };

  const disabledStartTime = (date) => {
    if (showTime && closeBefore) {
      return {
        disabledHours: () => {
          if (date && date.format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD')) {
            return range(0, 24).splice(0, dayjs().hour());
          }
          return [];
        },
        disabledMinutes: () => {
          // 如果天数和小时数一致，禁用当前时间之前的分钟数
          if (date && date.format('YYYY-MM-DD HH') === dayjs().format('YYYY-MM-DD HH')) {
            return range(0, 60).splice(0, dayjs().minute());
          }
          return [];
        }
      };
    }
    return null;
  };

  const disabledEndDate = (currentDate) => {
    if (maxDate) {
      return currentDate && dayjs(currentDate).startOf('day') > dayjs(maxDate).startOf('day');
    } else {
      // 首先判断是否有开始时间，如果有开始时间当天剩余时间是否
      if (!startValue || !currentDate) {
        return false;
      }
      return currentDate && currentDate < dayjs(startValue).add(timeDiff, unit).startOf('day');
    }
  };

  const disabledEndTime = (date) => {
    if (showTime && startValue) {
      return {
        disabledHours: () => {
          const minDate = dayjs(startValue).add(timeDiff, unit).format('YYYY-MM-DD');
          if (
            dayjs(dayjs(date).format('YYYY-MM-DD')).isBefore(minDate) ||
            dayjs(dayjs(date).format('YYYY-MM-DD')).isSame(minDate)
          ) {
            return range(0, 24).splice(0, dayjs(startValue).add(timeDiff, unit).hour());
          }
          return [];
        },
        disabledMinutes: () => {
          const minDate = dayjs(startValue).add(timeDiff, unit).format('YYYY-MM-DD HH');
          if (
            dayjs(dayjs(date).format('YYYY-MM-DD HH')).isBefore(minDate) ||
            dayjs(dayjs(date).format('YYYY-MM-DD HH')).isSame(minDate)
          ) {
            return range(0, 60).splice(0, dayjs(startValue).add(timeDiff, unit).minute());
          }
          return [];
        }
      };
    }
    return null;
  };

  const onStartChange = (value) => {
    const _value = _.cloneDeep(value);

    if (closeBefore && _value && _value.isBefore(dayjs())) {
      setStartValue(dayjs());
    } else {
      setStartValue(value);
    }
    // 判断如果结束时间小于起始时间加间隔，则把结束时间设置为起始时间加最小间隔
    if (endValue && value) {
      const targetValue = _value.add(timeDiff, unit);
      if (endValue.isBefore(targetValue)) {
        setEndValue(targetValue);
      }
    }
    const formatTime = [dayjs(value).format(format), dayjs(endValue).format(format)];
    props.onChange && props.onChange([value, endValue], formatTime);
  };

  const onEndChange = (value) => {
    const _value = _.cloneDeep(value);
    const _startValue = _.cloneDeep(startValue);
    const targetValue = _startValue && _startValue.add(timeDiff, unit);
    let lastValue = value;
    if (_startValue && _value && _value.isBefore(targetValue)) {
      lastValue = targetValue;
    }
    setEndValue(lastValue);
    const formatTime = [dayjs(startValue).format(format), dayjs(lastValue).format(format)];
    // TODO 如果用于失效时间，并且精确到天 就不需要时分秒 ，设置为当天的开始和结束
    if (limitTime && format === 'YYYY-MM-DD') {
      props.onChange &&
        props.onChange(
          [dayjs(startValue).startOf('day'), lastValue ? dayjs(lastValue).endOf('day') : null],
          formatTime
        );
    } else {
      props.onChange && props.onChange([startValue, lastValue], formatTime);
    }
  };

  const handleStartOpenChange = (open) => {
    if (!open) {
      setEndOpen(true);
    }
  };

  const handleEndOpenChange = () => {
    setEndOpen((open) => {
      return !open;
    });
  };

  return (
    <>
      {createVisble ? (
        <div className="ProcessDeadline">
          <div className="start_time">
            <span>
              {t('components-BpmkgdvKny')}
              <Tooltip overlayStyle={{ maxWidth: '340px' }} title={t('components-lPr4KLegHy')}>
                <InfoCircleOutlined />
              </Tooltip>
            </span>
            <DatePicker
              showToday={false}
              disabled={disabled || disableStartTime}
              disabledDate={disabledStartDate}
              showTime={showTime}
              disabledTime={disabledStartTime}
              format={format}
              value={startValue}
              placeholder={startPlaceHolder}
              onChange={onStartChange}
              onOpenChange={handleStartOpenChange}
            />
          </div>
          <div className="end_time">
            <span>
              {t('components-XaYkiv5Dg6')}
              <Tooltip
                overlayStyle={{ maxWidth: '450px' }}
                overlay={
                  <div>
                    {t('components-5mIQ32iyGw')}
                    <br />
                    {t('components-vH8aToilIs')}
                  </div>
                }
              >
                <InfoCircleOutlined />
              </Tooltip>
            </span>
            <div>
              {visibleFlag && (
                <Switch defaultChecked={endTimeVisible} size="small" onChange={(e) => setEndTimeVisible(e)} />
              )}
              {endTimeVisible ? (
                <DatePicker
                  showToday={false}
                  disabled={disabled}
                  disabledDate={limitTime ? disabledStartDate : disabledEndDate}
                  disabledTime={limitTime ? disabledStartTime : disabledEndTime}
                  showTime={showTime}
                  format={format}
                  value={endValue}
                  placeholder={t('components-XaYkiv5Dg6')}
                  onChange={onEndChange}
                  open={endOpen}
                  onOpenChange={handleEndOpenChange}
                />
              ) : (
                <div>不限制</div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <span className={`${props?.className || ''} custom-rangePicker`} style={style}>
          {!limitTime && (
            <>
              <DatePicker
                showToday={false}
                disabled={disabled || disableStartTime}
                disabledDate={disabledStartDate}
                showTime={showTime}
                disabledTime={disabledStartTime}
                format={format}
                value={startValue}
                placeholder={startPlaceHolder}
                onChange={onStartChange}
                onOpenChange={handleStartOpenChange}
              />
              <span className="connector"> ~ </span>
            </>
          )}
          <DatePicker
            showToday={false}
            disabled={disabled}
            disabledDate={limitTime ? disabledStartDate : disabledEndDate}
            disabledTime={limitTime ? disabledStartTime : disabledEndTime}
            showTime={showTime}
            format={format}
            value={endValue}
            placeholder={endPlaceHolder}
            onChange={onEndChange}
            open={endOpen}
            onOpenChange={handleEndOpenChange}
          />
        </span>
      )}
    </>
  );
});

function range(start, end) {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
}

export default RangePicker;
