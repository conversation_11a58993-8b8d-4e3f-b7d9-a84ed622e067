/*
 * @Author: Wxw
 * @Date: 2022-07-21 17:54:25
 * @LastEditTime: 2022-08-04 17:24:53
 * @LastEditors: Wxw
 * @Description: 条件筛选
 * @FilePath: \Front\src\components\featurecoms\MoreScreen\index.js
 */
import { DatePicker, Select } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { t } from 'utils/translation';
import './index.scss';

const { Option } = Select;
const { RangePicker } = DatePicker;

class VaildDateType extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: this.props.value
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (!_.isEqual(this.props.value, nextProps.value)) {
      this.setState({ value: nextProps.value });
    }
  }

  changeSelect = (trem) => {
    // 把this.state.value用逗号隔开
    const arr = this.state?.value?.split(',') || ['FOREVER'];
    if (arr !== '') {
      if (trem === 'FOREVER' && arr.length === 3) {
        arr.splice(1, 2);
      }
      arr[0] = trem;
      this.setState({ value: arr.toString() });
      if (this.props.onChange) {
        this.props.onChange(arr.toString());
      }
    }
  };

  changeDatePicker = (e, index) => {
    const arr = this.state?.value?.split(',') || ['FOREVER'];
    if (arr !== '') {
      if (e) {
        arr[index] = dayjs(e[0]).startOf('day').valueOf();
        arr[index + 1] = dayjs(e[1]).endOf('day').valueOf();
      } else {
        arr[index] = '';
      }

      this.setState({ value: arr.toString() });
      if (this.props.onChange) {
        this.props.onChange(arr.toString());
      }
    }
  };

  render() {
    return (
      <>
        <Select
          style={{ width: '28%', marginRight: '8px' }}
          onChange={this.changeSelect}
          placeholder={t('global-select-placeholder')}
          value={this.state?.value?.split(',')[0]}
        >
          <Option value="FOREVER">{t('global-forever')}</Option>
          <Option value="DATE">{t('global-date')}</Option>
        </Select>
        {this?.state?.value?.split(',')[0] === 'DATE' && (
          <span>
            <RangePicker
              onChange={(e) => this.changeDatePicker(e, 1)}
              value={
                this?.state?.value?.split(',')[1]
                  ? [dayjs(Number(this?.state?.value?.split(',')[1])), dayjs(Number(this?.state?.value?.split(',')[2]))]
                  : null
              }
              style={{
                width: '70%',
                textAlign: 'center'
              }}
            />
          </span>
        )}
      </>
    );
  }
}

VaildDateType.propTypes = {
  // 未绑定,已绑定,[true,false]
  value: PropTypes.string,

  // 选择颜色事件
  onChange: PropTypes.func
};

export default VaildDateType;
