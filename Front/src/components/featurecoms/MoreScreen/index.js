import { Input, InputNumber, Select } from 'antd';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { t } from 'utils/translation';

const { Option } = Select;

class MoreScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: this.props.value || 'GTE'
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (!_.isEqual(this.props.value, nextProps.value)) {
      this.setState({ value: nextProps.value });
    }
  }

  changeSelect = (trem) => {
    // 把this.state.value用逗号隔开
    const arr = this.state?.value?.split(',') || ['GTE'];
    if (arr !== '') {
      if (trem !== 'BETWEEN' && arr.length === 3) {
        arr.pop();
      }
      arr[0] = trem;
      setTimeout(() => {
        this.setState({ value: arr.toString() });
        if (this.props.onChange) {
          this.props.onChange(arr.toString());
        }
      }, 150);
    }
  };

  changeInput = (e, index) => {
    // 把this.state.value用逗号隔开
    const arr = this.state?.value?.split(',') || ['GTE'];
    if (arr !== '') {
      arr[index] = e;
      setTimeout(() => {
        this.setState({ value: arr.toString() });
        if (this.props.onChange) {
          this.props.onChange(arr.toString());
        }
      }, 150);
    }
  };

  render() {
    const { value } = this.state;
    const componentWidth = value && value.includes('DATE_BETWEEN') ? '28%' : this.props.width || '28%';
    return (
      <>
        <Select
          style={{
            width: componentWidth,
            marginRight: '8px',
            transition: 'none'
          }}
          onChange={this.changeSelect}
          placeholder={t('global-select-placeholder')}
          value={this.state?.value?.split(',')[0] || 'GTE'}
        >
          <Option value="GTE">{t('components-moreScreen-GTE')}</Option>
          <Option value="LTE">{t('components-moreScreenLTE')}</Option>
          <Option value="DATE_BETWEEN">{t('components-moreScreenDATE_BETWEEN')}</Option>
        </Select>
        <InputNumber
          style={{
            width: componentWidth,
            textAlign: 'center',
            transition: 'none'
          }}
          onChange={(e) => this.changeInput(e, 1)}
          min={0}
          value={this?.state?.value?.split(',')[1] || ''}
        />
        {this?.state?.value?.split(',')[0] === 'DATE_BETWEEN' && (
          <span>
            <Input
              className="site-input-split"
              style={{
                width: 30,
                height: 32,
                borderLeft: 0,
                borderRight: 0,
                pointerEvents: 'none',
                background: '#fff'
              }}
              placeholder="~"
              disabled
            />
            <InputNumber
              style={{
                width: '30%',
                textAlign: 'center',
                transition: 'none'
              }}
              min={0}
              onChange={(e) => this.changeInput(e, 2)}
              value={this?.state?.value?.split(',')[2] || ''}
            />
          </span>
        )}
      </>
    );
  }
}

MoreScreen.propTypes = {
  // 未绑定,已绑定,[true,false]
  value: PropTypes.string,

  // 选择颜色事件
  onChange: PropTypes.func
};

export default MoreScreen;
