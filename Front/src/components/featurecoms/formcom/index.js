import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Checkbox, Col, DatePicker, Input, Radio, Row, Select, Switch, TreeSelect } from 'antd';
import MoreScreen from 'components/featurecoms/MoreScreen/index.js';
import SketchColorCom from 'components/featurecoms/sketchcolorcom/index.js';
import { TagSearchCom } from 'components/featurecoms/tagSearch';
import VaildDateType from 'components/featurecoms/validDateType/index.js';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import './index.scss';

const { TextArea } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const defaultLayoutConfig = {
  layout: 'horizontal',
  labelCol: { span: 6 },
  wrapperCol: { span: 18 }
};

const FormCom = ({ children, elements, defaultFormData = {}, layoutConfig, form, colon, labelAlign, layout }) => {
  const mergedLayoutConfig = {
    ...defaultLayoutConfig,
    ...layoutConfig,
    layout: layout || 'horizontal'
  };

  const getComponent = (element, initialValue) => {
    const { type, componentOptions = {} } = element;
    let component = null;
    switch (type) {
      case 'input':
        component = <Input {...componentOptions} autoComplete="off" />;
        break;
      case 'sketchColorInput':
        component = <SketchColorCom {...componentOptions} autoComplete="off" />;
        break;
      case 'moreScreen':
        component = <MoreScreen {...componentOptions} autoComplete="off" />;
        break;
      case 'vaildDateType':
        component = <VaildDateType {...componentOptions} autoComplete="off" />;
        break;
      case 'tagSearch':
        component = <TagSearchCom {...componentOptions} autoComplete="off" />;
        break;
      case 'textArea':
        component = <TextArea {...componentOptions} />;
        break;
      case 'select':
        component = (
          <Select {..._.omit(componentOptions, 'options')} getPopupContainer={(triggerNode) => triggerNode.parentNode}>
            {_.map(componentOptions.options, (item) => {
              return (
                <Option key={item.key} value={item.value} title={item.text}>
                  {item.customContent && item.customContent}
                  {item.text}
                </Option>
              );
            })}
          </Select>
        );
        break;
      case 'dateRange':
        component = (
          <RangePicker
            {...componentOptions}
            style={{ width: '100%' }}
            getCalendarContainer={(triggerNode) => triggerNode.parentNode}
          />
        );
        break;
      case 'checkbox':
        component = getCheckbox(componentOptions);
        break;
      case 'radio':
        component = getRadio(componentOptions);
        break;
      case 'descriptions':
        component = <span>{initialValue}</span>;
        break;
      case 'switch':
        component = getSwitch(componentOptions);
        break;
      case 'treeSelect':
        component = getTreeSelect(componentOptions);
        break;
      default:
        break;
    }

    return component;
  };

  const getTreeSelect = (componentOptions) => {
    const { options, placeholder } = componentOptions;
    return (
      <TreeSelect
        allowClear
        showSearch
        filterTreeNode={(value, TreeNode) => TreeNode.props.title.indexOf(value) > -1}
        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
        treeData={options}
        placeholder={placeholder}
        treeDefaultExpandAll
        {...componentOptions}
      />
    );
  };

  const getRadio = (componentOptions) => {
    // 判断是单个还是组
    const { options, label } = componentOptions;
    if (!_.isEmpty(options)) {
      // checkboxGroup
      return <Radio.Group options={options} />;
    } else {
      return <Radio>{label}</Radio>;
    }
  };

  const getCheckbox = (componentOptions) => {
    // 判断是单个还是组
    const { options, label } = componentOptions;
    if (!_.isEmpty(options)) {
      // checkboxGroup
      return <Checkbox.Group options={options} />;
    } else {
      return <Checkbox>{label}</Checkbox>;
    }
  };

  const getSwitch = (componentOptions) => {
    const { label } = componentOptions;
    return <Switch>{label}</Switch>;
  };

  const { getFieldDecorator } = form;

  // 向外暴露获取组件数据方法，返回错误对象和数据
  // useImperativeHandle(cref, () => ({
  //   getFormData: () => {
  //     return form.validateFields(() => {

  //     }).then((values) => {
  //       return {
  //         formData: values
  //       };
  //     }).catch((e) => {
  //       return {
  //         formData: e.values,
  //         errors: e.errors
  //       };
  //     });
  //   }
  // }));

  useEffect(() => {
    // todo 因为Form.Item的initialValue是使用defaultFormData的值，所以需要在这里更新 而TagSearchCom的值是异步的，所以需要更新表单
    form.setFieldsValue(defaultFormData);
  }, [JSON.stringify(defaultFormData)]);

  return (
    <>
      <Form
        name="basicForm"
        className="formCom"
        {...mergedLayoutConfig}
        colon={colon}
        size="middle"
        labelAlign={labelAlign || 'right'}
      >
        <Row className="formMainContent">
          {_.map(elements, (element, key) => {
            const {
              type,
              label,
              className,
              width,
              labelWidth,
              colon = true,
              wrapperWidth,
              rules,
              validateFirst
            } = element;

            let itemLayoutConfig = {};
            if (!_.isNil(labelWidth))
              itemLayoutConfig = {
                ...itemLayoutConfig,
                labelCol: { span: labelWidth }
              };
            if (!_.isNil(wrapperWidth))
              itemLayoutConfig = {
                ...itemLayoutConfig,
                wrapperCol: { span: wrapperWidth }
              };

            const formItemProps = {
              label,
              className,
              colon,
              ...itemLayoutConfig
            };
            return (
              <Col span={width} key={key}>
                <Form.Item name={key} {...formItemProps}>
                  {getFieldDecorator(key, {
                    initialValue: _.get(defaultFormData, key),
                    rules,
                    validateFirst: validateFirst || false,
                    valuePropName: type === 'checkbox' || type === 'switch' ? 'checked' : 'value'
                  })(getComponent(element, _.get(defaultFormData, key)))}
                </Form.Item>
              </Col>
            );
          })}
        </Row>
      </Form>
      {children}
    </>
  );
};

FormCom.propTypes = {
  // 元素配置
  elements: PropTypes.object.isRequired,
  // 初始化FormData
  defaultFormData: PropTypes.object,
  // 布局对象
  layoutConfig: PropTypes.object,
  // 表单变更事件
  onChange: PropTypes.func //
  // 外围组件可以通过调用实例的formNode.current.validateFields获取err和数据，通过调用实例的formNode.current.resetFields()重置数据
};

const onValuesChange = (props, changedValues, allValues) => {
  // 当前组件变更是否触发其它数据域的验证规则
  const key = getPath(changedValues);
  let needVersField = false;
  if (key) needVersField = props.elements[key]?.associationVers;
  if (!_.isEmpty(needVersField)) {
    // 如果需要触发的输入域有值，触发验证
    const lastNeedVersField = [];
    _.forEach(props.elements[key].associationVers, (item) => {
      allValues[item] && lastNeedVersField.push(item);
    });
    setTimeout(() => {
      props.form.validateFields(lastNeedVersField);
    }, 4);
  }
  const realValue = _.get(allValues, key);
  props.onChange && props.onChange(changedValues, allValues, key, realValue);
};

// 获取对象key 路径
const getPath = (currentValue) => {
  const value = _.values(currentValue)[0];
  if (!_.isObject(value) || _.isArray(value)) {
    // 如果是基本数据类型
    return Object.keys(currentValue)[0];
  } else {
    return `${Object.keys(currentValue)[0]}.${getPath(value)}`;
  }
};

export default Form.create({
  name: 'FormCom',
  onValuesChange
})(FormCom);
