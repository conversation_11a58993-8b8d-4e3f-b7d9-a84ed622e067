export type TagSearchOptions = {
  id: string;
  label: string;
  name?: string;
  displayName: string;
  fieldType: string; // dataType
  operator: string; //
  dateType: 'RELATIVE' | 'ABSOLUTE' | 'LATEST';
  times: number;
  timeType: string;
  checkUserTag: boolean;
  isReady?: boolean;
  valid?: boolean;
};

export interface TagSearchProps {
  value?: TagSearchOptions[];
  onChange?: (value: TagSearchOptions[]) => void;
  placeholder?: string;
  options: TagSearchOptions[];
}
