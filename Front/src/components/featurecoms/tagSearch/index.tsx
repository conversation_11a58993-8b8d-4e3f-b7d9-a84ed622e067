import { DatePicker } from '@/components';
import { MyIcon } from '@/utils/myIcon';
import { CloseOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Divider, Input, message, Modal, Select, Spin, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useReducer, useRef } from 'react';
// @ts-ignore
import { FixedSizeList as List } from 'react-window';
import TagSearchService from 'service/tagSearch';
import Sortable from 'sortablejs';
import { useStore } from 'store/globalStore';
import { t } from 'utils/translation';
import './index.scss';
import { TagSearchOptions, TagSearchProps } from './types';

const { Text } = Typography;

type State = {
  _value: TagSearchOptions[];
  loading: boolean;
  isModalVisible: boolean;
  findValueArr: any[];
  options: TagSearchOptions[];
  searchValue: string;
};

const initialState: State = {
  _value: [],
  loading: false,
  isModalVisible: false,
  findValueArr: [],
  options: [],
  searchValue: ''
};

const reducer = (state: State, action: Partial<State>): State => ({ ...state, ...action });

const TagSearchCom: React.FC<TagSearchProps> = ({ value = [], onChange, placeholder = t('dataCenter-SzX3RSDhY1hl') }) => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const { tagSearchScenario } = useStore();
  const prevValueRef = useRef(value);
  const latestValueRef = useRef(state._value);

  useEffect(() => {
    latestValueRef.current = state._value;
  }, [state._value]);

  useEffect(() => {
    (async () => {
      // dispatch({ loading: true, findValueArr: [], _value: [] });
      onChange && onChange([]);
      if (!tagSearchScenario) return;
      try {
        const userLabelList = await TagSearchService.userLabelListBy([
          {
            operator: 'EQ',
            propertyName: 'scenario.code',
            value: tagSearchScenario.code
          },
          { operator: 'EQ', propertyName: 'status', value: 'NORMAL' }
        ]);
        dispatch({ options: userLabelList });
        console.warn(t('dataCenter-XGoUCWeXCUpX'), userLabelList.length, t('dataCenter-ohbjFUu73KwB'));
      } catch (error) {
        console.error(t('dataCenter-UFkHsYxAVLZY'), error);
      } finally {
        dispatch({ loading: false });
      }
    })();
  }, [tagSearchScenario]);

  const showModal = () => {
    if (!tagSearchScenario) return message.warning(t('dataCenter-OtxHUXAfKXAL'));
    dispatch({ isModalVisible: true, _value: value });
  };

  const formatAndSetSelectValue = (changeValue: TagSearchOptions[]) => {
    const formatValue = changeValue.map((item: TagSearchOptions) => {
      return {
        value: JSON.stringify(item),
        label: `${item.displayName}[${dayjs(item.times).format('YYYY-MM-DD')}]`
      };
    });
    dispatch({ findValueArr: formatValue });
  };

  // 校验标签是否能保存
  const checkTag = (tagValue: any[]) => {
    const tagNameArr = tagValue.map((item: any) => `${item.name}[${dayjs(item.times).format('YYYY-MM-DD')}]`);
    const repeatArr = tagNameArr.filter((item, index) => tagNameArr.indexOf(item) !== index);
    return [...new Set(repeatArr)];
  };

  const handleOk = () => {
    const repeatArr = checkTag(state._value);
    if (repeatArr.length > 0) {
      message.warning(t('dataCenter-BkD5hktUTSDW') + repeatArr.join(' , ') + t('dataCenter-6XVc15jh3RPi'));
      return;
    }
    formatAndSetSelectValue(state._value);
    onChange && onChange(state._value);
    dispatch({ isModalVisible: false });
  };

  const handleCancel = () => {
    dispatch({ isModalVisible: false });
  };

  const searchTag = (value: string) => {
    dispatch({ searchValue: value || '' });
  };

  useEffect(() => {
    if (JSON.stringify(prevValueRef.current) !== JSON.stringify(value)) {
      dispatch({ _value: value });
      formatAndSetSelectValue(value);
      prevValueRef.current = value;
    }
  }, [value]);

  /**
   * 做了一些性能优化
   * 这里是触发弹窗的时候找到listItem这个节点的父元素 并且这个父元素上五级必须有listWrapper这个类名
   * 是为了兼容虚拟滚动 和拖动容器，虚拟滚动会创建两层div 一层是列表虚拟滚动 一层是最大的高度
   * sortablejs 需要一个可以拖动的元素 所以找.listItem的父元素
   */
  useEffect(() => {
    const listItem = document.querySelector('.listWrapper .listItem');
    try {
      if (listItem) {
        let element = listItem.parentElement;
        let depth = 0;
        const maxDepth = 5;

        while (element && depth < maxDepth) {
          if (element.classList.contains('listWrapper')) {
            break;
          }
          element = element.parentElement;
          depth++;
        }

        if (element && element.classList.contains('listWrapper')) {
          const options = {
            group: { name: 'shared', pull: false, put: false },
            animation: 150,
            sort: false,
            filter: '.listTitle',
            onEnd: (event: any) => {
              const today = dayjs().startOf('day').valueOf();
              const tag = JSON.parse(event.item.getAttribute('data-tag'));
              const newTag = {
                ...tag,
                key: new Date().getTime(),
                times: today,
                label: tag.name
              };

              const currentValue = latestValueRef.current;
              if (currentValue.length >= 10) {
                message.warning(t('dataCenter-OCcr7WLs7dMj'));
                return;
              }

              dispatch({ _value: [...currentValue, newTag] });
            }
          };
          Sortable.create(listItem.parentElement as HTMLElement, options);
        } else {
          throw new Error(t('dataCenter-k3gVuzcO0eup'));
        }
      }
    } catch (error) {
      console.error(t('dataCenter-k3gVuzcO0eup'), error);
    }
  }, [state.isModalVisible, state.options]);

  const sortableRight = (element: HTMLElement | null) => {
    if (!element) return;
    const options = {
      group: 'shared',
      animation: 150,
      filter: '.listTitle'
    };
    Sortable.create(element, options);
  };

  const changeDate = (date: dayjs.Dayjs | null, key: number) => {
    dispatch({
      _value: state._value.map((item: any) =>
        item.key === key ? { ...item, times: !date ? undefined : dayjs(date).startOf('day').valueOf() } : item
      )
    });
  };

  const removeItem = (key: number) => {
    dispatch({ _value: state._value.filter((item: any) => item.key !== key) });
  };

  const filteredOptions = state.options.filter((item) =>
    `${item.name}[${item.displayName}]`.includes(state.searchValue)
  );

  const Row = useCallback(
    ({ index, style }) => {
      const item = filteredOptions[index];
      return (
        <div
          className="listItem"
          title={item!.displayName}
          key={item!.id}
          style={{ ...style, cursor: 'pointer' }}
          data-tag={JSON.stringify(_.omit(item, ['scenario']))}
        >
          <MyIcon
            type="icon-icon-draw"
            style={{
              fontSize: 16,
              lineHeight: '48px',
              marginRight: '13px'
            }}
          />
          <Text
            ellipsis={{
              tooltip: `${item!.name || item!.label}[${item!.displayName}]`
            }}
          >
            {`${item!.name || item!.label}[${item!.displayName}]`}
          </Text>
        </div>
      );
    },
    [filteredOptions]
  );

  return (
    <div className="tagSearchModal">
      <Select
        style={{ width: '100%' }}
        mode="multiple"
        maxTagCount={4}
        placeholder={placeholder}
        dropdownRender={undefined}
        dropdownStyle={{ display: 'none' }}
        value={state.findValueArr}
        onClick={showModal}
      />

      <Modal
        title={t('dataCenter-jMbTkPeGDK1g')}
        open={state.isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={880}
        wrapClassName="tagModal"
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            {t('dataCenter-p9i5gyiV4W88')}
          </Button>,
          <Button key="ok" type="primary" onClick={handleOk}>
            {t('dataCenter-jRep9eAN95xw')}
          </Button>
        ]}
      >
        <Spin spinning={state.loading}>
          <div className="tagSearchWrapper">
            <div className="leftWrapper">
              <div className="h-56 flex items-center">
                <Input
                  placeholder={t('dataCenter-5I3VWDdB1MNA')}
                  onChange={(e) => searchTag(e.target.value)}
                  allowClear
                  suffix={<SearchOutlined style={{ color: 'rgba(0, 0, 0, 0.25)' }} />}
                />
              </div>
              <div className="listTitle pl-45 !ml-0 font-bold">{t('dataCenter-zvMdsBmbvfbE')}</div>
              <div className="listWrapper">
                <List height={595} itemCount={filteredOptions.length} itemSize={50} width="100%">
                  {Row}
                </List>
              </div>
            </div>
            <Divider type="vertical" />
            <div className="rightWrapper">
              <div>
                <div
                  style={{
                    height: 54,
                    lineHeight: '54px',
                    display: 'flex',
                    justifyContent: 'space-around',
                    textAlign: 'center',
                    alignItems: 'center'
                  }}
                >
                  <span>{t('dataCenter-ITdUiLB8097Z')} {state._value.length}</span>
                  <a
                    onClick={() => {
                      dispatch({ _value: [] });
                    }}
                    style={{ color: 'var(--ant-primary-color)', margin: '0 15px 0 auto' }}
                  >
                    {t('dataCenter-2c9StY5TMeP0')}
                  </a>
                </div>
                <div className="showListItem bg-[#FAFAFA] h-48 !leading-[48px] !font-bold">
                  <div style={{ width: '48%' }}>{t('dataCenter-VAaxR413UZeL')}</div>
                  <div style={{ width: '48%' }}>{t('dataCenter-FgfRWZdrpCc7')}</div>
                </div>
              </div>
              <div
                className="listWrapper"
                ref={(el) => sortableRight(el)}
                style={{ width: '100%', height: 'calc(100% - 100px)' }}
              >
                {state._value.map((item: any) => (
                  <div className="showListItem" title={item.name} key={item.key}>
                    <span style={{ width: '48%' }}>
                      <Text
                        ellipsis={{
                          tooltip: `${item.name || item.label}[${item.displayName}]`
                        }}
                      >
                        {`${item.name || item.label}[${item.displayName}]`}
                      </Text>
                    </span>

                    <div
                      style={{
                        width: '48%',
                        display: 'flex',
                        gap: 8,
                        alignItems: 'center',
                        justifyContent: 'space-between'
                      }}
                    >
                      <DatePicker
                        value={dayjs(item.times)}
                        onChange={(date) => changeDate(date, item.key)}
                        allowClear={false}
                      />
                      <CloseOutlined onClick={() => removeItem(item.key)} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </Spin>
      </Modal>
    </div>
  );
};

export { TagSearchCom };
export type { TagSearchOptions };
