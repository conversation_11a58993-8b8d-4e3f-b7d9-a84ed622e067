.tagSearchWrapper {
  height: 700px;
  display: flex;
  overflow: hidden;

  .leftWrapper,
  .rightWrapper {
    width: 49%;

    .listTitle {
      display: flex;
      height: 48px;
      line-height: 48px;
      margin-left: 15px;
      background: #FAFAFA;
      border-bottom: 1px solid #F0F0F0;
    }

    .listWrapper {
      height: calc(100% - 50px);
      overflow-y: auto;
      cursor: pointer;

      .listItem {
        padding-left: 16px;
        height: 48px;
        line-height: 48px;
        font-style: normal;
        font-weight: 400;

        .ant-checkbox {
          margin-right: 16px;
        }

        border-bottom: 1px solid #F0F0F0;
        display: flex;
        // justify-content: space-between;
        align-items: center;

        .anticon-close {
          margin-right: 24px;
          cursor: pointer;
        }
      }
    }

    .ant-tree {
      padding: 0 16px;
      overflow-x: hidden;
    }

    .listItem,
    .showListItem {
      display: flex;
      padding: 0 16px;
      height: 48px;
      line-height: 48px;
      font-style: normal;
      font-weight: 400;
      border-bottom: 1px solid #F0F0F0;

      .ant-checkbox {
        margin-right: 16px;
      }

      // border-bottom: 1px solid #F0F0F0;
    }

    .ant-tree-list {
      .ant-tree-treenode {
        height: 40px;

        span {
          line-height: 40px;
        }
      }
    }
  }



  .ant-divider {
    height: 100%;
  }

  .selectArea {
    width: 49%;

    // max-height: 500px;
    .title {
      height: 54px;
      line-height: 54px;
      padding-left: 16px;
      font-style: normal;
      font-weight: 400;
      color: #000000A6;
    }

    .header {
      padding-left: 16px;
      background: #FAFAFA;
      height: 48px;
      line-height: 48px;
      font-style: normal;
      font-weight: 500;

      .ant-checkbox {
        margin-right: 16px;
      }

      border-bottom: 1px solid #F0F0F0;
    }

  }

  .selectedArea {
    width: 49%;

    // max-height: 500px;
    .title {
      height: 54px;
      line-height: 54px;
      padding-left: 16px;
      font-style: normal;
      font-weight: 400;
      color: #000000A6;
    }

    .header {
      padding-left: 16px;
      background: #FAFAFA;
      height: 48px;
      line-height: 48px;
      font-style: normal;
      font-weight: 500;

      .ant-checkbox {
        margin-right: 16px;
      }

      border-bottom: 1px solid #F0F0F0;
    }

  }
}

.tagModal {
  .ant-modal-content {
    border-radius: 8px;
  }
}