import React, { Component } from 'react';
import './index.scss';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { <PERSON>ton, Row, Col, Input, Select, DatePicker, Popconfirm } from 'antd';
import { withRouter } from 'react-router-dom';
import CustomRangePicker from 'components/featurecoms/rangepicker/index';
import _ from 'lodash';

const { TextArea } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

class Index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false
    };

    this.handleSubmit = this.handleSubmit.bind(this);
    this.confirm = this.confirm.bind(this);
    this.cancel = this.cancel.bind(this);
    this.handleNameValidator = this.handleNameValidator.bind(this);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (JSON.stringify(this.props.value) !== JSON.stringify(nextProps.value)) {
      // let temValue = JSON.parse(JSON.stringify(nextProps.value));
      // temValue.remark = nextProps.value.remark;
      // if (temValue.flows) delete temValue.flows;
      // console.log(nextProps.value, temValue);
      delete nextProps.value.flows;
      this.setState({
        value: nextProps.value
      });
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (JSON.stringify(prevState.value) !== JSON.stringify(this.state.value)) {
      this.props.form.setFieldsValue({
        ...this.state.value
      });
    }
  }

  //   copy(cloneData){
  //     let obj = new cloneData.con
  //     for(var i in ladel){
  //         if(ladel[i] instanceof Object){
  //             obj[i] = copy(ladel[i])//递归
  //         }else if(ladel[i] instanceof Array){
  //             obj[i] = copy(ladel[i])
  //         }else{
  //             obj[i] = ladel[i]
  //         }
  //     }
  //     return obj
  // }

  // 验证名称唯一
  async handleNameValidator(rule, value, callback) {
    if (!value) return;
    // const projectId = this.props.form.getFieldValue('projectId') || '';
    // const pattern = rule.field === 'name' ? /^[a-zA-Z][a-zA-Z0-9_]*$/ : rule.field === 'projectId' ? /^[a-zA-Z0-9]*$/ : rule.field === 'displayName' ? /^[a-zA-Z0-9_\u4e00-\u9fa5]*$/ : null;
    const pattern = rule.field === 'name' ? /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g : null;
    if (pattern.test(value)) {
      let params;
      if (rule.field === 'projectId') {
        params = {
          [rule.field]: value
        };
      } else {
        params = {
          [rule.field]: value
          // projectId
        };
      }
      if (this.props.id) {
        params.id = this.props.id;
      }
      const { serviceName } = this.props;
      try {
        const tableNameUnique = await serviceName.ensureNameUnique(params);
        if (!tableNameUnique) {
          callback(true);
        } else {
          callback();
        }
      } catch (error) {
        callback();
      }
    }
  }

  handleSubmit(isStep) {
    this.setState({
      loading: true
    });
    this.props.form.validateFieldsAndScroll((err, values) => {
      if (!err) {
        if (isStep) {
          this.props.onSubmit(values, isStep);
        } else {
          this.props.onSubmit(values);
        }
      }
      this.setState({
        loading: false
      });
    });
  }

  confirm() {
    this.handleSubmit();
  }

  cancel() {
    this.props.history.push('/aimarketer/home/<USER>');
  }

  handleSelectChange(propertyName, value) {
    this.props.form.setFieldsValue({
      [propertyName]: value
    });
  }

  render() {
    const { config } = this.props;
    const { getFieldDecorator } = this.props.form;

    // 数据类型渲染
    const handleOption = (values) =>
      _.map(values, (v, i) => (
        <Option key={i} value={v.value}>
          {v.name}
        </Option>
      ));

    _.forEach(config, (v) => {
      if (v.propertyName === 'name') {
        v.rules[v.rules.length - 1].validator = this.handleNameValidator;
      }
    });

    return (
      <div>
        <Form className="formBase">
          <Row className="formMainContent" gutter={24}>
            {_.map(config, (v, i) => {
              return (
                <Col span={v.span || 12} key={i.toString()}>
                  <Form.Item label={v.name} className={v.propertyName}>
                    {getFieldDecorator(v.propertyName, {
                      rules: v.rules
                    })(
                      v.nodeType === 'Input' ? (
                        <Input placeholder={v.placeholder} autoComplete="off" allowClear />
                      ) : v.nodeType === 'TextArea' ? (
                        <TextArea rows={4} placeholder={v.placeholder} />
                      ) : v.nodeType === 'Select' ? (
                        <Select
                          placeholder={v.placeholder}
                          onChange={(value) => this.handleSelectChange(v.propertyName, value)}
                          getPopupContainer={(triggerNode) => triggerNode.parentNode}
                        >
                          {handleOption(v.options)}
                        </Select>
                      ) : v.nodeType === 'RangePicker' ? (
                        <RangePicker
                          format="YYYY-MM-DD HH:mm"
                          getPopupContainer={(triggerNode) => triggerNode.parentNode}
                          showTime={v.showTime || true}
                          disabledDate={v.disabledDate}
                          disabledTime={v.disabledTime}
                        />
                      ) : v.nodeType === 'CustomRangePicker' ? (
                        <CustomRangePicker
                          showTime={{ format: 'HH:mm' }}
                          format="YYYY-MM-DD HH:mm"
                          timeDiff={5}
                          closeBefore
                        />
                      ) : null
                    )}
                  </Form.Item>
                </Col>
              );
            })}
          </Row>
          <div className="footer">
            <Popconfirm
              placement="topLeft"
              title="是否保存当前内容为草稿"
              onConfirm={this.confirm}
              onCancel={this.cancel}
              okText="保存"
              cancelText="不保存"
            >
              <Button onClick={() => this.props.cancelPrompt && this.props.cancelPrompt()}>退出创建</Button>
            </Popconfirm>
            <Button loading={this.state.loading} onClick={() => this.handleSubmit(true)}>
              流程配置
            </Button>
          </div>
        </Form>
      </div>
    );
  }
}
export default withRouter(Form.create({ name: 'baseForm' })(Index));
