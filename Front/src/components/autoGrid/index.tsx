import { Col, Row } from 'antd';
import React from 'react';

interface Breakpoint {
  minWidth?: number;
  maxWidth?: number;
  span: number;
}

interface AutoGridProps {
  children: React.ReactNode[];
  getSpan: (screenWidth: number) => number;
  breakpoints?: Breakpoint[];
}

/**
 * 将 breakpoints 转换为 getSpan 函数
 */
function createGetSpanFromBreakpoints(breakpoints: Breakpoint[]): (width: number) => number {
  return (width) => {
    for (const bp of breakpoints) {
      const min = bp.minWidth ?? 0;
      const max = bp.maxWidth ?? Infinity;
      if (width >= min && width < max) {
        return bp.span;
      }
    }
    // 如果没有匹配，返回最后一个断点的 span，或者默认值 12
    return breakpoints[breakpoints.length - 1]?.span ?? 12;
  };
}

/**
 * 自适应网格组件，根据屏幕宽度动态调整列宽
 */
const AutoGrid: React.FC<AutoGridProps> = ({ children, getSpan, breakpoints }) => {
  const [screenWidth, setScreenWidth] = React.useState(window.innerWidth);

  // 监听窗口大小变化
  React.useLayoutEffect(() => {
    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };
    window.addEventListener('resize', handleResize);
    handleResize(); // 初始化时调用一次
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 确定列宽计算函数
  const spanFunc = getSpan || (breakpoints ? createGetSpanFromBreakpoints(breakpoints) : undefined);
  if (!spanFunc) {
    throw new Error('没有提供 getSpan 或 breakpoints');
  }

  const colSpanVal = spanFunc(screenWidth);

  return (
    <Row gutter={[16, 16]}>
      {React.Children.map(children, (child, index) => (
        <Col key={index} span={colSpanVal}>
          {child}
        </Col>
      ))}
    </Row>
  );
};

export { AutoGrid };
