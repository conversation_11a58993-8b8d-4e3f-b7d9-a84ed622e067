import React, { Component } from 'react';
import './Query.scss';
import { Button, Row, Col, Input, Select, DatePicker, Checkbox } from 'antd';
import _ from 'lodash';
import dayjs from 'dayjs';
import ArrayUtil from './utils/arrayUtil';
import { t } from 'utils/translation';

const { Option } = Select;
const { RangePicker } = DatePicker;

// 数组转key:value对象
function arrToObj(arr, propertyName) {
  if (!Array.isArray(arr) || !arr.length || !propertyName) return;
  const obj = {};
  arr.forEach((v) => {
    switch (v.nodeType) {
      case 'input':
        obj[v[propertyName]] = '';
        break;
      case 'select':
        obj[v[propertyName]] = undefined;
        break;
      case 'dateRange':
        obj[v[propertyName]] = [null, null];
        break;
      case 'checkbox':
        obj[v[propertyName]] = false;
        break;
      default:
        break;
    }
  });
  return obj;
}

export default class Query extends Component {
  constructor(props) {
    super(props);
    this.state = this.props.queryFormData ? this.props.queryFormData : arrToObj(this.props.config, 'propertyName');

    this.clear = this.clear.bind(this);
    this.query = this.query.bind(this);
  }

  handleInput(e, propertyName) {
    this.setState({ [propertyName]: e.target.value }, () => {
      this.props.onChange && this.props.onChange(this.state);
    });
  }

  handleSelect(value, propertyName) {
    this.setState({ [propertyName]: value }, () => {
      this.props.onChange && this.props.onChange(this.state);
    });
  }

  handleDateRange(value, propertyName) {
    if (!value) {
      return this.setState({ [propertyName]: [null, null] }, () => {
        this.props.onChange && this.props.onChange(this.state);
      });
    }
    const startTime = value[0] ? dayjs(value[0]).startOf('day').valueOf() : null;
    const endTime = value[1] ? dayjs(value[1]).endOf('day').valueOf() : null;
    this.setState(
      {
        [propertyName]: [startTime, endTime]
      },
      () => {
        this.props.onChange && this.props.onChange(this.state);
      }
    );
  }

  handleCheckbox(e, propertyName) {
    this.setState({ [propertyName]: e.target.checked }, () => {
      this.props.onChange && this.props.onChange(this.state);
    });
  }

  // 清除
  clear() {
    const emptyState = arrToObj(this.props.config, 'propertyName');
    this.setState(
      {
        ...emptyState
      },
      () => {
        this.props.onChange && this.props.onChange(this.state);
        this.props.queryData(ArrayUtil.changeArrFormat(this.state, this.props.config));
      }
    );
  }

  // 查询
  query() {
    this.props.queryData(ArrayUtil.changeArrFormat(this.state, this.props.config));
  }

  render() {
    const { isFold, config, grid } = this.props;
    const { leftCol, rightCol, childCol } = grid;
    // 数据类型渲染
    const handleOption = (values) =>
      _.map(values, (v, i) => (
        <Option key={i} value={v.value}>
          {v.name}
        </Option>
      ));

    const formatDateRange = (value) => {
      if (!value) return;
      return [value[0] && dayjs(value[0]), value[1] && dayjs(value[1])];
    };
    return (
      <div className={`query ${!isFold ? 'fold' : null}`}>
        <Row gutter={24}>
          <Col span={leftCol} className="left">
            <Row gutter={24}>
              {_.map(config, (v, i) => {
                return (
                  <Col span={childCol || 8} key={i} className={v.nodeType === 'dateRange' ? 'data-range' : null}>
                    {v.nodeType === 'checkbox' ? null : <span>{v.name}：</span>}
                    {v.nodeType === 'input' ? (
                      <Input
                        placeholder={v.placeholder ? v.placeholder : t('setting-XYVj707Fg3iz')}
                        onChange={(e) => this.handleInput(e, v.propertyName)}
                        value={this.state[v.propertyName]}
                        allowClear
                      />
                    ) : v.nodeType === 'select' ? (
                      <Select
                        allowClear
                        showSearch
                        placeholder={v.placeholder ? v.placeholder : t('setting-1bfwPtSH6kJO')}
                        optionFilterProp="children"
                        onChange={(value) => this.handleSelect(value, v.propertyName)}
                        value={this.state[v.propertyName]}
                        getPopupContainer={(triggerNode) => triggerNode.parentNode}
                        disabled={!!this.state[v.disabledNode]}
                      >
                        {handleOption(v.options)}
                      </Select>
                    ) : v.nodeType === 'dateRange' ? (
                      <RangePicker
                        style={{ width: '100%' }}
                        onChange={(value) => this.handleDateRange(value, v.propertyName)}
                        value={formatDateRange(this.state[v.propertyName])}
                        getCalendarContainer={(triggerNode) => triggerNode.parentNode}
                        format={v.format ? v.format : 'YYYY-MM-DD HH:mm:ss'}
                      />
                    ) : v.nodeType === 'checkbox' ? (
                      <Checkbox
                        checked={this.state[v.propertyName]}
                        onChange={(e) => this.handleCheckbox(e, v.propertyName)}
                        disabled={!!this.state[v.disabledNode]}
                      >
                        {v.name}
                      </Checkbox>
                    ) : null}
                  </Col>
                );
              })}
            </Row>
          </Col>
          <Col span={rightCol} className="right" style={{ padding: rightCol === 24 ? 16 : null }}>
            <Button className="DTButton" type="primary" onClick={this.query}>
              {t('setting-rYnbvKNjcFxu')}
            </Button>
            <Button onClick={this.clear}>{t('setting-d26yT6CF5CkY')}</Button>
          </Col>
        </Row>
      </div>
    );
  }
}
