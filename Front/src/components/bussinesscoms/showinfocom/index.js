/**
 * 新建修改信息显示组件
 */
import React, { Component } from 'react';
import dayjs from 'dayjs';
import { connect } from 'react-redux';
import _ from 'lodash';
import './index.scss';

class ShowInfoCom extends Component {
  componentWillUnmount() {
    this.props.dispatch({
      type: 'clearGetShowInfo'
    });
  }

  render() {
    const { showInfo } = this.props;
    if (!_.isEmpty(showInfo)) {
      return (
        <div className="show-info-com">
          <div>{`ID: ${showInfo.id || ''}`}</div>
          <div>{`创建者： ${showInfo.createUserName || ''}`}</div>
          <div>{`创建时间： ${showInfo.createTime && dayjs(showInfo.createTime).format('YYYY-MM-DD HH:mm:ss')}`}</div>
          <div>{`更新者：${showInfo.updateUserName || ''}`}</div>
          <div>{`更新时间：${showInfo.updateTime && dayjs(showInfo.updateTime).format('YYYY-MM-DD HH:mm:ss')}`}</div>
        </div>
      );
    }
    return null;
  }
}

function mapStateToProps(state) {
  return {
    // 异步加载状态
    showInfo: state.showInfo
  };
}

export default connect(mapStateToProps)(ShowInfoCom);
