/**
 * 底部系统信息和版本组件
 */
import React from 'react';
import { connect } from 'react-redux';
import { t } from 'utils/translation';
import './index.scss';

const SystemInfo = (props) => {
  const { systemInfo, version, style } = props;
  return (
    <div className="systemInfoFooter" style={style}>
      <p className="copyRight">{systemInfo['aimarketer.page.copyright'] || '2019 Datatist Inc. All Rights Reserved'}</p>
      <a href="http://beian.miit.gov.cn" className="forRecord">
        {systemInfo['filing.information.text']}
      </a>
      <p className="version">{`${t('productCenter-QXUGTn5ipr')}: ${version?.tags}`}</p>
    </div>
  );
};

function mapStateToProps(state) {
  return {
    systemInfo: state.systemInfo,
    version: state.version
  };
}

export default connect(mapStateToProps)(SystemInfo);
