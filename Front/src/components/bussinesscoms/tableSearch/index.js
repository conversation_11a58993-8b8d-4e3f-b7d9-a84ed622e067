import { Button, Col, DatePicker, Form, Input, Row, Select, Space, TreeSelect } from 'antd';
import MoreScreen from 'components/featurecoms/MoreScreen/index.js';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { t } from 'utils/translation';
import './index.scss';

const { Option } = Select;
const { RangePicker } = DatePicker;

const initLayout = {
  labelCol: { span: 7 },
  wrapperCol: { span: 17 }
};

export default ({ elements, initialValues, onChange, layout }) => {
  const [form] = Form.useForm();
  const _elements = elements && Array.isArray(elements) ? elements : [];
  // const _span = span && !isNaN(span) ? span : 8;

  useEffect(() => {
    const value = {};
    initialValues &&
      _elements.forEach((item) => {
        const info = { propertyName: item.name, value: '' };
        if (Array.isArray(item.name)) info.propertyName = item.name.join('.');
        const findData = initialValues.find((n) => n.propertyName === info.propertyName);
        if (findData) {
          if (item.type === 'dateRange') {
            info.value = findData.value.split(',').map((n) => dayjs(Number(n)));
          } else {
            info.value = findData.value;
          }
          if (Array.isArray(item.name) && item.name.length === 2) {
            value[item.name[0]] = { [item.name[1]]: info.value };
          } else if (Array.isArray(item.name) && item.name.length === 3) {
            value[item.name[0]] = {
              [item.name[1]]: { [item.name[2]]: info.value }
            };
          } else {
            value[info.propertyName] = info.value;
          }
        }
      });
    form.setFieldsValue(value);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onFinish = (value) => {
    const search = [];
    _elements.forEach((item) => {
      const info = {
        operator: item.operator,
        propertyName: item.name,
        value: value[item.name]
      };
      if (Array.isArray(item.name)) {
        info.propertyName = item.name.join('.');
        if (item.name.length === 2) {
          info.value = value[item.name[0]][item.name[1]];
        } else if (item.name.length === 3) {
          info.value = value[item.name[1]][item.name[1]][item.name[2]];
        }
      }
      if (item.type === 'dateRange' && info.value && info.value.length === 2) {
        info.value = `${info.value[0].startOf('day').valueOf()},${info.value[1].endOf('day').valueOf()}`;
      }
      if (info.value !== undefined) {
        if (item.type === 'moreScreen') {
          const valueArr = info.value.split(',');
          search.push({
            connector: 'AND',
            propertyName: item.name,
            operator: valueArr[0] === '' ? 'EQ' : valueArr[0],
            value: valueArr.splice(1).toString()
          });
        } else {
          search.push(info);
        }
      }
      if (item.type === 'moreScreen') {
        const valueArr = (info.value || '').split(',');
        item.operator = valueArr[0] === '' ? 'EQ' : valueArr[0];
        item.value = valueArr.splice(1).toString();
      }
    });
    onChange({ search, page: 1 });
  };

  const onClear = () => {
    form.resetFields();
    onChange({ search: [], page: 1 });
  };

  const getComponent = (info) => {
    if (info.type === 'input') {
      return <Input allowClear placeholder={t('dataCenter-iglyCuvfypQl')} {...info.componentConfig} />;
    } else if (info.type === 'select') {
      return (
        <Select
          allowClear
          showSearch
          filterOption={(input, option) => {
            return option.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          }}
          placeholder={t('dataCenter-88c8Mits55YD')}
          {...info.componentConfig}
          getPopupContainer={(triggerNode) => triggerNode.parentNode}
        >
          {info.options &&
            info.options.map((item) => {
              const title = `${item.label}${item.customText || ''}`;
              return (
                <Option key={item.value} value={item.value} title={title}>
                  {title}
                </Option>
              );
            })}
        </Select>
      );
    } else if (info.type === 'dateRange') {
      return (
        <RangePicker
          style={{ width: '100%' }}
          {...info.componentConfig}
          getCalendarContainer={(triggerNode) => triggerNode.parentNode}
        />
      );
    } else if (info.type === 'treeSelect') {
      return (
        <TreeSelect
          allowClear
          showSearch
          filterTreeNode={(value, TreeNode) => TreeNode.props.title.indexOf(value) > -1}
          style={{ width: '100%' }}
          dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
          treeData={info.options}
          placeholder={t('dataCenter-88c8Mits55YD')}
          treeDefaultExpandAll
          {...info.componentConfig}
        />
      );
    } else if (info.type === 'moreScreen') {
      return <MoreScreen {...info} />;
    }
    return null;
  };

  return (
    <Form {...initLayout} {...layout} form={form} className="ant-advanced-search-form" onFinish={onFinish}>
      <Row gutter={24}>
        {_elements.map((n, i) => {
          return (
            <Col xs={12} sm={12} md={8} lg={8} xl={8} xxl={6} key={i}>
              <Form.Item name={n.name} label={n.label}>
                {getComponent(n)}
              </Form.Item>
            </Col>
          );
        })}
        <Col
          xs={24 - (_elements.length % (24 / 12)) * 12}
          sm={24 - (_elements.length % (24 / 12)) * 12}
          md={24 - (_elements.length % (24 / 8)) * 8}
          lg={24 - (_elements.length % (24 / 8)) * 8}
          xl={24 - (_elements.length % (24 / 8)) * 8}
          xxl={24 - (_elements.length % (24 / 6)) * 6}
          style={{ textAlign: 'right', height: 56 }}
        >
          <Space>
            <Button type="primary" htmlType="submit">
              {t('dataCenter-Im7hYnVO5HB4')}
            </Button>
            <Button onClick={onClear}>{t('dataCenter-r9CQt3z74nfK')}</Button>
          </Space>
        </Col>
      </Row>
    </Form>
  );
};
