.query-for-list {
  margin-bottom: 10px;
  padding: 16px 20px 0;
  background: #ffffff;
  border: 1px solid #eeeeee;
  border-radius: 4px;

  & .ant-select-selector,
  & .ant-picker,
  & .ant-input-number,
  & .ant-btn,
  & .ant-input-affix-wrapper {
    border-radius: 6px !important;
  }

  & .ant-input-number-handler-wrap {
    border-radius: 0px 6px 6px 0px;

    span:first-child {
      border-radius: 0px 6px 0px 0px;
    }

    span:last-child {
      border-radius: 0px 0px 6px 0px;
    }

  }

  .right {
    padding-top: 3px;
    display: flex;
    justify-content: flex-end;

    button:first-child {
      margin-right: 20px;
    }
  }
}