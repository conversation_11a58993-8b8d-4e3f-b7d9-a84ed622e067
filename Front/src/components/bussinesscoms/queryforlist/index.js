import { <PERSON><PERSON>, <PERSON>, Row } from 'antd';
import FormCom from 'components/featurecoms/formcom/index';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { t } from 'utils/translation';
import './index.scss';
// import PropTypes from 'prop-types';

// [dayjs(dayjs(parseInt(_.split(item.value, ','))).format('YYYY-MM-DD HH:mm:ss')), dayjs(dayjs(parseInt(_.split(item.value, ','))).format('YYYY-MM-DD HH:mm:ss'))]
function QueryForList(props, ref) {
  const { elements, onQuery, show, onChange, loading = false } = props;
  const formNode = useRef(null);
  // const [defaultFormData, setDefaultFormData] = useState(props.defaultFormData || {});
  const [defaultFormData, setDefaultFormData] = useState({});

  const [formData, setFormData] = useState(() => {
    // 把formData翻译为queryData
    const queryData = [];
    // console.log(_.split(props.defaultFormData.beginTime, ',').map(v => Number(v)));
    if (!_.isEmpty(elements)) {
      _.forEach(defaultFormData, (value, key) => {
        if (_.get(elements, key)) {
          if (key === 'createUser.id' || key === 'lastEnableUser') {
            value = _.split(value, ',').map((v) => Number(v));
          }
          queryData.push({
            dataType: _.get(elements, key)?.dataType,
            operator: _.get(elements, key)?.operator,
            propertyName: key,
            value: _.isUndefined(value) ? '' : value
          });
        }
      });
      return queryData || [];
    }
    return [];
  });

  useEffect(() => {
    // 把formData翻译为queryData
    if (!_.isEmpty(props.defaultFormData) && !_.isEmpty(elements)) {
      const queryData = [];
      _.forEach(props.defaultFormData, (value, key) => {
        if (_.get(elements, key)) {
          if (key === 'createUser.id' || key === 'lastEnableUser') {
            value = _.split(value, ',').map((v) => Number(v));
          }
          queryData.push({
            dataType: _.get(elements, key)?.dataType,
            operator: _.get(elements, key)?.operator,
            propertyName: key,
            value: _.isUndefined(value) ? '' : value
          });
        }
      });
      const newDefaultFormData = _.cloneDeep(props.defaultFormData);
      // newDefaultFormData.beginTime = [];
      _.forEach(newDefaultFormData, (val, propsKey) => {
        if (propsKey === 'createUser.id') {
          newDefaultFormData['createUser.id'] = _.split(newDefaultFormData['createUser.id'], ',').map((v) => Number(v));
        }

        if (propsKey === 'lastEnableUser') {
          newDefaultFormData.lastEnableUser = _.split(newDefaultFormData.lastEnableUser, ',').map((v) => Number(v));
        }
        if (propsKey === 'beginTime') {
          const dayjsTime = _.split(newDefaultFormData.beginTime, ',');
          newDefaultFormData.beginTime = [dayjs(parseInt(dayjsTime[0])), dayjs(parseInt(dayjsTime[1]))];
        }
      });
      setFormData(queryData);
      setDefaultFormData(newDefaultFormData);
      // setDefaultFormData(props.defaultFormData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.defaultFormData, elements]);

  // 表单变更，处理state数据
  const onQueryDataChange = (changedValue, allValues) => {
    // 在此处将allValues数据整理成需要的格式
    // {operator: "LIKE", propertyName: "name", value: "页面浏览事件北京"}
    // 生成所有key的数组
    const keyArr = getKeysArr([], allValues, '');
    const queryData = _.map(keyArr, (name) => {
      let value;
      // 如果是dateRange特殊处理下时间
      if (elements[name].type === 'dateRange') {
        if (_.isEmpty(_.get(allValues, `${name}`)) || _.isNil(_.get(allValues, `${name}`))) {
          value = '';
        } else {
          const a = _.get(allValues, `${name}`);
          debugger;
          value = `${a[0].startOf('day').valueOf()},${_.get(allValues, `${name}`)[1].endOf('day').valueOf()}`;
        }
      } else {
        value = _.get(allValues, `${name}`);
      }
      return {
        dataType: elements[name]?.dataType,
        operator: elements[name]?.operator,
        propertyName: name,
        value: _.isUndefined(value) ? '' : value
      };
    });
    setFormData(queryData);
    onChange && onChange(queryData);
  };

  // 清除state数据并且清空
  const onClear = async () => {
    await formNode.current.resetFields();
    setFormData([]);
    setDefaultFormData({});
    onQuery && onQuery([]);
  };

  const onCheck = async () => {
    try {
      const { scenario, tagValue } = await formNode.current.getFieldsValue();
      if (_.isEmpty(scenario) && _.isEmpty(tagValue)) {
        return false;
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  useImperativeHandle(ref, () => ({
    getFormData: () => {
      return formData;
    },
    onCheck,
    search: () => {
      onQuery && onQuery(formData);
    }
  }));

  return (
    <div className="query-for-list" style={{ display: show ? 'block' : 'none' }}>
      <Row gutter={24}>
        <Col span={18} className="left">
          <FormCom
            ref={formNode}
            layoutConfig={props.layoutConfig}
            defaultFormData={defaultFormData || {}}
            elements={elements}
            onChange={onQueryDataChange}
          />
        </Col>
        <Col span={6} className="right">
          <Button
            className="DTButton"
            type="primary"
            onClick={() => {
              onQuery && onQuery(formData);
            }}
            loading={loading}
          >
            {t('components-queryForList-search')}
          </Button>
          <Button onClick={onClear} loading={loading}>
            {t('components-queryForList-clear')}
          </Button>
        </Col>
      </Row>
    </div>
  );
}

function getKeysArr(arr = [], allValues, $prefix) {
  _.forOwn(allValues, (value, key) => {
    key = $prefix === '' ? key : `${$prefix}.${key}`;
    if (!_.isObject(value) || _.isArray(value)) {
      arr.push(key);
    } else {
      arr = getKeysArr(arr, value, key);
    }
  });

  return arr;
}

// 此组件是用于list页面上方查询组件
// QueryForList.propTypes = {
//   // 元素配置
//   elements: PropTypes.object.isRequired,
//   // 输入域变更触发
//   onChange: PropTypes.func,
//   // 点击query触发
//   onQuery: PropTypes.func,
//   // 组件是否展示
//   show: PropTypes.bool
// };

export default React.memo(forwardRef(QueryForList));
