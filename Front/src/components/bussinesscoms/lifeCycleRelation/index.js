/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState, useRef } from 'react';
import { Alert } from 'antd';
import G6 from '@antv/g6';
import RedLine from 'assets/images/redLine.png';
import GreenLine from 'assets/images/greenLine.png';
import LongLine from 'assets/images/longLine.png';
import './index.scss';

const initX = 100;
const initY = 50;
const spaceY = 100;
const spaceX = 600;
const offset = 100;

function Item(props) {
  const { nodeList, onClickNode } = props;
  const ref = useRef(null);
  const [graph, setGraph] = useState(null);
  // const [data, setData] = useState({});

  useEffect(() => {
    if (!graph) {
      const _graph = new G6.Graph({
        container: ref.current,
        width: 800,
        height: 300,
        defaultEdge: {
          type: 'polyline'
        },
        defaultNode: {
          type: 'rect',
          size: [150, 30],
          style: {
            // fill: '#bae637',
            // stroke: '#eaff8f',
            // lineWidth: 5,
            radius: 5
          }
        },
        nodeStateStyles: {
          hover: {
            fill: 'steelblue'
          }
        }
      });

      _graph.on('node:click', (evt) => {
        const node = evt.item;
        const nodeid = node.getModel().id;
        const id = nodeid.split('.')[0];
        if (onClickNode) {
          onClickNode(id);
        }
      });

      _graph.on('node:mouseenter', (evt) => {
        const node = evt.item;
        const id = node.getModel().id;
        const type = id.split('.')[1];
        const findNode = _graph.findAll('edge', (node) => {
          return node.get('model')[type] === id;
        });
        // hideItem
        findNode.forEach((n) => {
          _graph.showItem(n);
        });
        const model = node.getModel();
        model.oriLabel = model.label;

        const lastNode = _graph.findById(id);
        _graph.updateItem(lastNode, {
          style: {
            fill: 'steelblue'
          },
          labelCfg: {
            style: {
              fill: '#fff'
            }
          }
        });
      });

      _graph.on('node:mouseleave', (evt) => {
        const node = evt.item;

        const id = node.getModel().id;
        const type = id.split('.')[1];
        const findNode = _graph.findAll('edge', (node) => {
          return node.get('model')[type] === id;
        });
        // hideItem
        findNode.forEach((n) => {
          _graph.hideItem(n);
        });
        _graph.setItemState(node, 'hover', false);

        _graph.updateItem(node, {
          style: {
            fill: '#C6E5FF'
          },
          labelCfg: {
            style: {
              fill: '#555'
            }
          }
        });
      });
      setGraph(_graph);
    }
  }, []);

  useEffect(() => {
    if (graph) {
      const nodes = [];
      const edges = [];
      nodeList.forEach((n, i) => {
        nodes.push({
          id: `${n.id}.source`,
          x: initX,
          y: initY + i * spaceY,
          label: n.name
        });
        nodes.push({
          id: `${n.id}.target`,
          x: initX + spaceX,
          y: initY + i * spaceY,
          label: n.name
        });
        n.positiveTargets &&
          n.positiveTargets.forEach((w) => {
            edges.push({
              source: `${n.id}.source`,
              target: `${w}.target`,
              visible: false,
              style: {
                radius: 10,
                offset,
                endArrow: true,
                stroke: 'green'
              }
            });
          });

        n.negativeTargets &&
          n.negativeTargets.forEach((w) => {
            edges.push({
              source: `${n.id}.source`,
              target: `${w}.target`,
              visible: false,
              style: {
                radius: 10,
                offset,
                endArrow: true,
                stroke: 'red',
                lineDash: [3]
              }
            });
          });
      });

      graph.changeSize(800, initY + 100 * nodeList.length);
      graph.changeData({ nodes, edges });
    }
  }, [nodeList, graph]);

  return (
    <div className="lifeCycleRelation">
      <div className="lifeCycleContent">
        <div className="left">
          <div className="title">生命周期转化关系</div>
          <div className="trangeText">
            <img style={{ width: 50, marginRight: 10 }} alt="" src={GreenLine} /> <span>积极转化</span>{' '}
          </div>
          <div className="trangeText">
            <img style={{ width: 50, marginRight: 10 }} alt="" src={RedLine} /> <span>负面转化</span>{' '}
          </div>
        </div>
        <div className="right">
          <Alert message="鼠标移入节点，可查看对应的转化路径。点击查看配置详情" type="info" showIcon />
          <div className="mainMiddle">
            <div className="fromStyle">由该生命周期转化为</div>
            <div>
              <img style={{ width: 400 }} alt="" src={LongLine} />
            </div>
            <div className="toStyle">目标生命周期</div>
          </div>
          <div className="mainContent1" ref={ref} />
        </div>
      </div>
    </div>
  );
}

export default Item;
