.lifeCycleRelation{
    .lifeCycleContent{
        margin: 10px auto;
        width: 1100px;
        display: flex;
        .left{
            flex: 1;
            // width: 300px;
            .title{
                font-family: PingFangSC-Medium;
                font-size: 16px;
                color: rgba(0,0,0,0.85);
                line-height: 24px;
            }
            .trangeText{
                margin-top: 12px;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: #000000;
            }
        }
        .right{
            width: 800px;
            flex: 1;
            .mainMiddle{
                display: flex;
                justify-content: space-around;
                margin:auto;
                margin-top: 40px;
                .fromStyle{
                    width: 150px;
                    margin-left: 24px; 
                    text-align: center;
                    opacity: 0.8;
                    font-family: PingFangSC-Medium;
                    font-size: 16px;
                    color: #000000;
                    line-height: 24px;
                }
                .toStyle{
                    width: 150px;
                    margin-right: 24px; 
                    text-align: center;
                    opacity: 0.8;
                    font-family: PingFangSC-Medium;
                    font-size: 16px;
                    color: #000000;
                    line-height: 24px;
                }
            }
            .mainContent1{
                margin: auto;
            //    width: 800px;
                // background-color: chartreuse;
            }
        }
    }
    
}