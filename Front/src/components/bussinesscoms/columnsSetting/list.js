/**
 * filename: List
 * overview: 用来存放下方 Card 列表的 List 组件
 */

import _ from 'lodash';
import React, { useCallback } from 'react';
import { useDrop } from 'react-dnd';
import Card from './card';
import CheckboxListItem from './checkboxListItem';

const style = {
  backgroundColor: 'white',
  minHeight: '100px',
  maxHeight: '300px',
  overflow: 'auto',
  padding: '10px',
  width: 250
};

const ItemTypes = {
  Card: 'card'
};

const List = ({ cardList, changeCardList }) => {
  const [, drop] = useDrop({
    accept: ItemTypes.Card
  });

  const moveCard = useCallback(
    (id, targetIndex) => {
      /**
       * 1、把拖拽的元素放到hover元素的位置上
       */
      // [cardList[dragIndex], cardList[hoverIndex]] = [cardList[hoverIndex], cardList[dragIndex]];
      // 删除拖拽元素
      const newCardList = _.cloneDeep(cardList);

      const findIndex = _.findIndex(newCardList, (item) => item.id === id);

      const key = newCardList[findIndex];

      newCardList.splice(findIndex, 1);

      if (targetIndex === 0) {
        newCardList.unshift(key);
      } else {
        newCardList.splice(targetIndex, 0, key);
      }

      changeCardList(newCardList);
      // eslint-disable-next-line
    },
    [cardList]
  );

  return (
    <div style={style} ref={drop} className="coloumSettingList">
      {cardList.map((item, index) => (
        <Card
          index={index}
          key={item.id}
          id={item.id}
          end={(id, targetIndex) => {
            moveCard(id, targetIndex);
          }}
        >
          <div style={{ display: 'flex' }}>
            <CheckboxListItem changeCardList={changeCardList} columns={cardList} {...item} />
            <div style={{ marginLeft: 'auto' }}>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect opacity="0.01" width="16" height="16" fill="black" />
                <rect x="5" y="3" width="2" height="2" fill="black" fillOpacity="0.65" />
                <rect x="9" y="3" width="2" height="2" fill="black" fillOpacity="0.65" />
                <rect x="5" y="7" width="2" height="2" fill="black" fillOpacity="0.65" />
                <rect x="9" y="7" width="2" height="2" fill="black" fillOpacity="0.65" />
                <rect x="5" y="11" width="2" height="2" fill="black" fillOpacity="0.65" />
                <rect x="9" y="11" width="2" height="2" fill="black" fillOpacity="0.65" />
              </svg>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default List;
