// @import 'assets/css/variable.scss';
.columns-setting-com {
  .columns-setting-header {
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .columns-setting-handle {
    display: flex;
    padding: 5px;
    justify-content: flex-end;
    align-items: center;

    .ant-btn {
      height: 25px;
      padding: 0 10px;

      &:first-child {
        margin-right: 10px;
      }
    }
  }

  .coloumSettingList {
    .ant-typography-ellipsis-single-line {
      width: 170px;
    }
  }
  
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    /**/
  }
}