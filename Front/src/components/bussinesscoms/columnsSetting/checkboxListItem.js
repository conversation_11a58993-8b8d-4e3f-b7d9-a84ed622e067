import React from 'react';
import { Checkbox, Typography } from 'antd';
import _ from 'lodash';

const { Text } = Typography;

const CheckboxListItem = ({ columnKey, className, columns, title, changeCardList }) => {
  const config = _.find(columns, (item) => item.columnKey === columnKey);
  return (
    <span className={`${className}-list-item`} key={columnKey}>
      <Checkbox
        onChange={(e) => {
          if (columnKey) {
            const tempConfig = _.find(columns, (item) => item.columnKey === columnKey) || {};
            const newSetting = { ...tempConfig };
            newSetting.show = e.target.checked;
            const newColumns = _.cloneDeep(columns);
            const findIndex = _.findIndex(columns, (item) => item.columnKey === columnKey);
            newColumns[findIndex] = newSetting;
            changeCardList(newColumns);
          }
        }}
        checked={config?.show !== false || false}
      >
        <Text
          // style={{ width: 150 }}
          ellipsis={title}
        >
          {title}
        </Text>
      </Checkbox>
    </span>
  );
};

export default CheckboxListItem;
