import { Button, Checkbox } from 'antd';
import _ from 'lodash';
import React, { useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { t } from 'utils/translation';

import List from './list';

import './index.scss';

export default function ColumnsSetting(props) {
  const [cardList, setCardList] = useState(() => {
    return props.data;
  });

  // 设置全部列选中状态
  const [checked, setChecked] = useState(() => {
    const hasNoChecked = _.find(props.data, (item) => item.show === false);
    return !hasNoChecked;
  });

  const changeCardList = (list) => {
    if (_.isEmpty(list)) return;
    const hasNoChecked = _.find(list, (item) => item.show === false);
    setChecked(!hasNoChecked);
    setCardList(list);
    props.handleSetColumns && props.handleSetColumns(list);
  };

  const resetList = () => {
    changeCardList(props.data);
  };

  const onChange = (e) => {
    setChecked(e.target.checked);
    const newCardList = _.map(cardList, (item) => ({
      ...item,
      show: e.target.checked
    }));
    setCardList(newCardList);
    props.handleSetColumns && props.handleSetColumns(newCardList);
  };

  const handleCancel = () => {
    props.changeVisible && props.changeVisible(false);
  };

  const handleSubmit = () => {
    props.handleSubmit && props.handleSubmit(cardList);
  };

  return (
    <div className="columns-setting-com">
      <div className="columns-setting-header">
        <Checkbox checked={checked} onChange={onChange}>
          {t('components-columnsSetting-all')}
        </Checkbox>
        <a onClick={resetList}>{t('components-columnsSetting-reset')}</a>
      </div>
      <DndProvider backend={HTML5Backend}>
        <List cardList={cardList} changeCardList={changeCardList} />
      </DndProvider>
      <div className="columns-setting-handle" hidden={props.hideOK}>
        <Button onClick={handleCancel}>{t('components-columnsSetting-cancel')}</Button>
        <Button type="primary" onClick={handleSubmit}>
          {t('components-columnsSetting-ok')}
        </Button>
      </div>
    </div>
  );
}
