import { Input } from 'antd';
import React from 'react';

const NumericInput = (props) => {
  const { value, onChange, min = 0 } = props;
  const handleChange = (e) => {
    try {
      const { value: inputValue } = e.target;
      if (inputValue && inputValue.toString().length > 3) throw new Error('最大长度为3');
      const reg = new RegExp('^[0-9]\\d*$');
      if (reg.test(inputValue) || inputValue === '') {
        onChange(inputValue);
      }
    } catch (err) {
      console.error(err.message);
    }
  };

  // useEffect(() => {
  //   if (min !== 0) onChange(min);
  // }, [min]);

  const handleBlur = () => {
    onChange(value === '' || value < min ? min : value);
  };

  return (
    <Input {...props} onChange={handleChange} style={{ width: 32, padding: 0 }} maxLength={3} onBlur={handleBlur} />
  );
};

export default NumericInput;
