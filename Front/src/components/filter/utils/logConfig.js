export default {
  __ROOT__: {
    level: 'info'
  },
  FilterValue: {
    level: 'info'
  },
  InputValidator: {
    level: 'info'
  },
  FilterList: {
    level: 'info'
  },
  FilterModel: {
    level: 'info'
  },
  TagFilter: {
    level: 'info'
  },
  FilterListGroup: {
    level: 'info'
  },
  PageTagmanageCreate: {
    level: 'info'
  },
  Filter: {
    level: 'info'
  },
  FilterListModel: {
    level: 'info'
  },
  FilterListGroupModel: {
    level: 'info'
  },
  TypeValidateInput: {
    level: 'info'
  },
  TypeBetweenValidateInput: {
    level: 'info'
  }
};
