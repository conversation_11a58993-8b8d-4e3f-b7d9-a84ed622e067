import React from 'react';
import { Select, Form } from 'antd';

const { Option } = Select;

const SelectElem = (props) => {
  const { name, label, rules, labelCol, wrapperCol, componentOptions = {} } = props.item;
  const { disabled, placeholder, allowClear, onChange, options, filterOption, showSearch } = componentOptions;
  const optionItems = options.map((option) => {
    const { label, value } = option;
    return (
      <Option value={value} key={label} title={label}>
        {label}
      </Option>
    );
  });

  return (
    <Form.Item name={name} label={label} rules={rules} labelCol={labelCol} wrapperCol={wrapperCol}>
      <Select
        disabled={disabled}
        placeholder={placeholder}
        allowClear={allowClear}
        onChange={onChange}
        filterOption={filterOption}
        showSearch={showSearch}
      >
        {optionItems}
      </Select>
    </Form.Item>
  );
};

export default SelectElem;
