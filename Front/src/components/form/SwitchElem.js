import React from 'react';
import { Form, Switch, Col } from 'antd';

const DatePickerElem = (props) => {
  const { label, name, rules, colSpan, componentOptions = {} } = props.item;
  return (
    <Col span={colSpan}>
      <Form.Item label={label} name={name} rules={rules} valuePropName="checked">
        <Switch {...componentOptions} />
      </Form.Item>
    </Col>
  );
};

export default DatePickerElem;
