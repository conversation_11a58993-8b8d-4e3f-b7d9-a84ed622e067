import React from 'react';
import { Upload, Form } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

const UploadElem = (props) => {
  // console.log(props);
  const {
    item: { name, label, rules, extra, componentOptions = {} }
  } = props;
  const { pictureUrl } = componentOptions;
  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>点击上传图片</div>
    </div>
  );

  return (
    <Form.Item name={name} label={label} rules={rules} extra={extra}>
      <Upload {...componentOptions}>
        {pictureUrl ? <img src={pictureUrl} alt="example" style={{ width: '100%' }} /> : uploadButton}
      </Upload>
    </Form.Item>
  );
};

export default UploadElem;
