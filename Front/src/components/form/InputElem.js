import React from 'react';
import { Input, Form, Col } from 'antd';

const InputElem = (props) => {
  // console.log(props,'input props');
  const { item, textArea } = props;
  const { TextArea } = Input;
  const { label, name, rules, labelCol, wrapperCol, colSpan, componentOptions = {} } = item;
  // const { allowClear,disabled }= componentOptions
  return (
    <Col span={colSpan}>
      <Form.Item name={name} label={label} rules={rules} labelCol={labelCol} wrapperCol={wrapperCol}>
        {textArea ? <TextArea {...componentOptions} /> : <Input {...componentOptions} />}
      </Form.Item>
    </Col>
  );
};

export default InputElem;
