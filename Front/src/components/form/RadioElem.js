import React from 'react';
import { Form, Radio } from 'antd';

const RadioElem = (props) => {
  const { label, name, rules, componentOptions = {} } = props.item;
  const { options } = componentOptions;
  const Radios = options.map((option) => {
    const { text, value } = option;
    return (
      <Radio value={value} key={value}>
        {text}
      </Radio>
    );
  });

  return (
    <Form.Item label={label} name={name} rules={rules}>
      <Radio.Group>{Radios}</Radio.Group>
    </Form.Item>
  );
};

export default RadioElem;
