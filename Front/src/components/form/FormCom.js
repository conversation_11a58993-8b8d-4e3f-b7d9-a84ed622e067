import React, { forwardRef } from 'react';
import { Form, Row } from 'antd';
import _ from 'lodash';
import InputElem from './InputElem';
import SelectElem from './SelectElem';
import DatePickerElem from './DatePickerElem';
import UploadElem from './UploadElem';
import RadioElem from './RadioElem';
import SwitchElem from './SwitchElem';
import CheckboxElem from './CheckboxElem';
import SketchColorCom from './sketchcolorcom/index';

const FormCom = (props, ref) => {
  const { items } = props;
  const [form] = Form.useForm();
  const { rowSpan } = items;

  if (!items || (items && items.length === 0)) {
    return null;
  }

  const formItems = items.map((item) => {
    const { type, name, componentOptions, ...other } = item;
    let Component = null;

    switch (type) {
      case 'Input':
        Component = <InputElem item={item} key={name} />;
        break;
      case 'TextArea':
        Component = <InputElem item={item} key={name} textArea="textArea" />;
        break;
      case 'Select':
        Component = <SelectElem item={item} key={name} />;
        break;
      case 'DatePicker':
        Component = <DatePickerElem item={item} key={name} />;
        break;
      case 'sketchColorInput':
        Component = (
          <Form.Item name={name} key={name} {...other}>
            <SketchColorCom {...componentOptions} autoComplete="off" />
          </Form.Item>
        );
        break;
      case 'Upload':
        Component = <UploadElem item={item} key={name} />;
        break;
      case 'Radio':
        Component = <RadioElem item={item} key={name} />;
        break;
      case 'Switch':
        Component = <SwitchElem item={item} key={name} />;
        break;
      case 'Checkbox':
        Component = <CheckboxElem item={item} key={name} />;
        break;
      default:
        break;
    }

    return Component;
  });

  // 提交
  // const onSubmit = (fieldsValue) => {
  //   const values = {
  //     ...fieldsValue,
  //     'createTime': fieldsValue['createTime'].format('YYYY-MM-DD HH:mm:ss'),
  //   }
  //   console.log(values);
  // }

  const onValuesChange = (changedValues, allValues) => {
    // 当前组件变更是否触发其它数据域的验证规则
    const key = getPath(changedValues);
    const realValue = _.get(allValues, key);
    props.onChange && props.onChange(changedValues, allValues, key, realValue);
  };

  const getPath = (currentValue) => {
    const value = _.values(currentValue)[0];
    if (!_.isObject(value) || _.isArray(value)) {
      // 如果是基本数据类型
      return Object.keys(currentValue)[0];
    } else {
      return `${Object.keys(currentValue)[0]}.${getPath(value)}`;
    }
  };

  return (
    <Form form={form} onValuesChange={onValuesChange} initialValues={props.initialValues} ref={ref}>
      <Row span={rowSpan}>{formItems}</Row>
    </Form>
  );
};

// FormCom.propTypes = {
//   // 元素配置
//   elements: PropTypes.object.isRequired,
//   // 初始化FormData
//   defaultFormData: PropTypes.object,
//   // 布局对象
//   layoutConfig: PropTypes.object,
//   // 表单变更事件
//   onChange: PropTypes.func //
//   // 外围组件可以通过调用实例的formNode.current.validateFields获取err和数据，通过调用实例的formNode.current.resetFields()重置数据
// };

export default forwardRef(FormCom);
