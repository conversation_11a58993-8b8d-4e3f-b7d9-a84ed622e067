import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { CloseCircleFilled } from '@ant-design/icons';
import { Input, Popover } from 'antd';
import { BlockPicker } from 'react-color';
import _ from 'lodash';

import './index.scss';

class BlockColorCom extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: this.props.value || '',
      visible: false
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (!_.isEqual(this.props.value, nextProps.value)) {
      this.setState({ value: nextProps.value });
    }
  }

  hide = () => {
    this.setState({
      visible: false
    });
  };

  handleVisibleChange = (visible) => {
    this.setState({ visible });
  };

  /**
   * 选择颜色
   *
   * @memberof BlockColorCom
   */
  handleChange = (color) => {
    const _color = _.toUpper(_.has(color, 'hex') && color.hex ? color.hex : '#FFFFFF');
    if (this.state.color !== _color) {
      setTimeout(() => {
        this.setState({ value: _color });
        if (this.props.onChange) {
          this.props.onChange(_color);
        }
      }, 150);
    }
  };

  clear = (e) => {
    e.stopPropagation();
    this.setState(
      {
        value: ''
      },
      () => {
        if (this.props.onChange) {
          this.props.onChange('');
        }
      }
    );
  };

  render() {
    return (
      <Popover
        content={<BlockPicker disableAlpha color={this.state.value} onChange={this.handleChange} />}
        title="选择颜色"
        trigger="click"
        className="blockcolorcom"
        open={this.state.visible}
        onOpenChange={this.handleVisibleChange}
      >
        <div className={`blockcolorcom-input-wrap ${this.state.value ? 'input-has-color' : ''}`}>
          <Input
            placeholder="点击选择颜色"
            {..._.omit(this.props, ['value', 'onChange'])}
            maxLength={7}
            readOnly
            value={this.state.value}
          />
          {this.state.value && (
            <CloseCircleFilled
              onClick={this.clear}
              className="blockcolorcom-input-close-icon anticon anticon-close-circle ant-input-clear-icon"
            />
          )}
        </div>
        {this.state.value && <div className="preview" style={{ background: this.state.value }} />}
      </Popover>
    );
  }
}

BlockColorCom.propTypes = {
  // 未绑定,已绑定,[true,false]
  value: PropTypes.string,
  // 选择颜色事件
  onChange: PropTypes.func
};

export default BlockColorCom;
