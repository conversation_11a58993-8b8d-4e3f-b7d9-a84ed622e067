import React from 'react';
import { Select, Input, Form } from 'antd';

const InputElem = (props) => {
  const { item } = props;
  const { label, name, rules } = item;
  return (
    <Form.Item name={name} label={label} rules={rules}>
      <Input />
    </Form.Item>
  );
};

const SelectElem = (props) => {
  const {
    item: { name, label, rules, options }
  } = props;
  const { Option } = Select;

  const optionItems = options.map((option) => {
    const { label, value } = option;
    return (
      <Option value={value} key={label}>
        {label}
      </Option>
    );
  });

  return (
    <Form.Item name={name} label={label} rules={rules}>
      <Select>{optionItems}</Select>
    </Form.Item>
  );
};
export { InputElem, SelectElem };
