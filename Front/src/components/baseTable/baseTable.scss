// @import 'assets/css/variable.scss';

.baseTable {
  background-color: #fff;

  td {
    &.td-set {
      span {
        cursor: pointer;

        &.disabled {
          color: #ccc;
          cursor: not-allowed;
        }

        span {
          // &:first-child {
          //   min-width: 56px;
          // }
          display: inline-block;
          color: $primary_color;
        }

        i {
          color: rgba(0, 0, 0, 0.09);
        }

        a {
          width: 66px;
          padding-right: 10px;
        }
      }
    }
  }
}