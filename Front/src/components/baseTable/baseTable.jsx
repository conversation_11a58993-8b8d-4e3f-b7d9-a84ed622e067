import React, { Component } from 'react';
import { Table, Dropdown, Modal, message, Divider } from 'antd';
import { withRouter } from 'react-router-dom';
import _ from 'lodash';
import './baseTable.scss';

const { confirm } = Modal;

class baseTable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      tableParams: {
        size: 10,
        page: +localStorage.getItem('page') || 1,
        search: [],
        sorts: [
          {
            direction: 'desc',
            propertyName: 'updateTime'
          }
        ],
        ...props.tableParams
      },
      loading: false,
      data: [],
      totalCount: null
    };

    this.handleTableChange = this.handleTableChange.bind(this);
    this.query = this.query.bind(this);
    // 自动刷新的句柄，卸载时需要删掉
    this.refreshIntevalHandler = null;
  }

  static getDerivedStateFromProps(props, state) {
    if (JSON.stringify(props.tableParams.search) !== JSON.stringify(state.tableParams.search)) {
      return {
        tableParams: { ...state.tableParams, ...props.tableParams }
      };
    }
    return null;
  }

  componentDidMount() {
    localStorage.removeItem('page');
    this.refresh();
    const { autoRefreshTime } = this.props.tableConfig;
    if (autoRefreshTime) {
      this.refreshIntevalHandler = setInterval(() => {
        this.refresh();
      }, autoRefreshTime);
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (JSON.stringify(prevState.tableParams.search) !== JSON.stringify(this.state.tableParams.search)) {
      this.query(this.state.tableParams);
    }
  }

  componentWillUnmount() {
    if (this.refreshIntevalHandler) {
      clearInterval(this.refreshIntevalHandler);
    }
  }

  refresh() {
    const { tableParams } = this.state;
    this.query(tableParams);
  }

  async query(tableParams) {
    const {
      tableConfig: { serviceName, method }
    } = this.props;
    this.setState({ loading: true });
    let data;
    try {
      data = await serviceName[method](tableParams);
    } catch (error) {
      data = {
        content: [],
        totalElements: null
      };
    }
    const { content, totalElements } = data;
    this.setState({ data: content, totalCount: totalElements, loading: false });
  }

  // 操作
  async handel(v, val) {
    const {
      tableConfig: { serviceName }
    } = this.props;
    const { tableParams } = this.state;
    const _this = this;
    switch (v.type) {
      case 'DELETE':
        confirm({
          content: v.content,
          okText: '删除',
          okType: 'danger',
          cancelText: '取消',
          async onOk() {
            try {
              await serviceName.delete(val.id);
              _this.query(tableParams);
              message.success('删除成功', 1);
            } catch (error) {
              console.error(error);
            }
          }
        });
        break;
      case 'DRAFT':
      case 'DETAIL':
        this.props.history.push(v.content);
        break;
      case 'RUNNING':
        confirm({
          content: v.content,
          okText: v.name.slice(0, 2),
          okType: 'danger',
          cancelText: '取消',
          async onOk() {
            try {
              await serviceName.pause(val.id);
              _this.query(tableParams);
            } catch (error) {
              console.error(error);
            }
          }
        });
        break;
      case 'COPY':
        this.props.history.push({
          pathname: '/aimarketer/home/<USER>/create',
          state: { id: val.id, campaignType: val.campaignType }
        });
        break;
      default:
        break;
    }
  }

  /** 表格操作包含排序/翻页/每页显示条数 */
  handleTableChange = (page, filter, sorter) => {
    localStorage.setItem('page', page.current || 1);
    const { tableParams } = this.state;
    const { current, pageSize } = page;
    const direction = sorter.order ? sorter.order.substr(0, sorter.order.length - 3) : tableParams.sorts[0].direction;
    const propertyName = sorter.field ? sorter.field : 'updateTime';
    this.setState(
      {
        tableParams: {
          ...tableParams,
          sorts: [{ direction, propertyName }],
          page: current,
          size: pageSize
        }
      },
      () => {
        this.query(this.state.tableParams);
      }
    );
  };

  getDataSource = () => {
    return this.state.data;
  };

  render() {
    const {
      tableParams: { page, size },
      loading,
      data,
      totalCount
    } = this.state;
    const {
      tableConfig: { srcPath, handles, columns },
      pagination
    } = this.props;

    _.forEach(columns, (item) => {
      if (item.dataIndex === 'set') {
        item.render = (text, val) => {
          return <span>{domSet(text, val)}</span>;
        };
      }
    });

    /** 操作按钮 */
    const domSet = (text, val) => {
      const [a, b] = text; // 二维数组，区分更多里的
      // leftDom为更多左边的操作
      const leftDom = _.map(a, (v, i) => {
        return v.type === 'DETAIL' || v.type === val.status || v.type === val.phase ? (
          <span key={i} onClick={() => this.handel(v, val)}>
            {v.name}
          </span>
        ) : null;
      });
      // 判断更多左边的操作是否渲染
      const isLeft = _.filter(a, (v) => v.type === val[this.props.filterKey || 'status']);
      return (
        <>
          {leftDom}
          {b?.length ? (
            <>
              {isLeft.length ? <Divider type="vertical" /> : null}
              <Dropdown
                trigger={['click']}
                menu={{
                  items: b?.length
                    ? _.map(b, (v, i) => {
                        return {
                          label: v.name,
                          key: i,
                          onClick: () => {
                            this.handel(v, val);
                          }
                        };
                      })
                    : []
                }}
              >
                <span>更多</span>
              </Dropdown>
            </>
          ) : null}
        </>
      );
    };

    /** 处理列表数据 */
    _.forEach(data, (v) => {
      // v.id = this.props.tableConfig.method === 'listCalcLogs' ? v.campaignId : v.id;
      v.key = v.id;
      v.src = srcPath;
      const arr = [];
      _.forEach(handles, (item) => {
        const arrChild = [];
        _.forEach(item, (value) => {
          arrChild.push({
            name: value.name,
            content:
              value.type === 'DRAFT'
                ? `${value.content}${v.id}&type=${v.campaignType}`
                : value.type === 'DETAIL'
                  ? `${value.content}${v.id}`
                  : value.content,
            type: value.type
          });
        });
        arr.push(arrChild);
      });
      v.set = arr;
    });

    return (
      <div className="baseTable">
        <Table
          columns={columns}
          dataSource={data}
          bordered={false}
          loading={loading}
          onChange={this.handleTableChange}
          pagination={
            pagination ?? {
              current: page,
              total: totalCount,
              defaultPageSize: size,
              showQuickJumper: true,
              showSizeChanger: true,
              showLessItems: true,
              pageSizeOptions: ['10', '20', '50']
            }
          }
        />
      </div>
    );
  }
}

export default withRouter(baseTable);
