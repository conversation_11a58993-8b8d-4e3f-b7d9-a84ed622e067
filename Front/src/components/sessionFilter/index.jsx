import { DeleteOutlined } from '@ant-design/icons';
import { Select } from 'antd';
import _ from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import DataEngineService from 'service/dataEngineService';
import FilterField from './FilterField';
import FilterOperator from './FilterOperator';
import './index.scss';

const dataEngineService = new DataEngineService();

export default function SessionFilter({ value, sessionData = [], sessionList, onChange, disabled }) {
  const [propertyList, setPropertyList] = useState([]);
  useEffect(() => {
    (async () => {
      if (!value?.event?.id) return;
      const _propertyList = await dataEngineService.propertyList({
        eventId: value.event.id
      });
      setPropertyList(_propertyList);
    })();
  }, [value?.event?.id]);

  const handleFieldChange = useCallback(
    (e, index) => {
      if (onChange) {
        const _sessionData = _.cloneDeep(sessionData);
        _sessionData[index] = {
          ..._sessionData[index],
          ...e,
          name: '',
          operator: undefined,
          value: undefined
        };
        onChange && onChange(_sessionData);
      }
    },
    [onChange]
  );

  const handleOperatorChange = useCallback(
    (e, index) => {
      if (onChange) {
        const _sessionData = _.cloneDeep(sessionData);
        _sessionData[index] = { ..._sessionData[index], ...e };
        onChange && onChange(_sessionData);
      }
    },
    [onChange]
  );

  const handleSessionChange = useCallback(
    (e, index) => {
      if (onChange) {
        const _sessionData = _.cloneDeep(sessionData);
        _sessionData[index].value = e;
        onChange && onChange(_sessionData);
      }
    },
    [onChange]
  );

  const deleteSession = (index) => {
    const _sessionData = _.cloneDeep(sessionData);
    _sessionData.splice(index, 1);
    onChange && onChange(_sessionData);
  };

  return (
    <>
      {sessionData &&
        sessionData.map((item, index) => (
          <div className="sessionFilter" key={index}>
            <FilterField
              disabled={disabled}
              value={item}
              propertyList={propertyList}
              onChange={(e) => handleFieldChange(e, index)}
            />
            <FilterOperator disabled={disabled} value={item} onChange={(e) => handleOperatorChange(e, index)} />
            <Select
              disabled={disabled}
              placeholder="会话数据"
              style={{ width: '30%' }}
              allowClear
              showSearch
              optionFilterProp="label"
              value={item.value}
              onChange={(e) => handleSessionChange(e, index)}
              options={sessionList
                .filter((v) => v.dataType === item.fieldType)
                .map((item) => ({
                  value: item.dataKeyword,
                  label: `${item.dataKeyword}[${item.dataType}]`,
                  title: `${item.dataKeyword}[${item.dataType}]`
                }))}
            />
            <span className="leading-[32px]" hidden={disabled}>
              <DeleteOutlined onClick={() => deleteSession(index)} />
            </span>
          </div>
        ))}
    </>
  );
}
