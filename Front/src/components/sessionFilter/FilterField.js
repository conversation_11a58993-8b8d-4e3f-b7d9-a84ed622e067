import { DownOutlined, SyncOutlined } from '@ant-design/icons';
import { Dropdown, Input, Menu } from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import useDebounce from './useDebounce';

export default function FilterField({ propertyList, value, onChange, disabled }) {
  const [propertyMap, setPropertyMap] = useState({});
  const [searchText, setSearchText] = useState(value?.field);
  const [menuVisible, setMenuVisible] = useState(false);
  const [fetching, setFetching] = useState(false);
  const debounceSearchText = useDebounce(searchText, 200);

  useEffect(() => {
    (() => {
      const levelMap = _.groupBy(propertyList, (v) => v.level1);
      _.keys(levelMap).forEach((level1) => {
        levelMap[level1] = _.groupBy(levelMap[level1], (v) => v.level2);
      });
      setFetching(false);
      setPropertyMap(levelMap);
    })();
  }, [propertyList]);

  useEffect(() => {
    if (menuVisible) {
      setSearchText('');
    } else {
      setSearchText(value?.fieldName || '');
    }
  }, [value?.fieldName, menuVisible, propertyList]);

  useEffect(() => {
    if (!menuVisible) return;
    setFetching(true);
    // eslint-disable-next-line no-useless-return
    if (debounceSearchText === value?.fieldName) return;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceSearchText, menuVisible, value]);

  const propertyMenu = (properties) => {
    const isSession = properties.some((item) => item.isSession);

    return properties
      .filter((v) => !searchText || v.field.indexOf(searchText) >= 0 || v.fieldName.indexOf(searchText) >= 0)
      .map((p, i) => {
        return (
          <Menu.Item key={`${p.level1}_${p.level2}_${p.fieldName}_${i}`} onClick={() => onSelectProperty(p)}>
            {p.fieldName}[{isSession ? p.dataDisplayType || p.fieldType : p.field}]
          </Menu.Item>
        );
      });
  };

  const level2Menu = (children, level2) => {
    if (!level2) {
      if (_.isArray(children)) {
        return propertyMenu(children);
      }
    }

    return (
      <Menu.ItemGroup title={level2} key={level2}>
        {propertyMenu(children)}
      </Menu.ItemGroup>
    );
  };

  const onSelectProperty = (property) => {
    onChange(property);
    setMenuVisible(false);
  };

  const level1Menu = (children, level1) => {
    if (!level1) {
      if (_.isArray(children)) {
        return propertyMenu(children);
      } else if (_.isObject(children) && !Object.entries(children)[0][0]) {
        return propertyMenu(Object.entries(children)[0][1]);
      }
    }

    return (
      <Menu.ItemGroup title={level1} key={level1}>
        {_.map(children, level2Menu)}
      </Menu.ItemGroup>
    );
  };

  const menu = () => {
    return (
      <Menu
        style={{
          maxHeight: 400,
          overflowY: 'auto',
          maxWidth: 500,
          overflowX: 'auto'
        }}
      >
        {fetching && _.isEmpty(propertyMap) ? (
          <Menu.Item>
            <SyncOutlined spin />
            loading...
          </Menu.Item>
        ) : (
          _.map(propertyMap, level1Menu)
        )}
      </Menu>
    );
  };

  return (
    <Dropdown
      disabled={disabled}
      arrow
      getPopupContainer={(triggerNode) => triggerNode.parentNode}
      overlay={menu}
      trigger={['click']}
      onOpenChange={setMenuVisible}
    >
      <div className="clickWrapper">
        <Input
          disabled={disabled}
          className="ant-dropdown-link"
          placeholder="属性"
          // suffix={}
          onChange={(e) => setSearchText(e.target.value)}
          onFocus={(event) => event.target.select()}
          value={searchText}
        />
        <DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />
      </div>
    </Dropdown>
  );
}
