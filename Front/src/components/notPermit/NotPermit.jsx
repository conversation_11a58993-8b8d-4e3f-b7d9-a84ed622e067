import React, { Component } from 'react';
import { Result, Button } from 'antd';
import { Link } from 'react-router-dom';

class NotPermit extends Component {
  render() {
    return (
      <Result
        status="404"
        title="404"
        subTitle="抱歉，您没有该系统的权限"
        extra={
          <Link to={'/aimarketer/ssologin' || '/aimarketer/login'}>
            <Button type="primary">回到登录页面</Button>
          </Link>
        }
      />
    );
  }
}

export default NotPermit;
