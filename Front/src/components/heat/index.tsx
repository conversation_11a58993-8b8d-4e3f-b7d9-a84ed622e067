import heatStatistics from '@/service/heatStatistics';
import { CopyOutlined, LinkOutlined, ShareAltOutlined, StarOutlined } from '@ant-design/icons';
import { Spin, Tooltip } from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { t } from 'utils/translation';

interface Props {
  dataId: string | string[];
  dataType: 'CAMPAIGN' | 'CAMPAIGN_TEMPLATE' | 'USER_LABEL';
  notSearch?: boolean;
  findValue: Value;
  componentKey?: number;
}

interface Value {
  operateType: 'FAVORITE' | 'COPY' | 'USE';
  totalCount: number;
  dataType?: Props['dataType'];
  source?: 'CAMPAIGN' | 'SEGMENT' | 'CHART_CONFIG';
}

/**
 *
 * @param dataId 活动id
 * @param dataType 类型
 * @param notSearch 是否搜索
 * @param findValue 不搜索默认值
 * @returns
 */
export default function Heat({ dataId, dataType, notSearch = false, findValue, componentKey }: Props) {
  const [value, setValue] = useState<Value[] | []>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    init();
  }, [componentKey || '']);

  const init = async () => {
    if (notSearch) return;
    setLoading(true);
    const _value = await heatStatistics.listBy([
      {
        operator: 'EQ',
        propertyName: 'projectId',
        value: localStorage.getItem('projectId')
      },
      {
        operator: 'IN',
        propertyName: 'dataId',
        value: dataId
      },
      {
        operator: 'EQ',
        propertyName: 'dataType',
        value: dataType
      },
      {
        operator: 'IN',
        propertyName: 'operateType',
        value: 'FAVORITE,COPY,USE'
      }
    ]);
    setValue(_value);
    setLoading(false);
  };

  const renderDom = () => {
    switch (dataType) {
      case 'CAMPAIGN':
        const findFavorite = _.find(value, (item) => item.operateType === 'FAVORITE')?.totalCount || 0;
        const findCopy = _.find(value, (item) => item.operateType === 'COPY')?.totalCount || 0;
        return (
          <>
            <Tooltip title={t('dataCenter-YhJh6rinMMTi') + findFavorite}>
              <StarOutlined />
              <span className="ml-4">{findFavorite}</span>
            </Tooltip>

            <Tooltip title={t('dataCenter-XElsZcA3R4z2') + findCopy}>
              <CopyOutlined />
              <span className="ml-4">{findCopy}</span>
            </Tooltip>
          </>
        );
      case 'CAMPAIGN_TEMPLATE':
        const findUse = notSearch
          ? findValue?.totalCount || 0
          : _.find(value, (item) => item.operateType === 'USE')?.totalCount || 0;
        return (
          <div>
            <Tooltip title={t('dataCenter-qW7ZIzg3nV9C') + findUse}>
              <LinkOutlined />
              <span className="font-[500] mx-[4px] ">{findUse}</span>
            </Tooltip>
          </div>
        );
      case 'USER_LABEL':
        const findUserLabel = _.filter(value, (item) => item.dataType === 'USER_LABEL');

        const segmentCount = _.find(findUserLabel, (i) => i.source === 'SEGMENT')?.totalCount || 0;
        const campaignCount = _.find(findUserLabel, (i) => i.source === 'CAMPAIGN')?.totalCount || 0;
        const chatCount = _.find(findUserLabel, (i) => i.source === 'CHART_CONFIG')?.totalCount || 0;
        return (
          <div>
            <Tooltip
              title={t('dataCenter-tQTV34S3RBpz') + segmentCount + t('dataCenter-andpH9OmmqrE') + campaignCount + t('dataCenter-BA8a4Uc1Gfpv') + chatCount}
            >
              <ShareAltOutlined />
              <span className="ml-4">{segmentCount + campaignCount + chatCount}</span>
            </Tooltip>
          </div>
        );
      default:
    }
  };
  return (
    <Spin spinning={loading}>
      <div className="w-[95px] flex justify-between text-sm text-[#8c8c8c] px-16">{renderDom()}</div>
    </Spin>
  );
}
