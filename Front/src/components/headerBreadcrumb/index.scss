// .breadcrumb {
//   background: #fff;
//   margin: 0 -24px;
//   padding: 0 24px;

//   .ant-breadcrumb {
//     margin: 12px 0;
//     color: rgba(0, 0, 0, .45);
//     font-size: 14px;
//   }

//   .createTitle {
//     font-size: 24px;
//     color: rgba(0, 0, 0, .85);
//     margin-bottom: 16px;
//     font-weight: 600;
//   }
// }

// .breadcrumb-full-screen {
//   background: #fff;
//   margin: 0 -24px;
//   padding: 0 24px;

//   .ant-breadcrumb {
//     margin: 12px 0;
//     color: rgba(0, 0, 0, .45);
//     font-size: 14px;
//   }

//   .createTitle {
//     font-size: 24px;
//     color: rgba(0, 0, 0, .85);
//     margin-bottom: 16px;
//     font-weight: 600;
//   }
// }

%createTitle-common {
  font-size: 20px;
  color: rgba(0, 0, 0, .85);
  margin-bottom: 8px;
  font-weight: 600;
}

%breadcrumb-common {
  background: #fff;
  margin: 0 -24px;
  padding: 0 24px;
}

%ant-breadcrumb {
  color: rgba(0, 0, 0, .45);
  font-size: 14px;
}


.breadcrumb{
  @extend %breadcrumb-common;
  .ant-breadcrumb{
    margin: 12px 0 8px 0;
    @extend %ant-breadcrumb;
  }
  .createTitle{
    @extend %createTitle-common
  }
}

.breadcrumb-full-screen{
  @extend %breadcrumb-common;
  .ant-breadcrumb{
    padding: 12px 0 8px 0;
    @extend %ant-breadcrumb;
  }
  .createTitle{
    @extend %createTitle-common
  }
}