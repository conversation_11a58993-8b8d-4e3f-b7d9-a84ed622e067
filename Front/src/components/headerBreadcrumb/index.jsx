import { Breadcrumb } from 'antd';
import _ from 'lodash';
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import './index.scss';

const querystring = require('querystring');

export default function Index(props) {
  const { config, type, id } = props;
  const { link, level2List } = config;
  const [level1Name] = useState(config.level1Name);
  const currentLevelName = type ? _.find(level2List, (v) => v.value === type).name : '';
  const params = querystring.parse(window.location.search.substr(1));
  // currentLevelName
  return (
    <div className={`${params.fullScreen ? 'breadcrumb-full-screen' : 'breadcrumb'}`}>
      <Breadcrumb>
        <Breadcrumb.Item>
          <Link to={link[params.type || type]}>{level1Name[params.type || type]}</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{id ? `编辑${currentLevelName}` : `创建${currentLevelName}`}</Breadcrumb.Item>
      </Breadcrumb>
      {
        // 判断是否是全屏模式，如果全屏模式，隐藏下方标题
        params.fullScreen ? null : (
          <div className="createTitle">{id ? `编辑${currentLevelName}` : `创建${currentLevelName}`}</div>
        )
      }
    </div>
  );
}
