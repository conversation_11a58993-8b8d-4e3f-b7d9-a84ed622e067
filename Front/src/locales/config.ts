import languageService from '@/service/language';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { getInitCnLang, getInitEnLang } from './utils/helper';

const lang = ['zh_CN', 'en_US'].includes(localStorage.getItem('lang')!) ? localStorage.getItem('lang') : 'zh_CN';

let json = {
  cn: getInitCnLang(),
  en: getInitEnLang()
} as {
  cn: Object;
  en: Object;
};

i18n.use(initReactI18next).init({
  fallbackLng: lang as string,
  resources: {
    en_US: {
      translation: json.en
    },
    zh_CN: {
      translation: json.cn
    }
  },
  debug: false,
  interpolation: {
    escapeValue: false
  }
});

const updateI18nResources = async () => {
  try {
    const res = await languageService.getDatas({});
    json = {
      cn: {
        ...getInitCnLang(),
        ...res.cn
      },
      en: {
        ...getInitEnLang(),
        ...res.en
      }
    };
    i18n.addResourceBundle('zh_CN', 'translation', json.cn, true, true);
    i18n.addResourceBundle('en_US', 'translation', json.en, true, true);
  } catch (error) {
    console.error(error);
  }
  const cn = i18n.getResourceBundle('zh_CN', 'translation');
  const en = i18n.getResourceBundle('en_US', 'translation');
  Object.assign(window, { cn, en });
};

export { updateI18nResources };
